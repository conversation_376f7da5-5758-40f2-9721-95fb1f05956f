(function($) {
    'use strict';
    
    // SLMM debug system with fallback
    function debugLog(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.log('Broken Link Detector', message, data);
        } else {
            console.log('[Broken Link Detector] ' + message, data || '');
        }
    }
    
    function debugError(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.error('Broken Link Detector', message, data);
        } else {
            console.error('[Broken Link Detector] ' + message, data || '');
        }
    }

    var BrokenLinkDetector = {
        brokenLinks: [],
        dialog: null,
        checkInProgress: false,
        pendingChecks: [],
        settings: {
            checkOnLoad: true, // Check once on page load
            excludeDomains: [], // Domains to exclude from checking
            devMode: false, // Development mode flag - default OFF
            ignoreRelativeUrls: false // Ignore well-formed relative URLs - default OFF
        },
        validLinkCount: 0,
        recheckTimeout: null,

        init: function() {
            // Initialize variable to track valid links
            this.validLinkCount = 0;
            
            // Load saved settings if available
            this.loadSettings();
            
            // Create dialog container
            this.createDialog();
            
            // Initialize TinyMCE integration
            this.initTinyMCE();
            
            // Add the broken links button to the toolbar
            this.addToolbarButton();

            // Initialize with a single check on load if enabled
            if (this.settings.checkOnLoad) {
                $(document).ready(function() {
                    setTimeout(function() {
                        BrokenLinkDetector.checkForBrokenLinksInBackground();
                    }, 2000); // Initial delay to ensure editor is fully loaded
                });
            } else {
                // Set green indicator by default
                $(document).ready(function() {
                    setTimeout(function() {
                        // Set default valid state
                        var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
                        var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
                        tinymceIndicator.addClass('active valid').css('background-color', '#00a32a');
                        customIndicator.addClass('active valid').css('background-color', '#00a32a');
                    }, 2000);
                });
            }
        },

        // Load settings from localStorage
        loadSettings: function() {
            var savedSettings = localStorage.getItem('brokenLinkDetectorSettings');
            if (savedSettings) {
                try {
                    var parsedSettings = JSON.parse(savedSettings);
                    if (parsedSettings && typeof parsedSettings === 'object') {
                        this.settings = $.extend({}, this.settings, parsedSettings);
                    }
                } catch (e) {
                    debugError('Error loading broken link detector settings', e);
                }
            }
        },

        // Save settings to localStorage
        saveSettings: function() {
            localStorage.setItem('brokenLinkDetectorSettings', JSON.stringify(this.settings));
        },

        initTinyMCE: function() {
            var self = this;

            // Add button to editor toolbar if TinyMCE is active
            if (typeof tinymce !== 'undefined') {
                tinymce.PluginManager.add('broken_links', function(editor) {
                    editor.addButton('brokenlinks', {
                        icon: 'link',
                        tooltip: 'Check for broken links',
                        onPostRender: function() {
                            var btn = this;
                            var indicator = $('<span class="broken-links-indicator"></span>');
                            
                            // Add the indicator next to the button
                            editor.on('init', function() {
                                setTimeout(function() {
                                    $('.mce-i-link').closest('.mce-btn').append(indicator);
                                    
                                    // Setup content change handler to clear broken-link classes
                                    self.setupContentChangeHandler(editor);
                                }, 500);
                            });
                        },
                        onclick: function() {
                            self.checkForBrokenLinks(editor);
                        }
                    });
                    
                    // Add custom CSS to the editor to highlight broken links
                    editor.on('init', function() {
                        editor.dom.addStyle(
                            '.broken-link { border: 2px dotted #d63638 !important; padding: 2px; background-color: rgba(214, 54, 56, 0.1); }' +
                            '.broken-link::after { content: "⚠️"; font-size: 0.8em; vertical-align: super; color: #d63638; }'
                        );
                    });
                });
            }
        },
        
        // Add a broken links button to the custom toolbar in WordPress
        addToolbarButton: function() {
            var self = this;
            
            $(document).ready(function() {
                if ($('#broken-links-button').length === 0 && $('#custom-editor-buttons').length > 0) {
                    var buttonHTML = '<button id="broken-links-button" class="button custom-button">Links<span class="broken-links-indicator active valid" style="background-color: #00a32a;"></span><span class="broken-links-count"></span></button>';
                    $('#custom-editor-buttons').append(buttonHTML);
                    
                    // Add click handler
                    $('#broken-links-button').on('click', function(e) {
                        e.preventDefault();
                        if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                            self.checkForBrokenLinks(tinymce.activeEditor);
                        }
                    });
                }
            });
        },

        createDialog: function() {
            // Add overlay element
            var overlayHTML = '<div id="broken-links-overlay" style="display:none;"></div>';
            
            var dialogHTML = '\
                <div id="broken-links-dialog" style="display:none;" class="wp-core-ui">\
                    <div class="broken-links-content">\
                        <div class="dialog-header">\
                            <div class="title-group">\
                                <span class="dialog-title">Broken Links</span>\
                                <span id="broken-links-counter"></span>\
                            </div>\
                            <button type="button" class="close-button" aria-label="Close">&times;</button>\
                        </div>\
                        <div class="settings-panel">\
                            <div class="settings-row">\
                                <div class="check-on-load-toggle">\
                                    <label for="check-on-load-checkbox">Check on page load:</label>\
                                    <input type="checkbox" id="check-on-load-checkbox" ' + (this.settings.checkOnLoad ? 'checked' : '') + '>\
                                </div>\
                                <div class="dev-mode-toggle">\
                                    <label for="dev-mode-checkbox">Development mode:</label>\
                                    <input type="checkbox" id="dev-mode-checkbox" ' + (this.settings.devMode ? 'checked' : '') + '>\
                                </div>\
                                <button type="button" class="button button-small" id="save-settings">Save</button>\
                            </div>\
                            <div class="settings-actions">\
                                <button type="button" class="button" id="rescan-links">Rescan All Links</button>\
                                <button type="button" class="button" id="clear-highlights">Clear All Highlights</button>\
                            </div>\
                        </div>\
                        <div id="broken-links-list"></div>\
                    </div>\
                </div>';

            // Remove any existing dialog and overlay
            $('#broken-links-dialog, #broken-links-overlay').remove();
            
            // Add new elements
            $('body').append(overlayHTML + dialogHTML);

            // Style the dialog and overlay
            $('<style>').text(`
                #broken-links-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.2);
                    z-index: 159998;
                }
                #broken-links-dialog {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #ffffff;
                    padding: 20px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 159999;
                    width: 700px;
                    max-width: 90vw;
                    max-height: 80vh;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                }
                .broken-links-content {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    max-height: calc(80vh - 40px);
                }
                .dialog-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .title-group {
                    display: flex;
                    align-items: flex-end;
                    gap: 8px;
                }
                .dialog-title {
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 1;
                }
                #broken-links-counter {
                    color: #000;
                    font-size: 11px;
                    font-weight: 600;
                    line-height: 1;
                    margin-bottom: 1px;
                }
                .close-button {
                    background: none;
                    border: none;
                    font-size: 24px;
                    line-height: 1;
                    padding: 0;
                    cursor: pointer;
                    color: #666;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                }
                .close-button:hover {
                    background: #f0f0f0;
                    color: #000;
                }
                .link-item {
                    margin-bottom: 15px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 10px;
                }
                .link-item-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 5px;
                }
                .link-url {
                    font-weight: bold;
                    word-break: break-all;
                }
                .link-text {
                    color: #555;
                    margin-bottom: 5px;
                }
                .link-reason {
                    color: #d63638;
                    font-weight: 600;
                    margin-bottom: 10px;
                }
                .link-actions {
                    display: flex;
                    gap: 10px;
                }
                .goto-button,
                .edit-button {
                    cursor: pointer;
                    background: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 3px;
                    padding: 3px 8px;
                    font-size: 12px;
                }
                .goto-button:hover,
                .edit-button:hover {
                    background: #e0e0e0;
                }
                .broken-links-indicator {
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    margin-left: 5px;
                    vertical-align: middle;
                }
                .broken-links-indicator.active {
                    background-color: #d63638;
                }
                .broken-links-indicator.active.valid {
                    background-color: #00a32a !important;
                }
                .broken-links-count {
                    margin-left: 3px;
                    font-size: 0.8em;
                    vertical-align: middle;
                }
                .settings-panel {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                    padding: 10px;
                    background: #f8f8f8;
                    border-radius: 4px;
                    margin-bottom: 15px;
                }
                .settings-row {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    flex-wrap: wrap;
                    width: 100%;
                }
                .settings-actions {
                    display: flex;
                    gap: 10px;
                    padding-top: 5px;
                    border-top: 1px solid #e0e0e0;
                }
                .check-on-load-toggle, .dev-mode-toggle {
                    display: flex;
                    align-items: center;
                    gap: 5px;
                    margin-right: 15px;
                }
                #check-on-load-checkbox, #dev-mode-checkbox {
                    margin-top: 1px;
                }
                #rescan-links, #clear-highlights {
                    flex: 1;
                    text-align: center;
                }
                #broken-links-list {
                    flex: 1;
                    overflow-y: auto;
                    padding-right: 5px;
                    margin-bottom: 20px;
                }
                .scanning {
                    background-color: #f0f0f0 !important;
                    cursor: wait !important;
                }
                
                @keyframes pulse {
                    0% { opacity: 0.6; }
                    50% { opacity: 1; }
                    100% { opacity: 0.6; }
                }
                
                .scanning {
                    animation: pulse 1.5s infinite;
                }
                
                .link-item.success {
                    border-color: #00a32a;
                    background-color: rgba(0, 163, 42, 0.05);
                    padding: 15px;
                    text-align: center;
                }
                .link-item.success p {
                    color: #00a32a;
                    font-weight: 600;
                    margin: 0;
                }
            `).appendTo('head');

            this.bindDialogEvents();
        },

        showDialog: function() {
            $('#broken-links-overlay').show();
            $('#broken-links-dialog').show();
        },

        hideDialog: function() {
            $('#broken-links-overlay').hide();
            $('#broken-links-dialog').hide();
            
            // Clear any highlights in the editor
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                this.clearHighlights(tinymce.activeEditor);
            }
        },

        bindDialogEvents: function() {
            var self = this;

            // Close button click
            $(document).on('click', '#broken-links-dialog .close-button', function() {
                self.hideDialog();
            });

            // Escape key press
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#broken-links-dialog').is(':visible')) {
                    self.hideDialog();
                }
            });

            // Close dialog when clicking overlay
            $(document).on('click', '#broken-links-overlay', function() {
                self.hideDialog();
            });
            
            // Jump to broken link
            $(document).on('click', '.goto-button', function() {
                var index = $(this).data('index');
                self.jumpToLink(index);
                self.hideDialog(); // Close dialog after jumping to link
            });
            
            // Edit link directly
            $(document).on('click', '.edit-button', function() {
                var index = $(this).data('index');
                self.editLink(index);
                self.hideDialog(); // Close dialog after editing link
            });
            
            // Save settings from settings panel
            $(document).on('click', '#save-settings', function() {
                self.settings.checkOnLoad = $('#check-on-load-checkbox').is(':checked');
                self.settings.devMode = $('#dev-mode-checkbox').is(':checked');
                self.saveSettings();
                
                // Immediately run check with new settings
                if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                    self.checkForBrokenLinksInBackground(tinymce.activeEditor);
                }
            });
            
            // Clear all highlights button
            $(document).on('click', '#clear-highlights', function() {
                if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                    self.clearHighlights(tinymce.activeEditor);
                }
            });
            
            // Rescan all links button
            $(document).on('click', '#rescan-links', function() {
                if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                    // Clear all highlights first
                    self.clearHighlights(tinymce.activeEditor);
                    
                    // Show immediate visual feedback
                    var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
                    var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
                    
                    // Show gray "scanning" indicator
                    tinymceIndicator.removeClass('valid').addClass('active').css('background-color', '#cccccc');
                    customIndicator.removeClass('valid').addClass('active').css('background-color', '#cccccc');
                    
                    // Run the rescan
                    self.rescanLinks(tinymce.activeEditor);
                }
            });
        },
        
        // Debounce function to limit how often a function is called
        debounce: function(func, wait) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        },
        
        // Background check without showing dialog
        checkForBrokenLinksInBackground: function(editor) {
            if (!editor && typeof tinymce !== 'undefined') {
                editor = tinymce.activeEditor;
            }
            if (!editor || this.checkInProgress) return;
            
            this.checkInProgress = true;
            
            // Reset valid link count
            this.validLinkCount = 0;
            
            // Show indicator as red during scanning (if we're not sure yet, assume broken)
            var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
            var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
            tinymceIndicator.removeClass('valid').addClass('active').css('background-color', '#d63638');
            customIndicator.removeClass('valid').addClass('active').css('background-color', '#d63638');
            
            var content = editor.getContent({format: 'html'});
            this.findBrokenLinks(content, editor, function(brokenLinks, validLinkCount) {
                // Store the valid link count
                this.validLinkCount = validLinkCount || 0;
                
                // Update indicator - Default to green if no broken links
                if (brokenLinks.length > 0) {
                    tinymceIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                    customIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                    $('#broken-links-button').find('.broken-links-count').text(brokenLinks.length > 1 ? ` x ${brokenLinks.length}` : '');
                } else {
                    tinymceIndicator.addClass('active valid').css('background-color', '#00a32a');
                    customIndicator.addClass('active valid').css('background-color', '#00a32a');
                    $('#broken-links-button').find('.broken-links-count').text('');
                }
                
                // Force repaint to ensure visual update
                tinymceIndicator[0]?.offsetHeight;
                customIndicator[0]?.offsetHeight;
                
                // Store the broken links for later use
                this.brokenLinks = brokenLinks;
                
                // Highlight broken links in the editor
                this.highlightBrokenLinks(editor, brokenLinks);
                
                this.checkInProgress = false;
            }.bind(this));
        },
        
        // Check for broken links and show dialog if found
        checkForBrokenLinks: function(editor) {
            if (!editor) return;
            
            // Get the content once
            var content = editor.getContent({format: 'html'});
            
            // Update UI to show scan is happening
            this.updateIndicator(false, 0);
            var customButton = $('#broken-links-button');
            if (customButton.length) {
                customButton.addClass('scanning');
                customButton.text('Scanning...');
            }
            
            this.findBrokenLinks(content, editor, function(brokenLinks, validLinkCount) {
                // Store the valid link count
                this.validLinkCount = validLinkCount || 0;
                
                // Initialize variables for indicators
                var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
                var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
                
                // Update indicator
                if (brokenLinks.length > 0) {
                    tinymceIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                    customIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                    $('#broken-links-button').find('.broken-links-count').text(brokenLinks.length > 1 ? ` x ${brokenLinks.length}` : '');
                } else {
                    tinymceIndicator.addClass('active valid').css('background-color', '#00a32a');
                    customIndicator.addClass('active valid').css('background-color', '#00a32a');
                    $('#broken-links-button').find('.broken-links-count').text('');
                }
                
                // Store the broken links for later use
                this.brokenLinks = brokenLinks;
                
                // Highlight broken links in the editor
                this.highlightBrokenLinks(editor, brokenLinks);
                
                // Show dialog with results
                this.showBrokenLinksDialog(brokenLinks, editor);
                
                // Reset button state
                if (customButton.length) {
                    customButton.removeClass('scanning');
                    customButton.html('Links<span class="broken-links-indicator"></span><span class="broken-links-count"></span>');
                    
                    // Update the indicator inside the button
                    var newIndicator = customButton.find('.broken-links-indicator');
                    if (brokenLinks.length > 0) {
                        newIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                        customButton.find('.broken-links-count').text(brokenLinks.length > 1 ? ` x ${brokenLinks.length}` : '');
                    } else {
                        newIndicator.addClass('active valid').css('background-color', '#00a32a');
                        customButton.find('.broken-links-count').text('');
                    }
                }
            }.bind(this));
        },
        
        // Find broken links in the editor content
        findBrokenLinks: function(content, editor, callback) {
            // Create a temporary div to parse the HTML
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            
            // Collect all links
            var links = tempDiv.querySelectorAll('a');
            var brokenLinks = [];
            var checkedCount = 0;
            var validLinkCount = 0;
            var batchSize = 5; // Process links in small batches for responsiveness
            
            debugLog('🔍 Starting scan of ' + links.length + ' links for issues');
            
            if (links.length === 0) {
                debugLog('No links found in content');
                // Support both callback signatures for backward compatibility
                if (callback.length === 1) {
                    callback(brokenLinks);
                } else {
                    callback(brokenLinks, validLinkCount);
                }
                return;
            }
            
            // Define patterns for potential broken links - ONLY structural issues
            var suspiciousPatterns = [
                // ALWAYS BROKEN - Structural issues only
                { pattern: /^\s*$/, reason: 'Empty URL', alwaysBroken: true },
                { pattern: /^#$/, reason: 'Empty anchor', alwaysBroken: true },
                { pattern: /^\s+|\s+$/, reason: 'URL contains leading/trailing whitespace', alwaysBroken: true },
                { pattern: /\s+/, reason: 'URL contains whitespace', alwaysBroken: true },
                
                // Non-HTTP protocols that don't need checking
                { pattern: /^(javascript|mailto|tel|sms|whatsapp|viber|skype|market|itms|intent):/, reason: 'Not a web link', skipCheck: true, notBroken: true },
                
                // Anchor links
                { pattern: /^#.*$/, reason: 'Anchor link', skipCheck: true, warning: true },
                
                // Malformed URLs - Always broken
                { pattern: /^https?:\/\/\s*$/, reason: 'Incomplete URL', alwaysBroken: true },
                { pattern: /^https?:(?!\/\/)/, reason: 'Malformed URL (missing //)', alwaysBroken: true },
                { pattern: /^https?:\/\/$/, reason: 'Incomplete URL with protocol only', alwaysBroken: true },
                { pattern: /^\/\/\s*$/, reason: 'Incomplete protocol-relative URL', alwaysBroken: true },
                
                // Common typos and mistakes - Always broken
                { pattern: /^https?\/\//, reason: 'Missing colon in protocol', alwaysBroken: true },
                { pattern: /^https?:\/([^\/])/, reason: 'Missing slash in protocol', alwaysBroken: true },
                { pattern: /^http:\/\/https?:\/\//, reason: 'Double protocol', alwaysBroken: true }
            ];

            // First pass: Check patterns synchronously - these are immediate checks
            var linksToCheck = [];
            Array.from(links).forEach(function(link, index) {
                var href = link.getAttribute('href') || '';
                var text = link.textContent.trim();
                var isBroken = false;
                var reason = '';
                var skipStatusCheck = false;
                var isWarningOnly = false;
                var forceStatusCheck = false;
                var isAlwaysBroken = false;
                
                debugLog('🔗 Checking link[' + index + ']: ' + href);
                
                // Check against suspicious patterns first
                for (var i = 0; i < suspiciousPatterns.length; i++) {
                    var pattern = suspiciousPatterns[i];
                    if (href.match(pattern.pattern)) {
                        debugLog('  ↳ ✓ Link matched pattern: ' + pattern.pattern + ' - ' + pattern.reason);
                        
                        if (pattern.notBroken) {
                            skipStatusCheck = true;
                            break;
                        }
                        
                        // If it's marked as always broken, this overrides any other setting
                        if (pattern.alwaysBroken) {
                            isAlwaysBroken = true;
                            debugLog('  ↳ 🚫 This is an always-broken pattern and will be flagged regardless of settings');
                        }
                        
                        // For patterns that should be checked via HTTP status regardless
                        if (pattern.checkStatus) {
                            forceStatusCheck = true;
                            isWarningOnly = pattern.warning || false;
                            reason = pattern.reason;
                            
                            debugLog('  ↳ ⚠️ Suspicious URL pattern detected, will check HTTP status');
                            break;
                        }
                        
                        isBroken = true;
                        reason = pattern.reason;
                        skipStatusCheck = pattern.skipCheck || false;
                        isWarningOnly = pattern.warning || false;
                        break;
                    }
                }
                
                if (isBroken && !forceStatusCheck) {
                    debugLog('  ↳ ❌ BROKEN LINK: ' + href + ' - Reason: ' + reason);
                    
                    brokenLinks.push({
                        index: index,
                        href: href,
                        text: text,
                        reason: reason,
                        warning: isWarningOnly,
                        element: link,
                        alwaysBroken: isAlwaysBroken
                    });
                } else if (!skipStatusCheck || forceStatusCheck) {
                    // Add to the list of links that need to be checked via HTTP
                    debugLog('  ↳ 🔄 Will check link status via HTTP: ' + href);
                    linksToCheck.push({
                        index: index,
                        href: href,
                        text: text,
                        element: link,
                        suspiciousReason: reason || null,
                        isWarningOnly: isWarningOnly,
                        alwaysBroken: isAlwaysBroken
                    });
                } else {
                    // This is a link that doesn't need checking (like mailto, tel, etc.)
                    debugLog('  ↳ ✅ Link type doesn\'t need checking (e.g., mailto, tel): ' + href);
                    validLinkCount++;
                }
            }.bind(this));
            
            // If we've already found all broken links or no links need checking, return
            if (linksToCheck.length === 0) {
                debugLog('No links need HTTP status checking. Done with ' + brokenLinks.length + ' broken links found.');
                // Support both callback signatures for backward compatibility
                if (callback.length === 1) {
                    callback(brokenLinks);
                } else {
                    callback(brokenLinks, validLinkCount);
                }
                return;
            }
            
            // Immediately highlight the links we've found so far
            if (brokenLinks.length > 0) {
                debugLog('Highlighting ' + brokenLinks.length + ' broken links found in first pass');
                this.highlightBrokenLinks(editor, brokenLinks);
            }
            
            // Second pass: Process HTTP checks in batches
            var self = this;
            var currentBatch = 0;
            
            debugLog('Starting HTTP status checks for ' + linksToCheck.length + ' links in batches of ' + batchSize);
            
            function processBatch() {
                var batch = linksToCheck.slice(
                    currentBatch * batchSize,
                    (currentBatch + 1) * batchSize
                );
                
                if (batch.length === 0) {
                    // All batches processed, return results
                    debugLog('✅ All HTTP checks completed. Final results: ' + brokenLinks.length + ' broken links, ' + validLinkCount + ' valid links');
                    // Support both callback signatures for backward compatibility
                    if (callback.length === 1) {
                        callback(brokenLinks);
                    } else {
                        callback(brokenLinks, validLinkCount);
                    }
                    return;
                }
                
                debugLog('Processing batch ' + (currentBatch + 1) + ' with ' + batch.length + ' links');
                var batchCheckedCount = 0;
                
                batch.forEach(function(linkInfo) {
                    var href = linkInfo.href;
                    
                    // If it's a relative URL, make it absolute
                    var absoluteUrl = href;
                    if (href.indexOf('http') !== 0 && href.indexOf('//') !== 0) {
                        // Get site URL from WordPress data if available
                        var siteUrl = '';
                        if (typeof window.wpApiSettings !== 'undefined' && wpApiSettings.root) {
                            siteUrl = wpApiSettings.root.replace(/wp-json.*$/, '');
                        } else {
                            // Try to extract from current URL
                            var urlParts = window.location.href.split('/');
                            siteUrl = urlParts[0] + '//' + urlParts[2];
                        }
                        
                        // Make relative URL absolute - prevent double slashes
                        if (href.charAt(0) === '/') {
                            // Remove trailing slash from siteUrl if the href starts with a slash
                            if (siteUrl.endsWith('/')) {
                                siteUrl = siteUrl.slice(0, -1);
                            }
                            absoluteUrl = siteUrl + href;
                        } else {
                            // Make sure siteUrl ends with a slash if the href doesn't start with one
                            if (!siteUrl.endsWith('/')) {
                                siteUrl += '/';
                            }
                            absoluteUrl = siteUrl + href;
                        }
                        debugLog("Converted relative URL '" + href + "' to absolute: " + absoluteUrl);
                    }
                    
                    // Check if link is localhost 
                    var isLocalhost = absoluteUrl.indexOf('localhost') > -1 || absoluteUrl.indexOf('127.0.0.1') > -1;
                    
                    // Always log that we're checking the link
                    if (isLocalhost) {
                        debugLog("Localhost URL detected: " + absoluteUrl + " - Will check and report status");
                    }
                    
                    // Check all links regardless of type
                    self.checkLinkStatus(absoluteUrl, function(isValid, status) {
                        if (!isValid) {
                            // For structural errors or 404s, always report as broken
                            if (status === 404 || linkInfo.alwaysBroken || 
                                status === 'empty-url' || status.toString().indexOf('whitespace') > -1) {
                                
                                debugError("HTTP check FAILED for: " + absoluteUrl + " - Status: " + status + " (ALWAYS REPORTED)");
                                
                                // Use the suspicious reason if it was flagged in pattern check
                                var reason = linkInfo.suspiciousReason || 'URL returns ' + status + ' error response';
                                
                                var brokenLink = {
                                    index: linkInfo.index,
                                    href: linkInfo.href,
                                    text: linkInfo.text,
                                    reason: reason,
                                    warning: false,
                                    element: linkInfo.element,
                                    alwaysBroken: true
                                };
                                brokenLinks.push(brokenLink);
                                
                                // Highlight this broken link immediately
                                self.highlightSingleLink(editor, brokenLink);
                            }
                            // For other issues in dev mode, log but don't flag
                            else if (isLocalhost && self.settings.devMode) {
                                debugLog("HTTP check FAILED for: " + absoluteUrl + " - Status: " + status + " (Dev mode - informational only)");
                                validLinkCount++; // Count as valid in dev mode
                            } 
                            // All other errors are reported normally
                            else {
                                debugError("HTTP check FAILED for: " + absoluteUrl + " - Status: " + status);
                                
                                // Use the suspicious reason if it was flagged in pattern check
                                var reason = linkInfo.suspiciousReason || 'URL returns ' + status + ' error response';
                                
                                var brokenLink = {
                                    index: linkInfo.index,
                                    href: linkInfo.href,
                                    text: linkInfo.text,
                                    reason: reason,
                                    warning: linkInfo.isWarningOnly,
                                    element: linkInfo.element
                                };
                                brokenLinks.push(brokenLink);
                                
                                // Highlight this broken link immediately
                                self.highlightSingleLink(editor, brokenLink);
                            }
                        } else {
                            debugLog("HTTP check PASSED for: " + absoluteUrl + " - Status: " + status);
                            validLinkCount++; // Increment valid link count
                        }
                        
                        batchCheckedCount++;
                        if (batchCheckedCount === batch.length) {
                            currentBatch++;
                            processBatch();
                        }
                    });
                });
            }
            
            // Start processing batches
            processBatch();
        },
        
        // Highlight a single broken link in the editor
        highlightSingleLink: function(editor, brokenLink) {
            if (!editor || !brokenLink) return;
            
            var links = editor.dom.select('a');
            var link = links[brokenLink.index];
            
            if (link) {
                editor.dom.addClass(link, 'broken-link');
                
                // Update the indicator if needed
                var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
                var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
                
                tinymceIndicator.removeClass('valid').addClass('active').css('background-color', '#d63638');
                customIndicator.removeClass('valid').addClass('active').css('background-color', '#d63638');
                
                // Safety check to initialize brokenLinks if not yet defined
                if (!this.brokenLinks) {
                    this.brokenLinks = [];
                }
                
                var brokenLinksCount = this.brokenLinks.length + 1;
                $('#broken-links-button').find('.broken-links-count').text(brokenLinksCount > 1 ? ` x ${brokenLinksCount}` : '');
            }
        },
        
        // Check if a link is valid by sending a HEAD request
        checkLinkStatus: function(url, callback) {
            var self = this;
            
            debugLog('🌐 Checking URL status: ' + url);
            
            // Simple local URL pattern check as fast pre-validation
            if (url.trim() === '') {
                debugLog('  ↳ URL is empty, marking as invalid');
                callback(false, 'empty-url');
                return;
            }
            
            // For absolute URLs, check domain format is valid
            if (url.indexOf('http') === 0) {
                try {
                    new URL(url); // This will throw error for invalid URLs
                } catch(e) {
                    debugLog('  ↳ URL is malformed: ' + url, e);
                    callback(false, 'malformed-url');
                    return;
                }
            }
            
            // First try direct browser fetch for local URLs
            var isLocalUrl = this.isLocalUrl(url);
            if (isLocalUrl) {
                debugLog("Checking local URL directly: " + url);
                this.directLinkCheck(url, function(isValid, status) {
                    debugLog("Local URL check result: " + (isValid ? "Valid" : "Invalid") + " (Status: " + status + ")");
                    callback(isValid, status);
                });
                return;
            }
            
            // Use WordPress admin-ajax.php to proxy the request for external URLs
            debugLog("Using WordPress AJAX to check external URL: " + url);
            $.ajax({
                url: window.ajaxurl || (typeof slmmLinkCheck !== 'undefined' ? slmmLinkCheck.ajaxurl : '/wp-admin/admin-ajax.php'),
                type: 'POST',
                data: {
                    action: 'check_link_status',
                    url: url,
                    security: typeof slmmLinkCheck !== 'undefined' ? slmmLinkCheck.nonce : ''
                },
                success: function(response) {
                    if (response && typeof response.success !== 'undefined') {
                        debugLog("AJAX response: " + (response.success ? "Valid" : "Invalid") + (response.status ? " (Status: " + response.status + ")" : ""));
                        callback(response.success, response.status || (response.success ? 200 : 404));
                    } else {
                        debugLog("AJAX response was invalid, falling back to direct check");
                        // Fallback to direct fetch when admin-ajax fails
                        self.directLinkCheck(url, function(isValid, status) {
                            debugLog("AJAX fallback check result: " + (isValid ? "Valid" : "Invalid") + " (Status: " + status + ")");
                            callback(isValid, status);
                        });
                    }
                },
                error: function(xhr, status, error) {
                    debugError("AJAX error: " + status + " - " + error);
                    // If the AJAX check fails, try direct fetch as fallback
                    self.directLinkCheck(url, function(isValid, status) {
                        debugLog("AJAX fallback check result: " + (isValid ? "Valid" : "Invalid") + " (Status: " + status + ")");
                        callback(isValid, status);
                    });
                }
            });
        },
        
        // Check if a URL is on the local site
        isLocalUrl: function(url) {
            // Get the current domain
            var currentDomain = window.location.hostname;
            var currentPort = window.location.port ? ':' + window.location.port : '';
            var fullDomain = currentDomain + currentPort;
            
            // Special check for localhost URLs - always consider them local
            if (url.indexOf('localhost') > -1 || url.indexOf('127.0.0.1') > -1) {
                debugLog("Localhost URL detected, treating as local: " + url);
                return true;
            }
            
            try {
                // For absolute URLs, check the hostname
                if (url.indexOf('http') === 0) {
                    var urlObj = new URL(url);
                    return urlObj.hostname === currentDomain || 
                           urlObj.hostname + ':' + urlObj.port === fullDomain ||
                           urlObj.hostname === 'localhost';
                }
                
                // For relative URLs (not protocol-relative), they're on the local site
                if (url.indexOf('/') === 0 && url.indexOf('//') !== 0) {
                    return true;
                }
                
                return false;
            } catch(e) {
                debugError("Error checking if URL is local", e);
                return false;
            }
        },
        
        // Simple client-side check for URLs
        directLinkCheck: function(url, callback) {
            var self = this;
            var statusCode = null;
            
            // Handle relative URLs (make them absolute)
            if (url.charAt(0) === '/' && url.charAt(1) !== '/') {
                var baseUrl = window.location.protocol + '//' + window.location.host;
                
                // Prevent double slashes - remove trailing slash from baseUrl
                if (baseUrl.endsWith('/')) {
                    baseUrl = baseUrl.slice(0, -1);
                }
                
                url = baseUrl + url;
                debugLog("Converted relative URL to absolute: " + url);
            }
            
            // Special check for whitespace in URL - ALWAYS flag as broken
            if (url.match(/\s+/)) {
                debugError("URL contains whitespace, always invalid: " + url);
                callback(false, 'contains-whitespace');
                return;
            }
            
            // Special check for empty URLs - ALWAYS flag as broken
            if (url.trim() === '') {
                debugError("URL is empty, always invalid");
                callback(false, 'empty-url');
                return;
            }
            
            // For localhost or same-domain URLs, we can use fetch with cors: 'same-origin'
            debugLog("Performing direct fetch check for: " + url);
            
            // First try a HEAD request (lightweight)
            fetch(url, { 
                method: 'HEAD',
                cache: 'no-store',
                credentials: 'same-origin',
                redirect: 'follow'
            })
            .then(function(response) {
                statusCode = response.status;
                debugLog("HEAD response: " + response.status + " (" + response.statusText + ")");
                
                // 2xx status codes are OK, 304 is Not Modified (also OK)
                var isValid = (response.ok || response.status === 304);
                callback(isValid, response.status);
            })
            .catch(function(error) {
                debugLog("HEAD request failed, trying GET", error);
                
                // If HEAD fails, try a GET request as fallback
                fetch(url, { 
                    method: 'GET', 
                    cache: 'no-store',
                    credentials: 'same-origin',
                    redirect: 'follow'
                })
                .then(function(response) {
                    statusCode = response.status;
                    debugLog("GET response: " + response.status + " (" + response.statusText + ")");
                    callback(response.ok || response.status === 304, response.status);
                })
                .catch(function(error) {
                    debugError("Both HEAD and GET requests failed", error);
                    callback(false, 'network-error');
                });
            });
        },
        
        // Update the indicator dot
        updateIndicator: function(hasBrokenLinks, count) {
            var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
            var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
            var countSpan = $('#broken-links-button').find('.broken-links-count');
            
            // First, remove all classes to ensure a clean state
            tinymceIndicator.removeClass('active valid');
            customIndicator.removeClass('active valid');
            
            if (hasBrokenLinks) {
                tinymceIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                customIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                countSpan.text(count > 1 ? ` x ${count}` : '');
            } else {
                // Always show green indicator when no broken links (default state)
                tinymceIndicator.addClass('active valid').css('background-color', '#00a32a');
                customIndicator.addClass('active valid').css('background-color', '#00a32a');
                countSpan.text('');
            }
            
            // Force repaint to ensure visual update
            tinymceIndicator[0]?.offsetHeight;
            customIndicator[0]?.offsetHeight;
        },
        
        // Show dialog with broken links
        showBrokenLinksDialog: function(brokenLinks, editor) {
            // Update counter
            if (brokenLinks.length === 0) {
                $('#broken-links-counter').text('All links are valid');
                $('#broken-links-list').html('<div class="link-item success"><p>No broken links were found in your content.</p></div>');
                
                // Explicitly update indicator to show green for all valid links
                if (this.validLinkCount > 0) {
                    this.updateIndicator(false, 0);
                }
            } else {
                $('#broken-links-counter').text(brokenLinks.length + ' broken link' + (brokenLinks.length > 1 ? 's' : '') + ' found');
                
                // Generate list view
                var listHTML = '';
                brokenLinks.forEach(function(link, index) {
                    listHTML += '<div class="link-item">';
                    listHTML += '<div class="link-item-header">';
                    listHTML += '<span class="link-url">' + this.escapeHTML(link.href) + '</span>';
                    listHTML += '</div>';
                    
                    if (link.text) {
                        listHTML += '<div class="link-text">Text: "' + this.escapeHTML(link.text) + '"</div>';
                    }
                    
                    listHTML += '<div class="link-reason">Issue: ' + link.reason + '</div>';
                    
                    listHTML += '<div class="link-actions">';
                    listHTML += '<button class="goto-button" data-index="' + link.index + '">Jump to link</button>';
                    listHTML += '<button class="edit-button" data-index="' + link.index + '">Edit link</button>';
                    listHTML += '</div>';
                    
                    listHTML += '</div>';
                }.bind(this));
                
                $('#broken-links-list').html(listHTML);
            }
            
            // Show dialog
            this.showDialog();
        },
        
        // Highlight broken links in the editor
        highlightBrokenLinks: function(editor, brokenLinks) {
            // First, clear any existing highlights
            this.clearHighlights(editor);
            
            // Get editor DOM
            var editorBody = editor.getBody();
            
            // Find all links in the editor
            var links = editorBody.querySelectorAll('a');
            
            // Apply highlights to broken links
            brokenLinks.forEach(function(brokenLink) {
                var link = links[brokenLink.index];
                if (link) {
                    editor.dom.addClass(link, 'broken-link');
                }
            });
        },
        
        // Clear highlights in editor
        clearHighlights: function(editor) {
            var links = editor.dom.select('a.broken-link');
            for (var i = 0; i < links.length; i++) {
                editor.dom.removeClass(links[i], 'broken-link');
            }
        },
        
        // Jump to a specific link
        jumpToLink: function(index) {
            var editor = tinymce.activeEditor;
            if (!editor) return;
            
            // Get all links in the editor
            var links = editor.dom.select('a');
            var targetLink = links[index];
            
            if (!targetLink) return;
            
            // Scroll to the link
            editor.selection.select(targetLink);
            editor.selection.scrollIntoView(targetLink);
            
            // Add a temporary highlight
            editor.dom.addClass(targetLink, 'broken-link');
            
            // Focus the editor
            editor.focus();
        },
        
        // Edit a link directly
        editLink: function(index) {
            var editor = tinymce.activeEditor;
            var self = this;
            if (!editor) return;
            
            // Get all links in the editor
            var links = editor.dom.select('a');
            var targetLink = links[index];
            
            if (!targetLink) return;
            
            // Select the link
            editor.selection.select(targetLink);
            editor.focus();
            
            // Try multiple methods to open the link dialog
            try {
                // Method 1: TinyMCE command
                editor.execCommand('mceLink');
                
                // Set up a MutationObserver to detect when the link is changed
                this.observeLinkChanges(editor, targetLink);
            } catch(e) {
                // Method 2: Click the link button in the toolbar
                setTimeout(function() {
                    try {
                        var linkButton = $(editor.editorContainer).find('.mce-i-link').closest('.mce-btn');
                        if (linkButton.length) {
                            linkButton.click();
                            self.observeLinkChanges(editor, targetLink);
                        } else {
                            // Method 3: Simple prompt fallback
                            self.editLinkWithPrompt(editor, targetLink);
                        }
                    } catch(err) {
                        // Final fallback
                        self.editLinkWithPrompt(editor, targetLink);
                    }
                }, 100);
            }
        },
        
        // Fallback method using a simple prompt
        editLinkWithPrompt: function(editor, link) {
            var self = this;
            var currentUrl = editor.dom.getAttrib(link, 'href');
            var newUrl = prompt('Edit URL:', currentUrl);
            
            if (newUrl !== null && newUrl !== currentUrl) {
                editor.dom.setAttrib(link, 'href', newUrl);
                editor.dom.removeClass(link, 'broken-link');
                
                // Re-check the link
                setTimeout(function() {
                    self.checkForBrokenLinksInBackground(editor);
                }, 500);
            }
        },
        
        // Monitor link changes using MutationObserver
        observeLinkChanges: function(editor, link) {
            var self = this;
            var originalHref = editor.dom.getAttrib(link, 'href');
            
            // Setup mutation observer
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'href') {
                        var newHref = editor.dom.getAttrib(link, 'href');
                        if (newHref !== originalHref) {
                            // Remove broken-link class
                            editor.dom.removeClass(link, 'broken-link');
                            
                            // Re-check links immediately to update indicators
                            clearTimeout(self.recheckTimeout);
                            self.recheckTimeout = setTimeout(function() {
                                self.checkForBrokenLinksInBackground(editor);
                            }, 1000);
                            
                            // Stop observing
                            observer.disconnect();
                        }
                    }
                });
            });
            
            // Start observing
            observer.observe(link, { attributes: true });
            
            // Stop observing after 20 seconds (safety)
            setTimeout(function() {
                observer.disconnect();
            }, 20000);
        },
        
        // Helper to escape HTML
        escapeHTML: function(text) {
            var div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // Add a listener to TinyMCE to clear broken-link classes on content change
        setupContentChangeHandler: function(editor) {
            var self = this;
            
            // Listen for any click on links to prepare for edits
            editor.on('click', function(e) {
                if (e.target.tagName === 'A') {
                    // Store the link for potential edit
                    self.lastClickedLink = e.target;
                }
            });
            
            // Listen for mouseup events to detect link edits
            editor.on('mouseup', function(e) {
                if (self.lastClickedLink) {
                    // Remove the broken-link class on click
                    editor.dom.removeClass(self.lastClickedLink, 'broken-link');
                    
                    // Immediately update indicator to "scanning" state
                    var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
                    var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
                    tinymceIndicator.removeClass('active valid').addClass('scanning').css('background-color', '#cccccc');
                    customIndicator.removeClass('active valid').addClass('scanning').css('background-color', '#cccccc');
                    
                    // Re-check links after editing to update indicators
                    clearTimeout(self.recheckTimeout);
                    self.recheckTimeout = setTimeout(function() {
                        self.checkForBrokenLinksInBackground(editor);
                    }, 500);
                    
                    setTimeout(function() {
                        self.lastClickedLink = null;
                    }, 100);
                }
            });
            
            // Listen for changes to the editor content
            editor.on('Change input NodeChange SetContent', function(e) {
                // Get all links with the broken-link class
                var brokenLinks = editor.dom.select('a.broken-link');
                
                // For each link, check if it's being modified
                brokenLinks.forEach(function(link) {
                    // Check if this link is currently selected
                    if (editor.selection.getNode() === link || 
                        link.contains(editor.selection.getNode())) {
                        // If so, remove the broken-link class
                        editor.dom.removeClass(link, 'broken-link');
                        
                        // Show scanning indicator
                        var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
                        var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
                        tinymceIndicator.removeClass('active valid').addClass('scanning').css('background-color', '#cccccc');
                        customIndicator.removeClass('active valid').addClass('scanning').css('background-color', '#cccccc');
                        
                        // Re-check links after a delay to update indicators
                        clearTimeout(self.recheckTimeout);
                        self.recheckTimeout = setTimeout(function() {
                            self.checkForBrokenLinksInBackground(editor);
                        }, 500);
                    }
                });
            });
        },

        // Rescan function to ensure proper check
        rescanLinks: function(editor) {
            if (!editor) return;
            
            // Show indication of scanning in progress
            var customButton = $('#broken-links-button');
            var tinymceIndicator = $('.mce-i-link').closest('.mce-btn').find('.broken-links-indicator');
            var customIndicator = $('#broken-links-button').find('.broken-links-indicator');
            
            // Clear broken links array and highlights first
            this.brokenLinks = [];
            this.validLinkCount = 0;
            this.clearHighlights(editor);
            
            // Show scanning UI
            tinymceIndicator.removeClass('valid').addClass('active').css('background-color', '#cccccc');
            customIndicator.removeClass('valid').addClass('active').css('background-color', '#cccccc');
            
            if (customButton.length) {
                customButton.addClass('scanning');
                customButton.text('Scanning...');
            }
            
            debugLog('Rescanning all links...');
            
            // Force recheck of all links
            var content = editor.getContent({format: 'html'});
            this.findBrokenLinks(content, editor, function(brokenLinks, validLinkCount) {
                this.validLinkCount = validLinkCount || 0;
                this.brokenLinks = brokenLinks;
                this.highlightBrokenLinks(editor, brokenLinks);
                
                // Update indicator with fresh state - only green if no broken links
                if (brokenLinks.length > 0) {
                    tinymceIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                    customIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                    $('#broken-links-button').find('.broken-links-count').text(brokenLinks.length > 1 ? ` x ${brokenLinks.length}` : '');
                } else {
                    tinymceIndicator.addClass('active valid').css('background-color', '#00a32a');
                    customIndicator.addClass('active valid').css('background-color', '#00a32a');
                    $('#broken-links-button').find('.broken-links-count').text('');
                }
                
                // Force repaint to ensure visual update
                tinymceIndicator[0]?.offsetHeight;
                customIndicator[0]?.offsetHeight;
                
                debugLog('Rescan complete. Found ' + brokenLinks.length + ' broken links and ' + validLinkCount + ' valid links.');
                
                // Reset button state
                if (customButton.length) {
                    customButton.removeClass('scanning');
                    customButton.html('Links<span class="broken-links-indicator"></span><span class="broken-links-count"></span>');
                    
                    // Update the indicator inside the button
                    var newIndicator = customButton.find('.broken-links-indicator');
                    if (brokenLinks.length > 0) {
                        newIndicator.addClass('active').removeClass('valid').css('background-color', '#d63638');
                        customButton.find('.broken-links-count').text(brokenLinks.length > 1 ? ` x ${brokenLinks.length}` : '');
                    } else {
                        newIndicator.addClass('active valid').css('background-color', '#00a32a');
                        customButton.find('.broken-links-count').text('');
                    }
                }
            }.bind(this));
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        BrokenLinkDetector.init();
    });

})(jQuery);
