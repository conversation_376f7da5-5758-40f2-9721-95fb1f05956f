<?php
/**
 * Emergency Script: Reset Corrupted Model Settings
 * Run this ONCE to clear corrupted model/provider selections
 * Access: http://localhost:8884/wp-content/plugins/slmm_seo_bundle/reset-model-settings.php
 */

// Basic WordPress environment
define('WP_USE_THEMES', false);

// Try multiple possible paths to wp-load.php
$possible_paths = [
    '../../../../wp-load.php',
    '../../../wp-load.php',
    '/var/www/html/wp-load.php',
    dirname(__FILE__) . '/../../../../wp-load.php',
    dirname(__FILE__) . '/../../../wp-load.php'
];

$loaded = false;
foreach ($possible_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $loaded = true;
        break;
    }
}

if (!$loaded) {
    die('Error: Could not locate wp-load.php. Please run this script from WordPress admin or adjust the path.');
}

// Security check - only run for admin users
if (!current_user_can('manage_options')) {
    die('Access denied: Admin privileges required');
}

echo "<h2>SLMM Model Settings Reset</h2>";
echo "<p><strong>Clearing potentially corrupted model/provider settings...</strong></p>";

// List of potential corrupted options based on model selector functionality
$options_to_reset = array(
    // Main plugin options that could be corrupted
    'slmm_gpt_prompts',
    'chatgpt_generator_options',

    // Model selection fields (common field names from code analysis)
    'slmm_title_generation_model',
    'slmm_title_generation_provider',
    'slmm_description_generation_model',
    'slmm_description_generation_provider',

    // Model cache and temporary options
    'slmm_model_cache_openai',
    'slmm_model_cache_anthropic',
    'slmm_model_cache_openrouter',
    'slmm_model_selection_temp',

    // Any transients that might be corrupted
    '_transient_slmm_models_openai',
    '_transient_slmm_models_anthropic',
    '_transient_slmm_models_openrouter'
);

$reset_count = 0;
$found_options = array();

foreach ($options_to_reset as $option) {
    $current_value = get_option($option, 'NOT_FOUND');

    if ($current_value !== 'NOT_FOUND') {
        $found_options[] = $option;

        // Delete the option to clear corrupted data
        $deleted = delete_option($option);

        if ($deleted) {
            echo "<p>✅ RESET: {$option}</p>";
            $reset_count++;
        } else {
            echo "<p>⚠️ FAILED to reset: {$option}</p>";
        }
    }
}

echo "<hr>";
echo "<p><strong>Reset Summary:</strong></p>";
echo "<p>• Options found and reset: {$reset_count}</p>";
echo "<p>• Options that were found: " . implode(', ', $found_options) . "</p>";

if ($reset_count > 0) {
    echo "<p style='color: green;'><strong>✅ CORRUPTION CLEARED!</strong></p>";
    echo "<p>Corrupted model settings have been reset. The hover+click functionality should now work properly.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ol>";
    echo "<li>Go back to interlinking interface</li>";
    echo "<li>Test hover+click functionality (should work now)</li>";
    echo "<li>If needed, reconfigure your AI providers in Settings</li>";
    echo "<li>Delete this reset script for security: <code>rm " . __FILE__ . "</code></li>";
    echo "</ol>";
} else {
    echo "<p style='color: orange;'>No corrupted options found. The issue might be elsewhere.</p>";
}

echo "<hr>";
echo "<p><em>This script can be safely deleted after use.</em></p>";
?>