/**
 * SLMM Direct Editor - Modal & Interface Styles
 * 
 * WP Sheet Editor-style modal design with professional dark theme
 * integration and bulletproof data integrity interface elements.
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Direct_Editing
 * @version 1.0.0
 * @since 4.10.0
 */

/* =============================================================================
   MODAL FOUNDATION & Z-INDEX MANAGEMENT
   ============================================================================= */

.slmm-direct-editor-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 110001; /* Higher than Notes modal (100000) */
    display: none;
    backdrop-filter: blur(2px);
}

.slmm-direct-editor-modal {
    background: #1a1a1a;
    border: none;
    border-radius: 0;
    width: 100vw;
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Main content area with dynamic resizable layout */
.slmm-direct-editor-main {
    flex: 1;
    display: flex;
    flex-direction: row;
    height: calc(100vh - 70px); /* Account for header height */
    --editor-width: 75%;
    --sidebar-width: 25%;
}

/* Editor area (dynamic width with CSS custom properties) */
.slmm-direct-editor-editor-area {
    flex: 0 0 var(--editor-width);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Resize handle between editor and sidebar */
.slmm-resize-handle {
    width: 4px;
    background: #333;
    cursor: col-resize;
    position: relative;
    transition: background-color 0.15s ease;
    flex-shrink: 0;
}

.slmm-resize-handle:hover {
    background: #555;
}

.slmm-resize-handle:active {
    background: #666;
}

/* Resizing state visual feedback */
.slmm-resizing {
    user-select: none;
    cursor: col-resize;
}

.slmm-resizing .slmm-resize-handle {
    background: #888;
}

/* AI Tools sidebar (dynamic width with CSS custom properties) */
.slmm-direct-editor-sidebar {
    flex: 0 0 var(--sidebar-width);
    background: #111;
    border-left: 2px solid #333;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Sidebar header */
.slmm-direct-editor-sidebar-header {
    background: #2c2c2c;
    color: #f3f4f6;
    padding: 12px 16px;
    border-bottom: 1px solid #333;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.slmm-direct-editor-sidebar-header::before {
    content: "";
    margin-right: 0;
}

/* Slug input in sidebar header */
.slmm-sidebar-slug-input {
    flex: 1;
    background: #1a1a1a;
    border: 1px solid #4b5563;
    border-radius: 4px;
    color: #f3f4f6;
    padding: 6px 8px;
    font-size: 12px;
    font-weight: 400;
    min-width: 0;
}

.slmm-sidebar-slug-input:hover,
.slmm-sidebar-slug-input:focus {
    border-color: #7c3aed;
    outline: none;
    box-shadow: 0 0 0 2px rgba(124, 92, 237, 0.2);
}

.slmm-sidebar-slug-input::placeholder {
    color: #6b7280;
    font-size: 11px;
}

/* Slug label in sidebar header */
.slmm-sidebar-slug-label {
    font-size: 10px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

/* Sidebar content */
.slmm-direct-editor-sidebar-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    color: #9ca3af;
    font-size: 14px;
}

/* Placeholder styling for AI tools */
.slmm-ai-tools-placeholder {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    margin-top: 20px;
}

/* Special z-index management for Bricks Builder context */
.bricks-context .slmm-direct-editor-overlay {
    z-index: 999999;
}

/* =============================================================================
   MODAL HEADER - WP Sheet Editor Style
   ============================================================================= */

.slmm-direct-editor-header {
    background: linear-gradient(135deg, #2c2c2c 0%, #1e1e1e 100%);
    color: #f3f4f6;
    padding: 16px 20px;
    border-bottom: 2px solid #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.slmm-direct-editor-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 10px;
}

.slmm-direct-editor-post-info {
    font-size: 30px;
    color: #ffffff;
    margin-left: 10px;
}

/* ACF Title Swapping Visual Indicators */
.slmm-direct-editor-post-info.slmm-acf-swapped-title {
    color: #10b981; /* Green color to indicate ACF content */
    border-left: 3px solid #10b981;
    padding-left: 8px;
    position: relative;
}

.slmm-direct-editor-post-info.slmm-acf-swapped-title {
    position: relative;
    display: inline-block;
    margin-right: 60px; /* Space for external badge */
}

.slmm-direct-editor-post-info.slmm-acf-swapped-title::after {
    content: "ACF";
    position: absolute;
    top: 50%;
    left: calc(100% + 10px); /* Position completely outside the title to the right */
    transform: translateY(-50%);
    background: #10b981;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    letter-spacing: 0.5px;
    white-space: nowrap;
    z-index: 10; /* Ensure it stays on top */
}

.slmm-direct-editor-post-info[data-acf-title="true"] {
    cursor: text; /* Changed from help to text to indicate editability */
    transition: all 0.2s ease;
}

.slmm-direct-editor-post-info[data-acf-title="true"]:hover {
    opacity: 0.9;
    background: rgba(16, 185, 129, 0.05);
    border-radius: 4px;
}

/* ACF Title Editing States */
.slmm-direct-editor-post-info[data-acf-title="true"][contenteditable="true"] {
    cursor: text;
    outline: none; /* Remove default browser outline */
}

.slmm-direct-editor-post-info.acf-title-editing {
    border: 2px solid #3b82f6 !important;
    background: rgba(59, 130, 246, 0.05) !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    border-radius: 4px;
    animation: acf-edit-pulse 0.3s ease;
}

.slmm-direct-editor-post-info.acf-title-saving {
    opacity: 0.7;
    cursor: wait !important;
    border: 2px solid #f59e0b !important;
    background: rgba(245, 158, 11, 0.05) !important;
    animation: acf-saving-pulse 1s ease infinite;
}

.slmm-direct-editor-post-info.acf-title-saved {
    border: 2px solid #10b981 !important;
    background: rgba(16, 185, 129, 0.05) !important;
    box-shadow: 0 0 5px rgba(16, 185, 129, 0.3) !important;
    animation: acf-saved-flash 0.5s ease;
}

.slmm-direct-editor-post-info.acf-title-error {
    border: 2px solid #ef4444 !important;
    background: rgba(239, 68, 68, 0.05) !important;
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.3) !important;
    animation: acf-error-shake 0.5s ease;
}

/* ACF Title Editing Animations */
@keyframes acf-edit-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes acf-saving-pulse {
    0%, 100% { opacity: 0.7; }
    50% { opacity: 0.5; }
}

@keyframes acf-saved-flash {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

@keyframes acf-error-shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

/* Modal Controls */
.slmm-direct-editor-controls {
    display: flex;
    align-items: center;
    gap: 12px;
}

.slmm-direct-editor-status {
    font-size: 13px;
    color: #9ca3af;
    padding: 6px 12px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
    text-align: center;
}

.slmm-direct-editor-status.saving {
    color: #fbbf24;
    border-color: #fbbf24;
    background: rgba(251, 191, 36, 0.1);
}

.slmm-direct-editor-status.saved {
    color: #10b981;
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.slmm-direct-editor-status.error {
    color: #ef4444;
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.slmm-direct-editor-status.typing {
    color: #8b5cf6;
    border-color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

.slmm-direct-editor-close {
    background: none;
    border: 2px solid #6b7280;
    color: #d1d5db;
    font-size: 20px;
    width: 36px;
    height: 36px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.slmm-direct-editor-close:hover {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
    transform: scale(1.05);
}

/* =============================================================================
   CONTENT AREA & EDITOR CONTAINER
   ============================================================================= */

.slmm-direct-editor-content {
    flex: 1;
    padding: 20px;
    overflow: auto;
    background: #111;
    position: relative;
    height: 100%;
}

.slmm-direct-editor-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* WordPress Editor Integration */
.slmm-direct-editor-content .wp-editor-wrap {
    border: 2px solid #333;
    border-radius: 6px;
    background: #1a1a1a;
    overflow: hidden;
}

.slmm-direct-editor-content .wp-editor-tabs {
    background: #2c2c2c;
    border-bottom: 1px solid #333;
}

.slmm-direct-editor-content .wp-editor-tabs button {
    background: transparent;
    color: #d1d5db;
    border: none;
    padding: 8px 16px;
    cursor: pointer;
}

.slmm-direct-editor-content .wp-editor-tabs button.active {
    background: #1a1a1a;
    color: white;
}

.slmm-direct-editor-content .wp-editor-container {
    background: #1a1a1a;
}

/* TinyMCE Dark Theme Integration */
.slmm-direct-editor-content .mce-tinymce {
    border: none !important;
    background: #1a1a1a !important;
}

.slmm-direct-editor-content .mce-toolbar {
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
}

.slmm-direct-editor-content .mce-btn {
    background: transparent !important;
    border: 1px solid transparent !important;
    color: #d1d5db !important;
}

.slmm-direct-editor-content .mce-btn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: #6b7280 !important;
}

.slmm-direct-editor-content .mce-edit-area iframe {
    background: #1a1a1a !important;
}

/* CRITICAL: TinyMCE iframe content text styling */
.slmm-direct-editor-content .mce-content-body {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

.slmm-direct-editor-content .mce-content-body p {
    color: #f3f4f6 !important;
}

.slmm-direct-editor-content .mce-content-body h1,
.slmm-direct-editor-content .mce-content-body h2,
.slmm-direct-editor-content .mce-content-body h3,
.slmm-direct-editor-content .mce-content-body h4,
.slmm-direct-editor-content .mce-content-body h5,
.slmm-direct-editor-content .mce-content-body h6 {
    color: #f3f4f6 !important;
}

.slmm-direct-editor-content .mce-content-body a {
    color: #60a5fa !important;
}

.slmm-direct-editor-content .mce-content-body ul,
.slmm-direct-editor-content .mce-content-body ol,
.slmm-direct-editor-content .mce-content-body li {
    color: #f3f4f6 !important;
}

/* Text selection colors for dark theme */
.slmm-direct-editor-content .mce-content-body ::selection {
    background: #3b82f6 !important;
    color: white !important;
}

.slmm-direct-editor-content .mce-content-body ::-moz-selection {
    background: #3b82f6 !important;
    color: white !important;
}

/* Code editor (textarea) dark theme */
.slmm-direct-editor-content .wp-editor-area textarea,
.slmm-direct-editor-content textarea#content {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border: 2px solid #333 !important;
    border-radius: 4px !important;
    font-family: Consolas, Monaco, 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    padding: 16px !important;
}

.slmm-direct-editor-content .wp-editor-area textarea:focus,
.slmm-direct-editor-content textarea#content:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

/* Code textarea text selection */
.slmm-direct-editor-content .wp-editor-area textarea::selection,
.slmm-direct-editor-content textarea#content::selection {
    background: #3b82f6 !important;
    color: white !important;
}

.slmm-direct-editor-content .wp-editor-area textarea::-moz-selection,
.slmm-direct-editor-content textarea#content::-moz-selection {
    background: #3b82f6 !important;
    color: white !important;
}

/* Additional TinyMCE Dark Theme Elements */
.slmm-direct-editor-content .mce-btn.mce-active,
.slmm-direct-editor-content .mce-btn:active {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: #3b82f6 !important;
}

.slmm-direct-editor-content .mce-btn.mce-disabled {
    opacity: 0.4 !important;
    color: #6b7280 !important;
}

/* =============================================================================
   GLOBAL TINYMCE DARK THEME (FOR ELEMENTS OUTSIDE MODAL)
   ============================================================================= */

/* 
 * CRITICAL: TinyMCE often appends dropdowns, tooltips, and popup elements
 * directly to document.body, outside of the modal container. These global
 * selectors ensure dark theme styling works regardless of DOM placement.
 */

/* TinyMCE dropdown menus - GLOBAL (appended to body) */
.mce-menu {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6) !important;
    z-index: 100106 !important; /* Above TinyMCE modal block (100100) */
}

.mce-menu .mce-menu-item {
    color: #d1d5db !important;
    background: transparent !important;
}

.mce-menu .mce-menu-item:hover {
    background: #374151 !important;
    color: #f3f4f6 !important;
}

.mce-menu .mce-menu-item-normal.mce-active {
    background: #3b82f6 !important;
    color: white !important;
}

.mce-menu .mce-menu-item.mce-disabled {
    color: #6b7280 !important;
    opacity: 0.5 !important;
}

/* TinyMCE panels and popups - GLOBAL */
.mce-panel {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5) !important;
}

.mce-window-head {
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
}

.mce-window-head .mce-title {
    color: #f3f4f6 !important;
}

/* TinyMCE floating panels (format dropdown, color picker, etc.) - GLOBAL */
.mce-floatpanel {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6) !important;
    z-index: 100106 !important; /* Above TinyMCE dialogs */
}

/* TinyMCE tooltips - GLOBAL */
.mce-tooltip {
    background: #2c2c2c !important;
    color: #d1d5db !important;
    border: 1px solid #333 !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
    z-index: 100107 !important; /* Above TinyMCE menus */
}

.mce-tooltip .mce-tooltip-inner {
    background: #2c2c2c !important;
    color: #d1d5db !important;
}

/* TinyMCE color picker - GLOBAL */
.mce-colorpicker {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
}

.mce-colorpicker .mce-colorpicker-sv {
    background: #2c2c2c !important;
}

/* TinyMCE format preview - GLOBAL */
.mce-preview {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border: 1px solid #333 !important;
}

/* TinyMCE listbox dropdown - GLOBAL */
.mce-listbox .mce-txt {
    color: #d1d5db !important;
    background: #2c2c2c !important;
}

.mce-menubtn:hover .mce-txt,
.mce-listbox:hover .mce-txt {
    color: #f3f4f6 !important;
    background: #374151 !important;
}

/* TinyMCE split button dropdowns - GLOBAL */
.mce-splitbtn:hover .mce-open,
.mce-splitbtn.mce-active .mce-open {
    background: #374151 !important;
    border-color: #6b7280 !important;
}

/* TinyMCE form controls in dialogs - GLOBAL */
.mce-window .mce-textbox,
.mce-window .mce-combobox input {
    background: #2c2c2c !important;
    color: #f3f4f6 !important;
    border: 1px solid #6b7280 !important;
}

.mce-window .mce-textbox:focus,
.mce-window .mce-combobox input:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

.mce-window .mce-btn {
    background: #374151 !important;
    color: #d1d5db !important;
    border: 1px solid #6b7280 !important;
}

.mce-window .mce-btn:hover {
    background: #4b5563 !important;
    border-color: #9ca3af !important;
}

.mce-window .mce-primary {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
}

.mce-window .mce-primary:hover {
    background: #2563eb !important;
    border-color: #2563eb !important;
}

/* TinyMCE dialog background - GLOBAL */
.mce-window {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.7) !important;
    z-index: 100105 !important; /* Above TinyMCE modal block (100100) */
}

.mce-window-body {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
}

/* TinyMCE window container overlay - GLOBAL */
.mce-container,
.mce-container-body {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
}

/* TinyMCE dropdown caret/arrow - GLOBAL */
.mce-caret {
    border-top-color: #333 !important;
    border-bottom-color: #333 !important;
}

/* TinyMCE scrollbars in floating elements - GLOBAL */
.mce-menu::-webkit-scrollbar,
.mce-floatpanel::-webkit-scrollbar,
.mce-window::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.mce-menu::-webkit-scrollbar-track,
.mce-floatpanel::-webkit-scrollbar-track,
.mce-window::-webkit-scrollbar-track {
    background: #2c2c2c;
}

.mce-menu::-webkit-scrollbar-thumb,
.mce-floatpanel::-webkit-scrollbar-thumb,
.mce-window::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

.mce-menu::-webkit-scrollbar-thumb:hover,
.mce-floatpanel::-webkit-scrollbar-thumb:hover,
.mce-window::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* TinyMCE status bar in dialogs - GLOBAL */
.mce-statusbar {
    background: #2c2c2c !important;
    border-top: 1px solid #333 !important;
    color: #9ca3af !important;
}

/* TinyMCE tab panels - GLOBAL */
.mce-tab {
    background: #2c2c2c !important;
    color: #d1d5db !important;
    border: 1px solid #4b5563 !important;
}

.mce-tab.mce-active,
.mce-tab:hover {
    background: #374151 !important;
    color: #f3f4f6 !important;
}

/* Modern TinyMCE 5/6 floating UI elements - GLOBAL */
.tox-tinymce,
.tox .tox-editor-header {
    background: #1a1a1a !important;
    border-color: #333 !important;
}

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
    background: #2c2c2c !important;
    border-color: #333 !important;
}

.tox .tox-tbtn,
.tox .tox-tbtn--select {
    color: #d1d5db !important;
    background: transparent !important;
}

.tox .tox-tbtn:hover,
.tox .tox-tbtn--enabled {
    background: #374151 !important;
    color: #f3f4f6 !important;
}

.tox .tox-tbtn--active {
    background: #3b82f6 !important;
    color: white !important;
}

/* TinyMCE dropdown and popup z-index management - GLOBAL */
.mce-floatpanel,
.mce-menu,
.mce-tooltip,
.mce-window,
.tox-pop,
.tox-dialog {
    z-index: 100105 !important; /* Above TinyMCE modal block (100100) */
}

/* WordPress-specific TinyMCE media modal integration - GLOBAL */
.media-modal {
    z-index: 100005 !important;
}

.media-modal-backdrop {
    z-index: 100004 !important;
}

/* Accessibility improvements for global TinyMCE elements */
.mce-menu [role="menuitem"]:focus,
.mce-floatpanel .mce-btn:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: -2px !important;
}

/* High contrast mode support for global elements */
@media (prefers-contrast: high) {
    .mce-menu,
    .mce-floatpanel,
    .mce-window {
        background: #000 !important;
        border-color: #fff !important;
    }
    
    .mce-menu .mce-menu-item,
    .mce-window .mce-btn {
        color: #fff !important;
        background: #000 !important;
    }
}

/* =============================================================================
   SCOPED TINYMCE DARK THEME (WITHIN MODAL CONTAINER)
   ============================================================================= */

/* TinyMCE listbox (format dropdown) within modal */
.slmm-direct-editor-content .mce-listbox .mce-txt {
    color: #d1d5db !important;
    background: #2c2c2c !important;
}

.slmm-direct-editor-content .mce-menubtn:hover .mce-txt,
.slmm-direct-editor-content .mce-listbox:hover .mce-txt {
    color: #f3f4f6 !important;
    background: #374151 !important;
}

/* Additional modal-scoped TinyMCE elements */
.slmm-direct-editor-content .mce-splitbtn:hover .mce-open,
.slmm-direct-editor-content .mce-splitbtn.mce-active .mce-open {
    background: #374151 !important;
    border-color: #6b7280 !important;
}

/* Ensure proper cursor visibility in TinyMCE */
.slmm-direct-editor-content .mce-content-body {
    caret-color: #f3f4f6 !important;
}

/* WordPress editor tabs dark theme */
.slmm-direct-editor-content .wp-editor-tabs {
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
}

.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor {
    background: transparent !important;
    color: #9ca3af !important;
    border: none !important;
    padding: 8px 16px !important;
    cursor: pointer !important;
}

.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #d1d5db !important;
}

.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor.switch-tmce,
.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor.switch-html {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
}

/* Placeholder text for empty content */
.slmm-direct-editor-content .mce-content-body p:empty::before {
    content: 'Start typing...';
    color: #6b7280 !important;
    font-style: italic;
    pointer-events: none;
}

/* =============================================================================
   GLOBAL TINYMCE DARK THEME - DOM PLACEMENT AGNOSTIC  
   ============================================================================= */
   
/* CRITICAL: Global selectors for TinyMCE elements appended to document.body */

/* Core TinyMCE toolbar buttons - works anywhere in DOM */
body .mce-btn,
.mce-toolbar .mce-btn,
.mce-container .mce-btn {
    background: transparent !important;
    border: 1px solid transparent !important;
    color: #d1d5db !important;
}

body .mce-btn:hover,
.mce-toolbar .mce-btn:hover,
.mce-container .mce-btn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    border-color: #6b7280 !important;
    color: #f3f4f6 !important;
}

body .mce-btn.mce-active,
.mce-toolbar .mce-btn.mce-active,
.mce-container .mce-btn.mce-active {
    background: rgba(59, 130, 246, 0.2) !important;
    border-color: #3b82f6 !important;
    color: #f3f4f6 !important;
}

body .mce-btn.mce-disabled,
.mce-toolbar .mce-btn.mce-disabled,
.mce-container .mce-btn.mce-disabled {
    opacity: 0.4 !important;
    color: #6b7280 !important;
}

/* TinyMCE toolbars - global styling */
body .mce-toolbar,
.mce-container .mce-toolbar {
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
    border-color: #333 !important;
}

/* TinyMCE button text and icons */
body .mce-btn .mce-txt,
.mce-container .mce-btn .mce-txt {
    color: #d1d5db !important;
}

body .mce-btn:hover .mce-txt,
.mce-container .mce-btn:hover .mce-txt {
    color: #f3f4f6 !important;
}

body .mce-btn .mce-ico,
.mce-container .mce-btn .mce-ico {
    color: #d1d5db !important;
}

body .mce-btn:hover .mce-ico,
.mce-container .mce-btn:hover .mce-ico {
    color: #f3f4f6 !important;
}

/* Global dropdown menus and panels - CRITICAL for body-appended elements */
body .mce-menu,
.mce-floatpanel,
.mce-popover {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
    z-index: 100106 !important; /* Above TinyMCE dialogs */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.8) !important;
}

body .mce-menu .mce-menu-item,
.mce-floatpanel .mce-menu-item {
    color: #d1d5db !important;
    background: transparent !important;
}

body .mce-menu .mce-menu-item:hover,
.mce-floatpanel .mce-menu-item:hover {
    background: #374151 !important;
    color: #f3f4f6 !important;
}

body .mce-menu .mce-menu-item.mce-active,
.mce-floatpanel .mce-menu-item.mce-active {
    background: #3b82f6 !important;
    color: white !important;
}

/* Global TinyMCE dialogs and windows */
body .mce-window,
.mce-panel {
    background: #1a1a1a !important;
    border: 1px solid #333 !important;
    z-index: 100105 !important; /* Above TinyMCE modal block (100100) */
}

body .mce-window-head,
.mce-panel .mce-window-head {
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
}

body .mce-window-head .mce-title,
.mce-panel .mce-title {
    color: #f3f4f6 !important;
}

/* Global tooltips */
body .mce-tooltip,
.mce-tooltip-inner {
    background: #2c2c2c !important;
    color: #f3f4f6 !important;
    border: 1px solid #333 !important;
    z-index: 100107 !important; /* Above menus and dialogs */
}

/* Modern TinyMCE 5.x/6.x support (.tox- prefix) */
body .tox .tox-tbtn,
.tox .tox-tbtn {
    color: #d1d5db !important;
    background: transparent !important;
}

body .tox .tox-tbtn:hover,
.tox .tox-tbtn:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #f3f4f6 !important;
}

body .tox .tox-tbtn--enabled,
.tox .tox-tbtn--enabled {
    background: rgba(59, 130, 246, 0.2) !important;
    color: #f3f4f6 !important;
}

body .tox-pop,
.tox-dialog {
    z-index: 100105 !important; /* Above TinyMCE modal block (100100) */
    background: #1a1a1a !important;
}

/* TinyMCE containers and wrappers */
body .mce-tinymce,
.mce-container.mce-tinymce {
    border: 2px solid #333 !important;
    background: #1a1a1a !important;
}

/* Icon colors for better visibility */
body .mce-i-bold:before,
body .mce-i-italic:before, 
body .mce-i-underline:before,
body .mce-i-strikethrough:before,
body .mce-i-link:before,
body .mce-i-unlink:before,
body .mce-i-bullist:before,
body .mce-i-numlist:before,
body .mce-i-alignleft:before,
body .mce-i-aligncenter:before,
body .mce-i-alignright:before,
body .mce-i-undo:before,
body .mce-i-redo:before {
    color: #d1d5db !important;
}

/* Hover state for icons */
body .mce-btn:hover .mce-i-bold:before,
body .mce-btn:hover .mce-i-italic:before,
body .mce-btn:hover .mce-i-underline:before,
body .mce-btn:hover .mce-i-strikethrough:before,
body .mce-btn:hover .mce-i-link:before,
body .mce-btn:hover .mce-i-unlink:before,
body .mce-btn:hover .mce-i-bullist:before,
body .mce-btn:hover .mce-i-numlist:before,
body .mce-btn:hover .mce-i-alignleft:before,
body .mce-btn:hover .mce-i-aligncenter:before,
body .mce-btn:hover .mce-i-alignright:before,
body .mce-btn:hover .mce-i-undo:before,
body .mce-btn:hover .mce-i-redo:before {
    color: #f3f4f6 !important;
}

/* =============================================================================
   LOADING & VALIDATION STATES
   ============================================================================= */

.slmm-direct-editor-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #9ca3af;
    font-size: 16px;
}

.slmm-direct-editor-loading::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #374151;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    margin-right: 12px;
    animation: slmm-spinner 1s linear infinite;
}

@keyframes slmm-spinner {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Content Lock Dialog */
.slmm-content-locked-dialog {
    background: #1a1a1a;
    border: 4px dashed #f97316;
    color: #d1d5db;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    max-width: 500px;
    margin: 0 auto;
}

.slmm-content-locked-dialog h3 {
    color: #f97316;
    margin: 0 0 16px 0;
    font-size: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.slmm-content-locked-dialog h3::before {
    content: "🔒";
    font-size: 24px;
}

.slmm-content-locked-dialog p {
    margin: 12px 0;
    line-height: 1.6;
    color: #d1d5db;
}

.slmm-content-locked-dialog code {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 13px;
}

.slmm-content-locked-dialog .lock-reason {
    font-weight: 500;
    color: #fbbf24;
    margin-top: 20px;
}

/* =============================================================================
   PROFESSIONAL BUTTON STYLING (40px standard)
   ============================================================================= */

.slmm-direct-editor-btn {
    min-height: 40px !important;
    line-height: 38px !important;
    padding: 0 16px !important;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    border: 2px solid;
}

.slmm-direct-editor-btn-primary {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.slmm-direct-editor-btn-primary:hover {
    background: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
}

.slmm-direct-editor-btn-secondary {
    background: transparent;
    color: #d1d5db;
    border-color: #6b7280;
}

.slmm-direct-editor-btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: #9ca3af;
}

/* =============================================================================
   INTERLINKING SUITE NODE INTEGRATION
   ============================================================================= */

/* Direct Edit Button (4th button alongside E, V, +) */
.node-button-direct-edit {
    background: #8b5cf6 !important;
    color: white !important;
    border: 2px solid #8b5cf6 !important;
    font-weight: bold;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-left: 4px;
}

.node-button-direct-edit:hover {
    background: #7c3aed !important;
    border-color: #7c3aed !important;
    transform: scale(1.1);
}

.node-button-direct-edit:active {
    transform: scale(0.95);
}

/* Disabled state for locked content */
.node-button-direct-edit.disabled {
    background: #4b5563 !important;
    border-color: #4b5563 !important;
    color: #9ca3af !important;
    cursor: not-allowed;
    opacity: 0.5;
}

.node-button-direct-edit.disabled:hover {
    transform: none;
}

/* Button container spacing adjustment for 4 buttons */
.node-buttons {
    gap: 3px;
}

/* =============================================================================
   RESPONSIVE DESIGN
   ============================================================================= */

@media (max-width: 1024px) {
    /* Stack layout on medium screens - disable resize handle */
    .slmm-direct-editor-main {
        flex-direction: column;
    }
    
    .slmm-direct-editor-editor-area {
        flex: 0 0 70%;
    }
    
    .slmm-direct-editor-sidebar {
        flex: 0 0 30%;
        border-left: none;
        border-top: 2px solid #333;
    }
    
    .slmm-resize-handle {
        display: none; /* Hide resize handle on mobile */
    }
}

@media (max-width: 768px) {
    /* Full width editor, collapse sidebar - disable resize handle */
    .slmm-direct-editor-main {
        flex-direction: column;
    }
    
    .slmm-direct-editor-editor-area {
        flex: 1;
    }
    
    .slmm-direct-editor-sidebar {
        flex: 0 0 200px;
        border-left: none;
        border-top: 2px solid #333;
    }
    
    .slmm-resize-handle {
        display: none; /* Hide resize handle on mobile */
    }
    
    .slmm-direct-editor-header {
        padding: 12px 16px;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .slmm-direct-editor-title {
        font-size: 22px;
    }
    
    .slmm-direct-editor-controls {
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .slmm-direct-editor-content {
        padding: 16px;
    }
    
    .slmm-direct-editor-sidebar-content {
        padding: 12px;
    }
}

@media (max-width: 480px) {
    /* Hide sidebar completely on very small screens */
    .slmm-direct-editor-sidebar {
        display: none;
    }
    
    .slmm-direct-editor-editor-area {
        flex: 1;
    }
    
    .slmm-content-locked-dialog {
        padding: 20px;
        max-width: none;
    }
}

/* =============================================================================
   ACCESSIBILITY ENHANCEMENTS
   ============================================================================= */

/* Focus management */
.slmm-direct-editor-modal:focus-within {
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
}

.slmm-direct-editor-close:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .slmm-direct-editor-modal {
        border-color: #fff;
        background: #000;
    }
    
    .slmm-direct-editor-header {
        background: #000;
        border-color: #fff;
    }
    
    .slmm-content-locked-dialog {
        border-color: #ff0;
        background: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .slmm-direct-editor-btn,
    .slmm-direct-editor-close,
    .node-button-direct-edit {
        transition: none;
    }
    
    .slmm-direct-editor-close:hover,
    .node-button-direct-edit:hover {
        transform: none;
    }
    
    @keyframes slmm-spinner {
        0%, 100% { transform: rotate(0deg); }
    }
}

/* =============================================================================
   MEMORY MANAGEMENT & CLEANUP INDICATORS
   ============================================================================= */

.slmm-editor-cleanup-notice {
    position: absolute;
    top: 10px;
    right: 60px;
    background: rgba(16, 185, 129, 0.9);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.slmm-editor-cleanup-notice.show {
    opacity: 1;
}

/* =============================================================================
   CONTEXT-SPECIFIC OVERRIDES
   ============================================================================= */

/* Bricks Builder Context */
.bricks-context .slmm-direct-editor-content .mce-toolbar {
    background: #1a1a1a !important;
}

.bricks-context .slmm-direct-editor-modal {
    border: 3px solid #f97316;
}

/* WordPress Classic Editor Context */
.classic-editor-context .slmm-direct-editor-content .wp-editor-wrap {
    border-color: #10b981;
}

/* Development Mode Indicators */
.wp-debug .slmm-direct-editor-header::after {
    content: "DEV";
    background: #ef4444;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    font-weight: bold;
    margin-left: auto;
}

/* =============================================================================
   WORDPRESS ADMIN ELEMENT OVERRIDES - SCOPED TO DIRECT EDITOR MODAL
   ============================================================================= */

/* CRITICAL: These overrides ensure WordPress admin elements within the Direct Editor
   modal use dark theme styling instead of inheriting light theme defaults.
   All selectors are scoped to prevent site-wide admin style conflicts. */

/* QuickTags Toolbar (HTML/Code Editor Toolbar) */
.slmm-direct-editor-modal .quicktags-toolbar,
.slmm-direct-editor-content .quicktags-toolbar {
    background: #2c2c2c !important;
    border: 1px solid #333 !important;
    border-radius: 4px 4px 0 0 !important;
    padding: 6px !important;
}

.slmm-direct-editor-modal .quicktags-toolbar .ed_button,
.slmm-direct-editor-content .quicktags-toolbar .ed_button {
    background: #374151 !important;
    border: 1px solid #6b7280 !important;
    color: #f3f4f6 !important;
    margin: 2px !important;
    padding: 2px 8px !important;
    border-radius: 3px !important;
    font-size: 11px !important;
}

.slmm-direct-editor-modal .quicktags-toolbar .ed_button:hover,
.slmm-direct-editor-content .quicktags-toolbar .ed_button:hover {
    background: #4b5563 !important;
    border-color: #9ca3af !important;
    color: white !important;
}

.slmm-direct-editor-modal .quicktags-toolbar .ed_button:active,
.slmm-direct-editor-content .quicktags-toolbar .ed_button:active {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
}

/* WordPress Editor Container & Textarea Overrides */
.slmm-direct-editor-modal .wp-editor-container,
.slmm-direct-editor-content .wp-editor-container {
    background: #1a1a1a !important;
    border: 2px solid #333 !important;
    border-radius: 6px !important;
}

.slmm-direct-editor-modal .wp-editor-container textarea.wp-editor-area,
.slmm-direct-editor-content .wp-editor-container textarea.wp-editor-area,
.slmm-direct-editor-modal textarea#content,
.slmm-direct-editor-content textarea#content {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border: none !important;
    font-family: Consolas, Monaco, 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.5 !important;
    padding: 16px !important;
    resize: vertical !important;
}

.slmm-direct-editor-modal .wp-editor-container textarea.wp-editor-area:focus,
.slmm-direct-editor-content .wp-editor-container textarea.wp-editor-area:focus,
.slmm-direct-editor-modal textarea#content:focus,
.slmm-direct-editor-content textarea#content:focus {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border: none !important;
    outline: 2px solid #3b82f6 !important;
    outline-offset: -2px !important;
    box-shadow: none !important;
}

/* WordPress Editor Tabs Override */
.slmm-direct-editor-modal .wp-editor-tabs,
.slmm-direct-editor-content .wp-editor-tabs {
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
    padding: 0 !important;
    margin: 0 !important;
}

.slmm-direct-editor-modal .wp-editor-tabs .wp-switch-editor,
.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor {
    background: transparent !important;
    color: #9ca3af !important;
    border: none !important;
    border-radius: 0 !important;
    padding: 8px 16px !important;
    margin: 0 !important;
    cursor: pointer !important;
    font-size: 13px !important;
}

.slmm-direct-editor-modal .wp-editor-tabs .wp-switch-editor:hover,
.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #f3f4f6 !important;
}

.slmm-direct-editor-modal .wp-editor-tabs .wp-switch-editor.switch-tmce,
.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor.switch-tmce,
.slmm-direct-editor-modal .wp-editor-tabs .wp-switch-editor.switch-html,  
.slmm-direct-editor-content .wp-editor-tabs .wp-switch-editor.switch-html {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border-bottom: 2px solid #8b5cf6 !important;
}

/* WordPress Media Buttons */
.slmm-direct-editor-modal .wp-media-buttons,
.slmm-direct-editor-content .wp-media-buttons {
    padding: 12px 16px !important;
    background: #2c2c2c !important;
    border-bottom: 1px solid #333 !important;
}

.slmm-direct-editor-modal .wp-media-buttons .button,
.slmm-direct-editor-content .wp-media-buttons .button {
    background: #374151 !important;
    border: 1px solid #6b7280 !important;
    color: #f3f4f6 !important;
    padding: 6px 12px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
}

.slmm-direct-editor-modal .wp-media-buttons .button:hover,
.slmm-direct-editor-content .wp-media-buttons .button:hover {
    background: #4b5563 !important;
    border-color: #9ca3af !important;
    color: white !important;
}

.slmm-direct-editor-modal .wp-media-buttons .button:active,
.slmm-direct-editor-content .wp-media-buttons .button:active {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
}

/* WordPress Editor Wrap States */
.slmm-direct-editor-modal .wp-editor-wrap.html-active .wp-editor-container textarea,
.slmm-direct-editor-content .wp-editor-wrap.html-active .wp-editor-container textarea {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
}

.slmm-direct-editor-modal .wp-editor-wrap.tmce-active .wp-editor-container,
.slmm-direct-editor-content .wp-editor-wrap.tmce-active .wp-editor-container {
    background: #1a1a1a !important;
}

/* GPT Prompt Container Elements */
.slmm-direct-editor-modal .slmm-gpt-prompt-container,
.slmm-direct-editor-content .slmm-gpt-prompt-container {
    background: transparent !important;
}

.slmm-direct-editor-modal .slmm-gpt-prompt-dropdown,
.slmm-direct-editor-content .slmm-gpt-prompt-dropdown {
    background: #2c2c2c !important;
    border: 1px solid #6b7280 !important;
    color: #f3f4f6 !important;
    border-radius: 4px !important;
    padding: 6px 10px !important;
}

.slmm-direct-editor-modal .slmm-gpt-prompt-dropdown:focus,
.slmm-direct-editor-content .slmm-gpt-prompt-dropdown:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

.slmm-direct-editor-modal .slmm-execute-gpt-prompt,
.slmm-direct-editor-content .slmm-execute-gpt-prompt {
    background: #3b82f6 !important;
    border: 1px solid #3b82f6 !important;
    color: white !important;
    border-radius: 4px !important;
    padding: 6px 12px !important;
    font-size: 12px !important;
}

.slmm-direct-editor-modal .slmm-execute-gpt-prompt:hover,
.slmm-direct-editor-content .slmm-execute-gpt-prompt:hover {
    background: #2563eb !important;
    border-color: #2563eb !important;
}

/* Override WordPress Admin Body Styles Within Modal */
.slmm-direct-editor-modal {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif !important;
}

.slmm-direct-editor-modal * {
    box-sizing: border-box;
}

/* Ensure All Form Elements Use Dark Theme */
.slmm-direct-editor-modal input,
.slmm-direct-editor-modal textarea,
.slmm-direct-editor-modal select,
.slmm-direct-editor-modal button {
    font-family: inherit !important;
}

.slmm-direct-editor-modal input:not([type="button"]):not([type="submit"]):not([type="reset"]),
.slmm-direct-editor-modal textarea,
.slmm-direct-editor-modal select {
    background: #2c2c2c !important;
    border: 1px solid #6b7280 !important;
    color: #f3f4f6 !important;
    border-radius: 4px !important;
}

.slmm-direct-editor-modal input:focus,
.slmm-direct-editor-modal textarea:focus,
.slmm-direct-editor-modal select:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3) !important;
}

/* WordPress Admin Link Overrides */
.slmm-direct-editor-modal a {
    color: #60a5fa !important;
    text-decoration: none !important;
}

.slmm-direct-editor-modal a:hover {
    color: #93c5fd !important;
    text-decoration: underline !important;
}

.slmm-direct-editor-modal a:focus {
    outline: 2px solid #3b82f6 !important;
    outline-offset: 1px !important;
}

/* Selection Colors Within Modal */
.slmm-direct-editor-modal ::selection {
    background: #3b82f6 !important;
    color: white !important;
}

.slmm-direct-editor-modal ::-moz-selection {
    background: #3b82f6 !important;
    color: white !important;
}

/* Scrollbar Styling for Modal */
.slmm-direct-editor-modal ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.slmm-direct-editor-modal ::-webkit-scrollbar-track {
    background: #2c2c2c;
}

.slmm-direct-editor-modal ::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 4px;
}

.slmm-direct-editor-modal ::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Additional WordPress Admin Element Coverage */
.slmm-direct-editor-modal .wp-core-ui .button,
.slmm-direct-editor-content .wp-core-ui .button {
    background: #374151 !important;
    border-color: #6b7280 !important;
    color: #f3f4f6 !important;
    text-shadow: none !important;
    box-shadow: none !important;
}

.slmm-direct-editor-modal .wp-core-ui .button:hover,
.slmm-direct-editor-content .wp-core-ui .button:hover {
    background: #4b5563 !important;
    border-color: #9ca3af !important;
    color: white !important;
}

.slmm-direct-editor-modal .wp-core-ui .button-primary,
.slmm-direct-editor-content .wp-core-ui .button-primary {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
    color: white !important;
}

.slmm-direct-editor-modal .wp-core-ui .button-primary:hover,
.slmm-direct-editor-content .wp-core-ui .button-primary:hover {
    background: #2563eb !important;
    border-color: #2563eb !important;
}

/* High Specificity Overrides for Stubborn WordPress Styles */
.slmm-direct-editor-overlay .slmm-direct-editor-modal .quicktags-toolbar {
    background: #2c2c2c !important;
    color: #f3f4f6 !important;
}

.slmm-direct-editor-overlay .slmm-direct-editor-modal .wp-editor-area {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
}

/* Force Override WordPress Admin Defaults */
.slmm-direct-editor-modal,
.slmm-direct-editor-modal *:not(.mce-*):not(.tox-*) {
    background-color: inherit !important;
    color: inherit !important;
}

/* ULTRA-HIGH SPECIFICITY OVERRIDES - Maximum CSS Specificity for Stubborn Elements */
.slmm-direct-editor-overlay .slmm-direct-editor-modal .slmm-direct-editor-content .quicktags-toolbar {
    background: #2c2c2c !important;
    color: #f3f4f6 !important;
    border: 1px solid #333 !important;
    border-radius: 4px 4px 0 0 !important;
}

.slmm-direct-editor-overlay .slmm-direct-editor-modal .slmm-direct-editor-content .wp-editor-container textarea.wp-editor-area {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border: none !important;
    font-family: Consolas, Monaco, 'Courier New', monospace !important;
}

/* Nuclear Option - Body-level overrides for WordPress admin styles within modal context */
body .slmm-direct-editor-overlay .slmm-direct-editor-modal .quicktags-toolbar {
    background: #2c2c2c !important;
    color: #f3f4f6 !important;
}

body .slmm-direct-editor-overlay .slmm-direct-editor-modal .wp-editor-container textarea.wp-editor-area,
body .slmm-direct-editor-overlay .slmm-direct-editor-modal textarea#content {
    background: #1a1a1a !important;
    color: #f3f4f6 !important;
    border: none !important;
}

/* =============================================================================
   COMPACT NODE SETTINGS DASHBOARD - PROFESSIONAL MINIMAL DESIGN
   ============================================================================= */

/* Node Dashboard Container */
.slmm-node-dashboard {
    height: 100%;
    display: flex;
    flex-direction: column;
}

/* Compact Loading State */
.slmm-dashboard-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 12px;
    text-align: center;
    color: #6b7280;
}

.slmm-dashboard-loading .slmm-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #374151;
    border-top: 2px solid #7c3aed;
    border-radius: 50%;
    animation: slmm-dashboard-spin 1s linear infinite;
    margin-bottom: 8px;
}

@keyframes slmm-dashboard-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.slmm-dashboard-loading p {
    margin: 0;
    font-size: 12px;
    color: #6b7280;
}

/* Compact Dashboard Content */
.slmm-dashboard-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0;
    overflow-y: auto;
}

/* Top Row Layout */
.slmm-top-row {
    display: flex;
    gap: 6px;
    margin-bottom: 4px;
}

.slmm-top-item {
    flex: 1;
    min-width: 0;
}

.slmm-keyword-item {
    flex: 2; /* Give keyword more space */
}

/* Mini Labels for Top Row */
.slmm-mini-label {
    display: block;
    font-size: 9px;
    font-weight: 600;
    color: #6b7280;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Mini Select for Status */
.slmm-mini-select {
    width: 100%;
    background: #2c2c2c;
    border: 1px solid #4b5563;
    border-radius: 2px;
    color: #f3f4f6;
    padding: 4px 6px;
    font-size: 9px; /* Consistent font size */
    font-weight: 700; /* Match other elements */
    cursor: pointer;
    appearance: none;
    height: 20px; /* Explicit height */
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f3f4f6' viewBox='0 0 16 16'%3e%3cpath d='M8 11L3 6h10l-5 5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 4px center;
    background-size: 8px;
    padding-right: 18px;
    box-sizing: border-box;
}

.slmm-mini-select:hover,
.slmm-mini-select:focus {
    border-color: #7c3aed;
    outline: none;
}

/* Mini Input for Keyword */
.slmm-mini-input {
    width: 100%;
    background: #2c2c2c;
    border: 1px solid #4b5563;
    border-radius: 2px;
    color: #f3f4f6;
    padding: 4px 6px;
    font-size: 14px; /* Consistent font size */
    font-weight: 400; /* Normal weight for input text */
    height: 20px; /* Explicit height */
    box-sizing: border-box;
}

.slmm-mini-input:hover,
.slmm-mini-input:focus {
    border-color: #7c3aed;
    outline: none;
}

.slmm-mini-input::placeholder {
    color: #6b7280;
    font-size: 9px; /* Match input font size */
    font-weight: 400;
}

/* Mini Stats for Links */
.slmm-mini-stats {
    display: flex;
    gap: 4px;
    height: 30px; /* Match other elements */
}

.slmm-mini-stat {
    background: #2c2c2c;
    border: 1px solid #4b5563;
    border-radius: 2px;
    padding: 4px 6px;
    font-size: 14px; /* Consistent with other elements */
    font-weight: 700; /* Match button font weight */
    color: #9ca3af;
    text-transform: uppercase;
    flex: 1;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px; /* Explicit height matching buttons */
}

.slmm-mini-stat span {
    color: #f3f4f6;
    margin-left: 2px;
    font-weight: 700; /* Keep count bold */
}

/* Inline Sections */
.slmm-inline-section {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 8px;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
    margin-bottom: 4px;
}

.slmm-inline-section:hover {
    border-color: #7c3aed;
}

/* Inline Labels */
.slmm-inline-label {
    font-size: 9px;
    font-weight: 700;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 55px;
    flex-shrink: 0;
}

/* Compact Select */
.slmm-compact-select {
    width: 100%;
    background: #2c2c2c;
    border: 1px solid #4b5563;
    border-radius: 3px;
    color: #f3f4f6;
    padding: 6px 8px;
    font-size: 12px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' fill='%23f3f4f6' viewBox='0 0 16 16'%3e%3cpath d='M8 11L3 6h10l-5 5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 6px center;
    background-size: 10px;
    padding-right: 24px;
}

.slmm-compact-select:hover,
.slmm-compact-select:focus {
    border-color: #7c3aed;
    outline: none;
}

/* Compact Input */
.slmm-compact-input {
    width: 100%;
    background: #2c2c2c;
    border: 1px solid #4b5563;
    border-radius: 3px;
    color: #f3f4f6;
    padding: 6px 8px;
    font-size: 12px;
}

.slmm-compact-input:hover,
.slmm-compact-input:focus {
    border-color: #7c3aed;
    outline: none;
}

.slmm-compact-input::placeholder {
    color: #6b7280;
    font-size: 11px;
}

/* Inline Buttons Container */
.slmm-inline-buttons {
    display: flex;
    gap: 4px;
    flex: 1;
}

/* Base Button Styles */
.slmm-difficulty-btn,
.slmm-priority-btn {
    flex: 1;
    min-width: 20px;
    height: 20px;
    border: 1px solid #4b5563;
    border-radius: 3px;
    font-size: 9px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #2c2c2c;
    color: #9ca3af;
}

/* Difficulty Button Colors (Neutral until selected) */
.slmm-difficulty-btn[data-difficulty="easy"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="easy"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="easy"].active {
    background: #10B981;
    border-color: #10B981;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="medium"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="medium"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="medium"].active {
    background: #F59E0B;
    border-color: #F59E0B;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="hard"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="hard"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="hard"].active {
    background: #F97316;
    border-color: #F97316;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="very-hard"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="very-hard"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-difficulty-btn[data-difficulty="very-hard"].active {
    background: #EF4444;
    border-color: #EF4444;
    color: white;
}

/* Priority Button Colors (Neutral until selected) */
.slmm-priority-btn[data-importance="1"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-priority-btn[data-importance="1"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-priority-btn[data-importance="1"].active {
    background: #EAB308;
    border-color: #EAB308;
    color: white;
}

.slmm-priority-btn[data-importance="2"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-priority-btn[data-importance="2"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-priority-btn[data-importance="2"].active {
    background: #EF4444;
    border-color: #EF4444;
    color: white;
}

.slmm-priority-btn[data-importance="3"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-priority-btn[data-importance="3"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-priority-btn[data-importance="3"].active {
    background: #3B82F6;
    border-color: #3B82F6;
    color: white;
}

.slmm-priority-btn[data-importance="4"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-priority-btn[data-importance="4"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-priority-btn[data-importance="4"].active {
    background: #6B7280;
    border-color: #6B7280;
    color: white;
}

.slmm-priority-btn[data-importance="5"] {
    background: #2c2c2c;
    border-color: #4b5563;
    color: white;
}

.slmm-priority-btn[data-importance="5"]:hover {
    background: #374151;
    border-color: #6b7280;
    color: white;
}

.slmm-priority-btn[data-importance="5"].active {
    background: #1F2937;
    border-color: #1F2937;
    color: white;
}

/* Compact Stats */
.slmm-compact-stats {
    display: flex;
    gap: 8px;
}

.slmm-compact-stat {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 6px 8px;
    background: #2c2c2c;
    border-radius: 3px;
    border: 1px solid #4b5563;
}

.slmm-stat-label {
    font-size: 10px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
}

.slmm-stat-count {
    font-size: 12px;
    font-weight: 700;
    color: #f3f4f6;
}

/* Compact Status */
.slmm-compact-status {
    padding: 6px 8px;
    background: #1f2937;
    border: 1px solid #374151;
    border-radius: 3px;
    color: #9ca3af;
    font-size: 10px;
    text-align: center;
    margin-top: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Error State */
.slmm-dashboard-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px 12px;
    text-align: center;
}

.slmm-dashboard-error p {
    margin: 0 0 8px 0;
    color: #ef4444;
    font-size: 11px;
    font-weight: 500;
}

.slmm-compact-retry {
    background: #374151;
    border: 1px solid #6b7280;
    border-radius: 3px;
    color: #f3f4f6;
    padding: 4px 8px;
    font-size: 10px;
    font-weight: 500;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.slmm-compact-retry:hover {
    background: #4b5563;
    border-color: #9ca3af;
}

/* Links Section Special Styling */
.slmm-links-section .slmm-compact-stat:nth-child(1) {
    border-left: 3px solid #3b82f6;
}

.slmm-links-section .slmm-compact-stat:nth-child(2) {
    border-left: 3px solid #f59e0b;
}

/* Scrollbar for compact design */
.slmm-dashboard-content::-webkit-scrollbar {
    width: 4px;
}

.slmm-dashboard-content::-webkit-scrollbar-track {
    background: transparent;
}

.slmm-dashboard-content::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 2px;
}

.slmm-dashboard-content::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

/* Circles Boxes Container */
.slmm-circles-row {
    display: flex;
    gap: 8px;
    margin-bottom: 4px;
}

/* Individual Circles Boxes */
.slmm-circles-box {
    flex: 1;
    padding: 8px;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 4px;
}

/* Circles Container */
.slmm-circles-container {
    display: flex;
    gap: 6px;
    margin-top: 4px;
}

/* Circle Buttons with Letters */
.slmm-circle-btn {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 1px solid #4b5563;
    cursor: pointer;
    transition: all 0.15s ease;
    background: #4b5563;
    color: white;
    font-size: 9px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    text-transform: uppercase;
}

.slmm-circle-btn:hover {
    border-color: #6b7280;
}

/* Active Difficulty Circle Colors (Only Selected) */
.slmm-difficulty-circle.active[data-difficulty="easy"] {
    background: #10B981;
    border-color: #10B981;
}

.slmm-difficulty-circle.active[data-difficulty="medium"] {
    background: #F59E0B;
    border-color: #F59E0B;
}

.slmm-difficulty-circle.active[data-difficulty="hard"] {
    background: #F97316;
    border-color: #F97316;
}

.slmm-difficulty-circle.active[data-difficulty="very-hard"] {
    background: #EF4444;
    border-color: #EF4444;
}

/* Active Priority Circle Colors (Only Selected) */
.slmm-priority-circle.active[data-importance="1"] {
    background: #EAB308;
    border-color: #EAB308;
}

.slmm-priority-circle.active[data-importance="2"] {
    background: #EF4444;
    border-color: #EF4444;
}

.slmm-priority-circle.active[data-importance="3"] {
    background: #3B82F6;
    border-color: #3B82F6;
}

.slmm-priority-circle.active[data-importance="4"] {
    background: #6B7280;
    border-color: #6B7280;
}

.slmm-priority-circle.active[data-importance="5"] {
    background: #1F2937;
    border-color: #1F2937;
}

/* Active State - No size changes, only background/border */
.slmm-circle-btn.active {
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

/* Box Labels - Left Aligned Like Other Labels */
.slmm-box-label {
    font-size: 9px;
    font-weight: 700;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: left;
    margin-bottom: 6px;
    display: block;
}

/* Links Stats in Box Format */
.slmm-links-stats {
    display: flex;
    gap: 8px;
}

.slmm-link-stat {
    font-size: 9px;
    font-weight: 700;
    color: #d1d5db;
    background: #374151;
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #4b5563;
}

/* Dashboard End Divider */
.slmm-dashboard-divider {
    height: 1px;
    background: #333;
    margin: 12px 0 8px 0;
    opacity: 0.6;
}

/* =============================================================================
   SILO NAVIGATION LINKS
   ============================================================================= */

.slmm-silo-navigation {
    margin-top: 8px;
    padding-top: 8px;
}

.slmm-silo-parents,
.slmm-silo-siblings,
.slmm-silo-children,
.slmm-silo-semantic {
    margin-bottom: 12px;
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.slmm-silo-parents:last-child,
.slmm-silo-siblings:last-child,
.slmm-silo-children:last-child,
.slmm-silo-semantic:last-child {
    margin-bottom: 0;
}

/* Accordion Header Styles */
.slmm-silo-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;
    font-size: 10px;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    color: #9ca3af;
    margin-bottom: 0;
    padding: 8px 10px;
    background: #2a2a2a;
    border: 1px solid #404040;
    cursor: pointer;
    transition: all 0.2s ease;
    user-select: none;
}

.slmm-silo-section-header:hover {
    background: #333333;
    color: #d1d5db;
}

.slmm-silo-section-header-left {
    display: flex;
    align-items: center;
    gap: 6px;
}

.slmm-silo-section-label {
    width: 100px;
    flex-shrink: 0;
}

/* Section State Indicators - Green for Complete Sections */
.slmm-silo-section-header.slmm-section-complete {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
    border-color: #10b981;
    color: #10b981;
}

.slmm-silo-section-header.slmm-section-complete:hover {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.3), rgba(16, 185, 129, 0.2));
}

.slmm-silo-section-header.slmm-section-complete .slmm-silo-section-icon,
.slmm-silo-section-header.slmm-section-complete .slmm-silo-toggle-icon {
    color: #10b981;
    opacity: 1;
}

.slmm-silo-section-icon {
    font-size: 12px;
    opacity: 0.8;
}

/* Accordion Toggle Icon */
.slmm-silo-toggle-icon {
    font-size: 12px;
    transition: transform 0.2s ease;
    color: #6b7280;
}

.slmm-silo-section-collapsed .slmm-silo-toggle-icon {
    transform: rotate(-90deg);
}

/* Collapsible Content Container */
.slmm-silo-content-wrapper {
    overflow: hidden;
    transition: max-height 0.3s ease, padding 0.3s ease;
    max-height: 1000px; /* Large enough for content */
    background: #1e1e1e;
    border: 1px solid #404040;
    border-top: none;
}

.slmm-silo-section-collapsed .slmm-silo-content-wrapper {
    max-height: 0;
    padding: 0;
}

.slmm-silo-content-wrapper .slmm-silo-links-container {
    padding: 10px;
}

.slmm-silo-links-container {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.slmm-silo-link-item {
    background: #374151;
    border: 1px solid #4b5563;
    border-radius: 4px;
    padding: 8px 10px;
    transition: all 0.2s ease;
}

.slmm-silo-link-item:hover {
    background: #4b5563;
    border-color: #6b7280;
}

.slmm-silo-link-title {
    font-size: 11px;
    font-weight: 500;
    color: #f3f4f6;
    margin-bottom: 4px;
    line-height: 1.2;
    /* Truncate long titles */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.slmm-silo-link-url {
    font-size: 9px;
    color: #ffffff;
    font-family: 'Courier New', monospace;
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.2s ease;
    line-height: 1.3;
    word-break: break-all;
    user-select: none;
}

.slmm-silo-link-url:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    color: #ffffff;
}

.slmm-silo-link-url.slmm-copied {
    background: rgba(34, 197, 94, 0.2);
    border-color: rgba(34, 197, 94, 0.4);
    color: #4ade80;
}

/* Empty state styling */
.slmm-silo-links-container:empty::after {
    content: 'No links available';
    display: block;
    font-size: 9px;
    color: #6b7280;
    font-style: italic;
    padding: 4px 8px;
    text-align: center;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .slmm-compact-section {
        padding: 6px;
    }
    
    .slmm-compact-btn {
        min-width: 20px;
        height: 20px;
        font-size: 9px;
    }
    
    .slmm-compact-buttons {
        gap: 2px;
    }
    
    .slmm-dashboard-divider {
        margin: 8px 0 6px 0;
    }
    
    /* Silo navigation responsive adjustments */
    .slmm-silo-navigation {
        margin-top: 6px;
        padding-top: 6px;
    }
    
    .slmm-silo-parents,
    .slmm-silo-siblings {
        margin-bottom: 8px;
    }
    
    .slmm-silo-link-item {
        padding: 6px 8px;
    }
    
    .slmm-silo-link-title {
        font-size: 10px;
    }
    
    .slmm-silo-link-url {
        font-size: 8px;
        padding: 2px 3px;
    }
}

/* =============================================================================
   LINK TRACKING: Visual Indicators for Found Links
   ============================================================================= */

/* Main visual indicator styling */
.slmm-silo-link-item.slmm-link-found-in-content {
    border: 2px solid #10b981 !important; /* Green border */
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(16, 185, 129, 0.05)) !important;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.3) !important;
    position: relative !important;
}

/* Green checkmark indicator */
.slmm-silo-link-item.slmm-link-found-in-content::before {
    content: "✓";
    position: absolute;
    top: 4px;
    right: 6px;
    background: #10b981;
    color: white;
    font-size: 10px;
    font-weight: bold;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Subtle animation for newly found links */
.slmm-silo-link-item.slmm-link-found-in-content {
    animation: slmm-link-found-pulse 1.5s ease-in-out;
}

@keyframes slmm-link-found-pulse {
    0% { 
        box-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
    }
    50% { 
        box-shadow: 0 0 15px rgba(16, 185, 129, 0.6);
    }
    100% { 
        box-shadow: 0 0 8px rgba(16, 185, 129, 0.3);
    }
}

/* Responsive adjustments for link tracking */
@media (max-width: 768px) {
    .slmm-silo-link-item.slmm-link-found-in-content::before {
        width: 14px;
        height: 14px;
        font-size: 9px;
        top: 3px;
        right: 4px;
    }
}

/* =============================================================================
   WARNING STYLES FOR SUMMARY SECTION
   ============================================================================= */

.slmm-warning-section {
    border: 1px solid #ef4444 !important;
    border-radius: 4px !important;
    background: #1a1a1a !important;
    padding: 8px 12px !important;
    margin: 8px 0 !important;
}

.slmm-warning-section .slmm-section-header {
    border-bottom: none !important;
    margin-bottom: 4px !important;
    padding: 0 !important;
}

.slmm-warning-section .slmm-section-header h3 {
    font-size: 13px !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
}

/* Article Summary Section Header - Always White Text */
.slmm-article-summary-section .slmm-section-header h3 {
    color: #f1f5f9 !important;
    font-size: 14px !important;
    font-weight: 600 !important;
}

.slmm-warning-section .slmm-section-content {
    padding: 0 !important;
}

.slmm-warning-text {
    color: #ef4444 !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    margin: 4px 0 6px 0 !important;
    line-height: 1.3 !important;
}

.slmm-warning-section .slmm-sidebar-btn {
    border-color: #ef4444 !important;
    color: #ef4444 !important;
    font-size: 11px !important;
    padding: 4px 8px !important;
    min-height: 26px !important;
    line-height: 1.2 !important;
}

.slmm-warning-section .slmm-sidebar-btn:hover {
    background: #ef4444 !important;
    color: white !important;
}

/* =============================================================================
   CHAT INTERFACE STYLES - AI CONVERSATION CONTINUATION
   ============================================================================= */

.slmm-chat-section {
    margin-top: 20px;
    border-top: 2px solid #374151;
    padding-top: 15px;
}

.slmm-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 0 10px;
}

.slmm-chat-title {
    font-size: 16px;
    font-weight: 600;
    color: #3b82f6;
}

.slmm-clear-chat {
    background: #374151;
    color: #e5e7eb;
    border: 1px solid #4b5563;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
}

.slmm-clear-chat:hover {
    background: #4b5563;
    color: white;
}

/* Chat Messages Container */
.slmm-chat-messages {
    background: #0d1117;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 15px;
    height: 300px;
    overflow-y: auto;
    margin-bottom: 15px;
    scroll-behavior: smooth;
}

/* Custom scrollbar for chat messages */
.slmm-chat-messages::-webkit-scrollbar {
    width: 8px;
}

.slmm-chat-messages::-webkit-scrollbar-track {
    background: #1c2128;
    border-radius: 4px;
}

.slmm-chat-messages::-webkit-scrollbar-thumb {
    background: #30363d;
    border-radius: 4px;
}

.slmm-chat-messages::-webkit-scrollbar-thumb:hover {
    background: #484f58;
}

/* Individual Chat Message */
.slmm-chat-message {
    margin-bottom: 15px;
    padding: 12px;
    border-radius: 8px;
    position: relative;
}

.slmm-chat-user {
    background: #1c2937;
    border-left: 3px solid #3b82f6;
    margin-left: 20%;
}

.slmm-chat-assistant {
    background: #161b22;
    border-left: 3px solid #10b981;
    margin-right: 20%;
}

.slmm-chat-role {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    margin-bottom: 8px;
    opacity: 0.7;
}

.slmm-chat-user .slmm-chat-role {
    color: #3b82f6;
}

.slmm-chat-assistant .slmm-chat-role {
    color: #10b981;
}

.slmm-chat-content {
    color: #e5e7eb;
    line-height: 1.6;
}

.slmm-chat-text {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, monospace;
    font-size: 13px;
}

/* Plain text view in chat */
.slmm-chat-content pre.slmm-chat-text {
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
    white-space: pre-wrap;
    word-wrap: break-word;
    color: inherit;
    overflow-x: auto;
}

/* Formatted text in chat */
.slmm-chat-content .slmm-chat-text {
    background: transparent;
    padding: 0;
    margin: 0;
}

.slmm-chat-content .slmm-chat-text a {
    color: #58a6ff;
    text-decoration: none;
}

.slmm-chat-content .slmm-chat-text a:hover {
    text-decoration: underline;
}

.slmm-chat-content .slmm-chat-text strong {
    color: #f0f6fc;
    font-weight: 600;
}

.slmm-chat-content .slmm-chat-text em {
    color: #c9d1d9;
    font-style: italic;
}

.slmm-chat-content .slmm-chat-text code {
    background: #161b22;
    border: 1px solid #30363d;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
    color: #79c0ff;
}

/* Chat Input Area */
.slmm-chat-input-area {
    background: #0d1117;
    border: 1px solid #30363d;
    border-radius: 8px;
    padding: 12px;
}

.slmm-chat-input {
    width: 100%;
    background: #161b22;
    border: 1px solid #30363d;
    color: #e5e7eb;
    padding: 10px;
    border-radius: 6px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    font-size: 14px;
    resize: vertical;
    min-height: 60px;
    max-height: 150px;
    margin-bottom: 10px;
    transition: border-color 0.2s ease;
}

.slmm-chat-input:focus {
    outline: none;
    border-color: #3b82f6;
    background: #1c2128;
}

.slmm-chat-input::placeholder {
    color: #6e7681;
}

.slmm-chat-input:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Chat Controls */
.slmm-chat-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10px;
}

.slmm-chat-view-mode {
    background: #161b22;
    border: 1px solid #30363d;
    color: #e5e7eb;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.slmm-chat-view-mode:hover {
    background: #1c2128;
    border-color: #484f58;
}

.slmm-chat-view-mode:focus {
    outline: none;
    border-color: #3b82f6;
}

.slmm-chat-send {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 8px 20px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.slmm-chat-send:hover:not(:disabled) {
    background: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.slmm-chat-send:active:not(:disabled) {
    transform: translateY(0);
}

.slmm-chat-send:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Loading state for chat */
.slmm-chat-loading {
    display: inline-block;
    width: 8px;
    height: 8px;
    border: 2px solid #3b82f6;
    border-radius: 50%;
    border-color: #3b82f6 transparent #3b82f6 transparent;
    animation: slmm-chat-spin 1s linear infinite;
}

@keyframes slmm-chat-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chat message animations */
.slmm-chat-message {
    animation: slmm-chat-slide-in 0.3s ease-out;
}

@keyframes slmm-chat-slide-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive adjustments for chat */
@media (max-width: 768px) {
    .slmm-chat-messages {
        height: 200px;
    }
    
    .slmm-chat-user {
        margin-left: 10%;
    }
    
    .slmm-chat-assistant {
        margin-right: 10%;
    }
    
    .slmm-chat-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .slmm-chat-view-mode,
    .slmm-chat-send {
        width: 100%;
        justify-content: center;
    }
}

/* Dark theme consistency */
.slmm-chat-section * {
    box-sizing: border-box;
}

/* Ensure chat doesn't interfere with other elements */
.slmm-chat-section {
    position: relative;
    z-index: 1;
}

/* Chat status indicator */
.slmm-chat-status {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6e7681;
}

.slmm-chat-status.active {
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
    }
}