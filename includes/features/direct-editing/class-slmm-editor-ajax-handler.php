<?php
/**
 * SLMM Direct Editor AJAX Handler
 * 
 * Handles all AJAX endpoints for direct post editing functionality.
 * Provides ultra-strict Classic Editor validation and WordPress-compliant 
 * post lock management with real-time auto-save capabilities.
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Direct_Editing
 * @version 1.0.0
 * @since 4.10.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_Editor_AJAX_Handler
 * 
 * Manages all AJAX operations for direct post editing with comprehensive
 * security, validation, and WordPress compliance.
 */
class SLMM_Editor_AJAX_Handler {
    
    /**
     * Singleton instance
     * @var SLMM_Editor_AJAX_Handler|null
     */
    private static $instance = null;
    
    /**
     * Content validator instance
     * @var SLMM_Direct_Editor_Content_Validator|null
     */
    private $content_validator = null;
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_Editor_AJAX_Handler
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor - enforces singleton pattern
     */
    private function __construct() {
        $this->init_content_validator();
        $this->setup_ajax_hooks();
        
        error_log('[SLMM Direct Editor] AJAX Handler initialized');
    }
    
    /**
     * Initialize content validator
     */
    private function init_content_validator() {
        $this->content_validator = new SLMM_Direct_Editor_Content_Validator();
    }
    
    /**
     * Setup AJAX hooks for all direct editing endpoints
     */
    private function setup_ajax_hooks() {
        // Content validation endpoint
        add_action('wp_ajax_slmm_validate_post_content', array($this, 'ajax_validate_post_content'));
        
        // Editor loading endpoint  
        add_action('wp_ajax_slmm_load_post_editor', array($this, 'ajax_load_post_editor'));
        
        // Session establishment endpoint (save-on-close optimization)
        add_action('wp_ajax_slmm_establish_editing_session', array($this, 'ajax_establish_editing_session'));
        
        // Save-on-close endpoint (replaces auto-save for resource efficiency)
        add_action('wp_ajax_slmm_save_on_close_post', array($this, 'ajax_save_on_close_post'));
        
        // Auto-save endpoint (WP Sheet Editor standard) - DEPRECATED in favor of save-on-close
        add_action('wp_ajax_slmm_auto_save_post', array($this, 'ajax_auto_save_post'));
        
        // Post title update endpoint (for regular title editing)
        add_action('wp_ajax_slmm_update_post_title', array($this, 'ajax_update_post_title'));
        
        error_log('[SLMM Direct Editor] AJAX hooks configured');
    }
    
    /**
     * AJAX handler for content validation
     * Ultra-strict validation: ONLY Classic Editor content allowed
     */
    public function ajax_validate_post_content() {
        // Security checks
        if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $post_id = absint($_POST['post_id']);
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
            return;
        }
        
        // Capability check
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Permission denied');
            return;
        }
        
        // Ultra-strict content validation
        $validation = $this->content_validator->validate_post_for_editing($post_id);
        
        // Apply context-aware validation filters
        $validation = apply_filters('slmm_direct_editor_validation', $validation);
        
        wp_send_json_success($validation);
    }
    
    /**
     * AJAX handler for loading post editor
     * WordPress-compliant conflict detection and wp_editor() rendering
     */
    public function ajax_load_post_editor() {
        // Security checks
        if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $post_id = absint($_POST['post_id']);
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
            return;
        }
        
        // Capability check
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        // CRITICAL: Validate content is Classic Editor compatible
        $validation = $this->content_validator->validate_post_for_editing($post_id);
        $validation = apply_filters('slmm_direct_editor_validation', $validation);
        
        if (!$validation['can_edit']) {
            // Return validation failure - content is LOCKED
            wp_send_json_success(array(
                'editor_html' => null,
                'validation_failed' => true,
                'validation_result' => $validation,
                'locked' => true
            ));
            return;
        }
        
        // OPTIONAL: Check if post is locked by another user RIGHT NOW
        $lock_warning = null;
        $post_lock = wp_check_post_lock($post_id);
        if ($post_lock) {
            $lock_user = get_userdata($post_lock);
            // Don't prevent opening, just warn
            $lock_warning = array(
                'warning' => 'Post may be edited by: ' . $lock_user->display_name,
                'locked_by' => $lock_user->display_name,
                'lock_time' => get_post_meta($post_id, '_edit_lock', true)
            );
        }
        
        // DON'T set post lock here - modal sessions are not active editing
        
        // Load post content (validated as Classic Editor compatible)
        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
            return;
        }
        
        $content = $post->post_content;
        
        // Render Classic Editor using wp_editor()
        ob_start();
        $editor_id = 'slmm_post_content_' . $post_id;
        wp_editor($content, $editor_id, array(
            'textarea_name' => 'post_content',
            'media_buttons' => true,
            'teeny' => false,
            'dfw' => true,
            'default_editor' => 'tinymce', // CRITICAL FIX: Default to Visual mode to show dark theme
            'tinymce' => array(
                'height' => 800,
                'toolbar1' => 'bold,italic,underline,strikethrough,|,link,unlink,|,bullist,numlist,|,undo,redo',
                'toolbar2' => 'formatselect,|,alignleft,aligncenter,alignright,|,indent,outdent,|,wp_adv',
                'menubar' => false
            ),
            'quicktags' => array(
                'buttons' => 'strong,em,link,block,del,ins,img,ul,ol,li,code,more,close'
            )
        ));
        $editor_html = ob_get_clean();
        
        // CRITICAL FIX: Add CSS to hide GPT prompt elements in Direct Editor
        $hide_gpt_css = '<style>
            .slmm-direct-editor-modal .slmm-gpt-prompt-container,
            .slmm-direct-editor-modal .slmm-execute-gpt-prompt,
            .slmm-direct-editor-modal #slmm-execute-gpt-prompt-1,
            .slmm-direct-editor-modal [id*="slmm-gpt-prompt"],
            .slmm-direct-editor-modal [id*="slmm-execute-gpt-prompt"],
            .slmm-direct-editor-modal [id*="wp-slmm_post_content_"][id*="-media-buttons"] {
                display: none !important;
            }
        </style>';
        
        $editor_html = $hide_gpt_css . $editor_html;
        
        // Check for ACF title data to include in response
        $acf_title_data = $this->get_acf_title_data($post_id);
        
        wp_send_json_success(array(
            'editor_html' => $editor_html,
            'editor_id' => $editor_id,
            'post_title' => $post->post_title,
            'post_id' => $post_id,
            'last_modified' => get_the_modified_time('U', $post_id),
            'validation_result' => $validation,
            'lock_warning' => $lock_warning,
            'acf_title_data' => $acf_title_data // Include ACF title data for immediate display
        ));
    }
    
    /**
     * AJAX handler for real-time auto-save (WP Sheet Editor standard)
     * WordPress-compliant post lock management and conflict resolution
     */
    public function ajax_auto_save_post() {
        // Multi-layer security (following Notes system patterns)
        if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $post_id = absint($_POST['post_id']);
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Permission denied');
            return;
        }
        
        // WordPress-compliant conflict detection
        $existing_lock = wp_check_post_lock($post_id);
        if ($existing_lock && $existing_lock != get_current_user_id()) {
            $lock_user = get_userdata($existing_lock);
            wp_send_json_error(array(
                'conflict' => true,
                'message' => 'Another user started editing this post. Please refresh to see latest changes.',
                'locked_by' => $lock_user->display_name
            ));
            return;
        }
        
        // Content sanitization (multi-layer approach)
        $content = wp_unslash($_POST['content']);
        $content = wp_kses_post($content);
        
        // Additional validation for content size
        if (strlen($content) > 100000) { // 100KB limit
            wp_send_json_error('Content too large (max 100KB)');
            return;
        }
        
        // Check if content has actually changed (helps with revision logic)
        $current_post = get_post($post_id);
        $content_changed = ($current_post && $current_post->post_content !== $content);
        
        // Database transaction safety for atomic operations
        $result = $this->safe_post_update($post_id, $content, $content_changed);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
            return;
        }
        
        // Try to create a revision after successful save
        $final_revision_id = wp_save_post_revision($post_id);
        
        wp_send_json_success(array(
            'saved_at' => current_time('mysql'),
            'auto_saved' => true,
            'revision_id' => $final_revision_id, // Will be false if no changes, that's OK
            'post_id' => $post_id,
            'revision_created' => !empty($final_revision_id)
        ));
    }
    
    /**
     * AJAX handler for establishing editing session
     * Resource-efficient session establishment with persistent nonce
     */
    public function ajax_establish_editing_session() {
        // Security checks
        if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $post_id = absint($_POST['post_id']);
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
            return;
        }
        
        // Capability check
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Permission denied');
            return;
        }
        
        // Validate post exists and is editable
        $validation = $this->content_validator->validate_post_for_editing($post_id);
        $validation = apply_filters('slmm_direct_editor_validation', $validation);
        
        if (!$validation['can_edit']) {
            wp_send_json_error(array(
                'message' => 'Content not editable',
                'validation' => $validation
            ));
            return;
        }
        
        // Set initial post lock for session
        $lock_set = wp_set_post_lock($post_id);
        
        // Create session-specific nonce for enhanced security
        $session_nonce = wp_create_nonce('slmm_editing_session_' . $post_id . '_' . get_current_user_id());
        
        // Store session data temporarily (5 minutes)
        $session_data = array(
            'post_id' => $post_id,
            'user_id' => get_current_user_id(),
            'established' => current_time('timestamp'),
            'lock_set' => $lock_set,
            'session_nonce' => $session_nonce
        );
        
        set_transient('slmm_editing_session_' . $post_id . '_' . get_current_user_id(), $session_data, 300); // 5 minutes
        
        error_log('[SLMM Direct Editor] Editing session established for post ' . $post_id . ' by user ' . get_current_user_id());
        
        wp_send_json_success(array(
            'session_nonce' => $session_nonce,
            'lock_set' => $lock_set,
            'session_timeout' => 300, // 5 minutes
            'post_id' => $post_id
        ));
    }
    
    /**
     * AJAX handler for save-on-close
     * Efficient single-save operation when closing editor
     */
    public function ajax_save_on_close_post() {
        // Security checks
        if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        $post_id = absint($_POST['post_id']);
        if (!$post_id) {
            wp_send_json_error('Invalid post ID');
            return;
        }
        
        // Session nonce validation
        $session_nonce = sanitize_text_field($_POST['session_nonce']);
        if (!$session_nonce || !wp_verify_nonce($session_nonce, 'slmm_editing_session_' . $post_id . '_' . get_current_user_id())) {
            wp_send_json_error('Session expired or invalid');
            return;
        }
        
        if (!current_user_can('edit_post', $post_id)) {
            wp_send_json_error('Permission denied');
            return;
        }
        
        // Verify session exists
        $session_key = 'slmm_editing_session_' . $post_id . '_' . get_current_user_id();
        $session_data = get_transient($session_key);
        if (!$session_data) {
            wp_send_json_error('Editing session expired - please refresh and try again');
            return;
        }
        
        // WordPress-compliant conflict detection
        $existing_lock = wp_check_post_lock($post_id);
        if ($existing_lock && $existing_lock != get_current_user_id()) {
            $lock_user = get_userdata($existing_lock);
            wp_send_json_error(array(
                'conflict' => true,
                'message' => 'Another user started editing this post. Please refresh to see latest changes.',
                'locked_by' => $lock_user->display_name
            ));
            return;
        }
        
        // Content sanitization
        $content = wp_unslash($_POST['content']);
        $content = wp_kses_post($content);
        
        // Content size validation
        if (strlen($content) > 100000) { // 100KB limit
            wp_send_json_error('Content too large (max 100KB)');
            return;
        }
        
        // Check if content actually changed
        $current_post = get_post($post_id);
        $content_changed = ($current_post && $current_post->post_content !== $content);
        
        // Perform the save operation
        $result = $this->safe_post_update($post_id, $content, $content_changed);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
            return;
        }
        
        // Create final revision
        $final_revision_id = wp_save_post_revision($post_id);
        
        // Clean up session data
        delete_transient($session_key);
        
        // DON'T maintain lock - let it expire naturally per WordPress design
        
        error_log('[SLMM Direct Editor] Save-on-close successful for post ' . $post_id);
        
        wp_send_json_success(array(
            'saved_at' => current_time('mysql'),
            'save_on_close' => true,
            'revision_id' => $final_revision_id,
            'post_id' => $post_id,
            'revision_created' => !empty($final_revision_id),
            'content_changed' => $content_changed
        ));
    }
    
    /**
     * AJAX handler for updating post titles
     * Handles regular title updates from Direct Editor
     * Following exact ajax_change_slug pattern for immediate canvas updates
     */
    public function ajax_update_post_title() {
        try {
            // 1. Security & Permission Checks (following slug pattern)
            if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
                wp_send_json_error('Security check failed');
                return;
            }
            
            if (!current_user_can('edit_posts') && !current_user_can('edit_pages')) {
                wp_send_json_error('Insufficient permissions to change post title');
                return;
            }
            
            // 2. Input Validation (following slug pattern)
            $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            if (empty($post_id)) {
                wp_send_json_error('Invalid post ID');
                return;
            }
            
            $new_title = isset($_POST['new_title']) ? sanitize_text_field($_POST['new_title']) : '';
            if (empty($new_title)) {
                wp_send_json_error('Title cannot be empty');
                return;
            }
            
            $post = get_post($post_id);
            if (!$post) {
                wp_send_json_error('Post not found');
                return;
            }
            
            // 3. Specific Post Permission Check (following slug pattern)
            $post_type_obj = get_post_type_object($post->post_type);
            if (!$post_type_obj || !current_user_can($post_type_obj->cap->edit_post, $post_id)) {
                wp_send_json_error('Insufficient permissions to edit this ' . $post->post_type);
                return;
            }
            
            // 4. Check if Already Updated - Optimization (following slug pattern)
            if ($post->post_title === $new_title) {
                wp_send_json_success(array(
                    'message' => 'Title is already set to this value',
                    'post_id' => $post_id,
                    'new_title' => $new_title,
                    'unchanged' => true
                ));
                return;
            }
            
            error_log('[SLMM Direct Editor] Updating post title for post ' . $post_id . ' to: "' . $new_title . '"');
            
            // 5. Perform Database Update (following slug pattern)
            $update_result = wp_update_post(array(
                'ID' => $post_id,
                'post_title' => $new_title
            ), true);
            
            if ($update_result && !is_wp_error($update_result)) {
                // 6. Audit Trail Logging (following slug pattern)
                error_log('[SLMM Direct Editor] Post title changed - ID: ' . $post_id . ', Old Title: ' . $post->post_title . ', New Title: ' . $new_title . ', User: ' . get_current_user_id());
                
                // 7. Get Fresh Post Data (following slug pattern)
                $fresh_post = get_post($post_id);
                
                // 8. Return Success with Fresh Node Data for Immediate Canvas Update (following slug pattern)
                wp_send_json_success(array(
                    'message' => 'Post title changed successfully',
                    'post_id' => $post_id,
                    'old_title' => $post->post_title,
                    'new_title' => $fresh_post->post_title,
                    'node_data' => array(
                        'id' => $post_id,
                        'name' => $fresh_post->post_title, // This is the key field for canvas title updates
                        'post_status' => $fresh_post->post_status,
                        'post_type' => $fresh_post->post_type,
                        'permalink' => get_permalink($post_id),
                        'edit_url' => get_edit_post_link($post_id),
                        'post_date' => $fresh_post->post_date,
                        'post_modified' => $fresh_post->post_modified,
                        'slug' => $fresh_post->post_name,
                        'title' => $fresh_post->post_title, // Explicit title field
                        'status_display' => $this->get_status_display_text($fresh_post->post_status),
                        'status_color' => $this->get_status_color($fresh_post->post_status)
                    )
                ));
            } else {
                $error_message = is_wp_error($update_result) ? $update_result->get_error_message() : 'WordPress update failed';
                error_log('[SLMM Direct Editor] Title update failed: ' . $error_message);
                wp_send_json_error('Failed to change post title: ' . $error_message);
            }
            
        } catch (Exception $e) {
            error_log('[SLMM Direct Editor] Title change error: ' . $e->getMessage());
            wp_send_json_error('Error changing title: ' . $e->getMessage());
        }
    }
    
    /**
     * Get display text for post status (following slug pattern)
     * 
     * @param string $status Post status
     * @return string Display text
     */
    private function get_status_display_text($status) {
        switch ($status) {
            case 'publish':
                return 'Published';
            case 'draft':
                return 'Draft';
            case 'pending':
                return 'Pending Review';
            case 'private':
                return 'Private';
            case 'future':
                return 'Scheduled';
            case 'trash':
                return 'Trash';
            default:
                return ucfirst($status);
        }
    }
    
    /**
     * Get color for post status (following slug pattern)
     * 
     * @param string $status Post status
     * @return string Color code
     */
    private function get_status_color($status) {
        switch ($status) {
            case 'publish':
                return '#10B981';
            case 'draft':
                return '#EF4444';
            case 'pending':
                return '#8B5CF6';
            case 'private':
                return '#F59E0B';
            case 'future':
                return '#3B82F6';
            case 'trash':
                return '#6B7280';
            default:
                return '#6B7280';
        }
    }
    
    /**
     * Get ACF title data for a post if available
     * 
     * @param int $post_id Post ID
     * @return array|null ACF title data or null if no ACF
     */
    private function get_acf_title_data($post_id) {
        // Check if ACF is available
        if (!function_exists('get_field')) {
            return null;
        }
        
        // Get ACF settings to check for configured title field
        $acf_settings = get_option('slmm_acf_settings', array());
        $title_field_name = isset($acf_settings['title_field_name']) ? $acf_settings['title_field_name'] : '';
        
        if (empty($title_field_name)) {
            return null;
        }
        
        // Get the ACF field value
        $acf_title_value = get_field($title_field_name, $post_id);
        
        if (empty($acf_title_value)) {
            return null;
        }
        
        // Return ACF title data in the format expected by the frontend
        return array(
            'acf_enabled' => true,
            'has_acf_content' => true,
            'display_title' => $acf_title_value,
            'acf_field_name' => $title_field_name,
            'original_title' => get_the_title($post_id)
        );
    }
    
    /**
     * Database transaction safety for post updates
     * Prevents data corruption during concurrent operations
     * 
     * @param int $post_id Post ID
     * @param string $content Post content
     * @param bool $content_changed Whether content has actually changed
     * @return bool|WP_Error
     */
    private function safe_post_update($post_id, $content, $content_changed = true) {
        global $wpdb;
        
        // CRITICAL: Start database transaction
        $wpdb->query('START TRANSACTION');
        
        try {
            // CRITICAL: Lock the post row to prevent race conditions
            $wpdb->query($wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} WHERE ID = %d FOR UPDATE",
                $post_id
            ));
            
            // CRITICAL: Re-check lock status within transaction
            $current_lock = wp_check_post_lock($post_id);
            if ($current_lock && $current_lock != get_current_user_id()) {
                throw new Exception('Post lock changed during save operation');
            }
            
            // Set temporary lock during save operation
            wp_set_post_lock($post_id);
            
            // OPTIONAL: Create revision backup before update (WordPress may skip if no changes)
            $revision_id = wp_save_post_revision($post_id);
            // Note: wp_save_post_revision() returns false when no meaningful changes detected - this is normal
            if ($revision_id) {
                error_log('[SLMM Direct Editor] Created revision ' . $revision_id . ' for post ' . $post_id . ' (content changed: ' . ($content_changed ? 'yes' : 'no') . ')');
            } else {
                error_log('[SLMM Direct Editor] No revision created for post ' . $post_id . ' (content changed: ' . ($content_changed ? 'yes' : 'no') . ') - WordPress skipped revision');
            }
            
            // CRITICAL: Perform the actual post update
            $result = wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $content
            ));
            
            if (is_wp_error($result) || !$result) {
                throw new Exception('Post update failed: ' . (is_wp_error($result) ? $result->get_error_message() : 'Unknown error'));
            }
            
            // DON'T maintain lock after save - let it expire naturally per WordPress design
            
            // CRITICAL: Commit transaction only if all operations succeed
            $wpdb->query('COMMIT');
            
            error_log('[SLMM Direct Editor] Post ' . $post_id . ' saved successfully (content changed: ' . ($content_changed ? 'yes' : 'no') . ')');
            return true;
            
        } catch (Exception $e) {
            // CRITICAL: Rollback on any failure
            $wpdb->query('ROLLBACK');
            error_log('SLMM Direct Editor Transaction Failed: ' . $e->getMessage());
            return new WP_Error('save_failed', $e->getMessage());
        }
    }
}

/**
 * WP Sheet Editor-Style Content Validator
 * Mirrors WP Sheet Editor approach: Simple WordPress capability-based validation
 * If WordPress allows editing the post, Direct Editor allows editing it too
 */
class SLMM_Direct_Editor_Content_Validator {
    
    /**
     * WP Sheet Editor-style validation: Simple editor availability detection
     * Mirrors WP Sheet Editor approach - if we can edit it there, we can edit it here
     * 
     * @param int $post_id Post ID to validate
     * @return array Validation result with debug information
     */
    public function validate_post_for_editing($post_id) {
        $post = get_post($post_id);
        if (!$post) {
            return array(
                'can_edit' => false,
                'reason' => 'Post not found',
                'editor_type' => 'unknown',
                'lock_reason' => 'Post does not exist or is not accessible.'
            );
        }
        
        // WP Sheet Editor approach: Check if WordPress allows editing this post
        $can_edit_post = current_user_can('edit_post', $post_id);
        $post_type_supports_editor = post_type_supports($post->post_type, 'editor');
        $post_is_published_or_draft = in_array($post->post_status, array('publish', 'draft', 'pending', 'private'));
        
        // Simple logic: If WordPress allows editing and post type supports editor, allow it
        if ($can_edit_post && $post_type_supports_editor && $post_is_published_or_draft) {
            // Debug logging for troubleshooting
            error_log('[SLMM Direct Editor] Post ' . $post_id . ' allowed for editing (WP Sheet Editor approach)');
            
            return array(
                'can_edit' => true,
                'reason' => 'Post is editable (WP Sheet Editor approach)',
                'editor_type' => 'editable',
                'validation_method' => 'wp_sheet_editor_style',
                'debug_data' => array(
                    'can_edit_post' => $can_edit_post,
                    'post_type_supports_editor' => $post_type_supports_editor,
                    'post_status' => $post->post_status,
                    'post_type' => $post->post_type
                )
            );
        } else {
            // Post cannot be edited due to WordPress restrictions
            $lock_reason = 'WordPress does not allow editing this content.';
            if (!$can_edit_post) {
                $lock_reason = 'You do not have permission to edit this post.';
            } else if (!$post_type_supports_editor) {
                $lock_reason = 'This post type does not support content editing.';
            } else if (!$post_is_published_or_draft) {
                $lock_reason = 'This post status (' . $post->post_status . ') cannot be edited.';
            }
            
            error_log('[SLMM Direct Editor] Post ' . $post_id . ' locked: ' . $lock_reason);
            
            return array(
                'can_edit' => false,
                'reason' => $lock_reason,
                'editor_type' => 'restricted',
                'lock_reason' => $lock_reason,
                'debug_data' => array(
                    'can_edit_post' => $can_edit_post,
                    'post_type_supports_editor' => $post_type_supports_editor,
                    'post_status' => $post->post_status,
                    'post_type' => $post->post_type
                )
            );
        }
    }
    
    // REMOVED: Over-engineered validation methods
    // Now using WP Sheet Editor approach: simple WordPress capability checks
    
    // REMOVED: Enhanced block detection method
    // WP Sheet Editor approach doesn't need content format validation
    
    // REMOVED: Enhanced lock reason determination method
    // WP Sheet Editor approach uses simple WordPress permission messages
    
    // REMOVED: Enhanced editor type determination method
    // WP Sheet Editor approach doesn't need complex editor type classification
    
    // REMOVED: Legacy validation methods that contained false positive '<!-- wp:' pattern matching
    // These methods were causing legitimate TinyMCE content to be flagged as Gutenberg blocks
    // WP Sheet Editor approach eliminates the need for content format validation entirely
    
    // NO CONVERSION METHODS - Content is either editable (Classic Editor) or LOCKED
    // This maintains data integrity and prevents content corruption
}

// Initialize AJAX Handler (singleton pattern)
SLMM_Editor_AJAX_Handler::get_instance();