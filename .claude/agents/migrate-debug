#!/bin/bash

# SLMM Debug Migration Agent Wrapper
# Simplified interface for running debug migrations

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MIGRATION_SCRIPT="$SCRIPT_DIR/debug-migration.js"

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is required but not installed"
    exit 1
fi

# Check if migration script exists
if [ ! -f "$MIGRATION_SCRIPT" ]; then
    echo "❌ Migration script not found at: $MIGRATION_SCRIPT"
    exit 1
fi

# Show usage if no arguments
if [ $# -eq 0 ]; then
    echo "🔧 SLMM Debug Migration Agent"
    echo ""
    echo "Usage: migrate-debug <file-path> [options]"
    echo ""
    echo "Options:"
    echo "  --category=<name>  Override default category detection"
    echo "  --dry-run          Preview changes without modifying files"  
    echo "  --emergency        Extra safety checks for critical files"
    echo ""
    echo "Examples:"
    echo "  migrate-debug assets/js/slmm-direct-editor.js"
    echo "  migrate-debug assets/js/slmm-link-popup.js --category=\"Link Popup\""
    echo "  migrate-debug assets/js/file.js --dry-run"
    echo ""
    echo "Safety Features:"
    echo "  ✅ Creates new git branch automatically"
    echo "  ✅ Creates backup of original file"
    echo "  ✅ Validates syntax and linting"
    echo "  ✅ NEVER commits without your approval"
    echo "  ✅ Handles dangerous callback contexts"
    echo "  ✅ Emergency rollback on failure"
    exit 1
fi

# Run the migration agent
echo "🚀 Launching Debug Migration Agent..."
node "$MIGRATION_SCRIPT" "$@"