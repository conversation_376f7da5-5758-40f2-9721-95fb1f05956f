---
task: m-implement-page-summarization
branch: feature/implement-page-summarization
status: in-progress
created: 2025-01-14
modules: [interlinking-suite, settings, ajax-handlers, database]
---

# Page Summarization Feature Implementation

## Problem/Goal
Implement AI-powered page summarization functionality that integrates with the SLMM Interlinking Suite tree view, allowing users to generate, store, and reuse page summaries via database persistence with hover shortcuts and popup previews.

## Success Criteria
- [ ] New "Page Summarization" settings section added between Auto-Segmentation and AI Interlinking Prompts
- [ ] Database table `wp_slmm_page_summaries` created with content hash change detection
- [ ] Summary button positioned right of QuickBulk button in tree nodes with 4 visual states
- [ ] Hover+S keyboard shortcut triggers summarization for hovered nodes
- [ ] Hover popup displays existing summaries with metadata and actions
- [ ] AJAX handler processes single pages and batches with all AI providers
- [ ] {article_summary} token integration with Direct Editor
- [ ] Multi-selection support for parallel processing
- [ ] All functionality works seamlessly with existing interlinking suite architecture

## Architecture Components

### 1. Settings Configuration
**Location:** http://localhost:8884/wp-admin/admin.php?page=chatgpt-generator-settings#interlinking-suite

**New Section:** "Page Summarization" (positioned between Auto-Segmentation and AI Interlinking Prompts)

**Settings Structure:**
```php
'slmm_page_summarization_prompt' => array(
    'title' => 'Page Summarization',
    'provider' => 'openai', // or 'openrouter', 'anthropic'
    'model' => 'gpt-4o',
    'prompt' => 'What is this page about? Write a 100 word TLDR summary: {content}',
    'temperature' => 0.7,
    'max_tokens' => 200
)
```

**UI Elements:**
- Provider dropdown (OpenAI/OpenRouter/Anthropic)
- Model selection with refresh button
- Plain text prompt input (no processing/reformatting)
- Temperature slider (0-1)
- Max tokens input (50-500)

### 2. Database Schema
**Table:** `wp_slmm_page_summaries`

```sql
CREATE TABLE wp_slmm_page_summaries (
    id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    url varchar(500) NOT NULL,
    content_hash varchar(64) NOT NULL,
    summary text NOT NULL,
    prompt_used text NOT NULL,
    model_used varchar(100) NOT NULL,
    provider_used varchar(50) NOT NULL,
    generated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY url_hash (url, content_hash),
    KEY url_index (url),
    KEY generated_at_index (generated_at)
);
```

**Safety Note:** This is NEW table creation only - no data migration required. Uses WordPress dbDelta() for safe table creation with zero risk to existing functionality.

### 3. Tree Node UI Integration
**Button Placement:** Inside `.slmm-node-rect` elements, positioned RIGHT of `.slmm-node-quickbulk-button`

**Visual Design:**
```html
<circle class="slmm-node-summary-button" 
        cx="[calculated]" 
        cy="[calculated]" 
        r="8" 
        data-post-id="123"
        data-url="https://example.com/page"
        title="Generate/View Page Summary (Hover+S)">
</circle>
<text class="slmm-node-summary-icon" 
      x="[calculated]" 
      y="[calculated]" 
      text-anchor="middle" 
      dominant-baseline="central">S</text>
```

**Button States:**
- No Summary: Gray circle with "S" icon
- Summary Exists: Blue circle with "S" icon  
- Generating: Animated spinner with "S" icon
- Error: Red circle with "!" icon

### 4. Keyboard Shortcut Integration
**Trigger:** Hover + S key

**Implementation:**
- Detect mouse hover over tree node
- Listen for "S" keypress during hover
- Trigger summarization for hovered node
- Support multi-selection for parallel processing

### 5. AJAX Handler
**File:** `includes/ajax/page-summarization-handler.php`
**Endpoint:** `wp_ajax_slmm_summarize_page`

**Process Flow:**
1. **Input Validation**
   - Verify nonce and capabilities
   - Sanitize URL and post ID
   - Validate AI settings exist
2. **Content Retrieval**
   - Use existing SLMM_URL_Renderer_Handler for same-origin pages
   - Generate content hash (SHA-256)
   - Check database for existing summary
3. **AI Processing**
   - Replace {content} token in prompt
   - Make API call based on provider settings
   - Handle rate limiting and errors
4. **Database Storage**
   - Insert/update summary record
   - Store generation metadata
   - Return summary with metadata

**Parallel Processing Support:**
- Accept array of URLs/post IDs
- Process in batches to respect API limits
- Return progress updates via separate endpoint

### 6. Hover Popup System
**Trigger:** Hover over summary button when summary exists

**Popup Structure:**
```html
<div class="slmm-summary-popup">
    <div class="slmm-summary-header">
        <span class="slmm-summary-title">Page Summary</span>
        <span class="slmm-summary-meta">Generated 2 hours ago</span>
    </div>
    <div class="slmm-summary-content">
        [Summary text content]
    </div>
    <div class="slmm-summary-actions">
        <button class="slmm-regenerate-summary">Regenerate</button>
        <button class="slmm-copy-summary">Copy</button>
    </div>
</div>
```

**Positioning Logic:**
- Similar to existing link indicator popups
- Smart positioning to avoid viewport overflow
- Z-index management for proper layering

### 7. Token Integration for Direct Editor
**Token Name:** `{article_summary}`

**Implementation:**
- Detect when URL is loaded in Direct Editor
- Check database for existing summary
- Auto-populate token in sidebar
- Provide manual refresh option

## File Modifications Required

### Core Files to Modify:
1. **`includes/settings/general-settings.php`**
   - Add `render_page_summarization_prompt_box()` method
   - Add `get_default_page_summarization_prompt()` method
   - Update settings registration

2. **`includes/interlinking/interlinking-suite.php`**
   - Add database table creation on activation
   - Enqueue new assets for summarization
   - Add data localization for AJAX

3. **Asset files (extend existing or create new)**
   - Add summary button rendering logic
   - Implement hover+S keyboard handling
   - Handle popup display/positioning

### New Files to Create:
1. **`includes/ajax/page-summarization-handler.php`**
   - Main AJAX handler class
   - Batch processing support
   - Database operations

2. **`assets/js/slmm-page-summarization.js`**
   - Frontend JavaScript functionality
   - Keyboard shortcut handling
   - Popup management

3. **`assets/css/slmm-page-summarization.css`**
   - Styling for buttons and popups
   - Animation states

## Integration with Interlinking Suite

**Seamless Integration Points:**
- **Existing button pattern:** Follows same positioning logic as QuickBulk buttons
- **D3.js compatibility:** Uses same SVG structure and event handlers
- **Asset loading:** Integrates with existing `enqueue_assets()` method in interlinking-suite.php
- **AJAX pattern:** Follows same nonce/security patterns as existing handlers
- **Styling consistency:** Uses existing CSS variables and themes

**No Breaking Changes:**
- Pure additive feature - no modification of existing functionality
- Database table is independent of existing data
- Button placement calculated relative to existing elements
- Keyboard shortcuts use different key combination (Hover+S vs existing shortcuts)

## Security & Performance

### Security Measures:
- Input sanitization for all user data
- Capability checks (`edit_posts` minimum)
- Nonce verification for all AJAX calls
- Content hash verification prevents tampering
- SQL injection protection via prepared statements

### Performance Optimizations:
- Database indexing for fast lookups
- Content hash comparison prevents unnecessary regeneration
- Lazy loading of summary data
- Efficient DOM manipulation
- Batch processing for multiple selections

## Testing Strategy

### Manual Testing Scenarios:
1. **Settings Configuration**
   - Test all provider/model combinations
   - Verify prompt customization
   - Test settings persistence

2. **Tree Integration**
   - Button placement accuracy across different node types
   - Hover+S shortcut functionality
   - Multi-selection handling
   - Visual state transitions

3. **Summary Generation**
   - Single page processing
   - Batch processing with rate limiting
   - Error handling and recovery
   - Database persistence verification

4. **Popup System**
   - Hover detection accuracy
   - Positioning edge cases
   - Content display formatting
   - Action button functionality

## Context Files
<!-- Will be populated by context-gathering agent -->

## User Notes
**Database Migration Clarification:** This is NOT a migration in the traditional sense. We're simply creating a new table (`wp_slmm_page_summaries`) using WordPress's safe `dbDelta()` function. There's zero risk to existing data or functionality. If table creation fails, the feature simply doesn't work - no site breakage possible.

**Perfect Integration Requirement:** The implementation must work flawlessly with the existing interlinking suite architecture, following all established patterns for buttons, AJAX, security, and styling.

## Work Log
- [2025-01-14] Task created with comprehensive technical specification
- [2025-01-14] Database safety concerns addressed - new table creation only, no migration risk
- [2025-01-14] Fixed confusing "Leave site?" popup when saving settings by updating beforeunload handler in slmm-settings.js