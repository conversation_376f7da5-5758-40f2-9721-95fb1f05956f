<?php
// File: includes/interlinking/seo-calculator.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * SEO Calculator - Core mathematical algorithms for silo strength calculation
 * 
 * Implements the advanced SEO formulas:
 * SILO_STRENGTH = (Topical_Relevance × Link_Density × Content_Depth) / Topic_Dilution
 * Authority Distribution using PageRank simulation
 */
class SLMM_SEO_Calculator {
    
    /**
     * Calculate silo strength using mathematical SEO formula
     * 
     * @param array $silo_data Contains pages, links, topics data
     * @return float Calculated silo strength (0-100)
     */
    public function calculate_silo_strength($silo_data) {
        if (empty($silo_data) || !is_array($silo_data)) {
            return 0.0;
        }
        
        // Extract components for calculation
        $topical_relevance = $this->calculate_topical_relevance($silo_data);
        $link_density = $this->calculate_link_density($silo_data);
        $content_depth = $this->calculate_content_depth($silo_data);
        $topic_dilution = $this->calculate_topic_dilution($silo_data);
        
        // Apply the core SEO formula
        if ($topic_dilution == 0) {
            $topic_dilution = 0.1; // Prevent division by zero
        }
        
        $silo_strength = ($topical_relevance * $link_density * $content_depth) / $topic_dilution;
        
        // Normalize to 0-100 scale
        $normalized_strength = min(100, max(0, $silo_strength * 10));
        
        error_log('[SLMM SEO Calculator] Calculated silo strength: ' . $normalized_strength);
        
        return round($normalized_strength, 2);
    }
    
    /**
     * Calculate topical relevance score based on keyword density and semantic relationships
     * 
     * @param array $silo_data 
     * @return float Topical relevance (0-10)
     */
    private function calculate_topical_relevance($silo_data) {
        if (!isset($silo_data['pages']) || empty($silo_data['pages'])) {
            return 0.0;
        }
        
        $total_relevance = 0.0;
        $page_count = 0;
        
        foreach ($silo_data['pages'] as $page) {
            if (!isset($page['content']) || !isset($page['target_keywords'])) {
                continue;
            }
            
            $page_relevance = $this->analyze_page_relevance($page);
            $total_relevance += $page_relevance;
            $page_count++;
        }
        
        if ($page_count == 0) {
            return 0.0;
        }
        
        $average_relevance = $total_relevance / $page_count;
        
        return min(10, max(0, $average_relevance));
    }
    
    /**
     * Analyze individual page relevance using keyword density and semantic analysis
     * 
     * @param array $page Page data with content and keywords
     * @return float Page relevance score (0-10)
     */
    private function analyze_page_relevance($page) {
        $content = strtolower(strip_tags($page['content']));
        $keywords = array_map('strtolower', $page['target_keywords']);
        
        if (empty($content) || empty($keywords)) {
            return 0.0;
        }
        
        $word_count = str_word_count($content);
        if ($word_count == 0) {
            return 0.0;
        }
        
        $keyword_density = 0.0;
        $semantic_score = 0.0;
        
        // Calculate keyword density
        foreach ($keywords as $keyword) {
            $keyword_count = substr_count($content, $keyword);
            $density = ($keyword_count / $word_count) * 100;
            
            // Optimal density is 1-3%
            if ($density >= 1 && $density <= 3) {
                $keyword_density += 2.0; // Optimal range
            } elseif ($density > 0 && $density < 1) {
                $keyword_density += 1.0; // Under-optimized
            } elseif ($density > 3 && $density <= 5) {
                $keyword_density += 1.5; // Slightly over-optimized
            }
            // Over 5% density = 0 points (keyword stuffing)
        }
        
        // Calculate semantic relationships (simplified LSI simulation)
        $semantic_score = $this->calculate_semantic_relationships($content, $keywords);
        
        // Combine scores (50% density, 50% semantic)
        $page_relevance = ($keyword_density * 0.5) + ($semantic_score * 0.5);
        
        return min(10, max(0, $page_relevance));
    }
    
    /**
     * Calculate semantic relationships using simplified LSI
     * 
     * @param string $content Page content
     * @param array $keywords Target keywords
     * @return float Semantic score (0-10)
     */
    private function calculate_semantic_relationships($content, $keywords) {
        // Common semantic word patterns for SEO
        $semantic_patterns = array(
            'seo' => array('optimization', 'ranking', 'search', 'google', 'traffic', 'keywords'),
            'content' => array('article', 'blog', 'writing', 'text', 'information', 'quality'),
            'link' => array('anchor', 'url', 'href', 'connection', 'reference', 'citation'),
            'page' => array('website', 'site', 'web', 'html', 'document', 'resource')
        );
        
        $semantic_score = 0.0;
        
        foreach ($keywords as $keyword) {
            $base_keyword = explode(' ', $keyword)[0]; // Get first word
            
            if (isset($semantic_patterns[$base_keyword])) {
                foreach ($semantic_patterns[$base_keyword] as $related_word) {
                    if (strpos($content, $related_word) !== false) {
                        $semantic_score += 0.5;
                    }
                }
            }
        }
        
        return min(10, $semantic_score);
    }
    
    /**
     * Calculate link density within the silo structure
     * 
     * @param array $silo_data
     * @return float Link density score (0-10)
     */
    private function calculate_link_density($silo_data) {
        if (!isset($silo_data['links']) || !isset($silo_data['pages'])) {
            return 0.0;
        }
        
        $total_links = count($silo_data['links']);
        $total_pages = count($silo_data['pages']);
        
        if ($total_pages == 0) {
            return 0.0;
        }
        
        // Calculate internal link ratio
        $internal_links = 0;
        foreach ($silo_data['links'] as $link) {
            if (isset($link['type']) && $link['type'] === 'internal') {
                $internal_links++;
            }
        }
        
        // Optimal internal link density: 2-5 links per page
        $links_per_page = $internal_links / $total_pages;
        
        if ($links_per_page >= 2 && $links_per_page <= 5) {
            $density_score = 10.0; // Optimal
        } elseif ($links_per_page >= 1 && $links_per_page < 2) {
            $density_score = 7.0; // Good but could be better
        } elseif ($links_per_page > 5 && $links_per_page <= 8) {
            $density_score = 8.0; // Slightly high but acceptable
        } elseif ($links_per_page > 8) {
            $density_score = 5.0; // Too many links
        } else {
            $density_score = 3.0; // Too few links
        }
        
        return $density_score;
    }
    
    /**
     * Calculate content depth score based on word count and structure
     * 
     * @param array $silo_data
     * @return float Content depth score (0-10)
     */
    private function calculate_content_depth($silo_data) {
        if (!isset($silo_data['pages'])) {
            return 0.0;
        }
        
        $total_depth = 0.0;
        $page_count = 0;
        
        foreach ($silo_data['pages'] as $page) {
            if (!isset($page['content'])) {
                continue;
            }
            
            $content = strip_tags($page['content']);
            $word_count = str_word_count($content);
            
            // Calculate depth score based on word count
            if ($word_count >= 1500) {
                $depth_score = 10.0; // Comprehensive content
            } elseif ($word_count >= 1000) {
                $depth_score = 8.0; // Good depth
            } elseif ($word_count >= 500) {
                $depth_score = 6.0; // Moderate depth
            } elseif ($word_count >= 300) {
                $depth_score = 4.0; // Minimal depth
            } else {
                $depth_score = 2.0; // Thin content
            }
            
            // Bonus for structured content (headings)
            $heading_count = substr_count($page['content'], '<h2>') + 
                           substr_count($page['content'], '<h3>') + 
                           substr_count($page['content'], '<h4>');
            
            if ($heading_count >= 3) {
                $depth_score += 1.0; // Well-structured
            }
            
            $total_depth += min(10, $depth_score);
            $page_count++;
        }
        
        if ($page_count == 0) {
            return 0.0;
        }
        
        return $total_depth / $page_count;
    }
    
    /**
     * Calculate topic dilution factor
     * 
     * @param array $silo_data
     * @return float Topic dilution factor (1-10, lower is better)
     */
    private function calculate_topic_dilution($silo_data) {
        if (!isset($silo_data['pages'])) {
            return 5.0; // Default moderate dilution
        }
        
        // Collect all topics/keywords from pages
        $all_topics = array();
        foreach ($silo_data['pages'] as $page) {
            if (isset($page['target_keywords'])) {
                $all_topics = array_merge($all_topics, $page['target_keywords']);
            }
        }
        
        // Calculate topic diversity
        $unique_topics = array_unique(array_map('strtolower', $all_topics));
        $total_topics = count($all_topics);
        $unique_count = count($unique_topics);
        
        if ($total_topics == 0) {
            return 5.0;
        }
        
        // Topic focus ratio (lower ratio = more focused = less dilution)
        $focus_ratio = $unique_count / $total_topics;
        
        // Convert to dilution score (1 = highly focused, 10 = highly diluted)
        $dilution_score = $focus_ratio * 10;
        
        return max(1.0, min(10.0, $dilution_score));
    }
    
    /**
     * Calculate PageRank-style authority distribution
     * 
     * @param array $silo_data Silo structure with pages and links
     * @param float $damping_factor PageRank damping factor (default 0.85)
     * @param int $iterations Number of iterations (default 10)
     * @return array Authority scores for each page
     */
    public function calculate_authority_distribution($silo_data, $damping_factor = 0.85, $iterations = 10) {
        if (!isset($silo_data['pages']) || !isset($silo_data['links'])) {
            return array();
        }
        
        $pages = $silo_data['pages'];
        $links = $silo_data['links'];
        $page_count = count($pages);
        
        if ($page_count == 0) {
            return array();
        }
        
        // Initialize authority scores
        $authority_scores = array();
        $initial_score = 1.0 / $page_count;
        
        foreach ($pages as $page_id => $page) {
            $authority_scores[$page_id] = $initial_score;
        }
        
        // Build link matrix
        $link_matrix = $this->build_link_matrix($pages, $links);
        
        // Iterate PageRank algorithm
        for ($i = 0; $i < $iterations; $i++) {
            $new_scores = array();
            
            foreach ($pages as $page_id => $page) {
                $rank = (1 - $damping_factor) / $page_count;
                
                // Sum authority from incoming links
                foreach ($pages as $source_id => $source_page) {
                    if (isset($link_matrix[$source_id][$page_id])) {
                        $outbound_count = count($link_matrix[$source_id]);
                        if ($outbound_count > 0) {
                            $rank += $damping_factor * ($authority_scores[$source_id] / $outbound_count);
                        }
                    }
                }
                
                $new_scores[$page_id] = $rank;
            }
            
            $authority_scores = $new_scores;
        }
        
        error_log('[SLMM SEO Calculator] Authority distribution calculated for ' . $page_count . ' pages');
        
        return $authority_scores;
    }
    
    /**
     * Build link matrix for PageRank calculation
     * 
     * @param array $pages
     * @param array $links
     * @return array Link matrix
     */
    private function build_link_matrix($pages, $links) {
        $matrix = array();
        
        // Initialize matrix
        foreach ($pages as $source_id => $page) {
            $matrix[$source_id] = array();
        }
        
        // Populate matrix with links
        foreach ($links as $link) {
            if (isset($link['from']) && isset($link['to']) && 
                isset($pages[$link['from']]) && isset($pages[$link['to']])) {
                $matrix[$link['from']][$link['to']] = true;
            }
        }
        
        return $matrix;
    }
    
    /**
     * Generate optimization recommendations based on silo analysis
     * 
     * @param array $silo_data
     * @param float $current_strength Current silo strength
     * @return array Optimization recommendations
     */
    public function generate_recommendations($silo_data, $current_strength) {
        $recommendations = array();
        
        // Analyze each component and provide specific recommendations
        $topical_relevance = $this->calculate_topical_relevance($silo_data);
        $link_density = $this->calculate_link_density($silo_data);
        $content_depth = $this->calculate_content_depth($silo_data);
        $topic_dilution = $this->calculate_topic_dilution($silo_data);
        
        // Content recommendations
        if ($content_depth < 6.0) {
            $recommendations[] = array(
                'type' => 'content',
                'priority' => 'high',
                'title' => 'Increase Content Depth',
                'description' => 'Pages in this silo need more comprehensive content. Aim for 1500+ words per page.',
                'impact' => 'High'
            );
        }
        
        // Link density recommendations
        if ($link_density < 7.0) {
            $recommendations[] = array(
                'type' => 'linking',
                'priority' => 'high',
                'title' => 'Improve Internal Linking',
                'description' => 'Add more strategic internal links. Target 3-5 contextual links per page.',
                'impact' => 'High'
            );
        }
        
        // Topic focus recommendations
        if ($topic_dilution > 6.0) {
            $recommendations[] = array(
                'type' => 'topical',
                'priority' => 'medium',
                'title' => 'Improve Topic Focus',
                'description' => 'This silo covers too many different topics. Consider splitting into separate silos.',
                'impact' => 'Medium'
            );
        }
        
        // Keyword optimization recommendations
        if ($topical_relevance < 6.0) {
            $recommendations[] = array(
                'type' => 'keywords',
                'priority' => 'high',
                'title' => 'Optimize Keyword Usage',
                'description' => 'Improve keyword density and semantic relationships in your content.',
                'impact' => 'High'
            );
        }
        
        error_log('[SLMM SEO Calculator] Generated ' . count($recommendations) . ' recommendations');
        
        return $recommendations;
    }
}