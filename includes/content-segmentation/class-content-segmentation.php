<?php
/**
 * SLMM Content Segmentation - Core Logic
 * 
 * Provides content segmentation functionality for dividing articles into
 * TOP, MIDDLE, and BOTTOM sections for targeted AI processing.
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Content_Segmentation
 * @version 1.0.0
 * @since 4.10.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SLMM Content Segmentation Core Class
 */
class SLMM_Content_Segmentation {

    /**
     * Singleton instance
     * @var SLMM_Content_Segmentation|null
     */
    private static $instance = null;

    /**
     * Hook registration tracking
     * @var bool
     */
    private static $hooks_registered = false;

    /**
     * Segmentation markers for identifying sections (6-marker system)
     */
    const TOP_START_MARKER = '<!-- SLMM_SEGMENT_TOP_START -->';
    const TOP_END_MARKER = '<!-- SLMM_SEGMENT_TOP_END -->';
    const MIDDLE_START_MARKER = '<!-- SLMM_SEGMENT_MIDDLE_START -->';
    const MIDDLE_END_MARKER = '<!-- SLMM_SEGMENT_MIDDLE_END -->';
    const BOTTOM_START_MARKER = '<!-- SLMM_SEGMENT_BOTTOM_START -->';
    const BOTTOM_END_MARKER = '<!-- SLMM_SEGMENT_BOTTOM_END -->';

    /**
     * Legacy markers for backward compatibility (to be removed)
     */
    const TOP_MARKER = '<!-- slmm-segment-top -->';
    const BOTTOM_MARKER = '<!-- slmm-segment-bottom -->';

    /**
     * Section identifiers
     */
    const SECTION_TOP = 'top';
    const SECTION_MIDDLE = 'middle';
    const SECTION_BOTTOM = 'bottom';
    const SECTION_ALL = 'all';

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the content segmentation system (private constructor)
     */
    private function __construct() {
        // CRITICAL FIX: Only register hooks once
        if (!self::$hooks_registered) {
            add_action('init', array($this, 'init'));
            self::$hooks_registered = true;
            error_log('[SLMM Content Segmentation] Hooks registered once via singleton');
        }
    }
    
    /**
     * Initialize hooks and filters
     */
    public function init() {
        // Add filters for content processing
        add_filter('slmm_process_segmented_content', array($this, 'process_segmented_content'), 10, 2);
        add_filter('slmm_extract_content_section', array($this, 'extract_content_section'), 10, 2);
        add_filter('slmm_validate_segmentation_markers', array($this, 'validate_segmentation_markers'), 10, 1);
        
        // Enqueue assets for admin pages (Direct Editor integration)
        add_action('admin_enqueue_scripts', array($this, 'enqueue_assets'));
    }
    
    /**
     * Enqueue content segmentation assets
     */
    public function enqueue_assets($hook) {
        // Only load on post editor pages where Direct Editor is used
        if (!in_array($hook, array('post.php', 'post-new.php'))) {
            return;
        }
        
        // Enqueue CSS for visual overlays and Direct Editor modal integration
        wp_enqueue_style('slmm-content-segmentation', SLMM_SEO_PLUGIN_URL . 'assets/css/content-segmentation.css', array(), SLMM_SEO_VERSION);
    }
    
    /**
     * Extract specific section from segmented content
     * 
     * @param string $content The full content with markers
     * @param string $section The section to extract (top, middle, bottom, all)
     * @return string The extracted section content
     */
    public function extract_content_section($content, $section = self::SECTION_ALL) {
        if (empty($content)) {
            return '';
        }
        
        // If no segmentation requested, return full content
        if ($section === self::SECTION_ALL) {
            return $this->clean_content_markers($content);
        }
        
        // Find marker positions
        $markers = $this->find_marker_positions($content);
        
        // If no markers found, handle accordingly
        if (!$markers['has_markers']) {
            // If requesting top section and no markers, return all content
            if ($section === self::SECTION_TOP) {
                return $content;
            }
            // For middle or bottom without markers, return empty
            return '';
        }
        
        // Extract the requested section
        switch ($section) {
            case self::SECTION_TOP:
                return $this->extract_top_section($content, $markers);
                
            case self::SECTION_MIDDLE:
                return $this->extract_middle_section($content, $markers);
                
            case self::SECTION_BOTTOM:
                return $this->extract_bottom_section($content, $markers);
                
            default:
                return $this->clean_content_markers($content);
        }
    }
    
    /**
     * Find positions of segmentation markers in content (6-marker system)
     * 
     * @param string $content The content to search
     * @return array Marker positions and metadata
     */
    public function find_marker_positions($content) {
        // Find all 6 markers
        $markers = array(
            'top_start' => strpos($content, self::TOP_START_MARKER),
            'top_end' => strpos($content, self::TOP_END_MARKER),
            'middle_start' => strpos($content, self::MIDDLE_START_MARKER),
            'middle_end' => strpos($content, self::MIDDLE_END_MARKER),
            'bottom_start' => strpos($content, self::BOTTOM_START_MARKER),
            'bottom_end' => strpos($content, self::BOTTOM_END_MARKER)
        );
        
        // Count found markers
        $found_markers = array_filter($markers, function($pos) { return $pos !== false; });
        
        $result = array(
            'has_markers' => count($found_markers) > 0,
            'marker_count' => count($found_markers),
            'positions' => $markers,
            'has_top_start' => $markers['top_start'] !== false,
            'has_top_end' => $markers['top_end'] !== false,
            'has_middle_start' => $markers['middle_start'] !== false,
            'has_middle_end' => $markers['middle_end'] !== false,
            'has_bottom_start' => $markers['bottom_start'] !== false,
            'has_bottom_end' => $markers['bottom_end'] !== false,
            'is_valid' => false,
            'sequence_valid' => false
        );
        
        // Check marker sequence validity
        if (count($found_markers) > 1) {
            $sorted_positions = array_filter($markers);
            asort($sorted_positions);
            $expected_order = array('top_start', 'top_end', 'middle_start', 'middle_end', 'bottom_start', 'bottom_end');
            $actual_order = array_keys($sorted_positions);
            
            // Check if actual order matches expected subsequence
            $result['sequence_valid'] = $this->is_valid_marker_sequence($actual_order, $expected_order);
        } else {
            $result['sequence_valid'] = true; // Single marker or no markers is valid
        }
        
        $result['is_valid'] = $result['sequence_valid'];
        
        // Check for complete sections
        $result['has_complete_top'] = $result['has_top_start'] && $result['has_top_end'];
        $result['has_complete_middle'] = $result['has_middle_start'] && $result['has_middle_end'];
        $result['has_complete_bottom'] = $result['has_bottom_start'] && $result['has_bottom_end'];
        
        return $result;
    }
    
    /**
     * Check if marker sequence is valid (markers appear in correct order)
     * 
     * @param array $actual_order Actual order of found markers
     * @param array $expected_order Expected order of markers
     * @return bool True if sequence is valid
     */
    private function is_valid_marker_sequence($actual_order, $expected_order) {
        $last_index = -1;
        foreach ($actual_order as $marker) {
            $current_index = array_search($marker, $expected_order);
            if ($current_index === false || $current_index <= $last_index) {
                return false;
            }
            $last_index = $current_index;
        }
        return true;
    }
    
    /**
     * Extract content from TOP section (between TOP_START and TOP_END markers)
     * 
     * @param string $content Full content
     * @param array $markers Marker positions
     * @return string TOP section content
     */
    private function extract_top_section($content, $markers) {
        // Check if we have a complete TOP section
        if ($markers['has_complete_top']) {
            $start_pos = $markers['positions']['top_start'] + strlen(self::TOP_START_MARKER);
            $end_pos = $markers['positions']['top_end'];
            $length = $end_pos - $start_pos;
            
            if ($length > 0) {
                return trim(substr($content, $start_pos, $length));
            }
        }
        
        // If only TOP_START exists, content from start to TOP_START
        if ($markers['has_top_start'] && !$markers['has_top_end']) {
            return trim(substr($content, 0, $markers['positions']['top_start']));
        }
        
        // If no TOP markers but has other markers, content before first marker
        if (!$markers['has_top_start'] && !$markers['has_top_end']) {
            $first_marker_pos = $this->find_first_marker_position($markers);
            if ($first_marker_pos !== false) {
                return trim(substr($content, 0, $first_marker_pos));
            }
            // No markers at all, return full content
            return $content;
        }
        
        return '';
    }
    
    /**
     * Extract content from MIDDLE section (between MIDDLE_START and MIDDLE_END markers)
     * 
     * @param string $content Full content
     * @param array $markers Marker positions
     * @return string MIDDLE section content
     */
    private function extract_middle_section($content, $markers) {
        // Check if we have a complete MIDDLE section
        if ($markers['has_complete_middle']) {
            $start_pos = $markers['positions']['middle_start'] + strlen(self::MIDDLE_START_MARKER);
            $end_pos = $markers['positions']['middle_end'];
            $length = $end_pos - $start_pos;
            
            if ($length > 0) {
                return trim(substr($content, $start_pos, $length));
            }
        }
        
        return '';
    }
    
    /**
     * Extract content from BOTTOM section (between BOTTOM_START and BOTTOM_END markers)
     * 
     * @param string $content Full content
     * @param array $markers Marker positions
     * @return string BOTTOM section content
     */
    private function extract_bottom_section($content, $markers) {
        // Check if we have a complete BOTTOM section
        if ($markers['has_complete_bottom']) {
            $start_pos = $markers['positions']['bottom_start'] + strlen(self::BOTTOM_START_MARKER);
            $end_pos = $markers['positions']['bottom_end'];
            $length = $end_pos - $start_pos;
            
            if ($length > 0) {
                return trim(substr($content, $start_pos, $length));
            }
        }
        
        // If only BOTTOM_START exists, content from BOTTOM_START to end
        if ($markers['has_bottom_start'] && !$markers['has_bottom_end']) {
            $start_pos = $markers['positions']['bottom_start'] + strlen(self::BOTTOM_START_MARKER);
            return trim(substr($content, $start_pos));
        }
        
        return '';
    }
    
    /**
     * Find position of first marker in content
     * 
     * @param array $markers Marker positions
     * @return int|false Position of first marker or false if none found
     */
    private function find_first_marker_position($markers) {
        $positions = array_filter($markers['positions'], function($pos) { 
            return $pos !== false; 
        });
        
        return empty($positions) ? false : min($positions);
    }
    
    /**
     * Remove segmentation markers from content
     * 
     * @param string $content Content with potential markers
     * @return string Clean content without markers
     */
    public function clean_content_markers($content) {
        // Remove all 6 markers
        $content = str_replace(self::TOP_START_MARKER, '', $content);
        $content = str_replace(self::TOP_END_MARKER, '', $content);
        $content = str_replace(self::MIDDLE_START_MARKER, '', $content);
        $content = str_replace(self::MIDDLE_END_MARKER, '', $content);
        $content = str_replace(self::BOTTOM_START_MARKER, '', $content);
        $content = str_replace(self::BOTTOM_END_MARKER, '', $content);
        
        // Remove legacy markers for backward compatibility
        $content = str_replace(self::TOP_MARKER, '', $content);
        $content = str_replace(self::BOTTOM_MARKER, '', $content);
        
        return $content;
    }
    
    /**
     * Insert segmentation markers using sequential 6-marker logic
     * 
     * @param string $content Current content
     * @param int $position Position to insert markers (cursor position)
     * @return string Content with markers inserted
     */
    public function insert_sequential_markers($content, $position = null) {
        error_log('SLMM Segmentation Core: insert_sequential_markers called - Position: ' . ($position ?? 'null'));
        
        // Analyze current marker state
        $markers = $this->find_marker_positions($content);
        $insertion_plan = $this->determine_marker_insertion($markers);
        
        error_log('SLMM Segmentation Core: Insertion plan: ' . json_encode($insertion_plan));
        
        if (!$insertion_plan['should_insert']) {
            error_log('SLMM Segmentation Core: ' . $insertion_plan['message']);
            return $content;
        }
        
        // If no position specified, use cursor or smart placement
        if ($position === null) {
            $position = $this->determine_smart_position($content, $markers, $insertion_plan);
            error_log('SLMM Segmentation Core: Determined position: ' . $position);
        }
        
        // Validate position
        if ($position < 0 || $position > strlen($content)) {
            error_log('SLMM Segmentation Core: Invalid position (' . $position . '), content length: ' . strlen($content));
            return $content;
        }
        
        // Initialize with original content
        $new_content = $content;
        
        // Insert markers at cursor position (only if there are markers to insert)
        if (!empty($insertion_plan['markers'])) {
            $new_content = $this->insert_markers_at_position($new_content, $insertion_plan['markers'], $position);
        }
        
        // Auto-complete boundaries if needed (for Stage 1: button click)
        if (isset($insertion_plan['auto_complete']) && $insertion_plan['auto_complete']) {
            error_log('SLMM Segmentation Core: Applying auto-completion: ' . $insertion_plan['auto_complete']);
            $new_content = $this->auto_complete_boundaries($new_content, $insertion_plan['auto_complete']);
            error_log('SLMM Segmentation Core: Content after auto-completion, length: ' . strlen($new_content));
        }
        
        return $new_content;
    }
    
    /**
     * Determine what markers to insert based on current state (3-click workflow)
     * 
     * @param array $markers Current marker analysis
     * @return array Insertion plan
     */
    public function determine_marker_insertion($markers) {
        $plan = array(
            'should_insert' => false,
            'markers' => array(),
            'message' => '',
            'stage' => 0
        );
        
        // Stage 1: No markers exist -> Button click auto-inserts TOP_START + BOTTOM_END
        if ($markers['marker_count'] === 0) {
            $plan['should_insert'] = true;
            $plan['markers'] = array(); // No markers at cursor position
            $plan['message'] = 'Button clicked: Auto-inserting TOP_START and BOTTOM_END boundaries';
            $plan['stage'] = 1;
            $plan['auto_complete'] = 'boundaries'; // Insert TOP_START at beginning, BOTTOM_END at end
            return $plan;
        }
        
        // Stage 2: TOP_START + BOTTOM_END exist -> First content click inserts TOP_END + MIDDLE_START
        if ($markers['marker_count'] === 2 && $markers['has_top_start'] && $markers['has_bottom_end']) {
            $plan['should_insert'] = true;
            $plan['markers'] = array(self::TOP_END_MARKER, self::MIDDLE_START_MARKER);
            $plan['message'] = 'First content click: Inserting TOP_END + MIDDLE_START';
            $plan['stage'] = 2;
            return $plan;
        }
        
        // Stage 3: 4 markers exist -> Second content click inserts MIDDLE_END + BOTTOM_START  
        if ($markers['marker_count'] === 4 && $markers['has_top_start'] && $markers['has_top_end'] && 
            $markers['has_middle_start'] && $markers['has_bottom_end']) {
            $plan['should_insert'] = true;
            $plan['markers'] = array(self::MIDDLE_END_MARKER, self::BOTTOM_START_MARKER);
            $plan['message'] = 'Second content click: Inserting MIDDLE_END + BOTTOM_START';
            $plan['stage'] = 3;
            return $plan;
        }
        
        // All 6 markers exist
        if ($markers['marker_count'] === 6) {
            $plan['message'] = 'Content segmentation already complete';
            return $plan;
        }
        
        // Invalid state - clear and restart
        $plan['message'] = 'Invalid marker state - please clear existing markers and restart';
        return $plan;
    }
    
    /**
     * Auto-complete boundaries (TOP_START at beginning, BOTTOM_END at end)
     * 
     * @param string $content Content with markers
     * @param string|int $auto_complete_type Type of auto-completion
     * @return string Content with boundaries completed
     */
    private function auto_complete_boundaries($content, $auto_complete_type) {
        $updated_content = $content;
        
        error_log('SLMM Segmentation Core: auto_complete_boundaries called with type: ' . $auto_complete_type);
        error_log('SLMM Segmentation Core: Original content length: ' . strlen($content));
        
        if ($auto_complete_type === 'boundaries') {
            // Button click: Add TOP_START at beginning and BOTTOM_END at end
            $top_marker = self::TOP_START_MARKER;
            $bottom_marker = self::BOTTOM_END_MARKER;
            
            error_log('SLMM Segmentation Core: TOP_START marker: ' . $top_marker);
            error_log('SLMM Segmentation Core: BOTTOM_END marker: ' . $bottom_marker);
            
            $updated_content = $top_marker . "\n" . $updated_content . "\n" . $bottom_marker;
            
            error_log('SLMM Segmentation Core: Content after boundary insertion, length: ' . strlen($updated_content));
            error_log('SLMM Segmentation Core: First 200 chars: ' . substr($updated_content, 0, 200));
            error_log('SLMM Segmentation Core: Last 200 chars: ' . substr($updated_content, -200));
            
        } else if ($auto_complete_type === 1) {
            // Legacy: After first click: Add TOP_START at the very beginning
            $updated_content = self::TOP_START_MARKER . "\n" . $updated_content;
        } else if ($auto_complete_type === 2) {
            // Legacy: After second click: Add BOTTOM_END at the very end
            $updated_content = $updated_content . "\n" . self::BOTTOM_END_MARKER;
        }
        
        return $updated_content;
    }
    
    /**
     * Insert multiple markers at a specific position
     * 
     * @param string $content Content to modify
     * @param array $markers Array of marker strings to insert
     * @param int $position Position to insert at
     * @return string Modified content
     */
    private function insert_markers_at_position($content, $markers, $position) {
        if (empty($markers)) {
            return $content;
        }
        
        // Insert markers at position with proper line breaks (each marker on its own line)
        $marker_text = implode("\n", $markers); // Each marker on separate line
        $new_content = substr($content, 0, $position) . "\n" . $marker_text . "\n" . substr($content, $position);
        
        error_log('SLMM Segmentation Core: Inserted markers at position ' . $position . ': ' . $marker_text);
        error_log('SLMM Segmentation Core: Content length before: ' . strlen($content) . ', after: ' . strlen($new_content));
        return $new_content;
    }
    
    /**
     * Legacy method for backward compatibility
     */
    public function insert_marker($content, $marker_type, $position = null) {
        // Convert legacy calls to sequential marker insertion
        return $this->insert_sequential_markers($content, $position);
    }
    
    /**
     * Determine smart position for marker insertion based on content and stage
     * 
     * @param string $content Current content
     * @param array $markers Current marker analysis
     * @param array $insertion_plan Planned insertion
     * @return int Suggested insertion position
     */
    private function determine_smart_position($content, $markers, $insertion_plan) {
        $content_length = strlen($content);
        
        switch ($insertion_plan['stage']) {
            case 1: // Inserting TOP_START at beginning
                return 0;
                
            case 2: // First click: TOP_END + MIDDLE_START
                // Position after about 1/3 of content
                return $this->find_content_boundary($content, 0, intval($content_length * 0.4), 'forward') ?: intval($content_length * 0.33);
                
            case 3: // Second click: MIDDLE_END + BOTTOM_START  
                // Position after about 2/3 of content
                return $this->find_content_boundary($content, intval($content_length * 0.6), $content_length, 'forward') ?: intval($content_length * 0.67);
                
            case 4: // Auto BOTTOM_END at end
                return $content_length;
                
            default:
                return intval($content_length * 0.5);
        }
    }
    
    
    /**
     * Find a good content boundary for marker insertion
     * 
     * @param string $content Content to search
     * @param int $start Start position
     * @param int $end End position
     * @param string $direction 'forward' or 'backward'
     * @return int|false Position of good boundary or false if none found
     */
    private function find_content_boundary($content, $start, $end, $direction) {
        // Look for paragraph breaks, heading endings, or other natural breaks
        $boundaries = array(
            '</h1>',
            '</h2>',
            '</h3>',
            '</h4>',
            '</h5>',
            '</h6>',
            '</p>',
            '</ul>',
            '</ol>',
            '</div>',
            '</blockquote>'
        );
        
        if ($direction === 'forward') {
            foreach ($boundaries as $boundary) {
                $pos = strpos($content, $boundary, $start);
                if ($pos !== false && $pos < $end) {
                    return $pos + strlen($boundary);
                }
            }
        } else {
            foreach ($boundaries as $boundary) {
                $pos = strrpos(substr($content, $start, $end - $start), $boundary);
                if ($pos !== false) {
                    return $start + $pos + strlen($boundary);
                }
            }
        }
        
        return false;
    }
    
    /**
     * Validate segmentation markers in content
     * 
     * @param string $content Content to validate
     * @return array Validation result
     */
    public function validate_segmentation_markers($content) {
        $markers = $this->find_marker_positions($content);
        
        $validation = array(
            'is_valid' => $markers['is_valid'],
            'has_markers' => $markers['has_markers'],
            'has_top' => $markers['has_top'],
            'has_bottom' => $markers['has_bottom'],
            'errors' => array(),
            'warnings' => array()
        );
        
        // Check for errors
        if ($markers['has_top'] && $markers['has_bottom'] && !$markers['is_valid']) {
            $validation['errors'][] = 'Bottom marker appears before top marker';
        }
        
        // Check for duplicate markers
        $top_count = substr_count($content, self::TOP_MARKER);
        $bottom_count = substr_count($content, self::BOTTOM_MARKER);
        
        if ($top_count > 1) {
            $validation['errors'][] = 'Multiple top markers found';
        }
        
        if ($bottom_count > 1) {
            $validation['errors'][] = 'Multiple bottom markers found';
        }
        
        // Add warnings
        if (!$markers['has_markers']) {
            $validation['warnings'][] = 'No segmentation markers found';
        }
        
        if ($markers['has_top'] && !$markers['has_bottom']) {
            $validation['warnings'][] = 'Only top marker found - middle and bottom sections will be empty';
        }
        
        if (!$markers['has_top'] && $markers['has_bottom']) {
            $validation['warnings'][] = 'Only bottom marker found - middle section will be empty';
        }
        
        return $validation;
    }
    
    /**
     * Process segmented content with optional section filtering
     * 
     * @param string $content Content to process
     * @param array $options Processing options
     * @return array Processed content sections
     */
    public function process_segmented_content($content, $options = array()) {
        $defaults = array(
            'section' => self::SECTION_ALL,
            'clean_markers' => true,
            'validate' => true,
            'include_metadata' => false
        );
        
        $options = array_merge($defaults, $options);
        
        $result = array(
            'success' => true,
            'content' => '',
            'section' => $options['section'],
            'has_segmentation' => false
        );
        
        // Validate if requested
        if ($options['validate']) {
            $validation = $this->validate_segmentation_markers($content);
            if (!empty($validation['errors'])) {
                $result['success'] = false;
                $result['errors'] = $validation['errors'];
                return $result;
            }
            
            $result['has_segmentation'] = $validation['has_markers'];
            
            if ($options['include_metadata']) {
                $result['validation'] = $validation;
            }
        }
        
        // Extract the requested section
        $extracted_content = $this->extract_content_section($content, $options['section']);
        
        // Clean markers if requested
        if ($options['clean_markers']) {
            $extracted_content = $this->clean_content_markers($extracted_content);
        }
        
        $result['content'] = $extracted_content;
        
        // Add metadata if requested
        if ($options['include_metadata']) {
            $markers = $this->find_marker_positions($content);
            $result['metadata'] = array(
                'original_length' => strlen($content),
                'extracted_length' => strlen($extracted_content),
                'markers_found' => $markers,
                'sections_available' => $this->get_available_sections($content)
            );
        }
        
        return $result;
    }
    
    /**
     * Get list of available sections in content
     * 
     * @param string $content Content to analyze
     * @return array Available sections
     */
    private function get_available_sections($content) {
        $markers = $this->find_marker_positions($content);
        $sections = array();
        
        // TOP section is always available if there's content
        if (!empty(trim($content))) {
            $sections[] = self::SECTION_TOP;
        }
        
        // MIDDLE section available if both markers exist
        if ($markers['has_top'] && $markers['has_bottom'] && $markers['is_valid']) {
            $sections[] = self::SECTION_MIDDLE;
        }
        
        // BOTTOM section available if bottom marker exists
        if ($markers['has_bottom']) {
            $sections[] = self::SECTION_BOTTOM;
        }
        
        return $sections;
    }
    
    /**
     * Get section statistics for content
     * 
     * @param string $content Content to analyze
     * @return array Section statistics
     */
    public function get_section_statistics($content) {
        $stats = array(
            'total_length' => strlen($content),
            'has_segmentation' => false,
            'sections' => array()
        );
        
        $markers = $this->find_marker_positions($content);
        $stats['has_segmentation'] = $markers['has_markers'];
        
        // Get statistics for each section
        foreach (array(self::SECTION_TOP, self::SECTION_MIDDLE, self::SECTION_BOTTOM) as $section) {
            $section_content = $this->extract_content_section($content, $section);
            $stats['sections'][$section] = array(
                'length' => strlen($section_content),
                'word_count' => str_word_count(strip_tags($section_content)),
                'available' => !empty(trim($section_content))
            );
        }
        
        return $stats;
    }
}