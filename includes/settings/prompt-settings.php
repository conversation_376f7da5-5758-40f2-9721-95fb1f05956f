<?php
// File: includes/settings/prompt-settings.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_Prompt_Settings {
    public function init() {
        add_action('admin_menu', array($this, 'add_settings_page'));
        add_action('admin_init', array($this, 'register_settings'));
        add_action('admin_head', array($this, 'remove_admin_notices'));
        add_action('wp_ajax_slmm_execute_gpt_prompt', array($this, 'ajax_execute_gpt_prompt'));
        add_action('wp_ajax_refresh_prompt_models', array($this, 'handle_refresh_prompt_models'));
        add_action('wp_ajax_slmm_refresh_prompt_models', array($this, 'handle_refresh_prompt_models'));
        add_action('wp_ajax_slmm_get_prompt_provider_models', array($this, 'handle_get_prompt_provider_models'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'localize_prompt_data'));
        add_action('media_buttons', array($this, 'add_gpt_prompt_dropdown'));
    }

    public function add_settings_page() {
        global $submenu;

        // Add the page without adding a visible menu item
        $hookname = add_submenu_page(
            null,                   // Don't add to any menu
            'SLMM GPT Prompts',     // Page title
            'SLMM GPT Prompts',     // Menu title (unused)
            'manage_options',       // Capability
            'slmm-gpt-prompts',     // Menu slug
            array($this, 'render_settings_page') // Callback function
        );

        // Remove from options submenu if it was added there
        if (isset($submenu['options-general.php'])) {
            foreach ($submenu['options-general.php'] as $key => $item) {
                if ($item[2] === 'slmm-gpt-prompts') {
                    unset($submenu['options-general.php'][$key]);
                    break;
                }
            }
        }

        // Ensure scripts are enqueued for this page
        add_action("admin_print_scripts-{$hookname}", array($this, 'enqueue_scripts'));
    }

    public function register_settings() {
        register_setting('slmm_gpt_prompt_settings', 'slmm_gpt_prompts');
    }

    public function remove_admin_notices() {
        $screen = get_current_screen();
        if ($screen && $screen->id === 'admin_page_slmm-gpt-prompts') {  // Change this line
            remove_all_actions('admin_notices');
            remove_all_actions('all_admin_notices');
        }
    }

    public function enqueue_scripts($hook) {
        // For settings page - make sure we include scripts for the admin page
        if (strpos($hook, 'slmm-gpt-prompts') !== false) {
            wp_enqueue_script('jquery-ui-sortable');
            wp_enqueue_script('slmm-prompt-settings', plugin_dir_url(__FILE__) . '../../assets/js/slmm-prompt-settings.js', array('jquery', 'jquery-ui-sortable'), '1.0', true);
            wp_enqueue_style('slmm-prompt-settings', plugin_dir_url(__FILE__) . '../../assets/css/slmm-prompt-settings.css', array(), '1.0');
            wp_enqueue_style('dashicons');
            
            // Localize AJAX URL for the refresh functionality
            wp_localize_script('slmm-prompt-settings', 'slmmPromptSettings', array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'refreshNonce' => wp_create_nonce('refresh_prompt_models')
            ));
        }

        // For post edit screens, enqueue the GPT prompt script
        if (in_array($hook, array('post.php', 'post-new.php'))) {
            wp_enqueue_script('jquery');
            wp_enqueue_script('slmm-gpt-prompt', plugin_dir_url(__FILE__) . '../../assets/js/slmm-gpt-prompt.js', array('jquery'), '1.0', true);
            
            // Add tooltip CSS inline since it's small
            $tooltip_css = '
            <style>
            .slmm-tooltip {
                position: relative !important;
                display: inline-block !important;
                cursor: help !important;
            }
            .slmm-tooltip .slmm-tooltip-text {
                visibility: hidden !important;
                width: 320px !important;
                background-color: #333 !important;
                color: #fff !important;
                text-align: left !important;
                border-radius: 6px !important;
                padding: 8px 12px !important;
                position: absolute !important;
                z-index: 999999 !important;
                top: 28px !important;
                left: 0 !important;
                opacity: 0 !important;
                transition: opacity 0.3s !important;
                font-size: 13px !important;
                line-height: 1.5 !important;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
                pointer-events: none !important;
            }
            .slmm-tooltip:hover .slmm-tooltip-text {
                visibility: visible !important;
                opacity: 1 !important;
            }
            /* Arrow */
            .slmm-tooltip .slmm-tooltip-text::after {
                content: "" !important;
                position: absolute !important;
                top: -5px !important;
                left: 20px !important;
                margin-left: -5px !important;
                border-width: 5px !important;
                border-style: solid !important;
                border-color: transparent transparent #333 transparent !important;
            }
            </style>';
            echo $tooltip_css;
        }
    }

    public function localize_prompt_data($hook) {
        // Only on post edit screens
        if (!in_array($hook, array('post.php', 'post-new.php'))) {
            return;
        }

        $prompts = get_option('slmm_gpt_prompts', array());
        if (empty($prompts)) {
            return;
        }

        // Ensure jQuery is loaded
        wp_enqueue_script('jquery');
        
        // Register and enqueue the prompt execution script
        wp_register_script('slmm-prompt-execution', plugin_dir_url(__FILE__) . '../../assets/js/slmm-prompt-execution.js', array('jquery'), '1.0', true);
        wp_enqueue_script('slmm-prompt-execution');
        
        // Localize the prompts data globally for all scripts including TinyMCE
        wp_localize_script('jquery', 'slmmGptPromptData', array(
            'prompts' => $prompts,
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
        ));
    }

    public function render_settings_page() {
        if (!current_user_can('manage_options')) {
            return;
        }

        $prompts = get_option('slmm_gpt_prompts', array());

        ?>
        <div class="wrap slmm-prompts-dark-theme">
            <h1>SLMM GPT Prompts</h1>
            <form action="options.php" method="post">
                <?php
                settings_fields('slmm_gpt_prompt_settings');
                ?>
                <div id="slmm-gpt-prompts-container">
                    <?php
                    if (!empty($prompts)) {
                        foreach ($prompts as $index => $prompt) {
                            $this->render_prompt_fields($index, $prompt);
                        }
                    }
                    ?>
                </div>
                <button type="button" id="slmm-add-prompt" class="button button-secondary">Add New Prompt</button>
                <?php submit_button('Save Changes'); ?>
            </form>
        </div>
        <?php
    }

    private function render_prompt_fields($index, $prompt = null) {
        // Include OpenRouter integration
        require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
        $openrouter = new SLMM_OpenRouter_Integration();
        
        // Get current provider (default to OpenAI for backwards compatibility)
        $current_provider = $prompt['provider'] ?? 'openai';
        
        // Get models based on provider
        if ($current_provider === 'openrouter' && $openrouter->is_configured()) {
            $models = $openrouter->get_models();
            $default_model = 'google/gemini-2.0-flash-exp:free';
        } else {
            $models = $this->get_openai_models();
            $default_model = 'gpt-4o';
        }
        
        $current_model = $prompt['model'] ?? $default_model;
        ?>
        <div class="slmm-gpt-prompt" data-index="<?php echo $index; ?>">
            <div class="slmm-gpt-prompt-header">
                <span class="slmm-gpt-prompt-handle dashicons dashicons-menu"></span>
                <div class="slmm-gpt-prompt-arrows">
                    <span class="dashicons dashicons-arrow-up-alt2 slmm-move-prompt" data-direction="up"></span>
                    <span class="dashicons dashicons-arrow-down-alt2 slmm-move-prompt" data-direction="down"></span>
                </div>
                <input type="text" name="slmm_gpt_prompts[<?php echo $index; ?>][title]" value="<?php echo esc_attr($prompt['title'] ?? ''); ?>" placeholder="GPT Menu Title" required class="slmm-gpt-prompt-title">
                
                <div class="slmm-model-selector">
                    <!-- Provider Selection -->
                    <select name="slmm_gpt_prompts[<?php echo $index; ?>][provider]" class="slmm-prompt-provider" data-index="<?php echo $index; ?>">
                        <option value="openai" <?php selected($current_provider, 'openai'); ?>>OpenAI</option>
                        <?php if ($openrouter->is_configured()) : ?>
                            <option value="openrouter" <?php selected($current_provider, 'openrouter'); ?>>OpenRouter</option>
                        <?php endif; ?>
                    </select>
                    
                    <?php if (!$openrouter->is_configured()) : ?>
                        <div style="font-size: 11px; color: #666; margin-top: 3px;">
                            <em>Note: Enter OpenRouter API key in main settings to enable OpenRouter models.</em>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Model Selection -->
                    <select name="slmm_gpt_prompts[<?php echo $index; ?>][model]" class="slmm-gpt-prompt-model" data-index="<?php echo $index; ?>">
                        <?php foreach ($models as $model_id => $model_name) : ?>
                            <option value="<?php echo esc_attr($model_id); ?>" <?php selected($current_model, $model_id); ?>>
                                <?php echo esc_html($model_name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <button type="button" class="button button-small slmm-refresh-prompt-models" data-index="<?php echo $index; ?>" title="Refresh Models">↻</button>
                </div>
                
                <button type="button" class="button button-secondary slmm-remove-prompt">Remove Prompt</button>
            </div>
            <textarea name="slmm_gpt_prompts[<?php echo $index; ?>][prompt]" placeholder="Enter your GPT prompt here. Use {INSERT} as a placeholder for the selected text." required class="slmm-gpt-prompt-content"><?php echo esc_textarea($prompt['prompt'] ?? ''); ?></textarea>
            <div class="slmm-gpt-prompt-footer">
                <label>Temperature: <input type="number" step="0.1" min="0" max="1" name="slmm_gpt_prompts[<?php echo $index; ?>][temperature]" value="<?php echo esc_attr($prompt['temperature'] ?? '0.7'); ?>" class="slmm-gpt-prompt-temperature"></label>
                <label>Max Tokens: <input type="number" step="1" min="1" max="4096" name="slmm_gpt_prompts[<?php echo $index; ?>][max_tokens]" value="<?php echo esc_attr($prompt['max_tokens'] ?? '1000'); ?>" class="slmm-gpt-prompt-max-tokens"></label>
            </div>
        </div>
        
        <?php if ($index === 0) : // Only add the script once ?>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Handle provider selection changes
            document.addEventListener('change', function(e) {
                if (e.target.classList.contains('slmm-prompt-provider')) {
                    const index = e.target.dataset.index;
                    const provider = e.target.value;
                    const modelSelect = document.querySelector(`select[name="slmm_gpt_prompts[${index}][model]"]`);
                    
                    if (modelSelect) {
                        // Show loading state
                        modelSelect.disabled = true;
                        modelSelect.innerHTML = '<option>Loading models...</option>';
                        
                        // Fetch models for the new provider
                        const ajaxUrl = typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>';
                        
                        fetch(ajaxUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `action=slmm_get_prompt_provider_models&provider=${provider}&nonce=<?php echo wp_create_nonce('slmm_prompt_provider_models'); ?>`
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update model dropdown
                                modelSelect.innerHTML = '';
                                Object.keys(data.data.models).forEach(modelId => {
                                    const option = document.createElement('option');
                                    option.value = modelId;
                                    option.textContent = data.data.models[modelId];
                                    modelSelect.appendChild(option);
                                });
                                
                                // Select first model as default
                                if (Object.keys(data.data.models).length > 0) {
                                    modelSelect.value = Object.keys(data.data.models)[0];
                                }
                            } else {
                                alert('Failed to load models for ' + provider + '. Please check your API key.');
                                modelSelect.innerHTML = '<option value="">No models available</option>';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            alert('Failed to load models. Please try again.');
                            modelSelect.innerHTML = '<option value="">Error loading models</option>';
                        })
                        .finally(() => {
                            modelSelect.disabled = false;
                        });
                    }
                }
            });
            
            // Handle refresh button clicks
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('slmm-refresh-prompt-models')) {
                    e.preventDefault();
                    const button = e.target;
                    const index = button.dataset.index;
                    const providerSelect = document.querySelector(`select[name="slmm_gpt_prompts[${index}][provider]"]`);
                    const provider = providerSelect ? providerSelect.value : 'openai';
                    const originalText = button.textContent;
                    
                    button.disabled = true;
                    button.textContent = '⟳';
                    button.style.animation = 'spin 1s linear infinite';
                    
                    // Use the localized ajaxurl from WordPress
                    const ajaxUrl = typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>';
                    
                    fetch(ajaxUrl, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=slmm_refresh_prompt_models&provider=${provider}&nonce=<?php echo wp_create_nonce('slmm_refresh_prompt_models'); ?>`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            location.reload();
                        } else {
                            alert('Failed to refresh models: ' + (data.data || 'Unknown error'));
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('Failed to refresh models. Please try again.');
                    })
                    .finally(() => {
                        button.disabled = false;
                        button.textContent = originalText;
                        button.style.animation = '';
                    });
                }
            });
        });
        
        // Add CSS for the spinner animation and improved styling
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .slmm-model-selector {
                display: inline-flex;
                align-items: center;
                gap: 5px;
            }
            .slmm-prompt-provider {
                min-width: 100px;
                font-size: 12px;
            }
            .slmm-gpt-prompt-model {
                min-width: 200px;
                font-size: 12px;
            }
            .slmm-refresh-prompt-models {
                font-size: 14px !important;
                line-height: 1 !important;
                padding: 2px 6px !important;
                height: auto !important;
                min-height: 0 !important;
            }
        `;
        document.head.appendChild(style);
        </script>
        <?php endif; ?>
        <?php
    }

    public function add_gpt_prompt_dropdown() {
        $prompts = get_option('slmm_gpt_prompts', array());
        if (empty($prompts)) {
            return;
        }
    
        // Enqueue prompt execution script
        wp_enqueue_script('slmm-prompt-execution');
        
        // Generate unique ID for this instance to avoid conflicts with ACF fields
        static $instance_counter = 0;
        $instance_counter++;
        $unique_id = $instance_counter;
        
        // No need to localize here as it's already done in the localize_prompt_data method
        ?>
        <div class="slmm-gpt-prompt-container" data-instance="<?php echo $unique_id; ?>" style="display:inline-flex;align-items:center;position:relative;gap:8px;">
            <span class="slmm-tooltip">
                <span style="font-size:20px;color:#222;font-weight:bold;">&#9432;</span>
                <span class="slmm-tooltip-text">
                    <strong>How to use:</strong><br>
                    1. Highlight text in the editor.<br>
                    2. Trigger a prompt instantly with <strong>Control + Command + [1-9]</strong> (Mac) or <strong>Control + Alt + [1-9]</strong> (Windows).<br>
                    3. Clicking the Execute button is optional; keyboard shortcuts are faster!
                </span>
            </span>
            <select id="slmm-gpt-prompt-dropdown-<?php echo $unique_id; ?>" class="slmm-gpt-prompt-dropdown" style="min-height:40px;">
                <option value="">Select GPT Prompt</option>
                <?php foreach (
                    $prompts as $index => $prompt) : ?>
                    <option value="<?php echo $index; ?>"><?php echo esc_html($prompt['title']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <button type="button" id="slmm-execute-gpt-prompt-<?php echo $unique_id; ?>" class="button slmm-execute-gpt-prompt" data-instance="<?php echo $unique_id; ?>">Execute GPT Prompt</button>
        <?php
    }

    public function handle_refresh_prompt_models() {
        // Handle both old and new nonce names for backward compatibility
        if (isset($_POST['nonce'])) {
            $nonce = sanitize_text_field($_POST['nonce']);
            if (!wp_verify_nonce($nonce, 'refresh_prompt_models') && !wp_verify_nonce($nonce, 'slmm_refresh_prompt_models')) {
                wp_send_json_error('Invalid nonce');
            }
        } else {
            wp_send_json_error('Missing nonce');
        }
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        
        if ($provider === 'openrouter') {
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if ($openrouter->is_configured()) {
                // Clear cache first
                $openrouter->clear_model_cache();
            }
        } else {
            // Clear OpenAI cache
            delete_transient('slmm_openai_models');
        }
        
        wp_send_json_success('Models refreshed successfully');
    }

    public function ajax_execute_gpt_prompt() {
        check_ajax_referer('slmm_execute_gpt_prompt', 'nonce');
    
        $prompt_index = isset($_POST['prompt_index']) ? intval($_POST['prompt_index']) : -1;
        $selected_text = isset($_POST['selected_text']) ? sanitize_textarea_field($_POST['selected_text']) : '';
    
        $prompts = get_option('slmm_gpt_prompts', array());
        if (!isset($prompts[$prompt_index])) {
            wp_send_json_error('Invalid prompt index');
        }
    
        $prompt = $prompts[$prompt_index];
        $provider = $prompt['provider'] ?? 'openai';
        $options = get_option('chatgpt_generator_options', array());
        
        $prompt_text = str_replace('{INSERT}', $selected_text, $prompt['prompt']);

        if ($provider === 'openrouter') {
            // Use OpenRouter
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if (!$openrouter->is_configured()) {
                wp_send_json_error('OpenRouter API key is not configured');
            }
            
            $result = $openrouter->generate_content(
                $prompt_text,
                $prompt['model'],
                intval($prompt['max_tokens']),
                floatval($prompt['temperature'])
            );
            
            if (is_wp_error($result)) {
                wp_send_json_error('Error calling OpenRouter API: ' . $result->get_error_message());
            }
            
            wp_send_json_success($result);
        } else {
            // Use OpenAI
            $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
            
            if (empty($api_key)) {
                wp_send_json_error('OpenAI API key is not set');
            }

            $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json',
                ),
                'body' => json_encode(array(
                    'model' => $prompt['model'],
                    'messages' => array(
                        array('role' => 'user', 'content' => $prompt_text)
                    ),
                    'max_tokens' => intval($prompt['max_tokens']),
                    'temperature' => floatval($prompt['temperature']),
                )),
                'timeout' => 60,
            ));

            if (is_wp_error($response)) {
                wp_send_json_error('Error calling OpenAI API: ' . $response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            if (isset($body['choices'][0]['message']['content'])) {
                wp_send_json_success($body['choices'][0]['message']['content']);
            } else {
                wp_send_json_error('Unexpected response from OpenAI API');
            }
        }
    }

    /**
     * Fetch available OpenAI models from the API (same as general settings)
     */
    private function get_openai_models() {
        $options = get_option('chatgpt_generator_options', array());
        $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
        
        if (empty($api_key)) {
            return $this->get_fallback_models();
        }

        // Check if we have cached models that are still valid (cache for 1 hour)
        $cached_models = get_transient('slmm_openai_models');
        if ($cached_models !== false) {
            return $cached_models;
        }

        $url = 'https://api.openai.com/v1/models';
        $headers = array(
            'Authorization' => 'Bearer ' . $api_key,
            'Content-Type' => 'application/json'
        );

        $response = wp_remote_get($url, array(
            'headers' => $headers,
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            return $this->get_fallback_models();
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            return $this->get_fallback_models();
        }

        $models = array();
        foreach ($data['data'] as $model) {
            if (isset($model['id'])) {
                // Filter for GPT models (you can adjust this filter as needed)
                if (strpos($model['id'], 'gpt') === 0 || 
                    strpos($model['id'], 'o1') === 0 || 
                    strpos($model['id'], 'o3') === 0) {
                    $models[$model['id']] = $this->format_model_name($model['id']);
                }
            }
        }

        // Sort models by name
        asort($models);

        // Cache the results for 1 hour
        set_transient('slmm_openai_models', $models, HOUR_IN_SECONDS);

        return $models;
    }

    /**
     * Get fallback models when API call fails
     */
    private function get_fallback_models() {
        return array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4o-mini' => 'GPT-4o Mini',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4' => 'GPT-4',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'o1-preview' => 'o1 Preview',
            'o1-mini' => 'o1 Mini',
            'o3-mini' => 'o3 Mini'
        );
    }

    /**
     * Format model name for display
     */
    private function format_model_name($model_id) {
        $formatted_names = array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4o-mini' => 'GPT-4o Mini',
            'gpt-4o-2024-08-06' => 'GPT-4o (2024-08-06)',
            'gpt-4o-2024-05-13' => 'GPT-4o (2024-05-13)',
            'gpt-4o-mini-2024-07-18' => 'GPT-4o Mini (2024-07-18)',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4-turbo-2024-04-09' => 'GPT-4 Turbo (2024-04-09)',
            'gpt-4-turbo-preview' => 'GPT-4 Turbo Preview',
            'gpt-4-0125-preview' => 'GPT-4 (0125 Preview)',
            'gpt-4-1106-preview' => 'GPT-4 (1106 Preview)',
            'gpt-4' => 'GPT-4',
            'gpt-4-0613' => 'GPT-4 (0613)',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'gpt-3.5-turbo-0125' => 'GPT-3.5 Turbo (0125)',
            'gpt-3.5-turbo-1106' => 'GPT-3.5 Turbo (1106)',
            'o1-preview' => 'o1 Preview',
            'o1-preview-2024-09-12' => 'o1 Preview (2024-09-12)',
            'o1-mini' => 'o1 Mini',
            'o1-mini-2024-09-12' => 'o1 Mini (2024-09-12)',
            'o3-mini' => 'o3 Mini',
            'o3-mini-2025-01-31' => 'o3 Mini (2025-01-31)'
        );

        if (isset($formatted_names[$model_id])) {
            return $formatted_names[$model_id];
        }

        // Convert kebab-case to Title Case for unknown models
        return ucwords(str_replace('-', ' ', $model_id));
    }

    public function handle_get_prompt_provider_models() {
        check_ajax_referer('slmm_prompt_provider_models', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Permission denied');
        }

        $provider = isset($_POST['provider']) ? sanitize_text_field($_POST['provider']) : 'openai';
        
        $models = array();
        
        if ($provider === 'openrouter') {
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if ($openrouter->is_configured()) {
                $models = $openrouter->get_models();
            } else {
                wp_send_json_error('OpenRouter API key not configured');
            }
        } else {
            $models = $this->get_openai_models();
        }
        
        wp_send_json_success(array(
            'models' => $models,
            'provider' => $provider
        ));
    }
}