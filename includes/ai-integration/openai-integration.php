<?php
// File: includes/ai-integration/openai-integration.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_OpenAI_Integration {
    private $api_key;

    public function __construct() {
        $this->api_key = $this->get_api_key();
    }

    public function initialize() {
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_generate_title', array($this, 'ajax_generate_title'));
        add_action('wp_ajax_generate_description', array($this, 'ajax_generate_description'));
        add_action('wp_ajax_generate_vs_snippet', array($this, 'ajax_generate_vs_snippet'));
    }

    private function get_api_key() {
        $options = get_option('chatgpt_generator_options');
        return isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
    }

    public function enqueue_scripts($hook) {
        if ('post.php' != $hook && 'post-new.php' != $hook) {
            return;
        }

        wp_enqueue_script('jquery');

        $chatgpt_options = get_option('chatgpt_generator_options');
        $localized_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'vs_snippet_prompt' => $chatgpt_options['vs_snippet_prompt'] ?? '',
            'nonce' => wp_create_nonce('chatgpt_generate_content'),
            'vs_nonce' => wp_create_nonce('chatgpt_generate_vs_snippet'),
            'title_prompt' => $chatgpt_options['title_prompt'] ?? '',
            'description_prompt' => $chatgpt_options['description_prompt'] ?? '',
            'business_name' => $chatgpt_options['business_name'] ?? '',
            'phone_number' => $chatgpt_options['phone_number'] ?? '',
            'title_model' => $chatgpt_options['model_for_title'] ?? 'gpt-4o',
            'description_model' => $chatgpt_options['model_for_description'] ?? 'gpt-4o',
        );
    }

    public function generate_content($prompt, $model = 'gpt-4o', $max_tokens = 1000) {
        $url = 'https://api.openai.com/v1/chat/completions';

        $headers = array(
            'Authorization' => 'Bearer ' . $this->api_key,
            'Content-Type' => 'application/json'
        );

        $body = wp_json_encode(array(
            'model' => $model,
            'messages' => array(
                array('role' => 'user', 'content' => $prompt)
            ),
            'max_tokens' => $max_tokens,
            'n' => 1,
            'stop' => null,
            'temperature' => 0.7
        ));

        $response = wp_remote_post($url, array(
            'headers' => $headers,
            'body' => $body,
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['error'])) {
            return new WP_Error('openai_error', $data['error']['message']);
        }

        return isset($data['choices'][0]['message']['content']) ? trim($data['choices'][0]['message']['content']) : '';
    }

    public function ajax_generate_title() {
        check_ajax_referer('chatgpt_generate_content', 'security');

        $original_title = sanitize_text_field($_POST['originalTitle']);
        $chatgpt_options = get_option('chatgpt_generator_options');
        $prompt = $chatgpt_options['title_prompt'] ?? '';
        $prompt = str_replace('[TOPIC]', $original_title, $prompt);
        $model = $chatgpt_options['model_for_title'] ?? 'gpt-4o';

        $generated_title = $this->generate_content($prompt, $model);

        if (is_wp_error($generated_title)) {
            wp_send_json_error($generated_title->get_error_message());
        } else {
            $business_name = $chatgpt_options['business_name'] ?? '';
            $phone_number = $chatgpt_options['phone_number'] ?? '';
            $modified_title = $original_title . ' | ' . $business_name . ' | ' . $phone_number . ' | ' . str_replace($original_title . ' | ', '', $generated_title);
            wp_send_json_success($modified_title);
        }
    }

    public function ajax_generate_description() {
        check_ajax_referer('chatgpt_generate_content', 'security');

        $terms = sanitize_text_field($_POST['terms']);
        $chatgpt_options = get_option('chatgpt_generator_options');
        $prompt = $chatgpt_options['description_prompt'] ?? '';
        $prompt = str_replace('INSERT_TERMS', $terms, $prompt);
        $model = $chatgpt_options['model_for_description'] ?? 'gpt-4o';

        $generated_description = $this->generate_content($prompt, $model);

        if (is_wp_error($generated_description)) {
            wp_send_json_error($generated_description->get_error_message());
        } else {
            wp_send_json_success($generated_description);
        }
    }

    public function ajax_generate_vs_snippet() {
        check_ajax_referer('chatgpt_generate_vs_snippet', 'security');

        $content = wp_kses_post($_POST['content']);
        $chatgpt_options = get_option('chatgpt_generator_options');
        $prompt = $chatgpt_options['vs_snippet_prompt'] ?? 'Write a VS snippet comparing the following:';
        $prompt .= "\n\n" . $content;
        $model = $chatgpt_options['vs_snippet_model'] ?? 'gpt-4o';

        $generated_snippet = $this->generate_content($prompt, $model);

        if (is_wp_error($generated_snippet)) {
            wp_send_json_error($generated_snippet->get_error_message());
        } else {
            wp_send_json_success($generated_snippet);
        }
    }
}