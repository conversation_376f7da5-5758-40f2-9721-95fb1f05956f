<?php
/**
 * SLMM URL Renderer AJAX Handler
 * 
 * Handles server-side URL fetching for the Direct Editor URL Renderer
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage AJAX_Handlers
 * @version 1.0.0
 * @since 4.10.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SLMM URL Renderer AJAX Handler Class
 */
class SLMM_URL_Renderer_Handler {
    
    /**
     * Maximum content size (1MB)
     */
    const MAX_CONTENT_SIZE = 1048576;
    
    /**
     * Request timeout (10 seconds)
     */
    const REQUEST_TIMEOUT = 10;
    
    /**
     * Allowed protocols
     */
    const ALLOWED_PROTOCOLS = ['http', 'https'];
    
    /**
     * Initialize the handler
     */
    public function __construct() {
        add_action('wp_ajax_slmm_fetch_url_content', array($this, 'handle_fetch_url_content'));
        add_action('wp_head', array($this, 'add_iframe_clean_styles'));
    }
    
    /**
     * Handle URL content fetching AJAX request
     */
    public function handle_fetch_url_content() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_direct_editor_nonce')) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'invalid_nonce'
            ));
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'insufficient_permissions'
            ));
            return;
        }
        
        // Get and validate URL
        $url = isset($_POST['url']) ? sanitize_url($_POST['url']) : '';
        if (empty($url)) {
            wp_send_json_error(array(
                'message' => 'URL is required',
                'code' => 'missing_url'
            ));
            return;
        }

        // SIMPLE REGEX SECURITY FILTER - Block dangerous URLs immediately
        $blocked_patterns = [
            '/169\.254\.169\.254/',           // AWS metadata
            '/169\.254\.169\.253/',           // AWS instance metadata
            '/168\.63\.129\.16/',             // Azure metadata
            '/metadata\.google\.internal/',   // GCP metadata
            '/metadata[\/\?]/',               // Generic metadata paths
        ];

        foreach ($blocked_patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                error_log("SLMM Security: Blocked URL pattern match - {$url}");
                wp_send_json_error(array(
                    'message' => 'Unable to access this URL',
                    'code' => 'url_not_accessible'
                ));
                return;
            }
        }
        
        // Validate URL format and protocol
        $validation_result = $this->validate_url($url);
        if (!$validation_result['valid']) {
            wp_send_json_error(array(
                'message' => $validation_result['message'],
                'code' => $validation_result['code']
            ));
            return;
        }
        
        // Fetch URL content
        $fetch_result = $this->fetch_url_content($url);
        if (!$fetch_result['success']) {
            wp_send_json_error(array(
                'message' => $fetch_result['message'],
                'code' => $fetch_result['code'],
                'details' => $fetch_result['details']
            ));
            return;
        }
        
        // Return successful result
        wp_send_json_success(array(
            'content' => $fetch_result['content'],
            'content_type' => $fetch_result['content_type'],
            'final_url' => $fetch_result['final_url'],
            'size' => $fetch_result['size'],
            'can_iframe' => $fetch_result['can_iframe']
        ));
    }
    
    /**
     * Validate URL format and security
     * 
     * @param string $url The URL to validate
     * @return array Validation result
     */
    private function validate_url($url) {
        // Parse URL
        $parsed = parse_url($url);
        if ($parsed === false) {
            return array(
                'valid' => false,
                'message' => 'Invalid URL format',
                'code' => 'invalid_format'
            );
        }
        
        // Check required components
        if (!isset($parsed['scheme']) || !isset($parsed['host'])) {
            return array(
                'valid' => false,
                'message' => 'URL must include protocol and hostname',
                'code' => 'missing_components'
            );
        }
        
        // Check allowed protocols
        if (!in_array(strtolower($parsed['scheme']), self::ALLOWED_PROTOCOLS)) {
            return array(
                'valid' => false,
                'message' => 'Only HTTP and HTTPS protocols are allowed',
                'code' => 'invalid_protocol'
            );
        }
        
        // IMMEDIATE SSRF PROTECTION - Block critical endpoints before any network requests
        $host = trim(strtolower($parsed['host']));

        // Internal logging only (not shown to user)
        error_log("SLMM URL Validation: Checking host '{$host}' for security blocks");

        // COMPREHENSIVE list of blocked metadata endpoints
        $blocked_endpoints = [
            '***************',          // AWS/GCP metadata (CRITICAL)
            '***************',          // AWS instance metadata
            '*************',            // Azure metadata
            'metadata.google.internal',  // GCP metadata hostname
            'metadata'                  // Generic metadata
        ];

        // IMMEDIATE blocking - no network requests made, generic error message
        if (in_array($host, $blocked_endpoints)) {
            // Log the real reason internally
            error_log("SLMM Security Block: Blocked access to metadata endpoint '{$host}'");

            // Return generic error to user (don't reveal security details)
            return array(
                'valid' => false,
                'message' => 'Unable to access this URL',
                'code' => 'url_not_accessible'
            );
        }

        // Additional IP-based check for direct IP access
        if (filter_var($host, FILTER_VALIDATE_IP)) {
            $blocked_ips = ['***************', '***************', '*************'];
            if (in_array($host, $blocked_ips)) {
                // Log the real reason internally
                error_log("SLMM Security Block: Blocked access to metadata IP '{$host}'");

                // Return generic error to user
                return array(
                    'valid' => false,
                    'message' => 'Unable to access this URL',
                    'code' => 'url_not_accessible'
                );
            }
        }
        
        return array('valid' => true);
    }


    /**
     * Fetch URL content using WordPress HTTP API
     * 
     * @param string $url The URL to fetch
     * @return array Fetch result
     */
    private function fetch_url_content($url) {
        $args = array(
            'timeout' => self::REQUEST_TIMEOUT,
            'user-agent' => 'SLMM SEO Bundle URL Renderer/1.0 (WordPress)',
            'headers' => array(
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
            ),
            'sslverify' => true,
            'redirection' => 5
        );
        
        // Make request
        $response = wp_remote_get($url, $args);
        
        // Check for errors
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => 'Failed to fetch URL: ' . $response->get_error_message(),
                'code' => 'fetch_error',
                'details' => $response->get_error_code()
            );
        }
        
        // Get response details
        $response_code = wp_remote_retrieve_response_code($response);
        $content_type = wp_remote_retrieve_header($response, 'content-type');
        $body = wp_remote_retrieve_body($response);
        $final_url = isset($response['http_response']->get_response_object()->url) ? 
                     $response['http_response']->get_response_object()->url : $url;
        
        // Check response code
        if ($response_code >= 400) {
            return array(
                'success' => false,
                'message' => 'Server returned error code: ' . $response_code,
                'code' => 'http_error',
                'details' => $response_code
            );
        }
        
        // Check content size
        $content_size = strlen($body);
        if ($content_size > self::MAX_CONTENT_SIZE) {
            return array(
                'success' => false,
                'message' => 'Content too large (max 1MB allowed)',
                'code' => 'content_too_large',
                'details' => array(
                    'size' => $content_size,
                    'max_size' => self::MAX_CONTENT_SIZE
                )
            );
        }
        
        // Check if content is HTML
        $is_html = strpos(strtolower($content_type), 'text/html') !== false;
        if (!$is_html) {
            return array(
                'success' => false,
                'message' => 'Content is not HTML',
                'code' => 'not_html',
                'details' => $content_type
            );
        }
        
        // Check for X-Frame-Options header
        $x_frame_options = wp_remote_retrieve_header($response, 'x-frame-options');
        $can_iframe = empty($x_frame_options) || 
                      (strtolower($x_frame_options) !== 'deny' && 
                       strtolower($x_frame_options) !== 'sameorigin');
        
        // Sanitize HTML content
        $sanitized_content = $this->sanitize_html_content($body);
        
        return array(
            'success' => true,
            'content' => $sanitized_content,
            'content_type' => $content_type,
            'final_url' => $final_url,
            'size' => $content_size,
            'can_iframe' => $can_iframe
        );
    }
    
    /**
     * Sanitize HTML content for safe display
     * 
     * @param string $html Raw HTML content
     * @return string Sanitized HTML
     */
    private function sanitize_html_content($html) {
        // Basic HTML sanitization to prevent XSS
        // Allow most HTML tags but remove dangerous ones
        $allowed_tags = wp_kses_allowed_html('post');
        
        // Add additional safe tags for better content display
        $additional_tags = array(
            'iframe' => array(
                'src' => array(),
                'width' => array(),
                'height' => array(),
                'frameborder' => array(),
                'allowfullscreen' => array(),
            ),
            'video' => array(
                'src' => array(),
                'width' => array(),
                'height' => array(),
                'controls' => array(),
                'autoplay' => array(),
            ),
            'audio' => array(
                'src' => array(),
                'controls' => array(),
            ),
            'canvas' => array(
                'width' => array(),
                'height' => array(),
            ),
            'svg' => array(
                'width' => array(),
                'height' => array(),
                'viewbox' => array(),
            ),
            'path' => array(
                'd' => array(),
                'fill' => array(),
                'stroke' => array(),
            )
        );
        
        $allowed_tags = array_merge($allowed_tags, $additional_tags);
        
        return wp_kses($html, $allowed_tags);
    }
    
    /**
     * Add clean iframe styles when parameters are present
     */
    public function add_iframe_clean_styles() {
        // Check if clean view parameters are present
        if (isset($_GET['slmm_clean_view']) || isset($_GET['frame_view']) || isset($_GET['minimal_view'])) {
            echo '<style>
                /* Hide WordPress admin bar */
                #wpadminbar,
                .admin-bar #wpadminbar {
                    display: none !important;
                }
                
                /* Hide MA Admin Quick Nav */
                #ma-admin-quick-nav-svg,
                #ma-admin-quick-nav-script,
                #ma-admin-quick-nav-info {
                    display: none !important;
                }
                
                /* Hide other admin elements */
                .logged-in.admin-bar #wpbody {
                    padding-top: 0 !important;
                }
                
                /* Clean body margins */
                body.admin-bar {
                    margin-top: 0 !important;
                    padding-top: 0 !important;
                }
                
                /* Hide analytics and tracking scripts output */
                #analyticswp-js-extra,
                textarea#gmbClickToCopy {
                    display: none !important;
                }
                
                /* Focus on main content */
                #brx-content,
                article.wordpress {
                    margin-top: 0 !important;
                }
                
                /* Hide any floating admin elements */
                .wp-admin,
                .admin-menu,
                .admin-footer {
                    display: none !important;
                }
            </style>';
        }
    }
}

// Initialize the handler only if not already initialized
if (!isset($GLOBALS['slmm_url_renderer_handler'])) {
    $GLOBALS['slmm_url_renderer_handler'] = new SLMM_URL_Renderer_Handler();
}