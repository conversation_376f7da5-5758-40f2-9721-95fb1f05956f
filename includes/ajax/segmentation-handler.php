<?php
/**
 * SLMM Content Segmentation AJAX Handler
 * 
 * Handles AJAX requests for content segmentation operations
 * including marker insertion, content extraction, and validation.
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage AJAX_Handlers
 * @version 1.0.0
 * @since 4.10.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SLMM Content Segmentation AJAX Handler Class
 */
class SLMM_Segmentation_Handler {

    /**
     * Singleton instance
     * @var SLMM_Segmentation_Handler|null
     */
    private static $instance = null;

    /**
     * Hook registration tracking
     * @var bool
     */
    private static $hooks_registered = false;

    /**
     * Instance of the core segmentation class
     */
    private $segmentation;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the AJAX handler (private constructor)
     */
    private function __construct() {
        // CRITICAL FIX: Use singleton instance instead of creating new instance
        $this->segmentation = SLMM_Content_Segmentation::get_instance();

        // CRITICAL FIX: Only register AJAX hooks once
        if (!self::$hooks_registered) {
            add_action('wp_ajax_slmm_insert_segmentation_marker', array($this, 'handle_insert_marker'));
            add_action('wp_ajax_slmm_remove_segmentation_marker', array($this, 'handle_remove_marker'));
            add_action('wp_ajax_slmm_extract_content_section', array($this, 'handle_extract_section'));
            add_action('wp_ajax_slmm_validate_segmentation', array($this, 'handle_validate_segmentation'));
            add_action('wp_ajax_slmm_get_section_statistics', array($this, 'handle_get_statistics'));
            self::$hooks_registered = true;
            error_log('[SLMM Segmentation Handler] AJAX hooks registered once via singleton');
        }
    }
    
    /**
     * Handle marker insertion AJAX request
     */
    public function handle_insert_marker() {
        // Enhanced debugging for nonce verification
        error_log('SLMM Segmentation: Received nonce: ' . ($_POST['nonce'] ?? 'none'));
        error_log('SLMM Segmentation: Checking nonces for actions: slmm_direct_edit_nonce, slmm_execute_gpt_prompt, slmm_interlinking_nonce');
        
        // Verify nonce (accept Direct Editor, GPT prompt, and interlinking nonces)
        $nonce_valid = wp_verify_nonce($_POST['nonce'], 'slmm_direct_edit_nonce') || 
                       wp_verify_nonce($_POST['nonce'], 'slmm_execute_gpt_prompt') ||
                       wp_verify_nonce($_POST['nonce'], 'slmm_interlinking_nonce');
        
        error_log('SLMM Segmentation: Nonce validation result: ' . ($nonce_valid ? 'VALID' : 'INVALID'));
        
        if (!$nonce_valid) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'invalid_nonce',
                'debug' => array(
                    'received_nonce' => $_POST['nonce'] ?? 'none',
                    'expected_actions' => ['slmm_direct_edit_nonce', 'slmm_execute_gpt_prompt', 'slmm_interlinking_nonce']
                )
            ));
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'insufficient_permissions'
            ));
            return;
        }
        
        // Get and validate parameters
        $content = isset($_POST['content']) ? wp_unslash($_POST['content']) : '';
        $position = isset($_POST['position']) ? intval($_POST['position']) : null;
        
        if (empty($content)) {
            wp_send_json_error(array(
                'message' => 'Content is required',
                'code' => 'missing_content'
            ));
            return;
        }
        
        // Enhanced debugging for content corruption issues
        error_log('SLMM Segmentation: Sequential marker insert request - Position: ' . ($position ?? 'auto') . ', Content length: ' . strlen($content));
        error_log('SLMM Segmentation: Original content first 500 chars: ' . substr($content, 0, 500));
        error_log('SLMM Segmentation: Original content last 200 chars: ' . substr($content, -200));
        
        // Check for existing HTML encoding issues
        if (strpos($content, '&lt;') !== false || strpos($content, '&gt;') !== false) {
            error_log('SLMM Segmentation: WARNING - Content already contains HTML entities');
            // Decode HTML entities before processing
            $content = html_entity_decode($content, ENT_QUOTES, 'UTF-8');
            error_log('SLMM Segmentation: Content after decoding, length: ' . strlen($content));
        }
        
        // Use the new sequential marker insertion
        $new_content = $this->segmentation->insert_sequential_markers($content, $position);
        
        error_log('SLMM Segmentation: Content changed: ' . ($new_content !== $content ? 'YES' : 'NO'));
        
        if ($new_content === $content) {
            // Get the insertion plan to understand why it failed - use find_marker_positions for 6-marker system
            $marker_positions = $this->segmentation->find_marker_positions($content);
            $insertion_plan = $this->segmentation->determine_marker_insertion($marker_positions);
            
            wp_send_json_error(array(
                'message' => $insertion_plan['message'] ?: 'Failed to insert markers',
                'code' => 'insertion_failed',
                'debug' => array(
                    'position' => $position,
                    'content_length' => strlen($content),
                    'insertion_plan' => $insertion_plan
                )
            ));
            return;
        }
        
        // Get updated marker analysis for response - use find_marker_positions for 6-marker system
        $updated_markers = $this->segmentation->find_marker_positions($new_content);
        
        error_log('SLMM Segmentation: Insert completed successfully - Content length: ' . strlen($new_content) . ', Markers found: ' . ($updated_markers['marker_count'] ?? 0));
        error_log('SLMM Segmentation: Final content first 500 chars: ' . substr($new_content, 0, 500));
        error_log('SLMM Segmentation: Final content last 200 chars: ' . substr($new_content, -200));
        
        // Return success with new content (ensure it's not double-encoded)
        wp_send_json_success(array(
            'content' => $new_content,
            'message' => 'Segmentation markers inserted successfully',
            'marker_info' => array(
                'total_markers' => $updated_markers['marker_count'] ?? 0,
                'complete_sections' => array(
                    'top' => $updated_markers['has_complete_top'] ?? false,
                    'middle' => $updated_markers['has_complete_middle'] ?? false,
                    'bottom' => $updated_markers['has_complete_bottom'] ?? false
                )
            ),
            'debug_info' => array(
                'insertion_plan' => $insertion_plan,
                'original_content_length' => strlen($content),
                'new_content_length' => strlen($new_content),
                'position_used' => $position
            )
        ));
    }
    
    /**
     * Handle marker removal AJAX request
     */
    public function handle_remove_marker() {
        // Verify nonce (accept Direct Editor, GPT prompt, and interlinking nonces)
        $nonce_valid = wp_verify_nonce($_POST['nonce'], 'slmm_direct_edit_nonce') || 
                       wp_verify_nonce($_POST['nonce'], 'slmm_execute_gpt_prompt') ||
                       wp_verify_nonce($_POST['nonce'], 'slmm_interlinking_nonce');
        
        if (!$nonce_valid) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'invalid_nonce'
            ));
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'insufficient_permissions'
            ));
            return;
        }
        
        // Get and validate parameters
        $content = isset($_POST['content']) ? wp_unslash($_POST['content']) : '';
        $marker_type = isset($_POST['marker_type']) ? sanitize_text_field($_POST['marker_type']) : '';
        
        if (empty($content)) {
            wp_send_json_error(array(
                'message' => 'Content is required',
                'code' => 'missing_content'
            ));
            return;
        }
        
        if (!in_array($marker_type, array('top', 'bottom', 'all'))) {
            wp_send_json_error(array(
                'message' => 'Invalid marker type',
                'code' => 'invalid_marker_type'
            ));
            return;
        }
        
        // Remove the specified marker(s)
        if ($marker_type === 'all') {
            $new_content = $this->segmentation->clean_content_markers($content);
        } else {
            // For specific marker types, still clean all for now since we're using 6-marker system
            $new_content = $this->segmentation->clean_content_markers($content);
        }
        
        if ($new_content === $content) {
            wp_send_json_error(array(
                'message' => 'No markers found to remove',
                'code' => 'no_markers_found'
            ));
            return;
        }
        
        // Return success with cleaned content
        wp_send_json_success(array(
            'content' => $new_content,
            'marker_type' => $marker_type,
            'message' => 'Markers removed successfully'
        ));
    }
    
    /**
     * Handle content section extraction AJAX request
     */
    public function handle_extract_section() {
        // Verify nonce (accept Direct Editor, GPT prompt, and interlinking nonces)
        $nonce_valid = wp_verify_nonce($_POST['nonce'], 'slmm_direct_edit_nonce') || 
                       wp_verify_nonce($_POST['nonce'], 'slmm_execute_gpt_prompt') ||
                       wp_verify_nonce($_POST['nonce'], 'slmm_interlinking_nonce');
        
        if (!$nonce_valid) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'invalid_nonce'
            ));
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'insufficient_permissions'
            ));
            return;
        }
        
        // Get and validate parameters
        $content = isset($_POST['content']) ? wp_unslash($_POST['content']) : '';
        $section = isset($_POST['section']) ? sanitize_text_field($_POST['section']) : SLMM_Content_Segmentation::SECTION_ALL;
        $clean_markers = isset($_POST['clean_markers']) ? (bool) $_POST['clean_markers'] : true;
        $include_metadata = isset($_POST['include_metadata']) ? (bool) $_POST['include_metadata'] : false;
        
        if (empty($content)) {
            wp_send_json_error(array(
                'message' => 'Content is required',
                'code' => 'missing_content'
            ));
            return;
        }
        
        // Process the segmented content
        $options = array(
            'section' => $section,
            'clean_markers' => $clean_markers,
            'validate' => true,
            'include_metadata' => $include_metadata
        );
        
        $result = $this->segmentation->process_segmented_content($content, $options);
        
        if (!$result['success']) {
            wp_send_json_error(array(
                'message' => 'Content processing failed',
                'code' => 'processing_failed',
                'errors' => isset($result['errors']) ? $result['errors'] : array()
            ));
            return;
        }
        
        // Return the extracted content
        wp_send_json_success($result);
    }
    
    /**
     * Handle segmentation validation AJAX request
     */
    public function handle_validate_segmentation() {
        // Verify nonce (accept Direct Editor, GPT prompt, and interlinking nonces)
        $nonce_valid = wp_verify_nonce($_POST['nonce'], 'slmm_direct_edit_nonce') || 
                       wp_verify_nonce($_POST['nonce'], 'slmm_execute_gpt_prompt') ||
                       wp_verify_nonce($_POST['nonce'], 'slmm_interlinking_nonce');
        
        if (!$nonce_valid) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'invalid_nonce'
            ));
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'insufficient_permissions'
            ));
            return;
        }
        
        // Get and validate parameters
        $content = isset($_POST['content']) ? wp_unslash($_POST['content']) : '';
        
        if (empty($content)) {
            wp_send_json_error(array(
                'message' => 'Content is required',
                'code' => 'missing_content'
            ));
            return;
        }
        
        // Validate the segmentation
        $validation = $this->segmentation->validate_segmentation_markers($content);
        
        // Return validation results
        wp_send_json_success($validation);
    }
    
    /**
     * Handle section statistics AJAX request
     */
    public function handle_get_statistics() {
        // Verify nonce (accept Direct Editor, GPT prompt, and interlinking nonces)
        $nonce_valid = wp_verify_nonce($_POST['nonce'], 'slmm_direct_edit_nonce') || 
                       wp_verify_nonce($_POST['nonce'], 'slmm_execute_gpt_prompt') ||
                       wp_verify_nonce($_POST['nonce'], 'slmm_interlinking_nonce');
        
        if (!$nonce_valid) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'invalid_nonce'
            ));
            return;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'insufficient_permissions'
            ));
            return;
        }
        
        // Get and validate parameters
        $content = isset($_POST['content']) ? wp_unslash($_POST['content']) : '';
        
        if (empty($content)) {
            wp_send_json_error(array(
                'message' => 'Content is required',
                'code' => 'missing_content'
            ));
            return;
        }
        
        // Get section statistics
        $statistics = $this->segmentation->get_section_statistics($content);
        
        // Return statistics
        wp_send_json_success($statistics);
    }
}