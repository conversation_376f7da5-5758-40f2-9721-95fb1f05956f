# SEO Overview ↔ D3 Canvas Communication Protocol

## Overview
This document details the communication system between the SEO Overview metabox and the D3 canvas visualization system, focusing on SILO STRUCTURE data (Keyword, Importance and Difficulty) synchronization with full two-way editing capabilities, plus hierarchical NAVIGATION data display with click-to-copy functionality.

## Data Flow Architecture

### Primary Data Storage
**Location**: WordPress `post_meta` table
- `_slmm_target_keyword`: Target keyword string (string, can be empty)
- `_slmm_importance_rating`: Values 1-5 (string)
- `_slmm_difficulty_level`: Values 'easy', 'medium', 'hard', 'very-hard' (string)

**Updated by**: 
- D3 Canvas → AJAX handlers in `interlinking-suite.php`
- Direct Editor → Dashboard controls
- SEO Overview metabox → Clickable interface with two-way sync

**Read by**:
- SEO Overview metabox → Multiple fallback methods with caching
- D3 Canvas → Node dashboard data loading

### Data Synchronization Points

#### 1. D3 Canvas → Database (Write Operations)
**File**: `includes/interlinking/interlinking-suite.php`

```php
// AJAX Handlers
add_action('wp_ajax_slmm_change_importance', array($this, 'ajax_change_importance'));
add_action('wp_ajax_slmm_change_difficulty', array($this, 'ajax_change_difficulty'));
add_action('wp_ajax_slmm_change_keyword', array($this, 'ajax_change_keyword'));

// Storage Implementation
public function ajax_change_importance() {
    // Security checks...
    $updated = update_post_meta($post_id, '_slmm_importance_rating', $new_importance);
    // Success response...
}

public function ajax_change_difficulty() {
    // Security checks...
    $updated = update_post_meta($post_id, '_slmm_difficulty_level', $new_difficulty);
    // Success response...
}
```

#### 2. Database → SEO Overview (Read Operations)
**File**: `src/seo_overview_meta_box.php`

**Method 1: PHP-rendered Hidden Fields (Primary)**
```php
public function render_meta_box($post) {
    $importance_rating = get_post_meta($post->ID, '_slmm_importance_rating', true);
    $difficulty_level = get_post_meta($post->ID, '_slmm_difficulty_level', true);
    
    // Defaults
    if (empty($importance_rating)) $importance_rating = '3';
    if (empty($difficulty_level)) $difficulty_level = 'medium';
    
    // Hidden fields for JS access
    echo '<input type="hidden" name="_slmm_importance_rating" value="' . esc_attr($importance_rating) . '">';
    echo '<input type="hidden" name="_slmm_difficulty_level" value="' . esc_attr($difficulty_level) . '">';
}
```

**Method 2: JavaScript Multi-Layer Access**
```javascript
getSiloStructureStatus: function() {
    // Layer 1: Cache check (30-second TTL)
    var cached = SEOOverviewMetaBox.cache && SEOOverviewMetaBox.cache[cacheKey];
    if (cached && (Date.now() - cached.timestamp < 30000)) {
        return cached_data;
    }
    
    // Layer 2: WordPress Block Editor Data
    if (typeof wp !== 'undefined' && wp.data && wp.data.select) {
        var currentPost = wp.data.select('core/editor').getCurrentPost();
        if (currentPost && currentPost.meta) {
            importance = currentPost.meta._slmm_importance_rating || '3';
            difficulty = currentPost.meta._slmm_difficulty_level || 'medium';
        }
    }
    
    // Layer 3: Hidden Fields (Fallback)
    var importanceMeta = $('input[name="_slmm_importance_rating"]').val();
    var difficultyMeta = $('input[name="_slmm_difficulty_level"]').val();
}
```

## Visual Representation System

### Circle Display Logic
**File**: `src/seo_overview_meta_box.js`

**Importance Circles (1-5)**
```javascript
getImportanceCircles: function(currentImportance) {
    // CRITICAL: Only exact match is active (not bar-style)
    for (var i = 1; i <= 5; i++) {
        var activeClass = (i === currentLevel) ? ' active' : ' inactive';
        // Creates: 12345 with only current level highlighted
    }
}
```

**Difficulty Circles (E/M/H/VH)**
```javascript
getDifficultyCircles: function(currentDifficulty) {
    difficulties.forEach(function(diff) {
        // CRITICAL: Only exact match is active
        var activeClass = (diff.key === currentDifficulty) ? ' active' : ' inactive';
        // Creates: E M H VH with only current level highlighted
    }
}
```

### CSS Styling System
**File**: `src/seo_overview_meta_box.css`

**Color Mapping (matches D3 canvas exactly)**:
```css
/* Importance Colors */
.importance-1.active { background: #eab308; } /* Yellow */
.importance-2.active { background: #ef4444; } /* Red */
.importance-3.active { background: #3b82f6; } /* Blue */
.importance-4.active { background: #6b7280; } /* Gray */
.importance-5.active { background: #1f2937; } /* Dark Gray */

/* Difficulty Colors */
.difficulty-easy.active { background: #10b981; } /* Green */
.difficulty-medium.active { background: #f59e0b; } /* Orange */
.difficulty-hard.active { background: #f97316; } /* Dark Orange */
.difficulty-very-hard.active { background: #ef4444; } /* Red */
```

## Energy Conservation Strategies

### 1. Caching Layer
```javascript
// 30-second client-side cache to prevent repeated meta queries
SEOOverviewMetaBox.cache[cacheKey] = {
    importance: importance,
    difficulty: difficulty,
    timestamp: Date.now()
};
```

### 2. Layered Data Access
1. **Cache** (fastest) → 30-second TTL
2. **WordPress Block Editor** → Native JS API
3. **Hidden Fields** → PHP-rendered, no AJAX
4. **Defaults** → Fallback values

### 3. Minimal DOM Updates
- Only updates when values actually change
- Uses existing SEO Overview refresh cycle
- No additional event listeners or timers

## Integration Points

### With Direct Editor
**File**: `assets/js/slmm-direct-editor.js`

The Direct Editor dashboard controls (lines 543-575) manage the same meta fields:
```javascript
// Direct Editor saves to same meta keys
data: {
    action: 'slmm_change_importance',
    post_id: this.currentPostId,
    new_importance: selectedImportance,
    nonce: slmmInterlinkingData.nonce
}
```

**Synchronization**: Changes in Direct Editor automatically appear in SEO Overview on next refresh cycle (5-second interval).

### With Interlinking Suite
**File**: `includes/interlinking/interlinking-suite.php`

**AJAX Endpoints** (lines 77-80):
```php
add_action('wp_ajax_slmm_change_difficulty', array($this, 'ajax_change_difficulty'));
add_action('wp_ajax_slmm_change_importance', array($this, 'ajax_change_importance'));
```

**Security Implementation**:
- Nonce verification: `slmm_interlinking_nonce`
- Capability checks: `edit_posts` and `edit_pages`
- Input sanitization and validation

## Real-Time Update Mechanism

### SEO Overview Refresh Cycle
**File**: `src/seo_overview_meta_box.js` (lines 256-259)

```javascript
// Automatic refresh every 5 seconds
setInterval(function() {
    SEOOverviewMetaBox.updateMetaBoxContent();
}, 5000);
```

### Cache Invalidation
- **Manual**: User changes in Direct Editor/D3 Canvas
- **Automatic**: 30-second TTL expiration
- **Forced**: Page refresh or navigation

## Error Handling & Fallbacks

### Data Access Failures
1. **WordPress API unavailable** → Use hidden fields
2. **Hidden fields missing** → Use default values
3. **Invalid data format** → Sanitize and default

### Default Values
- **Importance**: `'3'` (middle level)
- **Difficulty**: `'medium'` (middle level)

### Visual Fallbacks
- **Missing data** → Show all circles as inactive
- **Invalid values** → Default to middle levels
- **CSS load failure** → Basic styling from WordPress admin

## Security Considerations

### Data Validation
**Backend** (interlinking-suite.php):
```php
$valid_importances = array('1', '2', '3', '4', '5');
$valid_difficulties = array('easy', 'medium', 'hard', 'very-hard');
```

**Frontend**: Input sanitization before display
```javascript
var currentLevel = parseInt(currentImportance) || 3;
// Ensures only valid integer values
```

### Permission Checks
- WordPress capability system: `edit_posts`, `edit_pages`
- Nonce verification for all AJAX requests
- Post ownership validation

## Performance Optimization

### Database Queries
- **Minimized**: Only one `get_post_meta()` call per field per page load
- **Cached**: 30-second client-side TTL
- **Batched**: Retrieved during initial PHP render

### DOM Operations
- **Minimal**: Only updates when data changes
- **Efficient**: Uses existing refresh cycle
- **Optimized**: CSS classes over inline styles

### Memory Management
- **Cache cleanup**: Automatic 30-second expiration
- **Event cleanup**: No permanent event listeners
- **Scope management**: Contained within SEOOverviewMetaBox object

## Troubleshooting Guide

### Common Issues

**1. Circles not updating after D3 changes**
- Check 5-second refresh cycle
- Verify cache expiration (30 seconds)
- Ensure hidden fields are present

**2. Wrong colors displaying**
- Verify CSS file loaded: `seo_overview_meta_box.css`
- Check color mapping matches interlinking suite
- Validate data format (string values)

**3. Default values showing despite data**
- Check hidden field presence in DOM
- Verify meta field names: `_slmm_importance_rating`, `_slmm_difficulty_level`
- Ensure WordPress Block Editor compatibility

### Debug Commands
```javascript
// Check data availability
console.log('Hidden fields:', $('input[name="_slmm_importance_rating"]').val());
console.log('WP data:', wp.data.select('core/editor').getCurrentPost().meta);
console.log('Cache:', SEOOverviewMetaBox.cache);

// Force refresh
SEOOverviewMetaBox.updateMetaBoxContent();
```

## Future Enhancement Opportunities

### Real-Time Synchronization
- WebSocket connection for instant updates
- Server-sent events for change notifications
- Optimistic UI updates

### Advanced Caching
- Browser localStorage persistence
- Service worker caching
- IndexedDB for offline support

### Enhanced Integration  
- Direct click-to-edit functionality ✅ **IMPLEMENTED**
- Drag-and-drop value changes
- Keyboard shortcuts for quick updates

## Two-Way Synchronization System ✅ **IMPLEMENTED**

### SEO Overview → D3 Canvas Sync
**Implementation**: Clickable interface in SEO Overview metabox with immediate AJAX updates

#### Keyword Editing
```javascript
// Click-to-edit keyword field
$(document).on('click', '.clickable-keyword', function() {
    var currentKeyword = $(this).data('keyword');
    SEOOverviewMetaBox.editKeyword(currentKeyword); // Opens prompt dialog
});

// AJAX update function
updateKeyword: function(newKeyword) {
    $.ajax({
        url: slmmSeoOverview.ajaxurl,
        data: {
            action: 'slmm_change_keyword',
            post_id: postId,
            new_keyword: newKeyword,
            nonce: slmmSeoOverview.interlinkingNonce
        }
        // Clears cache and refreshes display on success
    });
}
```

#### Importance Level Editing  
```javascript
// Clickable importance circles (1-5)
$(document).on('click', '.clickable-importance', function() {
    var importance = $(this).data('importance');
    SEOOverviewMetaBox.updateImportance(importance);
});
```

#### Difficulty Level Editing
```javascript  
// Clickable difficulty circles (E/M/H/VH)
$(document).on('click', '.clickable-difficulty', function() {
    var difficulty = $(this).data('difficulty'); 
    SEOOverviewMetaBox.updateDifficulty(difficulty);
});
```

### Visual Feedback System
**Hover Effects**: Circles scale up (110%) and show shadow on hover
**Click Response**: Immediate AJAX call → Cache clear → UI refresh
**Error Handling**: Console logging and graceful failure

### Cache Management
**Strategy**: Aggressive cache invalidation on updates
- Clear entire cache on any SILO STRUCTURE change
- Immediate UI refresh after successful update
- 30-second TTL maintained for read operations

### Security Implementation
**Nonce**: Uses same `slmm_interlinking_nonce` as D3 Canvas
**Capability Checks**: Inherits from interlinking suite handlers  
**Input Sanitization**: Server-side validation in AJAX handlers

## Navigation System ✅ **IMPLEMENTED**

### Parents & Siblings Display
**Implementation**: AJAX-loaded hierarchical navigation with click-to-copy URLs

#### Data Source
```php
// AJAX Endpoint in seo_overview_meta_box.php
add_action('wp_ajax_get_silo_navigation', array($this, 'get_silo_navigation_ajax'));

// Navigation data retrieval
private function get_silo_navigation_data($post_id, $parent_id) {
    // Parent hierarchy chain (up to 4 levels)
    $ancestor_ids = get_post_ancestors($post_id);
    
    // Siblings with same parent  
    $siblings = get_posts(array(
        'post_parent' => $parent_id,
        'exclude' => array($post_id),
        'posts_per_page' => 10
    ));
}
```

#### Display Implementation
```javascript
// NAVIGATION section in SEO Overview
getNavigationStatus: function() {
    // 5-minute caching for navigation data
    var cached = SEOOverviewMetaBox.navigationCache[cacheKey];
    if (cached && (Date.now() - cached.timestamp < 300000)) {
        return formatNavigationData(cached.data);
    }
    
    // AJAX fetch with loading state
    SEOOverviewMetaBox.fetchNavigationData(postId);
}

formatNavigationData: function(data) {
    // Parents: Title › Title › Title (breadcrumb style)
    // Siblings: Title • Title • Title (bullet separated)
    return parentsHtml + siblingsHtml;
}
```

#### Click-to-Copy System
```javascript
// Modern clipboard API with fallback
copyToClipboard: function(url, element) {
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(url).then(function() {
            showCopyFeedback(element, 'Copied!');
        });
    } else {
        // document.execCommand fallback for older browsers
        fallbackCopyToClipboard(url, element);
    }
}

// Visual feedback system
showCopyFeedback: function(element, message, isError) {
    // Green "Copied!" or red "Copy failed" for 1.5 seconds
    element.addClass(isError ? 'copy-error' : 'copy-success');
    // Restore original state after feedback
}
```

### User Experience Features
- **Space Efficient**: Shows page titles only, not full URLs
- **Smart Truncation**: Long titles ellipsed at 120px max-width
- **Hover Feedback**: Visual elevation and color change
- **Instant Copy**: Click any title to copy full URL to clipboard
- **Visual Confirmation**: Green success/red error feedback
- **Graceful Fallbacks**: "No parent pages" / "No sibling pages" states

### Styling System
```css
.nav-link {
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 3px;
    max-width: 120px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-link:hover {
    background: #e5e7eb;
    transform: translateY(-1px);
}

.nav-link.copy-success {
    background: #10b981 !important;
    color: white !important;
}
```

### Performance Characteristics
- **5-minute Cache**: Prevents repeated navigation queries
- **Async Loading**: Non-blocking AJAX requests
- **Smart Caching**: Separate cache from SILO STRUCTURE data
- **Lazy Loading**: Data fetched only when metabox refreshes

---

## File Summary

**Modified Files**:
- `src/seo_overview_meta_box.php` - Added keyword meta field, navigation AJAX endpoint, silo navigation data retrieval
- `src/seo_overview_meta_box.js` - Added SILO STRUCTURE + NAVIGATION sections, two-way sync, click-to-copy functionality
- `src/seo_overview_meta_box.css` - Added circle styling, keyword styling, navigation links, copy feedback states

**Dependencies**:
- `includes/interlinking/interlinking-suite.php` - AJAX handlers
- `assets/js/slmm-direct-editor.js` - Dashboard controls
- WordPress Block Editor API - Optional data source

**Total Implementation**: ~400 lines of code across 3 files  
**New Features**: 
- **SILO STRUCTURE**: Keyword field with click-to-edit, two-way sync for all fields
- **NAVIGATION**: Parents & siblings display with click-to-copy URLs
- **User Experience**: Visual hover feedback, copy confirmation, optimistic updates
- **Performance**: Smart caching (30s for silo, 5min for navigation), aggressive cache invalidation

**Key Capabilities**:
- ✅ **Two-way sync**: SEO Overview ↔ D3 Canvas for importance/difficulty/keyword
- ✅ **Click-to-copy**: Navigation URLs copied to clipboard with visual feedback
- ✅ **Optimistic UI**: Instant visual updates with server confirmation
- ✅ **Smart caching**: Separate caches for different data types
- ✅ **Graceful fallbacks**: Modern clipboard API with legacy browser support

**Performance Impact**: Minimal (efficient caching, async loading, optimistic updates)
**Compatibility**: WordPress 5.0+, Classic & Block editors, Modern + Legacy browsers
**Security**: Uses existing interlinking nonce system, proper sanitization