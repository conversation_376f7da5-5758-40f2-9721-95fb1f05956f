<?php
// File: includes/interlinking/class-link-popup-handler.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * SLMM Link Popup Handler
 * 
 * Handles AJAX requests for fetching page link data to display in hover popups
 * for internal and external link indicators in the interlinking suite.
 * 
 * @since 4.10.1
 */
class SLMM_Link_Popup_Handler {
    
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Initialize the link popup handler
     */
    private function __construct() {
        $this->setup_hooks();
    }
    
    /**
     * Setup WordPress hooks
     */
    private function setup_hooks() {
        // AJAX handler for fetching page link data
        add_action('wp_ajax_slmm_get_page_links', array($this, 'ajax_get_page_links'));
    }
    
    /**
     * AJAX endpoint to fetch link data for a specific page
     */
    public function ajax_get_page_links() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_interlinking_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check user capability
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        // Get and validate page ID
        $page_id = intval($_POST['page_id']);
        if (!$page_id || !get_post($page_id)) {
            wp_send_json_error('Invalid page ID');
            return;
        }
        
        try {
            // Fetch link data with synchronized logic
            $internal_links = $this->get_internal_links($page_id);
            $external_links = $this->get_external_links($page_id);
            
            // Enhanced debug logging for response preparation
            error_log("[SLMM Link Popup] ==> RESPONSE PREPARATION for page {$page_id}:");
            error_log("[SLMM Link Popup] Internal links found: " . count($internal_links));
            error_log("[SLMM Link Popup] External links found: " . count($external_links));
            
            // Log first few internal links for debugging
            foreach (array_slice($internal_links, 0, 3) as $index => $link) {
                error_log("[SLMM Link Popup] Internal link {$index}: URL={$link['url']}, target_post_id={$link['target_post_id']}, target_page={$link['target_page']}");
            }
            
            // Log first few external links for debugging  
            foreach (array_slice($external_links, 0, 3) as $index => $link) {
                error_log("[SLMM Link Popup] External link {$index}: URL={$link['url']}, anchor={$link['anchor']}, domain={$link['domain']}");
            }
            
            // CRITICAL: Validate data structure consistency for JavaScript expectations
            $response = array(
                'internal' => array(
                    'count' => count($internal_links),
                    'links' => $internal_links // REMOVED LIMIT - Show ALL internal links
                ),
                'external' => array(
                    'count' => count($external_links), 
                    'links' => $external_links // REMOVED LIMIT - Show ALL external links
                ),
                'page_title' => get_the_title($page_id),
                'debug_info' => array(
                    'page_id' => $page_id,
                    'validation_performed' => class_exists('SLMM_Direct_Editor_Content_Validator'),
                    'synchronization_version' => '1.0'
                )
            );
            
            // Validate response structure before sending
            if (!is_array($response['internal']) || !isset($response['internal']['count'], $response['internal']['links']) ||
                !is_array($response['external']) || !isset($response['external']['count'], $response['external']['links'])) {
                error_log("[SLMM Link Popup] ==> RESPONSE STRUCTURE VALIDATION FAILED");
                wp_send_json_error('Invalid response structure');
                return;
            }
            
            error_log("[SLMM Link Popup] ==> RESPONSE STRUCTURE VALIDATION PASSED");
            error_log("[SLMM Link Popup] ==> FINAL RESPONSE SUMMARY - Internal: {$response['internal']['count']}, External: {$response['external']['count']}");
            
            wp_send_json_success($response);
            
        } catch (Exception $e) {
            error_log('[SLMM Link Popup] Error fetching links for page ' . $page_id . ': ' . $e->getMessage());
            wp_send_json_error('Failed to fetch link data');
        }
    }
    
    /**
     * Extract internal links from page content
     * SYNCHRONIZED with tree generation logic for consistent internal link detection
     */
    private function get_internal_links($page_id) {
        $post = get_post($page_id);
        if (!$post) {
            error_log("[SLMM Link Popup] ==> INTERNAL LINKS - Post not found for ID: {$page_id}");
            return array();
        }
        
        error_log("[SLMM Link Popup] ==> INTERNAL LINKS ANALYSIS START for page {$page_id} ({$post->post_title})");
        
        // CRITICAL: Use SYNCHRONIZED PERMISSIVE validation (matches tree generation logic)
        // Only skip system-generated content (Gutenberg blocks, headers, footers, nav)
        // Allow 'restricted' content for link analysis since we're only reading for display
        if (class_exists('SLMM_Direct_Editor_Content_Validator')) {
            $validator = new SLMM_Direct_Editor_Content_Validator();
            $validation = $validator->validate_post_for_editing($page_id);

            error_log("[SLMM Link Popup] ==> VALIDATION CHECK - can_edit: " . ($validation['can_edit'] ? 'YES' : 'NO') . ", editor_type: " . $validation['editor_type']);

            // PERMISSIVE VALIDATION: Only skip if content is truly system-generated (not user content)
            // Allow 'restricted' content for link analysis since we're only reading for display
            if ($validation['editor_type'] === 'system' || $validation['editor_type'] === 'gutenberg') {
                error_log("[SLMM Link Popup] ==> VALIDATION FAILED - Content is system-generated, skipping internal link analysis");
                return array();
            }
        }
        
        $content = $post->post_content;
        $site_url = get_site_url();
        $internal_links = array();
        
        error_log("[SLMM Link Popup] ==> CONTENT LENGTH: " . strlen($content) . " characters");
        
        if (empty($content)) {
            error_log("[SLMM Link Popup] ==> CONTENT EMPTY - No internal links to process");
            return array();
        }
        
        // SYNCHRONIZED: Use same regex parsing as tree generation (not DOMDocument)
        preg_match_all('/<a[^>]*href=["\']([^"\']+)["\'][^>]*>(.*?)<\/a>/i', $content, $matches, PREG_SET_ORDER);
        
        error_log("[SLMM Link Popup] ==> REGEX PARSING - Found " . count($matches) . " link matches");
        
        if (!empty($matches)) {
            foreach ($matches as $match) {
                $link_url = $match[1];
                $link_text = wp_strip_all_tags($match[2]); // Remove any nested HTML
                
                // Skip empty URLs and anchors
                if (empty($link_url) || $link_url === '#') {
                    continue;
                }
                
                // Check if link is internal
                if ($this->is_internal_link($link_url, $site_url)) {
                    $target_post_id = $this->get_robust_post_id($link_url);
                    $internal_links[] = array(
                        'url' => $link_url,
                        'anchor' => $link_text,
                        'target_page' => $this->get_page_title_from_url($link_url),
                        'target_post_id' => $target_post_id // Add post ID for node mapping
                    );
                    
                    error_log("[SLMM Link Popup] ==> INTERNAL LINK FOUND - URL: {$link_url}, anchor: {$link_text}, target_post_id: {$target_post_id}");
                } else {
                    error_log("[SLMM Link Popup] ==> EXTERNAL LINK SKIPPED: {$link_url}");
                }
            }
        }
        
        // Remove duplicates based on URL properly for multidimensional arrays
        $unique_urls = array();
        $unique_links = array();
        
        foreach ($internal_links as $link) {
            if (!in_array($link['url'], $unique_urls)) {
                $unique_urls[] = $link['url'];
                $unique_links[] = $link;
            }
        }
        
        $internal_links = $unique_links;
        
        error_log("[SLMM Link Popup] ==> INTERNAL LINKS ANALYSIS COMPLETE for page {$page_id}");
        error_log("[SLMM Link Popup] ==> FINAL INTERNAL LINKS COUNT: " . count($internal_links));
        
        if (count($internal_links) > 0) {
            $urls_list = array_map(function($link) { return $link['url']; }, array_slice($internal_links, 0, 5));
            error_log("[SLMM Link Popup] ==> FINAL INTERNAL LINKS (first 5): " . implode(', ', $urls_list));
        }
        
        return $internal_links;
    }
    
    /**
     * Extract external links from page content
     * SYNCHRONIZED with tree generation logic for consistent external link detection
     */
    private function get_external_links($page_id) {
        $post = get_post($page_id);
        if (!$post) {
            error_log("[SLMM Link Popup] ==> EXTERNAL LINKS - Post not found for ID: {$page_id}");
            return array();
        }
        
        error_log("[SLMM Link Popup] ==> EXTERNAL LINKS ANALYSIS START for page {$page_id} ({$post->post_title})");
        
        // CRITICAL: Use SYNCHRONIZED PERMISSIVE validation (matches tree generation logic)
        // Only skip system-generated content (Gutenberg blocks, headers, footers, nav)
        // Allow 'restricted' content for link analysis since we're only reading for display
        if (class_exists('SLMM_Direct_Editor_Content_Validator')) {
            $validator = new SLMM_Direct_Editor_Content_Validator();
            $validation = $validator->validate_post_for_editing($page_id);

            error_log("[SLMM Link Popup] ==> VALIDATION CHECK - can_edit: " . ($validation['can_edit'] ? 'YES' : 'NO') . ", editor_type: " . $validation['editor_type']);

            // PERMISSIVE VALIDATION: Only skip if content is truly system-generated (not user content)
            // Allow 'restricted' content for link analysis since we're only reading for display
            if ($validation['editor_type'] === 'system' || $validation['editor_type'] === 'gutenberg') {
                error_log("[SLMM Link Popup] ==> VALIDATION FAILED - Content is system-generated, skipping external link analysis");
                return array();
            }
        }
        
        $content = $post->post_content;
        $site_url = get_site_url();
        $external_links = array();
        
        error_log("[SLMM Link Popup] ==> CONTENT LENGTH: " . strlen($content) . " characters");
        
        if (empty($content)) {
            error_log("[SLMM Link Popup] ==> CONTENT EMPTY - No external links to process");
            return array();
        }
        
        // SYNCHRONIZED: Use same regex parsing as tree generation (not DOMDocument)
        preg_match_all('/<a[^>]*href=["\']([^"\']+)["\'][^>]*>(.*?)<\/a>/i', $content, $matches, PREG_SET_ORDER);
        
        error_log("[SLMM Link Popup] ==> REGEX PARSING - Found " . count($matches) . " link matches");
        
        if (!empty($matches)) {
            foreach ($matches as $match) {
                $link_url = $match[1];
                $link_text = wp_strip_all_tags($match[2]); // Remove any nested HTML
                
                // Skip empty URLs and anchors
                if (empty($link_url) || $link_url === '#') {
                    continue;
                }
                
                // SYNCHRONIZED: Use same internal link detection logic as tree generation
                if (!$this->is_internal_link($link_url, $site_url)) {
                    // CRITICAL SYNCHRONIZATION: External link must start with http/https (same as tree generation)
                    if (preg_match('/^https?:\/\//i', $link_url)) {
                        $domain = parse_url($link_url, PHP_URL_HOST);
                        if (!$domain) {
                            $domain = 'Unknown';
                        }
                        
                        $external_links[] = array(
                            'url' => $link_url,
                            'anchor' => $link_text,
                            'domain' => $domain
                        );
                        
                        error_log("[SLMM Link Popup] ==> EXTERNAL LINK FOUND - URL: {$link_url}, anchor: {$link_text}, domain: {$domain}");
                    } else {
                        error_log("[SLMM Link Popup] ==> EXTERNAL LINK REJECTED - Not http/https: {$link_url}");
                    }
                } else {
                    error_log("[SLMM Link Popup] ==> INTERNAL LINK SKIPPED: {$link_url}");
                }
            }
        }
        
        // Remove duplicates based on URL properly for multidimensional arrays
        $unique_urls = array();
        $unique_links = array();
        
        foreach ($external_links as $link) {
            if (!in_array($link['url'], $unique_urls)) {
                $unique_urls[] = $link['url'];
                $unique_links[] = $link;
            }
        }
        
        $external_links = $unique_links;
        
        error_log("[SLMM Link Popup] ==> EXTERNAL LINKS ANALYSIS COMPLETE for page {$page_id}");
        error_log("[SLMM Link Popup] ==> FINAL EXTERNAL LINKS COUNT: " . count($external_links));
        
        if (count($external_links) > 0) {
            $urls_list = array_map(function($link) { return $link['url']; }, array_slice($external_links, 0, 5));
            error_log("[SLMM Link Popup] ==> FINAL EXTERNAL LINKS (first 5): " . implode(', ', $urls_list));
        }
        
        return $external_links;
    }
    
    /**
     * Check if a URL is internal to this site
     */
    private function is_internal_link($url, $site_url) {
        // Handle relative URLs
        if (strpos($url, '/') === 0 && strpos($url, '//') !== 0) {
            return true; // Relative URLs are internal
        }
        
        // Handle absolute URLs
        if (strpos($url, $site_url) === 0) {
            return true; // URL starts with site URL
        }
        
        // Handle protocol-relative URLs
        if (strpos($url, '//') === 0) {
            $site_host = parse_url($site_url, PHP_URL_HOST);
            $url_host = parse_url('http:' . $url, PHP_URL_HOST);
            return $url_host === $site_host;
        }
        
        return false;
    }
    
    /**
     * Get page title from internal URL
     */
    private function get_page_title_from_url($url) {
        // Try to get post ID from URL using robust method
        $post_id = $this->get_robust_post_id($url);
        
        if ($post_id) {
            return get_the_title($post_id);
        }
        
        // Fallback: extract slug from URL
        $slug = basename(parse_url($url, PHP_URL_PATH));
        return ucwords(str_replace(array('-', '_'), ' ', $slug));
    }
    
    /**
     * Robust URL-to-post-ID resolution with multiple fallback strategies
     * 
     * This method attempts several approaches to resolve internal URLs to post IDs
     * since WordPress's url_to_postid() function often fails for complex URLs
     */
    private function get_robust_post_id($url) {
        $original_url = $url;
        
        error_log('[SLMM Link Popup] ==> STARTING URL RESOLUTION for: ' . $url);
        
        // Strategy 1: Try direct url_to_postid() first
        error_log('[SLMM Link Popup] Strategy 1 - Testing direct url_to_postid()...');
        $post_id = url_to_postid($url);
        if ($post_id && $post_id > 0) {
            error_log('[SLMM Link Popup] Strategy 1 ✅ SUCCESS - Direct url_to_postid(): ' . $post_id);
            return $post_id;
        }
        error_log('[SLMM Link Popup] Strategy 1 ❌ FAILED - Direct url_to_postid() returned: ' . $post_id);
        
        // Strategy 2: Clean URL (remove query parameters and fragments) then retry
        error_log('[SLMM Link Popup] Strategy 2 - Testing clean URL resolution...');
        $clean_url = $this->clean_url_for_resolution($url);
        error_log('[SLMM Link Popup] Strategy 2 - Original: ' . $url . ' | Cleaned: ' . $clean_url);
        
        if ($clean_url !== $url) {
            $post_id = url_to_postid($clean_url);
            if ($post_id && $post_id > 0) {
                error_log('[SLMM Link Popup] Strategy 2 ✅ SUCCESS - Clean URL url_to_postid(): ' . $post_id);
                return $post_id;
            }
            error_log('[SLMM Link Popup] Strategy 2 ❌ FAILED - Clean URL returned: ' . $post_id);
        } else {
            error_log('[SLMM Link Popup] Strategy 2 ⏭️ SKIPPED - URL already clean');
        }
        
        // Strategy 3: WordPress permalink pattern matching
        error_log('[SLMM Link Popup] Strategy 3 - Testing WordPress permalink patterns...');
        $post_id = $this->match_wordpress_permalink($url);
        if ($post_id && $post_id > 0) {
            error_log('[SLMM Link Popup] Strategy 3 ✅ SUCCESS - WordPress permalink match: ' . $post_id);
            return $post_id;
        }
        error_log('[SLMM Link Popup] Strategy 3 ❌ FAILED - No permalink pattern match');
        
        // Strategy 4: Database query by post slug/name
        error_log('[SLMM Link Popup] Strategy 4 - Testing database slug query...');
        $slug = $this->extract_slug_from_url($url);
        error_log('[SLMM Link Popup] Strategy 4 - Extracted slug: ' . $slug);
        
        if (!empty($slug)) {
            $post_id = $this->find_post_by_slug($slug);
            if ($post_id && $post_id > 0) {
                error_log('[SLMM Link Popup] Strategy 4 ✅ SUCCESS - Database slug query: ' . $post_id);
                return $post_id;
            }
            error_log('[SLMM Link Popup] Strategy 4 ❌ FAILED - No database match for slug: ' . $slug);
        } else {
            error_log('[SLMM Link Popup] Strategy 4 ⏭️ SKIPPED - No slug extracted');
        }
        
        // Strategy 5: Try absolute URL resolution if this was relative
        error_log('[SLMM Link Popup] Strategy 5 - Testing absolute URL conversion...');
        if (strpos($url, '/') === 0 && strpos($url, '//') !== 0) {
            $absolute_url = get_site_url() . $url;
            error_log('[SLMM Link Popup] Strategy 5 - Converted to absolute: ' . $absolute_url);
            
            $post_id = url_to_postid($absolute_url);
            if ($post_id && $post_id > 0) {
                error_log('[SLMM Link Popup] Strategy 5 ✅ SUCCESS - Absolute URL resolution: ' . $post_id);
                return $post_id;
            }
            error_log('[SLMM Link Popup] Strategy 5 ❌ FAILED - Absolute URL returned: ' . $post_id);
        } else {
            error_log('[SLMM Link Popup] Strategy 5 ⏭️ SKIPPED - Not a relative URL');
        }
        
        // Strategy 6: Hierarchical URL pattern matching (for URLs like /parent/child/)
        error_log('[SLMM Link Popup] Strategy 6 - Testing hierarchical URL pattern matching...');
        $post_id = $this->find_post_by_hierarchical_url($url);
        if ($post_id && $post_id > 0) {
            error_log('[SLMM Link Popup] Strategy 6 ✅ SUCCESS - Hierarchical URL match: ' . $post_id);
            return $post_id;
        }
        error_log('[SLMM Link Popup] Strategy 6 ❌ FAILED - No hierarchical match found');
        
        // Strategy 7: Fuzzy slug matching (partial matches)
        error_log('[SLMM Link Popup] Strategy 7 - Testing fuzzy slug matching...');
        $post_id = $this->find_post_by_fuzzy_slug($slug);
        if ($post_id && $post_id > 0) {
            error_log('[SLMM Link Popup] Strategy 7 ✅ SUCCESS - Fuzzy slug match: ' . $post_id);
            return $post_id;
        }
        error_log('[SLMM Link Popup] Strategy 7 ❌ FAILED - No fuzzy match found');
        
        error_log('[SLMM Link Popup] ==> ALL STRATEGIES FAILED for URL: ' . $original_url);
        return 0; // All strategies failed
    }
    
    /**
     * Clean URL by removing query parameters and fragments for better resolution
     */
    private function clean_url_for_resolution($url) {
        $parsed = parse_url($url);
        
        // Rebuild URL without query and fragment
        $clean_url = '';
        
        if (isset($parsed['scheme'])) {
            $clean_url .= $parsed['scheme'] . '://';
        }
        
        if (isset($parsed['host'])) {
            $clean_url .= $parsed['host'];
        }
        
        if (isset($parsed['port'])) {
            $clean_url .= ':' . $parsed['port'];
        }
        
        if (isset($parsed['path'])) {
            $clean_url .= $parsed['path'];
        }
        
        // If it was a relative URL, preserve that
        if (empty($parsed['host']) && isset($parsed['path'])) {
            $clean_url = $parsed['path'];
        }
        
        return $clean_url;
    }
    
    /**
     * Extract post slug from URL path
     */
    private function extract_slug_from_url($url) {
        $path = parse_url($url, PHP_URL_PATH);
        if (empty($path)) {
            return '';
        }
        
        // Remove leading/trailing slashes and extract the last segment
        $path = trim($path, '/');
        $segments = explode('/', $path);
        
        // Return the last non-empty segment as the slug
        return end($segments);
    }
    
    /**
     * Find post by slug using database query
     */
    private function find_post_by_slug($slug) {
        global $wpdb;
        
        if (empty($slug)) {
            return 0;
        }
        
        // Query for post by slug (post_name)
        $post_id = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_name = %s 
             AND post_status = 'publish' 
             AND post_type IN ('post', 'page') 
             ORDER BY post_type DESC, ID DESC 
             LIMIT 1",
            $slug
        ));
        
        return intval($post_id);
    }
    
    /**
     * Match WordPress permalink patterns for better URL resolution
     */
    private function match_wordpress_permalink($url) {
        global $wpdb;
        
        // Clean the URL first
        $path = parse_url($url, PHP_URL_PATH);
        if (empty($path)) {
            return 0;
        }
        
        // Remove leading/trailing slashes
        $path = trim($path, '/');
        
        // Try common WordPress permalink patterns
        $patterns = array(
            // Pattern: /year/month/day/postname/
            '/^(\d{4})\/(\d{1,2})\/(\d{1,2})\/([^\/]+)\/?$/',
            // Pattern: /year/month/postname/ 
            '/^(\d{4})\/(\d{1,2})\/([^\/]+)\/?$/',
            // Pattern: /category/postname/
            '/^[^\/]+\/([^\/]+)\/?$/',
            // Pattern: just postname
            '/^([^\/]+)\/?$/'
        );
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $path, $matches)) {
                $slug = end($matches); // Get the last capture group (postname)
                error_log('[SLMM Link Popup] Permalink pattern matched, testing slug: ' . $slug);
                
                $post_id = $this->find_post_by_slug($slug);
                if ($post_id > 0) {
                    return $post_id;
                }
            }
        }
        
        return 0;
    }
    
    /**
     * Find post by fuzzy slug matching (handles partial matches and variations)
     */
    private function find_post_by_fuzzy_slug($slug) {
        global $wpdb;
        
        if (empty($slug)) {
            return 0;
        }
        
        error_log('[SLMM Link Popup] Starting enhanced fuzzy matching for slug: ' . $slug);
        
        // Strategy 1: Try with URL decoding
        $decoded_slug = urldecode($slug);
        if ($decoded_slug !== $slug) {
            $post_id = $this->find_post_by_slug($decoded_slug);
            if ($post_id > 0) {
                error_log('[SLMM Link Popup] Fuzzy match success with URL decoding: ' . $decoded_slug);
                return $post_id;
            }
        }
        
        // Strategy 2: Generate comprehensive slug variations (matching JS logic)
        $variations = $this->generate_slug_variations($slug);
        error_log('[SLMM Link Popup] Generated ' . count($variations) . ' slug variations: ' . implode(', ', array_slice($variations, 0, 10)));
        
        foreach ($variations as $variation) {
            if ($variation !== $slug && !empty($variation)) {
                $post_id = $this->find_post_by_slug($variation);
                if ($post_id > 0) {
                    error_log('[SLMM Link Popup] Fuzzy match success with variation: ' . $variation . ' → post ID ' . $post_id);
                    return $post_id;
                }
            }
        }
        
        // Strategy 3: Try LIKE matching for partial slugs
        $post_id = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_name LIKE %s 
             AND post_status = 'publish' 
             AND post_type IN ('post', 'page') 
             ORDER BY post_type DESC, ID DESC 
             LIMIT 1",
            '%' . $slug . '%'
        ));
        
        if ($post_id) {
            error_log('[SLMM Link Popup] Fuzzy match success with LIKE query: ' . $post_id);
            return intval($post_id);
        }
        
        // Strategy 4: Try reverse matching - generate variations of post titles and match against slug
        $post_id = $this->find_post_by_reverse_title_matching($slug);
        if ($post_id > 0) {
            error_log('[SLMM Link Popup] Fuzzy match success with reverse title matching: ' . $post_id);
            return $post_id;
        }
        
        // Strategy 5: Try matching by post title with slug variations
        foreach ($variations as $variation) {
            if (!empty($variation)) {
                $post_id = $wpdb->get_var($wpdb->prepare(
                    "SELECT ID FROM {$wpdb->posts} 
                     WHERE REPLACE(REPLACE(LOWER(post_title), ' ', '-'), '_', '-') LIKE %s
                     AND post_status = 'publish' 
                     AND post_type IN ('post', 'page') 
                     ORDER BY post_type DESC, ID DESC 
                     LIMIT 1",
                    '%' . strtolower($variation) . '%'
                ));
                
                if ($post_id) {
                    error_log('[SLMM Link Popup] Fuzzy match success with title matching variation: ' . $variation . ' → post ID ' . $post_id);
                    return intval($post_id);
                }
            }
        }
        
        error_log('[SLMM Link Popup] All fuzzy matching strategies failed for slug: ' . $slug);
        return 0;
    }
    
    /**
     * Generate comprehensive slug variations (matching JavaScript logic)
     */
    private function generate_slug_variations($text) {
        if (empty($text)) {
            return array();
        }
        
        $variations = array();
        $original_text = trim($text);
        
        // Basic lowercase conversion
        $variations[] = strtolower($original_text);
        
        // Handle PascalCase/camelCase conversion (key fix for protected-word → protectedwords)
        $kebab_from_pascal = preg_replace('/([a-z])([A-Z])/', '$1-$2', $original_text);
        $kebab_from_pascal = preg_replace('/([A-Z])([A-Z][a-z])/', '$1-$2', $kebab_from_pascal);
        $kebab_from_pascal = strtolower($kebab_from_pascal);
        
        if ($kebab_from_pascal !== strtolower($original_text)) {
            $variations[] = $kebab_from_pascal;
            // Also try without the last 's' for plural matching
            if (substr($kebab_from_pascal, -1) === 's' && strlen($kebab_from_pascal) > 1) {
                $variations[] = substr($kebab_from_pascal, 0, -1);
            }
        }
        
        // Handle spaces and various delimiters
        $variations[] = str_replace(' ', '-', strtolower($original_text));
        $variations[] = str_replace(' ', '_', strtolower($original_text));
        $variations[] = str_replace(' ', '', strtolower($original_text));
        
        // Clean up non-alphanumeric characters
        $clean_dashed = preg_replace('/[^a-z0-9\s]/', '', strtolower($original_text));
        $clean_dashed = str_replace(' ', '-', $clean_dashed);
        $variations[] = $clean_dashed;
        
        $clean_no_dashes = preg_replace('/[^a-z0-9]/', '', strtolower($original_text));
        $variations[] = $clean_no_dashes;
        
        // Add singular/plural variations
        $temp_variations = $variations; // Create copy to avoid infinite loop
        foreach ($temp_variations as $variant) {
            if (substr($variant, -1) === 's' && strlen($variant) > 1) {
                $variations[] = substr($variant, 0, -1); // Remove 's'
            } elseif (substr($variant, -1) !== 's') {
                $variations[] = $variant . 's'; // Add 's'
            }
        }
        
        // Handle word variations (dash/underscore conversions)
        $temp_variations = $variations;
        foreach ($temp_variations as $variant) {
            if (strpos($variant, '-') !== false) {
                $variations[] = str_replace('-', '_', $variant); // dash to underscore
                $variations[] = str_replace('-', '', $variant); // remove dashes
            }
            if (strpos($variant, '_') !== false) {
                $variations[] = str_replace('_', '-', $variant); // underscore to dash
                $variations[] = str_replace('_', '', $variant); // remove underscores
            }
        }
        
        // Remove duplicates and empty strings
        $variations = array_unique($variations);
        $variations = array_filter($variations, function($v) {
            return !empty($v) && strlen($v) > 0;
        });
        
        return $variations;
    }
    
    /**
     * Find post by reverse title matching - generate variations of post titles and match against slug
     */
    private function find_post_by_reverse_title_matching($slug) {
        global $wpdb;
        
        if (empty($slug)) {
            return 0;
        }
        
        // Get all posts/pages with their titles and names (including drafts for comprehensive matching)
        $posts = $wpdb->get_results($wpdb->prepare(
            "SELECT ID, post_title, post_name, post_status, post_type FROM {$wpdb->posts} 
             WHERE post_type IN ('post', 'page')
             AND post_title IS NOT NULL 
             AND post_title != ''
             LIMIT 200" // Increased limit for comprehensive search
        ));
        
        if (empty($posts)) {
            error_log('[SLMM Link Popup] No posts found in database for reverse matching');
            return 0;
        }
        
        $slug_lower = strtolower($slug);
        error_log('[SLMM Link Popup] Reverse matching for slug "' . $slug . '" against ' . count($posts) . ' posts in database');
        
        // Log all available posts for debugging
        $available_posts = array();
        foreach ($posts as $post) {
            $available_posts[] = "ID:{$post->ID} title:\"{$post->post_title}\" name:\"{$post->post_name}\" status:{$post->post_status}";
        }
        error_log('[SLMM Link Popup] Available posts in database: ' . implode(' | ', array_slice($available_posts, 0, 10)));
        
        foreach ($posts as $post) {
            // Check direct post_name match first
            if (strtolower($post->post_name) === $slug_lower) {
                error_log('[SLMM Link Popup] Direct post_name match found: "' . $post->post_name . '" matches slug "' . $slug . '" → post ID ' . $post->ID);
                return intval($post->ID);
            }
            
            // Check title variations
            $title_variations = $this->generate_slug_variations($post->post_title);
            
            // Check if any title variation matches the slug
            foreach ($title_variations as $variation) {
                if ($variation === $slug_lower || 
                    strpos($variation, $slug_lower) !== false || 
                    strpos($slug_lower, $variation) !== false) {
                    error_log('[SLMM Link Popup] Reverse title match found: "' . $post->post_title . '" → variation "' . $variation . '" matches slug "' . $slug . '" → post ID ' . $post->ID);
                    return intval($post->ID);
                }
            }
            
            // Check post_name variations
            if ($post->post_name) {
                $name_variations = $this->generate_slug_variations($post->post_name);
                foreach ($name_variations as $variation) {
                    if ($variation === $slug_lower || 
                        strpos($variation, $slug_lower) !== false || 
                        strpos($slug_lower, $variation) !== false) {
                        error_log('[SLMM Link Popup] Reverse post_name match found: "' . $post->post_name . '" → variation "' . $variation . '" matches slug "' . $slug . '" → post ID ' . $post->ID);
                        return intval($post->ID);
                    }
                }
            }
        }
        
        error_log('[SLMM Link Popup] No reverse match found for slug "' . $slug . '"');
        return 0;
    }
    
    /**
     * Find post by hierarchical URL pattern matching (for URLs like /parent/child/)
     */
    private function find_post_by_hierarchical_url($url) {
        global $wpdb;
        
        if (empty($url)) {
            return 0;
        }
        
        $path = parse_url($url, PHP_URL_PATH);
        if (empty($path)) {
            return 0;
        }
        
        // Remove leading/trailing slashes and split path
        $path = trim($path, '/');
        $segments = explode('/', $path);
        
        if (count($segments) < 2) {
            return 0; // Not a hierarchical URL
        }
        
        error_log('[SLMM Link Popup] Hierarchical URL analysis for: ' . $url);
        error_log('[SLMM Link Popup] Path segments: ' . implode(' → ', $segments));
        
        // Try to find the parent page first
        $parent_slug = $segments[0];
        $child_slug = end($segments); // Get the last segment as the child
        
        error_log('[SLMM Link Popup] Looking for parent: "' . $parent_slug . '" and child: "' . $child_slug . '"');
        
        // Find parent page
        $parent_id = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_name = %s 
             AND post_status IN ('publish', 'draft') 
             AND post_type IN ('post', 'page') 
             LIMIT 1",
            $parent_slug
        ));
        
        if (!$parent_id) {
            error_log('[SLMM Link Popup] Parent page "' . $parent_slug . '" not found');
            return 0;
        }
        
        error_log('[SLMM Link Popup] Found parent page "' . $parent_slug . '" with ID: ' . $parent_id);
        
        // Now look for child page under this parent
        $child_id = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_name = %s 
             AND post_parent = %d
             AND post_status IN ('publish', 'draft') 
             AND post_type IN ('post', 'page') 
             LIMIT 1",
            $child_slug,
            $parent_id
        ));
        
        if ($child_id) {
            error_log('[SLMM Link Popup] Found child page "' . $child_slug . '" under parent "' . $parent_slug . '" with ID: ' . $child_id);
            return intval($child_id);
        }
        
        // Try fuzzy matching for child slug
        $child_variations = $this->generate_slug_variations($child_slug);
        foreach ($child_variations as $variation) {
            $child_id = $wpdb->get_var($wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} 
                 WHERE post_name = %s 
                 AND post_parent = %d
                 AND post_status IN ('publish', 'draft') 
                 AND post_type IN ('post', 'page') 
                 LIMIT 1",
                $variation,
                $parent_id
            ));
            
            if ($child_id) {
                error_log('[SLMM Link Popup] Found child page with variation "' . $variation . '" under parent "' . $parent_slug . '" with ID: ' . $child_id);
                return intval($child_id);
            }
        }
        
        // Also try matching by title for child pages
        $child_title_variations = $this->generate_slug_variations($child_slug);
        foreach ($child_title_variations as $variation) {
            $child_id = $wpdb->get_var($wpdb->prepare(
                "SELECT ID FROM {$wpdb->posts} 
                 WHERE LOWER(post_title) LIKE %s
                 AND post_parent = %d
                 AND post_status IN ('publish', 'draft') 
                 AND post_type IN ('post', 'page') 
                 LIMIT 1",
                '%' . $variation . '%',
                $parent_id
            ));
            
            if ($child_id) {
                error_log('[SLMM Link Popup] Found child page by title matching variation "' . $variation . '" under parent "' . $parent_slug . '" with ID: ' . $child_id);
                return intval($child_id);
            }
        }
        
        error_log('[SLMM Link Popup] No child page found for "' . $child_slug . '" under parent "' . $parent_slug . '"');
        
        // List all children of the parent for debugging
        $all_children = $wpdb->get_results($wpdb->prepare(
            "SELECT ID, post_title, post_name, post_status FROM {$wpdb->posts} 
             WHERE post_parent = %d
             AND post_type IN ('post', 'page')
             ORDER BY post_title",
            $parent_id
        ));
        
        if ($all_children) {
            $children_info = array();
            foreach ($all_children as $child) {
                $children_info[] = "ID:{$child->ID} title:\"{$child->post_title}\" name:\"{$child->post_name}\" status:{$child->post_status}";
            }
            error_log('[SLMM Link Popup] All children under parent "' . $parent_slug . '": ' . implode(' | ', $children_info));
        } else {
            error_log('[SLMM Link Popup] No children found under parent "' . $parent_slug . '"');
        }
        
        return 0;
    }
}