# NONCE Handling Standards & Best Practices

## Overview
This document establishes standards for nonce (Number Once) handling in the SLMM SEO Bundle plugin to prevent security vulnerabilities and avoid common implementation issues.

## Recent Issue Fixed (September 2025)
**Problem**: Page Summarization feature was failing with "Invalid nonce" errors due to inconsistent parameter naming between JavaScript and PHP.

**Root Cause**: 
- JavaScript was sending nonce as `security` in main AJAX call but `nonce` in other calls
- PHP handler only checked for `$_POST['nonce']` parameter
- Created validation mismatch causing all requests to fail

**Files Affected**:
- `assets/js/slmm-page-summarization.js` - Fixed inconsistent parameter naming
- `includes/ajax/page-summarization-handler.php` - Enhanced to accept both parameter types

## Standard Nonce Implementation Pattern

### 1. PHP Backend (AJAX Handlers)

#### ✅ **RECOMMENDED**: Robust Nonce Validation
```php
// Check both 'nonce' and 'security' parameters for maximum compatibility
$nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
if (!wp_verify_nonce($nonce, 'your_nonce_action_name')) {
    wp_send_json_error(array('message' => 'Invalid nonce'));
    return;
}
```

#### ❌ **AVOID**: Single Parameter Check
```php
// This creates compatibility issues
if (!wp_verify_nonce($_POST['nonce'], 'your_nonce_action_name')) {
    wp_send_json_error('Invalid nonce');
    return;
}
```

### 2. JavaScript Frontend (AJAX Calls)

#### ✅ **RECOMMENDED**: Consistent Parameter Naming
```javascript
$.ajax({
    url: ajaxUrl,
    type: 'POST',
    data: {
        action: 'your_ajax_action',
        nonce: yourScriptData.nonce,  // ALWAYS use 'nonce'
        // ... other data
    },
    // ... rest of AJAX config
});
```

#### ❌ **AVOID**: Inconsistent Parameter Naming
```javascript
// Don't mix 'security' and 'nonce' in the same codebase
data: {
    security: yourScriptData.nonce  // INCONSISTENT
}
```

### 3. Nonce Generation & Localization

#### Standard Pattern:
```php
// Generate nonce with descriptive action name
$nonce = wp_create_nonce('feature_specific_action_nonce');

// Localize to JavaScript
wp_localize_script('your-script-handle', 'yourScriptData', array(
    'nonce' => $nonce,
    'ajax_url' => admin_url('admin-ajax.php'),
    // ... other data
));
```

## Naming Conventions

### Nonce Action Names
Use descriptive, feature-specific action names:

#### ✅ **GOOD Examples**:
```php
'slmm_page_summarization_nonce'
'slmm_direct_editor_nonce'
'slmm_url_renderer_nonce'
'slmm_acf_settings_nonce'
```

#### ❌ **AVOID**:
```php
'nonce'                    // Too generic
'ajax_nonce'              // Not descriptive
'slmm_nonce'              // Too broad
```

### JavaScript Data Object Names
Follow the pattern: `featureName + Data`

#### ✅ **GOOD Examples**:
```javascript
slmmPageSummarizationData.nonce
slmmDirectEditorData.nonce
slmmUrlRendererData.nonce
```

## Security Best Practices

### 1. Always Validate User Capabilities
```php
if (!current_user_can('edit_posts')) {
    wp_send_json_error('Insufficient permissions');
    return;
}
```

### 2. Sanitize Input Data
```php
$url = esc_url_raw($_POST['url'] ?? '');
$post_id = absint($_POST['post_id'] ?? 0);
$text_input = sanitize_textarea_field($_POST['content'] ?? '');
```

### 3. Use Appropriate WordPress Functions
```php
wp_send_json_success($data);  // For success responses
wp_send_json_error($error);   // For error responses
wp_die('Error message');      // For fatal errors
```

## Testing Checklist

When implementing new AJAX functionality:

- [ ] **Nonce Generation**: Created with descriptive action name
- [ ] **JavaScript Consistency**: All AJAX calls use `nonce` parameter 
- [ ] **PHP Robustness**: Handler checks both `nonce` and `security` parameters
- [ ] **Capability Check**: Proper user permission validation
- [ ] **Input Sanitization**: All input data properly sanitized
- [ ] **Error Handling**: Appropriate error messages and logging
- [ ] **Browser Testing**: Test in actual browser, not just server logs
- [ ] **Network Tab**: Verify correct parameters are being sent

## Common Pitfalls to Avoid

### 1. **Parameter Name Inconsistency**
- Don't mix `nonce` and `security` parameters in the same feature
- Always use `nonce` as the standard parameter name

### 2. **Generic Nonce Action Names**
- Avoid generic names like 'nonce' or 'ajax_nonce'
- Use feature-specific action names for security isolation

### 3. **Missing JavaScript Data Validation**
```javascript
// Always check if localized data exists
if (typeof yourScriptData === 'undefined') {
    console.error('Script data not loaded');
    return;
}
```

### 4. **Insufficient Error Logging**
```php
// Add debug logging for nonce failures
error_log('SLMM Nonce Debug - Received: ' . $received_nonce);
error_log('SLMM Nonce Debug - Expected action: ' . $expected_action);
error_log('SLMM Nonce Debug - Valid: ' . ($nonce_valid ? 'YES' : 'NO'));
```

### 5. **Not Testing Both Systems**
- Always test both button systems and keyboard shortcuts
- Verify both success and failure scenarios

## File Organization Standards

### AJAX Handlers
- Place in `includes/ajax/` directory
- Use descriptive filenames: `feature-name-handler.php`
- Initialize in main plugin file with proper dependency loading

### JavaScript Files
- Place in `assets/js/` directory  
- Use descriptive filenames: `slmm-feature-name.js`
- Always include feature checks and error handling

### Documentation Updates
- Update this file when adding new nonce-based features
- Document any unique security considerations
- Include examples of proper implementation

## Integration with Plugin Architecture

### Main Plugin File Integration
```php
// Load dependencies first
require_once SLMM_SEO_PLUGIN_DIR . 'includes/features/feature-name/class-manager.php';

// Load AJAX handler
require_once SLMM_SEO_PLUGIN_DIR . 'includes/ajax/feature-handler.php';

// Initialize handler
new SLMM_Feature_Handler();
```

### Asset Loading
```php
// In appropriate enqueue function
wp_enqueue_script(
    'slmm-feature-script',
    SLMM_SEO_PLUGIN_URL . 'assets/js/slmm-feature.js',
    array('jquery'),
    SLMM_SEO_VERSION,
    true
);

wp_localize_script('slmm-feature-script', 'slmmFeatureData', array(
    'nonce' => wp_create_nonce('slmm_feature_nonce'),
    'ajax_url' => admin_url('admin-ajax.php'),
    // ... other data
));
```

## Maintenance Notes

- **Review Date**: September 10, 2025
- **Next Review**: When adding new AJAX functionality
- **Responsible**: Development team
- **Related Files**: All files in `includes/ajax/` and `assets/js/`

## Version History

### v1.0 - September 10, 2025
- Initial documentation created
- Based on Page Summarization nonce fix
- Established standard patterns and best practices
- Added comprehensive testing checklist

---

**Remember**: Consistent nonce handling prevents security vulnerabilities and reduces debugging time. Always follow these patterns for reliable AJAX functionality.