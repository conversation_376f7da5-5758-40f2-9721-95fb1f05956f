---
task: h-complete-llm-frontend-integration
branch: feature/complete-llm-integration
status: pending
created: 2025-01-10
modules: [slmm-direct-editor, url-renderer, ai-interlinking, ajax-handlers]
---

# Complete LLM Frontend Integration for AI Interlinking

## Problem/Goal
The AI interlinking system backend is configured with prompts in settings, but the frontend integration is incomplete. The system needs to:
1. Allow URL assignment with T/M/B (TOP/MIDDLE/BOTTOM) value selection
2. Replace prompt tokens ({content}, {target_page}, {page_hierarchy}, {linking_rules}) with actual values
3. Properly pass the formatted prompts to the LLM and display results

Currently, clicking "AI Suggest Links" fails because tokens aren't replaced and there's no way to specify target URLs with their segment assignments.

## Success Criteria
- [ ] Add URL input and T/M/B assignment buttons to the URL renderer section
- [ ] Store selected URLs with their T/M/B assignments for use in AI suggestions
- [ ] Implement token replacement system in `generateAiSuggestions()` function
- [ ] Replace {content} with actual segmented content from TinyMCE
- [ ] Replace {target_page} with selected URL from renderer
- [ ] Replace {page_hierarchy} with current page hierarchy information
- [ ] Replace {linking_rules} with configured rules from settings
- [ ] Successfully generate AI suggestions when clicking "AI Suggest Links"
- [ ] Display AI-generated anchor text variations in the accordion interface
- [ ] Implement click-to-insert functionality to add anchor text to TinyMCE

## Context Files
- @assets/js/slmm-direct-editor.js:1042-1098  # generateAiSuggestions function
- @assets/js/slmm-direct-editor.js:984-1006   # getInterlinkingContext function
- @assets/js/slmm-direct-editor.js:504-582    # URL renderer handlers
- @includes/ajax/ai-interlinking-handler.php   # Backend AJAX handler
- @includes/settings/general-settings.php:1645-1700  # AI interlinking prompts settings

## User Requirements
- URL renderer needs to accept URLs and assign them T/M/B values
- These assignments should be stored and used when generating AI suggestions
- Prompt tokens must be replaced with actual contextual data
- System should work with the existing backend prompt configuration

## Technical Details

### Current Token Structure
```
{content} - Should be replaced with segmented content from TinyMCE
{target_page} - Should be replaced with selected URL from renderer
{page_hierarchy} - Should be replaced with current page hierarchy (Mother/Son/Grandson)
{linking_rules} - Should be replaced with rules from slmm_interlinking_rules option
```

### Implementation Flow
1. User enters URL in renderer
2. User assigns T/M/B value to URL
3. User clicks "AI Suggest Links"
4. System replaces all tokens with actual values
5. Sends formatted prompt to LLM
6. Displays generated anchor text suggestions
7. User clicks suggestion to insert into TinyMCE

## Work Log
- [2025-01-10] Task created to complete LLM frontend integration