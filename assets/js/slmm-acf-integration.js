/**
 * SLMM ACF Integration - D3 Canvas Title Swapping
 * Handles ACF field integration with the interlinking suite D3 tree visualization
 * 
 * Features:
 * - ACF toggle switch and field name input management
 * - ACF field validation and settings persistence
 * - D3 node title swapping between regular and ACF titles
 * - Tree data refresh with ACF field inclusion
 * - Visual feedback for ACF states
 */

(function($) {
    'use strict';

    // MEMORY LEAK PROTECTION: Timeout tracking system
    window.acfTimeouts = window.acfTimeouts || [];

    function addACFTimeout(timeoutId) {
        window.acfTimeouts.push(timeoutId);
        return timeoutId;
    }

    // Debug system integration
    const debug = {
        log: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            }
        },
        success: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.success(category, message, data);
            }
        },
        warn: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            }
        },
        error: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            } else {
                // Fallback for errors: always show in console
                console.error('[SLMM ACF Integration] ' + message, data || '');
            }
        },
        info: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.info(category, message, data);
            }
        }
    };

    // ACF Integration State Management
    let acfState = {
        enabled: false,
        fieldName: '',
        validated: false,
        settings: {},
        treeData: null,
        originalNodeData: new Map(),
        initializationAttempts: 0,
        maxInitializationAttempts: 5,
        needsRefreshOnTreeLoad: false
    };

    // UI Element References
    let elements = {
        toggleCheckbox: null,
        fieldInput: null,
        validateButton: null,
        validationStatus: null,
        fieldInputContainer: null
    };

    /**
     * Initialize ACF Integration System
     */
    function initializeACFIntegration() {
        acfState.initializationAttempts++;
        debug.log('ACF Integration', `Initializing... (attempt ${acfState.initializationAttempts}/${acfState.maxInitializationAttempts})`);
        
        // Check if we've exceeded max attempts
        if (acfState.initializationAttempts > acfState.maxInitializationAttempts) {
            debug.error('ACF Integration', 'Maximum initialization attempts exceeded - giving up');
            debug.error('ACF Integration', 'ACF integration will not be available');
            return false;
        }
        
        // Pre-initialization checks
        if (!performPreInitChecks()) {
            debug.error('ACF Integration', 'Pre-initialization checks failed - aborting initialization');
            return false;
        }
        
        // Cache UI elements
        if (!cacheUIElements()) {
            if (acfState.initializationAttempts < acfState.maxInitializationAttempts) {
                const retryDelay = 500 * acfState.initializationAttempts; // Progressive delay
                debug.warn('ACF Integration', `UI elements not available - will retry in ${retryDelay}ms`);
                addACFTimeout(setTimeout(() => {
                    debug.log('ACF Integration', 'Retrying initialization...');
                    initializeACFIntegration();
                }, retryDelay));
            } else {
                debug.error('ACF Integration', 'UI elements never became available - ACF integration disabled');
            }
            return false;
        }
        
        // Setup event listeners
        setupEventListeners();
        
        // Setup synchronization systems
        listenForExternalSyncRequests();
        setupLifecycleHandlers();
        
        // Load saved ACF settings (with error handling)
        loadACFSettings();
        
        // Perform initial sync with other components
        performInitialSync();
        
        debug.success('ACF Integration', 'Initialization complete successfully');
        return true;
    }
    
    /**
     * Perform pre-initialization checks
     */
    function performPreInitChecks() {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Running pre-initialization checks', null, 'acf');
        }
        
        // Check jQuery availability
        if (typeof $ === 'undefined' || typeof jQuery === 'undefined') {
            debug.error('ACF Integration', 'jQuery not available');
            return false;
        }
        
        // Check if slmmInterlinkingData is available
        if (typeof slmmInterlinkingData === 'undefined') {
            debug.error('ACF Integration', 'slmmInterlinkingData not available');
            return false;
        }
        
        // Check if AJAX URL is available
        if (!slmmInterlinkingData.ajax_url) {
            debug.error('ACF Integration', 'AJAX URL not available');
            return false;
        }
        
        // Check if nonce is available
        if (!slmmInterlinkingData.nonce) {
            debug.error('ACF Integration', 'Nonce not available');
            return false;
        }
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Pre-initialization checks passed', null, 'acf');
        }
        return true;
    }

    /**
     * Cache UI element references
     */
    function cacheUIElements() {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Caching UI elements', null, 'acf');
        }
        
        elements.toggleCheckbox = $('#slmm-use-acf-titles');
        elements.fieldInput = $('#slmm-acf-field-name');
        elements.validateButton = $('#slmm-validate-acf-field');
        elements.validationStatus = $('#slmm-acf-validation-status');
        elements.fieldInputContainer = $('#slmm-acf-field-input-container');
        
        // Log element availability for debugging
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Element availability check', {
                toggleCheckbox: elements.toggleCheckbox.length > 0 ? 'FOUND' : 'NOT FOUND',
                fieldInput: elements.fieldInput.length > 0 ? 'FOUND' : 'NOT FOUND',
                validateButton: elements.validateButton.length > 0 ? 'FOUND' : 'NOT FOUND',
                validationStatus: elements.validationStatus.length > 0 ? 'FOUND' : 'NOT FOUND',
                fieldInputContainer: elements.fieldInputContainer.length > 0 ? 'FOUND' : 'NOT FOUND'
            }, 'acf');
        }
        
        // Check if all critical elements are found
        const criticalElementsFound = elements.toggleCheckbox.length > 0 && elements.fieldInput.length > 0 && elements.validateButton.length > 0;
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Critical elements status', {status: criticalElementsFound ? 'ALL FOUND' : 'MISSING ELEMENTS'}, 'acf');
        }
        
        if (!criticalElementsFound) {
            debug.error('ACF Integration', 'Missing critical UI elements', {
                acfControlsContainer: $('.slmm-acf-controls-container').length > 0,
                slmmAcfPrefixElements: $('[id*="slmm-acf"]').length,
                documentReadyState: document.readyState,
                bodyElementExists: $('body').length > 0,
                elementsWithAcfInId: $('[id*="acf"]').length,
                elementsWithSlmmInId: $('[id*="slmm"]').length,
                inputElements: $('input').length,
                buttonElements: $('button').length
            });
            
            return false;
        }
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('UI elements cached successfully', null, 'acf');
        }
        return true;
    }

    /**
     * Setup event listeners for ACF controls
     */
    function setupEventListeners() {
        // Toggle checkbox change handler
        elements.toggleCheckbox.on('change', handleACFToggleChange);
        
        // Field name input change handler (debounced)
        let fieldInputTimeout;
        elements.fieldInput.on('input', function() {
            clearTimeout(fieldInputTimeout);
            fieldInputTimeout = addACFTimeout(setTimeout(() => {
                handleFieldNameChange();
            }, 500));
        });
        
        // Validate button click handler
        elements.validateButton.on('click', handleValidateButtonClick);
        
        // Field name input enter key handler
        elements.fieldInput.on('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                handleValidateButtonClick();
            }
        });
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Event listeners setup complete', null, 'acf');
        }
    }

    /**
     * Load ACF settings from server
     */
    function loadACFSettings() {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Loading settings', {
                ajaxUrl: slmmInterlinkingData?.ajax_url,
                nonceAvailable: typeof slmmInterlinkingData?.nonce !== 'undefined',
                acfStateBefore: JSON.parse(JSON.stringify(acfState))
            }, 'acf');
        }
        
        $.ajax({
            url: slmmInterlinkingData.ajax_url,
            type: 'POST',
            data: {
                action: 'slmm_load_acf_settings',
                nonce: slmmInterlinkingData.nonce
            },
            timeout: 10000, // 10 second timeout
            beforeSend: function(xhr, settings) {
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('AJAX request starting', settings, 'acf');
                }
            },
            success: function(response, textStatus, xhr) {
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('AJAX response received', {
                        status: textStatus,
                        response: response
                    }, 'acf');
                }
                
                if (response && response.success) {
                    if (typeof SlmmDebugLogger !== 'undefined') {
                        SlmmDebugLogger.success('Response successful', response.data.settings, 'acf');
                        SlmmDebugLogger.log('Debug info', response.data.debug_info, 'acf');
                    }
                    
                    acfState.settings = response.data.settings;
                    
                    // Log critical values
                    if (typeof SlmmDebugLogger !== 'undefined') {
                        SlmmDebugLogger.log('Parsed settings', {enabled: acfState.settings.enabled, field_name: acfState.settings.field_name}, 'acf');
                    }
                    
                    applyLoadedSettings();
                    if (typeof SlmmDebugLogger !== 'undefined') {
                        SlmmDebugLogger.success('Settings loaded and applied successfully', null, 'acf');
                    }
                } else {
                    debug.error('ACF Integration', 'Server returned failure', response?.data || response);
                    showValidationMessage('Failed to load ACF settings', 'error');
                }
            },
            error: function(xhr, status, error) {
                debug.error('ACF Integration', 'AJAX request failed', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                
                // Try to parse response for more details
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    debug.error('ACF Integration', 'Parsed error response', errorResponse);
                } catch (parseError) {
                    debug.error('ACF Integration', 'Could not parse error response');
                }
                
                // Set default settings on error to prevent system from breaking
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.warn('Setting default settings due to load error', null, 'acf');
                }
                acfState.settings = {
                    enabled: false,
                    field_name: '',
                    acf_plugin_active: false,
                    can_manage_settings: false,
                    load_error: true
                };
                
                // Still apply settings even on error (will be defaults)
                applyLoadedSettings();
                
                // Show error message if elements are available
                if (elements.validateButton && elements.validateButton.length > 0) {
                    showValidationMessage('Error loading ACF settings', 'error');
                }
            },
            complete: function(xhr, status) {
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('AJAX request completed', {status: status}, 'acf');
                }
            }
        });
    }

    /**
     * Apply loaded settings to UI
     */
    function applyLoadedSettings() {
        const settings = acfState.settings;
        
        debug.log('ACF Integration', 'Applying loaded settings to UI');
        debug.log('ACF Integration', 'Settings to apply', settings);
        debug.log('ACF Integration', 'UI elements status:');
        debug.log('ACF Integration', 'UI elements check - Toggle: ' + (elements.toggleCheckbox.length > 0) + ', Field: ' + (elements.fieldInput.length > 0) + ', Validate: ' + (elements.validateButton.length > 0));
        
        if (settings.enabled) {
            debug.log('ACF Integration', '🎨 Enabling ACF mode...');
            
            // CRITICAL FIX: Set field name FIRST before any events trigger
            if (settings.field_name) {
                debug.log('ACF Integration', '🎨 Setting field name FIRST to prevent save timing bug: ' + settings.field_name);
                acfState.fieldName = settings.field_name;
                elements.fieldInput.val(settings.field_name);
                debug.log('ACF Integration', '🎨 Field input value after setting: ' + elements.fieldInput.val());
            }
            
            // Set validation status if provided (prevents auto-validation)
            if (settings.validated !== undefined) {
                acfState.validated = Boolean(settings.validated);
                debug.log('ACF Integration', '🎨 Loaded validation status: ' + acfState.validated);
            }
            
            // Enable ACF mode
            acfState.enabled = true;
            
            debug.log('ACF Integration', '🎨 Setting toggle checkbox to checked');
            elements.toggleCheckbox.prop('checked', true);
            
            debug.log('ACF Integration', '🎨 Triggering change event on toggle (field name already set)');
            elements.toggleCheckbox.trigger('change');
            
            // Only validate if not already validated or field name missing
            if (settings.field_name && !acfState.validated) {
                debug.log('ACF Integration', '🎨 Field not previously validated - validating now: ' + settings.field_name);
                validateACFField(settings.field_name);
            } else if (settings.field_name && acfState.validated) {
                debug.log('ACF Integration', '🎨 Field already validated - skipping validation, triggering tree refresh');
                
                // Field is already validated, just show the UI state and refresh tree
                showValidationMessage('✓ Valid', 'success');
                collapseFieldInput();
                showACFModeIndicator();
                
                // CRITICAL FIX: Mark for refresh when tree loads instead of immediate refresh
                // This handles the case where settings load before the tree is rendered
                acfState.needsRefreshOnTreeLoad = true;
                debug.log('ACF Integration', 'Marked for refresh when tree loads');
                
                // Also try immediate refresh in case tree is already available
                setTimeout(() => {
                    if (acfState.enabled) {
                        const treeNodes = document.querySelectorAll('.slmm-tree-node[data-node-id]');
                        if (treeNodes.length > 0) {
                            debug.success('ACF Integration', 'Tree nodes found - applying ACF immediately');
                            refreshTreeWithACFData();
                        } else {
                            debug.log('ACF Integration', 'Tree not ready yet - will refresh when tree loads');
                        }
                    }
                }, 300); // Increased delay to give tree more time to load
            } else {
                debug.log('ACF Integration', 'No field name in settings');
            }
        } else {
            debug.log('ACF Integration', 'ACF mode disabled in settings');
        }
        
        // Check if user can manage settings
        if (!settings.can_manage_settings) {
            debug.warn('ACF Integration', 'User cannot manage settings - enabling read-only mode');
            elements.toggleCheckbox.prop('disabled', true);
            elements.fieldInput.prop('disabled', true);
            elements.validateButton.prop('disabled', true);
            showValidationMessage('Read-only mode', 'warning');
        } else {
            debug.log('ACF Integration', 'User can manage settings');
        }
        
        // Check if ACF plugin is active
        if (!settings.acf_plugin_active) {
            debug.warn('ACF Integration', 'ACF plugin not active');
            showValidationMessage('ACF plugin not active', 'error');
            elements.toggleCheckbox.prop('disabled', true);
        } else {
            debug.log('ACF Integration', 'ACF plugin is active');
        }
        
        // Final state verification
        debug.log('ACF Integration', 'Final UI state after applying settings:');
        debug.log('ACF Integration', '  - Toggle checked: ' + elements.toggleCheckbox.prop('checked'));
        debug.log('ACF Integration', '  - Field input value: ' + elements.fieldInput.val());
        debug.log('ACF Integration', '  - acfState.enabled: ' + acfState.enabled);
        debug.log('ACF Integration', '  - acfState.fieldName: ' + acfState.fieldName);
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.success('Settings applied to UI successfully', null, 'acf');
        }
    }

    /**
     * Handle ACF toggle switch change
     */
    function handleACFToggleChange() {
        const isChecked = elements.toggleCheckbox.is(':checked');
        acfState.enabled = isChecked;
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Toggle changed to', {isChecked: isChecked}, 'acf');
        }
        
        if (isChecked) {
            // Show field input container
            elements.fieldInputContainer.slideDown(200);
            
            // If field was collapsed, expand it when enabling ACF
            if (elements.validateButton.hasClass('slmm-acf-collapsed')) {
                expandFieldInput();
            } else {
                elements.fieldInput.focus();
            }
        } else {
            // Hide field input container
            elements.fieldInputContainer.slideUp(200);
            acfState.validated = false;
            clearValidationMessage();
            
            // Expand field input if it was collapsed (for next time ACF is enabled)
            if (elements.validateButton.hasClass('slmm-acf-collapsed')) {
                expandFieldInput();
            }
            
            // Hide visual feedback
            hideACFModeIndicator();
            removeACFNodeBadges();
            
            // Revert to original titles if we had ACF titles active
            if (acfState.treeData) {
                revertToOriginalTitles();
            }
        }
        
        // Save settings
        saveACFSettings();
        
        // Trigger state synchronization event
        triggerACFModeChangeEvent();
    }

    /**
     * Handle field name input change
     */
    function handleFieldNameChange() {
        const fieldName = elements.fieldInput.val().trim();
        acfState.fieldName = fieldName;
        acfState.validated = false;
        
        // Clear validation status when field changes
        clearValidationMessage();
        
        // If field was collapsed and user is typing, expand it for better UX
        if (elements.validateButton.hasClass('slmm-acf-collapsed') && fieldName.length > 0) {
            debug.log('ACF Integration', 'Field being modified - preparing for re-validation');
            // Don't fully expand, just prepare for re-validation
        }
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Field name changed to', {fieldName: fieldName}, 'acf');
        }
        
        // Save settings
        saveACFSettings();
        
        // Trigger state synchronization event
        triggerACFModeChangeEvent();
    }

    /**
     * Handle validate button click
     */
    function handleValidateButtonClick() {
        // CRITICAL: Enforce toggle state - prevent validation button clicks when ACF mode is disabled
        if (!acfState.enabled) {
            debug.warn('ACF Integration', 'Validate button clicked but ACF mode is disabled');
            showValidationMessage('Enable ACF mode first', 'error');
            return;
        }
        
        const fieldName = elements.fieldInput.val().trim();
        
        if (!fieldName) {
            showValidationMessage('Please enter a field name', 'error');
            return;
        }
        
        validateACFField(fieldName);
    }

    /**
     * Validate ACF field existence and accessibility
     */
    function validateACFField(fieldName) {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Validating field', {fieldName: fieldName}, 'acf');
        }
        
        // CRITICAL: Enforce toggle state - prevent validation when ACF mode is disabled
        if (!acfState.enabled) {
            debug.warn('ACF Integration', 'Cannot validate - ACF mode is disabled');
            showValidationMessage('ACF mode is disabled', 'error');
            return;
        }
        
        // Additional safety check for field name
        if (!fieldName || fieldName.trim().length === 0) {
            debug.warn('ACF Integration', 'Cannot validate - field name is empty');
            showValidationMessage('Please enter a field name', 'error');
            return;
        }
        
        showValidationMessage('Validating...', 'loading');
        elements.validateButton.prop('disabled', true);
        
        $.ajax({
            url: slmmInterlinkingData.ajax_url,
            type: 'POST',
            data: {
                action: 'slmm_validate_acf_field',
                field_name: fieldName,
                nonce: slmmInterlinkingData.nonce
            },
            success: function(response) {
                elements.validateButton.prop('disabled', false);
                
                if (response.success) {
                    const data = response.data;
                    
                    if (data.field_exists && data.field_suitable) {
                        acfState.validated = true;
                        showValidationMessage('✓ Valid', 'success');
                        if (typeof SlmmDebugLogger !== 'undefined') {
                            SlmmDebugLogger.success('Field validated successfully', null, 'acf');
                        }
                        
                        // CRITICAL: Save validation status to database immediately
                        if (typeof SlmmDebugLogger !== 'undefined') {
                            SlmmDebugLogger.log('Saving validation status to database', null, 'acf');
                        }
                        saveACFSettings();
                        
                        // Collapse field input after successful validation
                        debug.log('ACF Integration', 'Collapsing field input after successful validation');
                        collapseFieldInput();
                        
                        // Show ACF mode indicator
                        if (acfState.enabled && acfState.fieldName) {
                            showACFModeIndicator();
                        }
                        
                        // Trigger tree refresh with ACF data if tree is already loaded
                        if (typeof SlmmDebugLogger !== 'undefined') {
                            SlmmDebugLogger.log('Checking conditions for tree refresh', null, 'acf');
                        }
                        debug.log('ACF Integration', 'Tree refresh conditions', {
                            acfStateEnabled: acfState.enabled,
                            currentPostType: window.currentPostType,
                            treeDataAvailable: typeof window.d3TreeData !== 'undefined'
                        });
                        
                        if (acfState.enabled) {
                            // Try to get current post type from multiple sources
                            let postType = window.currentPostType;
                            
                            if (!postType) {
                                // Fallback 1: Check active tab
                                const activeTab = $('.slmm-tab.active');
                                if (activeTab.length > 0) {
                                    postType = activeTab.data('post-type') || activeTab.attr('data-post-type');
                                    if (typeof SlmmDebugLogger !== 'undefined') {
                                        SlmmDebugLogger.log('Detected post type from active tab', {postType: postType}, 'acf');
                                    }
                                }
                            }
                            
                            if (!postType) {
                                // Fallback 2: Default to 'page' (most common case from logs)
                                postType = 'page';
                                if (typeof SlmmDebugLogger !== 'undefined') {
                                    SlmmDebugLogger.log('Using default post type', {postType: postType}, 'acf');
                                }
                            }
                            
                            if (typeof SlmmDebugLogger !== 'undefined') {
                                SlmmDebugLogger.log('Final post type for refresh', {postType: postType}, 'acf');
                            }
                            
                            // Set window.currentPostType for future use
                            window.currentPostType = postType;
                            
                            // Always refresh if we have ACF validation and tree exists
                            if (typeof window.d3TreeData !== 'undefined' || document.querySelector('.slmm-tree-node')) {
                                debug.success('ACF Integration', 'Tree exists - triggering ACF refresh');
                                refreshTreeWithACFData();
                            } else {
                                debug.log('ACF Integration', 'Tree not yet loaded - will refresh when tree loads');
                                // Set flag to refresh when tree loads
                                acfState.needsRefreshOnTreeLoad = true;
                                
                                // Add fallback mechanism - check for tree every 500ms for up to 5 seconds
                                let treeCheckAttempts = 0;
                                const maxTreeCheckAttempts = 10; // 5 seconds total
                                
                                const treeCheckInterval = setInterval(() => {
                                    treeCheckAttempts++;
                                    debug.log('ACF Integration', `Tree check attempt ${treeCheckAttempts}/${maxTreeCheckAttempts}`);
                                    
                                    if (typeof window.d3TreeData !== 'undefined' || document.querySelector('.slmm-tree-node')) {
                                        debug.success('ACF Integration', 'Tree found via fallback check - triggering ACF refresh');
                                        clearInterval(treeCheckInterval);
                                        refreshTreeWithACFData();
                                        acfState.needsRefreshOnTreeLoad = false;
                                    } else if (treeCheckAttempts >= maxTreeCheckAttempts) {
                                        debug.warn('ACF Integration', 'Tree check timeout - will wait for tree events');
                                        clearInterval(treeCheckInterval);
                                    }
                                }, 500);
                            }
                        }
                    } else if (data.field_exists && !data.field_suitable) {
                        acfState.validated = false;
                        showValidationMessage('⚠ Unsuitable field type', 'warning');
                        debug.warn('ACF Integration', 'Field exists but unsuitable type: ' + data.field_type);
                    } else {
                        acfState.validated = false;
                        showValidationMessage('✗ Field not found', 'error');
                        debug.warn('ACF Integration', 'Field does not exist');
                    }
                } else {
                    acfState.validated = false;
                    showValidationMessage('Validation failed', 'error');
                    debug.error('ACF Integration', 'Validation failed', response.data);
                }
            },
            error: function(xhr, status, error) {
                elements.validateButton.prop('disabled', false);
                acfState.validated = false;
                showValidationMessage('Validation error', 'error');
                debug.error('ACF Integration', 'AJAX error during validation', error);
            }
        });
    }

    /**
     * Save ACF settings to server
     */
    function saveACFSettings() {
        const settings = {
            acf_enabled: acfState.enabled,
            field_name: acfState.fieldName,
            validated: acfState.validated
        };
        
        debug.log('ACF Integration', 'Saving settings...');
        debug.log('ACF Integration', 'Current acfState:', JSON.parse(JSON.stringify(acfState)));
        debug.log('ACF Integration', 'Settings to save:', settings);
        
        $.ajax({
            url: slmmInterlinkingData.ajax_url,
            type: 'POST',
            data: {
                action: 'slmm_save_acf_settings',
                ...settings,
                nonce: slmmInterlinkingData.nonce
            },
            timeout: 10000, // 10 second timeout
            beforeSend: function(xhr, ajaxSettings) {
                debug.log('ACF Integration', 'Save AJAX request starting...', ajaxSettings.data);
            },
            success: function(response, textStatus, xhr) {
                debug.success('ACF Integration', 'Save response received');
                debug.info('ACF Integration', 'Response status: ' + textStatus);
                debug.info('ACF Integration', 'Full response', response);
                
                if (response && response.success) {
                    debug.success('ACF Integration', 'Settings saved successfully');
                    debug.success('ACF Integration', 'Server confirmed settings', response.data.settings);
                    debug.success('ACF Integration', 'Database updated: ' + response.data.updated);
                    debug.success('ACF Integration', 'Verified settings', response.data.verified_settings);
                    debug.success('ACF Integration', 'Save successful: ' + response.data.save_successful);
                    
                    // Request cross-tab sync for multi-tab consistency
                    requestCrossTabSync();
                } else {
                    debug.error('ACF Integration', 'Server returned save failure');
                    debug.error('ACF Integration', 'Error details', response?.data || response);
                }
            },
            error: function(xhr, status, error) {
                debug.error('ACF Integration', 'Save AJAX request failed');
                debug.error('ACF Integration', 'Save AJAX error details', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText
                });
                
                // Try to parse response for more details
                try {
                    const errorResponse = JSON.parse(xhr.responseText);
                    debug.error('ACF Integration', 'Parsed save error response', errorResponse);
                } catch (parseError) {
                    debug.error('ACF Integration', 'Could not parse save error response');
                }
            },
            complete: function(xhr, status) {
                debug.success('ACF Integration', 'Save AJAX request completed with status: ' + status);
            }
        });
    }

    /**
     * Refresh tree data with ACF field inclusion
     */
    function refreshTreeWithACFData() {
        if (!acfState.enabled || !acfState.validated || !acfState.fieldName) {
            debug.log('ACF Integration', 'Cannot refresh - ACF not properly configured');
            return;
        }
        
        debug.log('ACF Integration', 'Refreshing tree with ACF data...');
        
        // Show refresh overlay
        showRefreshOverlay('Loading ACF titles...');
        
        // Store original node data before swapping
        storeOriginalNodeData();
        
        // Get current post type from global context
        const postType = window.currentPostType || 'page';
        
        // Load tree data with ACF inclusion
        $.ajax({
            url: slmmInterlinkingData.ajax_url,
            type: 'POST',
            data: {
                action: 'slmm_load_silo_pages',
                post_type: postType,
                page: 1,
                per_page: 100, // MEMORY EFFICIENT: Reduced from 1000 to 100 for large sites
                include_acf: true,
                acf_field_name: acfState.fieldName,
                nonce: slmmInterlinkingData.nonce
            },
            success: function(response) {
                hideRefreshOverlay();
                
                debug.log('ACF Integration', 'AJAX Response received:');
                debug.info('ACF Integration', 'Response status: ' + response.success);
                debug.info('ACF Integration', 'Full response', response);
                
                if (response.success) {
                    debug.info('ACF Integration', 'Response data structure', response.data);
                    debug.info('ACF Integration', 'Pages data', response.data.pages);
                    debug.info('ACF Integration', 'Pages data type: ' + typeof response.data.pages);
                    debug.info('ACF Integration', 'Pages data length/keys: ' + (response.data.pages ? Object.keys(response.data.pages).length : 'undefined'));
                    
                    acfState.treeData = response.data.pages;
                    
                    // Debug what we actually stored
                    debug.info('ACF Integration', 'Stored treeData', acfState.treeData);
                    debug.info('ACF Integration', 'TreeData type: ' + typeof acfState.treeData);
                    debug.info('ACF Integration', 'TreeData keys', acfState.treeData ? Object.keys(acfState.treeData) : 'undefined');
                    
                    // Debug ACF content in the data
                    if (acfState.treeData && typeof acfState.treeData === 'object') {
                        let acfContentCount = 0;
                        let totalPosts = Object.keys(acfState.treeData).length;
                        
                        Object.keys(acfState.treeData).forEach(nodeId => {
                            const nodeData = acfState.treeData[nodeId];
                            if (typeof SlmmDebugLogger !== 'undefined') {
                                SlmmDebugLogger.log(`Post ${nodeId} ACF data`, {
                                    title: nodeData.title,
                                    acf_title: nodeData.acf_title,
                                    display_title: nodeData.display_title,
                                    has_acf_content: nodeData.has_acf_content,
                                    acf_field_name: nodeData.acf_field_name
                                }, 'acf');
                            }
                            
                            if (nodeData.has_acf_content) {
                                acfContentCount++;
                            }
                        });
                        
                        debug.info('ACF Integration', `ACF Summary - ${acfContentCount}/${totalPosts} posts have ACF content`);
                    }
                    
                    applyACFTitlesToTree();
                    addACFNodeBadges();
                    debug.success('ACF Integration', 'Tree refreshed with ACF data');
                } else {
                    debug.error('ACF Integration', 'Failed to refresh tree data', response.data);
                    showValidationMessage('Failed to load ACF data', 'error');
                }
            },
            error: function(xhr, status, error) {
                hideRefreshOverlay();
                debug.error('ACF Integration', 'AJAX error refreshing tree', error);
                showValidationMessage('Error loading ACF data', 'error');
            }
        });
    }

    /**
     * Store original node data before ACF title swapping
     */
    function storeOriginalNodeData() {
        if (typeof window.d3TreeData === 'undefined' || !window.d3TreeData) {
            return;
        }
        
        // Store original titles
        Object.keys(window.d3TreeData).forEach(nodeId => {
            const nodeData = window.d3TreeData[nodeId];
            if (nodeData && nodeData.title) {
                acfState.originalNodeData.set(parseInt(nodeId), {
                    originalTitle: nodeData.title,
                    originalName: nodeData.name || nodeData.title
                });
            }
        });
        
        debug.success('ACF Integration', `Original node data stored for ${acfState.originalNodeData.size} nodes`);
    }

    /**
     * Apply ACF titles to D3 tree nodes - DOM-first approach
     */
    function applyACFTitlesToTree() {
        debug.log('ACF Title', 'applyACFTitlesToTree called');
        debug.log('ACF Title', 'acfState.treeData exists', !!acfState.treeData);
        debug.log('ACF Title', 'acfState.treeData keys', acfState.treeData ? Object.keys(acfState.treeData).length : 0);
        
        if (!acfState.treeData) {
            debug.warn('ACF Title', 'No acfState.treeData available');
            return;
        }
        
        // CRITICAL FIX: Use DOM-based detection instead of non-existent window.d3TreeData
        // Check if tree nodes are available in the DOM
        const treeNodes = document.querySelectorAll('.slmm-tree-node[data-node-id]');
        debug.log('ACF Title', `Found ${treeNodes.length} tree nodes in DOM`);
        
        if (treeNodes.length === 0) {
            debug.log('ACF Title', 'No tree nodes in DOM yet - implementing smart retry logic');
            
            // Smart retry mechanism - wait for DOM nodes to be available
            let retryCount = 0;
            const maxRetries = 8;
            const baseRetryInterval = 250; // Base interval in ms
            
            const retryApplyTitles = () => {
                retryCount++;
                const currentRetryInterval = baseRetryInterval * retryCount; // Progressive delay
                debug.log('ACF Integration', `Retry attempt ${retryCount}/${maxRetries} - checking for DOM nodes`);
                
                const currentNodes = document.querySelectorAll('.slmm-tree-node[data-node-id]');
                
                if (currentNodes.length > 0) {
                    debug.success('ACF Integration', 'DOM nodes found on retry - applying titles');
                    applyACFTitlesToTree(); // Recursive call now that nodes exist
                    return;
                }
                
                if (retryCount < maxRetries) {
                    debug.log('ACF Integration', `Still waiting for DOM nodes, retry in ${currentRetryInterval}ms`);
                    setTimeout(retryApplyTitles, currentRetryInterval);
                } else {
                    debug.warn('ACF Integration', 'Max retries reached - DOM nodes not available. Tree may have failed to load or ACF was triggered too early.');
                }
            };
            
            setTimeout(retryApplyTitles, baseRetryInterval);
            return;
        }
        
        // Apply ACF titles directly to DOM elements (primary method)
        applyACFTitlesDirectlyToDOM();
    }
    
    /**
     * Apply ACF titles directly to DOM elements (fallback when window.d3TreeData unavailable)
     */
    function applyACFTitlesDirectlyToDOM() {
        if (!acfState.treeData) {
            debug.warn('ACF Title', 'No acfState.treeData for DOM-based title application');
            return;
        }
        
        debug.log('ACF Title', 'Applying ACF titles directly to DOM elements');
        debug.log('ACF Title', 'Using D3 data-bound approach since data-node-id attributes may not exist');
        
        let updatedNodes = 0;
        let totalACFNodes = 0;
        
        // Process each post with ACF data
        Object.keys(acfState.treeData).forEach(nodeId => {
            const acfNodeData = acfState.treeData[nodeId];
            
            if (acfNodeData.has_acf_content) {
                totalACFNodes++;
                debug.log('ACF Title', `Processing node ${nodeId} with ACF content: "${acfNodeData.display_title}"`);
                
                let titleElement = null;
                
                // METHOD 1: D3 data-bound approach (most reliable)
                try {
                    if (typeof d3 !== 'undefined') {
                        debug.log('ACF Title', `Trying D3 data-bound approach for node ${nodeId}`);
                        
                        // Find the node by its data.id using D3's data binding
                        const d3Node = d3.selectAll('.slmm-tree-node')
                            .filter(function(d) {
                                return d && d.data && d.data.id == nodeId;
                            });
                        
                        if (!d3Node.empty()) {
                            const titleSelection = d3Node.select('.slmm-node-title');
                            if (!titleSelection.empty()) {
                                titleElement = titleSelection.node();
                                debug.success('ACF Title', `Found title via D3 data-binding for node ${nodeId}`);
                            }
                        }
                    }
                } catch (e) {
                    debug.warn('ACF Title', `D3 data-bound approach failed for node ${nodeId}: ${e.message}`);
                }
                
                // METHOD 2: Direct DOM selectors (fallback)
                if (!titleElement) {
                    debug.log('ACF Title', `Trying direct DOM selectors for node ${nodeId}`);
                    
                    const selectors = [
                        // Try data-node-id first (may exist from QuickBulk)
                        `[data-node-id="${nodeId}"] .slmm-node-title`,
                        `[data-node-id="${nodeId}"] text.slmm-node-title`,
                        `g[data-node-id="${nodeId}"] text`,
                        // Try class-based approaches
                        `.slmm-tree-node .slmm-node-title`,
                        `g.slmm-tree-node text.slmm-node-title`,
                        // Generic text element approaches
                        'svg .slmm-node-title',
                        'svg text.slmm-node-title'
                    ];
                    
                    for (const selector of selectors) {
                        const elements = document.querySelectorAll(selector);
                        
                        // For generic selectors, we need to find the right element
                        if (elements.length > 0) {
                            if (selector.includes(nodeId)) {
                                // Specific selector - take first match
                                titleElement = elements[0];
                                debug.success('ACF Title', `Found title via selector "${selector}" for node ${nodeId}`);
                                break;
                            } else {
                                // Generic selector - need to match by content or position
                                for (const element of elements) {
                                    // Check if this text element contains content that matches our post
                                    const textContent = element.textContent || '';
                                    const originalTitle = acfNodeData.title || '';
                                    
                                    // Match by partial title content (first 20 chars)
                                    if (originalTitle.length > 0 && textContent.includes(originalTitle.substring(0, 20))) {
                                        titleElement = element;
                                        debug.success('ACF Title', `Found title via content match "${selector}" for node ${nodeId}`);
                                        break;
                                    }
                                }
                                if (titleElement) break;
                            }
                        }
                    }
                }
                
                // METHOD 3: Debug comprehensive search
                if (!titleElement) {
                    debug.log('ACF Integration', `Comprehensive debug search for node ${nodeId}`, {
                        treeNodeElements: document.querySelectorAll('.slmm-tree-node').length,
                        nodeTitleElements: document.querySelectorAll('.slmm-node-title').length,
                        svgTextElements: document.querySelectorAll('svg text').length,
                        allTitleContent: Array.from(document.querySelectorAll('.slmm-node-title')).map(el => el.textContent)
                    });
                }
                
                if (titleElement) {
                    // Store original title before changing
                    if (!acfState.originalNodeData.has(nodeId)) {
                        acfState.originalNodeData.set(nodeId, {
                            title: titleElement.textContent,
                            display_title: titleElement.textContent
                        });
                    }
                    
                    // Update the title text content with 34-character truncation
                    let displayTitle = acfNodeData.display_title;
                    const maxLength = 34;
                    if (displayTitle.length > maxLength) {
                        displayTitle = displayTitle.substring(0, maxLength - 3) + '...';
                    }
                    titleElement.textContent = displayTitle;
                    titleElement.setAttribute('data-acf-title', 'true');
                    titleElement.setAttribute('data-original-title', acfNodeData.title);
                    
                    updatedNodes++;
                    debug.success('ACF Title', `Updated node ${nodeId} title from "${acfNodeData.title}" to: "${acfNodeData.display_title}"`);
                } else {
                    debug.warn('ACF Title', `Could not find title element for node ${nodeId} using any method`);
                }
            }
        });
        
        debug.success('ACF Title', `DOM-based title update complete - ${updatedNodes}/${totalACFNodes} ACF titles applied`);
        
        if (updatedNodes > 0) {
            // Show visual feedback
            showValidationMessage(`✓ ${updatedNodes} titles updated with ACF content`, 'success');
        } else if (totalACFNodes > 0) {
            showValidationMessage(`⚠️ Could not update ACF titles - DOM structure may have changed`, 'warning');
        }
    }

    /**
     * Revert to original titles (when ACF is disabled) - DOM-based approach
     */
    function revertToOriginalTitles() {
        if (acfState.originalNodeData.size === 0) {
            debug.log('ACF Integration', 'No original data to revert');
            return;
        }
        
        debug.log('ACF Integration', `Reverting to original titles for ${acfState.originalNodeData.size} nodes`);
        let revertedNodes = 0;
        
        acfState.originalNodeData.forEach((originalData, nodeId) => {
            debug.log('ACF Integration', `Reverting node ${nodeId} to original title: "${originalData.title}"`);
            
            let titleElement = null;
            
            // METHOD 1: D3 data-bound approach (most reliable)
            try {
                if (typeof d3 !== 'undefined') {
                    const d3Node = d3.selectAll('.slmm-tree-node')
                        .filter(function(d) {
                            return d && d.data && d.data.id == nodeId;
                        });
                    
                    if (!d3Node.empty()) {
                        const titleSelection = d3Node.select('.slmm-node-title');
                        if (!titleSelection.empty()) {
                            titleElement = titleSelection.node();
                            debug.success('ACF Integration', `Found title element for revert via D3 for node ${nodeId}`);
                        }
                    }
                }
            } catch (e) {
                debug.warn('ACF Integration', `D3 approach failed for revert of node ${nodeId}: ${e.message}`);
            }
            
            // METHOD 2: Direct DOM approach (fallback)
            if (!titleElement) {
                // Try to find elements that have ACF data attributes
                const acfTitleElements = document.querySelectorAll('[data-acf-title="true"]');
                for (const element of acfTitleElements) {
                    const originalTitle = element.getAttribute('data-original-title');
                    if (originalTitle === originalData.title) {
                        titleElement = element;
                        debug.success('ACF Integration', `Found title element for revert via DOM attributes for node ${nodeId}`);
                        break;
                    }
                }
            }
            
            if (titleElement) {
                // Revert to original title
                titleElement.textContent = originalData.title;
                titleElement.removeAttribute('data-acf-title');
                titleElement.removeAttribute('data-original-title');
                
                revertedNodes++;
                debug.success('ACF Integration', `Reverted node ${nodeId} from ACF title back to: "${originalData.title}"`);
            } else {
                debug.warn('ACF Integration', `Could not find title element to revert for node ${nodeId}`);
            }
        });
        
        debug.success('ACF Integration', `Reverted ${revertedNodes} nodes to original titles`);
        
        // Show visual feedback
        if (revertedNodes > 0) {
            showValidationMessage(`✓ ${revertedNodes} titles reverted to original content`, 'success');
        }
        
        // Clear the stored original data
        acfState.originalNodeData.clear();
    }

    /**
     * Collapse field input after successful validation
     */
    function collapseFieldInput() {
        debug.log('ACF Integration', 'Collapsing field input...');
        
        // Hide the field input but keep the label and validation button visible
        if (elements.fieldInput.length > 0) {
            elements.fieldInput.slideUp(300, function() {
                debug.log('ACF Integration', 'Field input collapsed');
            });
            
            // Optionally add a small "expand" button or make the validation button clickable to expand
            const expandHint = $('<span class="slmm-acf-expand-hint" style="font-size: 11px; color: #666; margin-left: 8px;">(click to modify)</span>');
            
            // Remove any existing expand hints first
            $('.slmm-acf-expand-hint').remove();
            
            // Add expand functionality to validation button
            elements.validateButton.addClass('slmm-acf-collapsed').off('click.expand').on('click.expand', function(e) {
                if ($(this).hasClass('slmm-acf-collapsed')) {
                    e.preventDefault();
                    expandFieldInput();
                    return false;
                }
            });
            
            // Add the expand hint after the button
            elements.validateButton.after(expandHint);
        }
    }
    
    /**
     * Expand field input for modification
     */
    function expandFieldInput() {
        debug.log('ACF Integration', 'Expanding field input...');
        
        if (elements.fieldInput.length > 0) {
            elements.fieldInput.slideDown(300, function() {
                elements.fieldInput.focus();
                debug.success('ACF Integration', 'Field input expanded');
            });
            
            // Remove expand hint and click handler
            $('.slmm-acf-expand-hint').remove();
            elements.validateButton.removeClass('slmm-acf-collapsed').off('click.expand');
            
            // Restore original validate button functionality
            elements.validateButton.off('click.expand').on('click', handleValidateButtonClick);
        }
    }

    /**
     * Show validation message with appropriate styling
     */
    function showValidationMessage(message, type) {
        // Update button with status
        elements.validateButton
            .removeClass('success error warning loading')
            .addClass(type);
        
        // Update button text and icon based on status
        const icon = elements.validateButton.find('.dashicons');
        const textSpan = elements.validateButton.contents().filter(function() {
            return this.nodeType === 3; // Text nodes
        });
        
        switch(type) {
            case 'loading':
                icon.removeClass().addClass('dashicons dashicons-update');
                elements.validateButton.html('<span class="dashicons dashicons-update"></span>Validating...');
                break;
            case 'success':
                icon.removeClass().addClass('dashicons dashicons-yes-alt');
                elements.validateButton.html('<span class="dashicons dashicons-yes-alt"></span>✓ Valid');
                break;
            case 'error':
                icon.removeClass().addClass('dashicons dashicons-dismiss');
                elements.validateButton.html('<span class="dashicons dashicons-dismiss"></span>✗ Invalid');
                break;
            case 'warning':
                icon.removeClass().addClass('dashicons dashicons-warning');
                elements.validateButton.html('<span class="dashicons dashicons-warning"></span>⚠ Warning');
                break;
            default:
                icon.removeClass().addClass('dashicons dashicons-yes-alt');
                elements.validateButton.html('<span class="dashicons dashicons-yes-alt"></span>Validate');
        }
        
        // Add visual feedback to field input
        elements.fieldInput
            .removeClass('validating valid invalid')
            .addClass(type === 'loading' ? 'validating' : type === 'success' ? 'valid' : type === 'error' ? 'invalid' : '');
        
        // Flash animation for success/error
        if (type === 'success') {
            $('.slmm-acf-controls-container').addClass('flash-success');
            setTimeout(() => {
                $('.slmm-acf-controls-container').removeClass('flash-success');
            }, 600);
        } else if (type === 'error') {
            $('.slmm-acf-controls-container').addClass('flash-error');
            setTimeout(() => {
                $('.slmm-acf-controls-container').removeClass('flash-error');
            }, 600);
        }
    }

    /**
     * Clear validation message
     */
    function clearValidationMessage() {
        elements.validationStatus.hide().text('');
        elements.fieldInput.removeClass('validating valid invalid');
        elements.validateButton.removeClass('loading');
    }

    /**
     * Show ACF mode indicator
     */
    function showACFModeIndicator() {
        let indicator = $('.slmm-acf-mode-indicator');
        
        if (indicator.length === 0) {
            // Create the indicator if it doesn't exist
            indicator = $(`
                <div class="slmm-acf-mode-indicator">
                    <span class="dashicons dashicons-admin-customizer"></span>
                    <span>ACF Mode: ${acfState.fieldName}</span>
                </div>
            `);
            $('body').append(indicator);
        } else {
            // Update existing indicator
            indicator.find('span:last-child').text(`ACF Mode: ${acfState.fieldName}`);
        }
        
        indicator.addClass('active');
        debug.success('ACF Integration', 'Mode indicator shown');
    }

    /**
     * Hide ACF mode indicator
     */
    function hideACFModeIndicator() {
        $('.slmm-acf-mode-indicator').removeClass('active');
        debug.success('ACF Integration', 'Mode indicator hidden');
    }

    /**
     * Show refresh overlay during tree operations
     */
    function showRefreshOverlay(message = 'Loading ACF titles...') {
        let overlay = $('.slmm-acf-refresh-overlay');
        
        if (overlay.length === 0) {
            // Create the overlay if it doesn't exist
            overlay = $(`
                <div class="slmm-acf-refresh-overlay">
                    <div class="slmm-acf-refresh-message">
                        <div class="slmm-acf-refresh-spinner"></div>
                        <span>${message}</span>
                    </div>
                </div>
            `);
            $('.slmm-interlinking-container').append(overlay);
        } else {
            // Update message
            overlay.find('.slmm-acf-refresh-message span').text(message);
        }
        
        overlay.addClass('active');
    }

    /**
     * Hide refresh overlay
     */
    function hideRefreshOverlay() {
        $('.slmm-acf-refresh-overlay').removeClass('active');
    }

    /**
     * Add visual badges to ACF-enabled nodes
     */
    function addACFNodeBadges() {
        if (!acfState.treeData || typeof window.d3TreeData === 'undefined') {
            return;
        }
        
        // Remove existing badges first
        d3.selectAll('.slmm-acf-node-badge').remove();
        
        let badgeCount = 0;
        
        Object.keys(acfState.treeData).forEach(nodeId => {
            const acfNodeData = acfState.treeData[nodeId];
            
            if (acfNodeData.has_acf_content) {
                // Find the corresponding D3 node element using multiple approaches
                let nodeElement = null;
                
                // METHOD 1: Try data-node-id selector (should work now that we added the attribute)
                nodeElement = d3.select(`[data-node-id="${nodeId}"]`);
                
                // METHOD 2: Fallback to D3 data-bound approach
                if (nodeElement.empty()) {
                    nodeElement = d3.selectAll('.slmm-tree-node')
                        .filter(function(d) {
                            return d && d.data && d.data.id == nodeId;
                        });
                }
                
                if (!nodeElement.empty()) {
                    // Add ACF class to node
                    nodeElement.classed('acf-enabled', true);
                    
                    // Add badge to node (using SVG rect for compatibility)
                    const badge = nodeElement.append('rect')
                        .attr('class', 'slmm-acf-node-badge')
                        .attr('width', 16)
                        .attr('height', 16)
                        .attr('x', 80)  // Position on the right side of node
                        .attr('y', -65) // Position at top
                        .attr('rx', 2)  // Rounded corners
                        .style('fill', '#10b981') // Green color for ACF
                        .style('stroke', '#059669')
                        .style('cursor', 'help')
                        .attr('title', `ACF Field: ${acfState.fieldName}`);
                    
                    // Add text label inside badge
                    nodeElement.append('text')
                        .attr('class', 'slmm-acf-badge-text')
                        .attr('x', 88)  // Center in badge
                        .attr('y', -55) // Center vertically
                        .style('fill', 'white')
                        .style('font-family', 'Arial, sans-serif')
                        .style('font-size', '10px')
                        .style('font-weight', 'bold')
                        .style('text-anchor', 'middle')
                        .style('dominant-baseline', 'middle')
                        .style('pointer-events', 'none')
                        .text('A');
                    
                    badgeCount++;
                }
            }
        });
        
        debug.success('ACF Integration', `Added badges to ${badgeCount} ACF-enabled nodes`);
    }

    /**
     * Remove ACF node badges and classes
     */
    function removeACFNodeBadges() {
        d3.selectAll('.slmm-acf-node-badge').remove();
        d3.selectAll('.slmm-tree-node').classed('acf-enabled', false);
        debug.success('ACF Integration', 'Removed ACF node badges');
    }

    /**
     * Show tooltip for ACF nodes
     */
    function showACFTooltip(element, message) {
        hideACFTooltip(); // Hide any existing tooltip
        
        const tooltip = $(`<div class="slmm-acf-tooltip">${message}</div>`);
        $('body').append(tooltip);
        
        // Position tooltip above the element
        const rect = element.getBoundingClientRect();
        tooltip.css({
            left: rect.left + (rect.width / 2) - (tooltip.outerWidth() / 2),
            top: rect.top - tooltip.outerHeight() - 8
        });
        
        // Show tooltip with animation
        setTimeout(() => {
            tooltip.addClass('show');
        }, 10);
    }

    /**
     * Hide ACF tooltip
     */
    function hideACFTooltip() {
        $('.slmm-acf-tooltip').remove();
    }

    /**
     * Hook into tree loading events (if available)
     */
    function hookIntoTreeEvents() {
        // Listen for tree data loaded event
        $(document).on('slmm_tree_data_loaded', function(event, treeData) {
            debug.log('ACF Integration', 'Tree data loaded event received');
            debug.log('ACF Integration', 'ACF state - enabled: ' + acfState.enabled + ' validated: ' + acfState.validated + ' needsRefresh: ' + acfState.needsRefreshOnTreeLoad);
            
            if (acfState.enabled && acfState.validated) {
                debug.log('ACF Integration', 'ACF is enabled and validated - refreshing tree with ACF data');
                
                // Delay to ensure tree is rendered before swapping titles
                setTimeout(() => {
                    refreshTreeWithACFData();
                }, 100);
                
                // Clear the flag since we're refreshing now
                acfState.needsRefreshOnTreeLoad = false;
            } else if (acfState.needsRefreshOnTreeLoad) {
                debug.log('ACF Integration', 'Tree loaded but ACF not ready yet - refresh will happen after validation');
            }
        });
        
        // Listen for post type change event
        $(document).on('slmm_post_type_changed', function(event, postType) {
            debug.log('ACF Integration', `Post type changed to: ${postType}`);
            
            if (acfState.enabled && acfState.validated) {
                // Clear original data when post type changes
                acfState.originalNodeData.clear();
                
                // Refresh with new post type data
                setTimeout(() => {
                    refreshTreeWithACFData();
                }, 100);
            }
        });
    }

    /**
     * Trigger ACF mode change event for component synchronization
     */
    function triggerACFModeChangeEvent() {
        // Add debugging to identify call source
        const stack = new Error().stack;
        const caller = stack.split('\n')[2]?.trim() || 'unknown';
        debug.log('ACF Integration', 'triggerACFModeChangeEvent called from: ' + caller);
        
        const acfSettings = {
            enabled: Boolean(acfState.enabled),
            fieldName: String(acfState.fieldName || ''),
            validated: Boolean(acfState.validated)
        };
        
        debug.log('ACF Integration', 'Broadcasting state change event', acfSettings);
        debug.log('ACF Integration', 'Current acfState for verification', JSON.parse(JSON.stringify(acfState)));
        
        // Validate that we have a proper settings object
        if (!acfSettings || typeof acfSettings !== 'object') {
            debug.error('ACF Integration', 'Invalid acfSettings object created, aborting broadcast');
            return;
        }
        
        // Ensure we always pass a valid object - never undefined
        try {
            // Only trigger jQuery event (Direct Editor uses this)
            $(document).trigger('slmm_acf_mode_changed', [acfSettings]);
            debug.log('ACF Integration', 'jQuery event triggered successfully with', acfSettings);
            
            // Remove CustomEvent to avoid duplicate events
            // The Direct Editor only listens to jQuery events
            
        } catch (error) {
            debug.error('ACF Integration', 'Error broadcasting state change:', error);
            debug.error('ACF Integration', 'Error details:', error.message);
            debug.error('ACF Integration', 'Settings that failed:', acfSettings);
        }
    }

    /**
     * Listen for external state synchronization requests
     */
    function listenForExternalSyncRequests() {
        // Listen for tree refresh requests
        $(document).on('slmm_request_acf_refresh', function() {
            debug.log('ACF Integration', 'External refresh request received');
            if (acfState.enabled && acfState.validated) {
                refreshTreeWithACFData();
            }
        });
        
        // Listen for ACF state queries
        $(document).on('slmm_request_acf_state', function(event) {
            debug.log('ACF Integration', 'State query received');
            triggerACFModeChangeEvent();
        });
        
        // Listen for forced ACF mode changes (from external components)
        $(document).on('slmm_force_acf_mode', function(event, settings) {
            debug.log('ACF Integration', 'Forced mode change received', settings);
            
            if (settings.enabled !== undefined) {
                acfState.enabled = settings.enabled;
                elements.toggleCheckbox.prop('checked', settings.enabled);
                
                if (settings.enabled) {
                    elements.fieldInputContainer.slideDown(200);
                } else {
                    elements.fieldInputContainer.slideUp(200);
                    hideACFModeIndicator();
                    removeACFNodeBadges();
                }
            }
            
            if (settings.fieldName !== undefined) {
                acfState.fieldName = settings.fieldName;
                elements.fieldInput.val(settings.fieldName);
            }
            
            // Save and broadcast changes
            saveACFSettings();
            triggerACFModeChangeEvent();
        });
    }

    /**
     * Synchronize with other components on initialization
     */
    function performInitialSync() {
        // Wait for other components to initialize
        setTimeout(() => {
            // Check if Direct Editor is available and sync with it
            if (typeof window.SLMMDirectEditor !== 'undefined') {
                debug.log('ACF Integration', 'Syncing with Direct Editor');
                triggerACFModeChangeEvent();
            }
            
            // Check if tree system is available
            if (typeof window.d3TreeData !== 'undefined') {
                debug.log('ACF Integration', 'Tree system detected');
                if (acfState.enabled && acfState.validated) {
                    refreshTreeWithACFData();
                }
            }
            
            debug.success('ACF Integration', 'Initial synchronization complete');
        }, 500);
    }

    /**
     * Handle component lifecycle events
     */
    function setupLifecycleHandlers() {
        // Create named functions that can be properly removed during cleanup

        // Handle page visibility changes
        window.acfVisibilityHandler = function() {
            if (!document.hidden && acfState.enabled) {
                // Page became visible - refresh ACF state if needed
                debug.log('ACF Integration', 'Page visible - checking ACF state');

                // Re-broadcast current state for any newly loaded components
                addACFTimeout(setTimeout(() => {
                    triggerACFModeChangeEvent();
                }, 100));
            }
        };
        document.addEventListener('visibilitychange', window.acfVisibilityHandler);

        // Handle window focus (for multi-tab scenarios)
        window.acfFocusHandler = function() {
            if (acfState.enabled) {
                // Window gained focus - ensure state consistency
                addACFTimeout(setTimeout(() => {
                    triggerACFModeChangeEvent();
                }, 100));
            }
        };
        window.addEventListener('focus', window.acfFocusHandler);

        // Handle storage events (for multi-tab synchronization)
        window.acfStorageHandler = function(e) {
            if (e.key === 'slmm_acf_sync_request') {
                // Another tab requested sync
                setTimeout(() => {
                    triggerACFModeChangeEvent();
                }, 50);
            }
        };
        window.addEventListener('storage', window.acfStorageHandler);
    }

    /**
     * Cross-tab synchronization support
     */
    function requestCrossTabSync() {
        try {
            localStorage.setItem('slmm_acf_sync_request', Date.now().toString());
            localStorage.removeItem('slmm_acf_sync_request');
        } catch (e) {
            // Storage not available - skip cross-tab sync
            debug.warn('ACF Integration', 'Cross-tab sync unavailable', e);
        }
    }

    /**
     * Public API for external access
     */
    window.SLMMACFIntegration = {
        // State getters
        isEnabled: () => acfState.enabled,
        getFieldName: () => acfState.fieldName,
        isValidated: () => acfState.validated,
        
        // Actions
        refreshWithACF: refreshTreeWithACFData,
        revertToOriginal: revertToOriginalTitles,
        validateField: validateACFField,
        
        // Settings management
        saveSettings: saveACFSettings,
        loadSettings: loadACFSettings,
        
        // State synchronization
        triggerSync: triggerACFModeChangeEvent,
        requestCrossTabSync: requestCrossTabSync,
        
        // External control API
        forceMode: (settings) => {
            $(document).trigger('slmm_force_acf_mode', [settings]);
        },
        
        requestRefresh: () => {
            $(document).trigger('slmm_request_acf_refresh');
        },
        
        requestState: () => {
            $(document).trigger('slmm_request_acf_state');
        }
    };

    /**
     * ==========================================
     * CRITICAL MEMORY LEAK FIX
     * Cleanup all document/window event listeners that accumulate
     * ==========================================
     */
    function cleanupACFIntegration() {
        if (window.SLMM && window.SLMM.debug) {
            SLMM.debug.log('ACF Cleanup', '🧹 Starting comprehensive ACF cleanup...');
        }

        // Remove all ACF-specific document event listeners that accumulate
        $(document).off('slmm_tree_data_loaded');
        $(document).off('slmm_post_type_changed');
        $(document).off('slmm_request_acf_refresh');
        $(document).off('slmm_request_acf_state');
        $(document).off('slmm_force_acf_mode');

        // Remove window event listeners
        if (typeof window.acfVisibilityHandler !== 'undefined') {
            document.removeEventListener('visibilitychange', window.acfVisibilityHandler);
            window.acfVisibilityHandler = null;
        }
        if (typeof window.acfFocusHandler !== 'undefined') {
            window.removeEventListener('focus', window.acfFocusHandler);
            window.acfFocusHandler = null;
        }
        if (typeof window.acfStorageHandler !== 'undefined') {
            window.removeEventListener('storage', window.acfStorageHandler);
            window.acfStorageHandler = null;
        }

        // Clear any pending timeouts
        if (typeof window.acfTimeouts !== 'undefined' && Array.isArray(window.acfTimeouts)) {
            window.acfTimeouts.forEach(timeoutId => clearTimeout(timeoutId));
            window.acfTimeouts = [];
        }

        // Reset ACF state
        if (typeof acfState !== 'undefined') {
            if (acfState.originalNodeData && typeof acfState.originalNodeData.clear === 'function') {
                acfState.originalNodeData.clear();
            }
        }

        if (window.SLMM && window.SLMM.debug) {
            SLMM.debug.success('ACF Cleanup', '✅ ACF cleanup completed - all event listeners removed');
        }
    }

    // Make cleanup function globally available
    window.cleanupACFIntegration = cleanupACFIntegration;

    // AUTOMATIC CLEANUP ON PAGE UNLOAD/NAVIGATION
    // This prevents memory leaks when users navigate away or refresh
    $(window).on('beforeunload unload pagehide', function() {
        if (window.SLMM && window.SLMM.debug) {
            SLMM.debug.log('ACF Cleanup', '🚪 Page unload detected - running ACF cleanup');
        }
        cleanupACFIntegration();
    });

    /**
     * Initialize when DOM is ready
     */
    $(document).ready(function() {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('DOM ready - starting initialization', {
                jqueryAvailable: typeof $ !== 'undefined',
                dataAvailable: typeof slmmInterlinkingData !== 'undefined'
            }, 'acf');
        }
        
        if (typeof slmmInterlinkingData !== 'undefined') {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('AJAX configuration available', {
                    ajaxUrl: slmmInterlinkingData.ajax_url,
                    nonceAvailable: typeof slmmInterlinkingData.nonce !== 'undefined'
                }, 'acf');
            }
        } else {
            debug.error('ACF Integration', 'slmmInterlinkingData not available - AJAX requests will fail');
        }
        
        // Add small delay to ensure other systems are initialized
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Starting initialization with 100ms delay', null, 'acf');
        }
        addACFTimeout(setTimeout(() => {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Delay completed, initializing now', null, 'acf');
            }
            initializeACFIntegration();
            hookIntoTreeEvents();
        }, 100));
    });

})(jQuery);