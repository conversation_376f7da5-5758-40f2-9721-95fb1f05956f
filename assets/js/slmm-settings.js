/**
 * SLMM Settings JavaScript
 * Handles tabbed interface and other interactive features
 */

jQuery(document).ready(function($) {
    'use strict';

    // Debug helper - ALWAYS include this pattern for standalone functions
    var debug = {
        log: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            }
        },
        success: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.success(category, message, data);
            }
        },
        warn: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            }
        },
        error: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            }
        },
        info: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.info(category, message, data);
            }
        }
    };

    // Initialize settings page
    initializeSettingsPage();

    function initializeSettingsPage() {
        initializeTabs();
        initializeFormHandling();
        initializeFileUpload();
        initializeFormValidation();
        initializeTooltips();
        initializeAnimations();
        initializeKeyboardShortcuts();
        initializeColorPickers();
        initializeSearchReplace();
    }

    // Tab functionality
    function initializeTabs() {
        const $tabButtons = $('.slmm-tab-button');
        const $tabPanes = $('.slmm-tab-pane');
        
        // Handle tab clicks
        $tabButtons.on('click', function(e) {
            e.preventDefault();
            
            const targetTab = $(this).data('tab');
            
            // Remove active class from all tabs and panes
            $tabButtons.removeClass('active');
            $tabPanes.removeClass('active');
            
            // Add active class to clicked tab and corresponding pane
            $(this).addClass('active');
            $('#' + targetTab).addClass('active');
            
            // Hide/show Save Settings button based on active tab
            const $saveButtonContainer = $('.slmm-form-actions');
            if (targetTab === 'export-import' || targetTab === 'lorem-detector' || targetTab === 'search-replace') {
                $saveButtonContainer.hide();
            } else {
                $saveButtonContainer.show();
            }
            
            // Save current tab in localStorage
            localStorage.setItem('slmm_active_tab', targetTab);
            
            // Trigger tab change event
            $(document).trigger('slmm:tabChanged', [targetTab]);
        });
        
        // Restore last active tab from localStorage
        const savedTab = localStorage.getItem('slmm_active_tab');
        if (savedTab && $('#' + savedTab).length) {
            $tabButtons.filter('[data-tab="' + savedTab + '"]').trigger('click');
        }
        
        // Handle hash navigation
        if (window.location.hash) {
            const hashTab = window.location.hash.substring(1);
            if ($('#' + hashTab).length) {
                $tabButtons.filter('[data-tab="' + hashTab + '"]').trigger('click');
            }
        }
        
        // Ensure Save Settings button is hidden on page load if export-import, lorem-detector, or search-replace tab is active
        const $activeTab = $('.slmm-tab-button.active');
        if ($activeTab.length && ($activeTab.data('tab') === 'export-import' || $activeTab.data('tab') === 'lorem-detector' || $activeTab.data('tab') === 'search-replace')) {
            $('.slmm-form-actions').hide();
        }
    }

    // Form handling
    function initializeFormHandling() {
        const $form = $('.slmm-settings-form');
        const $saveButton = $('.slmm-save-button');
        let isFormSubmitting = false;
        
        // Handle form submission
        $form.on('submit', function(e) {
            // Mark that form is being submitted to prevent beforeunload warning
            isFormSubmitting = true;
            
            // Remove changed state immediately to prevent beforeunload trigger
            $form.removeClass('changed');
            
            // Show loading state
            $form.addClass('saving');
            $saveButton.addClass('saving');
            
            // Create a timeout to ensure loading state shows even for fast submissions
            setTimeout(function() {
                // The form will submit normally, WordPress will handle the reload
            }, 500);
        });
        
        // Handle form changes
        $form.on('change input', 'input, textarea, select', function() {
            // Only mark as changed if not currently submitting
            if (!isFormSubmitting) {
                $form.addClass('changed');
                
                // Show unsaved changes indicator
                if (!$('.slmm-unsaved-indicator').length) {
                    $saveButton.parent().append('<span class="slmm-unsaved-indicator">• Unsaved changes</span>');
                }
            }
        });
        
        // Remove unsaved indicator on save
        $form.on('submit', function() {
            $('.slmm-unsaved-indicator').remove();
        });
        
        // Handle page unload with unsaved changes
        $(window).on('beforeunload', function() {
            // Only show warning if form has unsaved changes AND we're not submitting
            if ($form.hasClass('changed') && !isFormSubmitting) {
                return 'You have unsaved changes. Are you sure you want to leave?';
            }
        });
    }

    // File upload handling
    function initializeFileUpload() {
        const $fileInput = $('#slmm-import-file');
        const $fileLabel = $('.slmm-file-label');
        
        $fileInput.on('change', function() {
            const fileName = this.files[0] ? this.files[0].name : 'Choose settings file...';
            $fileLabel.find('span:last-child').text(fileName);
            
            if (this.files[0]) {
                $fileLabel.addClass('file-selected');
            } else {
                $fileLabel.removeClass('file-selected');
            }
        });
        
        // Drag and drop functionality
        $fileLabel.on('dragover dragenter', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).addClass('drag-over');
        });
        
        $fileLabel.on('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('drag-over');
        });
        
        $fileLabel.on('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            $(this).removeClass('drag-over');
            
            const files = e.originalEvent.dataTransfer.files;
            if (files.length > 0) {
                $fileInput[0].files = files;
                $fileInput.trigger('change');
            }
        });
    }

    // Form validation
    function initializeFormValidation() {
        // Real-time validation for API keys
        $('input[type="password"]').on('input', function() {
            const $input = $(this);
            const value = $input.val();
            const fieldName = $input.attr('name');
            
            // Remove existing validation classes
            $input.removeClass('valid invalid');
            
            if (value.length > 0) {
                if (fieldName.includes('openai') && value.startsWith('sk-')) {
                    $input.addClass('valid');
                } else if (fieldName.includes('openrouter') && value.startsWith('sk-or-')) {
                    $input.addClass('valid');
                } else if (value.length < 10) {
                    $input.addClass('invalid');
                }
            }
        });
        
        // URL validation for webhook URLs
        $('input[type="url"]').on('input', function() {
            const $input = $(this);
            const value = $input.val();
            
            $input.removeClass('valid invalid');
            
            if (value.length > 0) {
                try {
                    new URL(value);
                    $input.addClass('valid');
                } catch (e) {
                    $input.addClass('invalid');
                }
            }
        });
    }

    // Tooltips for complex fields
    function initializeTooltips() {
        // Add tooltips to complex fields
        $('[data-tooltip]').each(function() {
            const $element = $(this);
            const tooltip = $element.data('tooltip');
            
            $element.on('mouseenter focus', function() {
                showTooltip($element, tooltip);
            });
            
            $element.on('mouseleave blur', function() {
                hideTooltip();
            });
        });
    }

    function showTooltip($element, text) {
        const $tooltip = $('<div class="slmm-tooltip">' + text + '</div>');
        $('body').append($tooltip);
        
        const elementOffset = $element.offset();
        const elementHeight = $element.outerHeight();
        
        $tooltip.css({
            top: elementOffset.top + elementHeight + 10,
            left: elementOffset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
        }).fadeIn(200);
    }

    function hideTooltip() {
        $('.slmm-tooltip').fadeOut(200, function() {
            $(this).remove();
        });
    }

    // Smooth animations
    function initializeAnimations() {
        // Animate form fields on focus
        $('.slmm-input, .slmm-textarea').on('focus', function() {
            $(this).closest('.slmm-form-field').addClass('focused');
        }).on('blur', function() {
            $(this).closest('.slmm-form-field').removeClass('focused');
        });
        
        // Animate toggles
        $('.slmm-toggle').on('change', function() {
            const $wrapper = $(this).closest('.slmm-toggle-wrapper');
            if (this.checked) {
                $wrapper.addClass('checked');
            } else {
                $wrapper.removeClass('checked');
            }
        });
        
        // Animate progress indicators
        animateProgressBars();
    }

    function animateProgressBars() {
        $('.slmm-progress-bar').each(function() {
            const $bar = $(this);
            const percentage = $bar.data('percentage') || 0;
            
            $bar.animate({
                width: percentage + '%'
            }, 1000, 'easeOutCubic');
        });
    }

    // Keyboard shortcuts
    function initializeKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Ctrl/Cmd + S to save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                $('.slmm-save-button').trigger('click');
                return false;
            }
            
            // Ctrl/Cmd + E to export
            if ((e.ctrlKey || e.metaKey) && e.key === 'e') {
                e.preventDefault();
                $('.slmm-export-button').trigger('click');
                return false;
            }
            
            // Escape to close any open modals or reset forms
            if (e.key === 'Escape') {
                hideTooltip();
                $('.slmm-modal').fadeOut(200);
            }
            
            // Tab navigation between settings tabs
            if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                const $activeTab = $('.slmm-tab-button.active');
                if ($activeTab.length && e.target === document.body) {
                    e.preventDefault();
                    
                    const $tabs = $('.slmm-tab-button');
                    const currentIndex = $tabs.index($activeTab);
                    let newIndex;
                    
                    if (e.key === 'ArrowLeft') {
                        newIndex = currentIndex > 0 ? currentIndex - 1 : $tabs.length - 1;
                    } else {
                        newIndex = currentIndex < $tabs.length - 1 ? currentIndex + 1 : 0;
                    }
                    
                    $tabs.eq(newIndex).trigger('click');
                }
            }
        });
    }

    // Color picker initialization
    function initializeColorPickers() {
        // Check if wp-color-picker is available
        if (typeof $.wp === 'undefined' || typeof $.wp.wpColorPicker === 'undefined') {
            debug.warn('Settings', 'WordPress Color Picker not available');
            return;
        }

        // Initialize color picker for development mode
        $('.slmm-color-picker').wpColorPicker({
            defaultColor: '#7a39e8',
            change: function(event, ui) {
                // Optional: Add real-time preview functionality
                const color = ui.color.toString();
                debug.log('Settings', 'Color changed to: ' + color);
                
                // Mark form as changed
                $(this).closest('form').addClass('changed');
                
                // Show unsaved changes indicator
                const $saveButton = $('.slmm-save-button');
                if (!$('.slmm-unsaved-indicator').length) {
                    $saveButton.parent().append('<span class="slmm-unsaved-indicator">• Unsaved changes</span>');
                }
            },
            clear: function() {
                // Handle color clear
                debug.log('Settings', 'Color cleared');
                
                // Reset to default color
                $(this).wpColorPicker('color', '#7a39e8');
            }
        });
    }

    // Search and Replace functionality
    function initializeSearchReplace() {
        let isSearchReplaceRunning = false;
        
        // Show/hide search-replace form based on active tab
        $(document).on('slmm:tabChanged', function(e, tabId) {
            if (tabId === 'search-replace') {
                // Show the search & replace form
                $('.slmm-search-replace-form-container').show();
                // Position it within the search-replace tab
                $('#slmm-search-replace-form-placeholder').append($('.slmm-search-replace-form-container'));
                // Load tables
                loadDatabaseTables();
            } else {
                // Hide the search & replace form and move it back to its original location
                $('.slmm-search-replace-form-container').hide();
                $('body').append($('.slmm-search-replace-form-container'));
            }
        });
        
        // If search-replace tab is already active on page load
        if ($('.slmm-tab-button.active').data('tab') === 'search-replace') {
            $('.slmm-search-replace-form-container').show();
            $('#slmm-search-replace-form-placeholder').append($('.slmm-search-replace-form-container'));
            loadDatabaseTables();
        }
        
        // Table search functionality
        $(document).on('input', '#table-search-input', function() {
            const searchTerm = $(this).val().toLowerCase();
            const $tableItems = $('#table-list .slmm-table-item');

            $tableItems.each(function() {
                const tableName = $(this).find('.slmm-table-name').text().toLowerCase();
                if (tableName.includes(searchTerm)) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });

        // Front End Only preset - selects only tables affecting frontend
        $(document).on('click', '#select-frontend-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', false);

            // Frontend-visible content tables
            const frontendTables = ['posts', 'postmeta', 'options', 'terms', 'term_taxonomy', 'term_relationships', 'comments', 'commentmeta'];
            frontendTables.forEach(function(table) {
                $('#table-list input[value*="_' + table + '"], #table-list input[value$="_' + table + '"]').prop('checked', true);
            });

            // Visual feedback - remove active state from all buttons first
            $('.slmm-table-buttons button').removeClass('button-primary-active').removeClass('button-primary').addClass('button-secondary');
            // Then set this button as active
            $(this).removeClass('button-secondary').addClass('button-primary').addClass('button-primary-active');
        });

        // Classic Editor Only preset - selects only posts table for Classic Editor content
        $(document).on('click', '#select-classic-editor', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', false);

            // Select only posts table as Classic Editor content is stored there
            $('#table-list input[value*="_posts"], #table-list input[value$="_posts"]').prop('checked', true);

            // Visual feedback - remove active state from all buttons first
            $('.slmm-table-buttons button').removeClass('button-primary-active').removeClass('button-primary').addClass('button-secondary');
            // Then set this button as active
            $(this).removeClass('button-secondary').addClass('button-primary').addClass('button-primary-active');
        });

        // Table selection buttons
        $(document).on('click', '#select-all-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', true);

            // Visual feedback - remove active state from all buttons first
            $('.slmm-table-buttons button').removeClass('button-primary-active').removeClass('button-primary').addClass('button-secondary');
            // Then set this button as active
            $(this).removeClass('button-secondary').addClass('button-primary').addClass('button-primary-active');
        });

        $(document).on('click', '#select-core-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', false);
            $('#table-list input[type="checkbox"][data-core="true"]').prop('checked', true);

            // Visual feedback - remove active state from all buttons first
            $('.slmm-table-buttons button').removeClass('button-primary-active').removeClass('button-primary').addClass('button-secondary');
            // Then set this button as active
            $(this).removeClass('button-secondary').addClass('button-primary').addClass('button-primary-active');
        });

        $(document).on('click', '#deselect-all-tables', function(e) {
            e.preventDefault();
            $('#table-list input[type="checkbox"]').prop('checked', false);

            // Visual feedback - remove active state from all buttons first
            $('.slmm-table-buttons button').removeClass('button-primary-active').removeClass('button-primary').addClass('button-secondary');
            // Then set this button as active
            $(this).removeClass('button-secondary').addClass('button-primary').addClass('button-primary-active');
        });
        
        // Function to highlight changes in text
        function highlightChanges(text, searchTerm, changeType) {
            if (!searchTerm || !text) return escapeHtml(text);

            const escapedText = escapeHtml(text);
            const escapedSearch = escapeHtml(searchTerm);

            // Create regex for case-insensitive and whole word matching based on form settings
            const caseInsensitive = $('#case_insensitive').is(':checked');
            const wholeWords = $('#whole_words').is(':checked');

            let pattern;
            if (wholeWords) {
                pattern = new RegExp('(^|\\s)(' + escapeRegex(escapedSearch) + ')(?=\\s|$)', caseInsensitive ? 'gi' : 'g');
            } else {
                pattern = new RegExp('(' + escapeRegex(escapedSearch) + ')', caseInsensitive ? 'gi' : 'g');
            }

            const highlightClass = changeType === 'removed' ? 'slmm-highlight-removed' : 'slmm-highlight-added';

            if (wholeWords) {
                return escapedText.replace(pattern, '$1<span class="' + highlightClass + '">$2</span>');
            } else {
                return escapedText.replace(pattern, '<span class="' + highlightClass + '">$1</span>');
            }
        }

        // Function to escape regex special characters
        function escapeRegex(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Help Accordion functionality
        $(document).on('click', '.slmm-accordion-toggle', function(e) {
            e.preventDefault();
            const $toggle = $(this);
            const $content = $toggle.next('.slmm-accordion-content');

            // Toggle active class and content visibility
            $toggle.toggleClass('active');
            $content.slideToggle(300);
        });

        // BSR-style form interception - bind to button click instead of form submit
        $(document).on('click', '#slmm-start-search-replace', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            debug.log('Settings', 'Search & Replace button clicked');
            
            if (isSearchReplaceRunning) {
                debug.warn('Settings', 'Search & Replace already running, ignoring click');
                return false;
            }
            
            const $button = $(this);
            const $form = $button.closest('form');
            const $searchText = $button.find('.slmm-search-text');
            const $loadingText = $button.find('.slmm-search-loading');
            const $resultsContainer = $('#slmm-search-replace-results');
            
            debug.log('Settings', 'Form element found', $form.length ? 'Valid' : 'Invalid');
            
            // Get form data
            const formData = {
                action: 'slmm_search_replace',
                nonce: $form.find('input[name="slmm_search_replace_nonce"]').val(),
                search_text: $form.find('#search_text').val(),
                replace_text: $form.find('#replace_text').val(),
                case_insensitive: $form.find('#case_insensitive').is(':checked'),
                dry_run: $form.find('#dry_run').is(':checked'),
                whole_words: $form.find('#whole_words').is(':checked'),
                page_content_only: $form.find('#page_content_only').is(':checked'),
                selected_tables: [],
                approved_posts: []
            };
            
            // Get selected tables
            $form.find('#table-list input[type="checkbox"]:checked').each(function() {
                formData.selected_tables.push($(this).val());
            });
            
            debug.log('Settings', 'Form data collected', {
                searchText: formData.search_text,
                replaceText: formData.replace_text,
                selectedTables: formData.selected_tables.length,
                dryRun: formData.dry_run
            });
            
            // Enhanced validation
            if (!formData.nonce) {
                debug.error('Settings', 'Missing nonce field');
                showNotification('Security error: Missing security token. Please refresh the page and try again.', 'error');
                return false;
            }
            
            // Validate AJAX URL availability
            if (typeof slmmSettings === 'undefined' || !slmmSettings.ajaxurl) {
                debug.error('Settings', 'AJAX URL not available');
                showNotification('Configuration error: AJAX endpoint not available. Please refresh the page.', 'error');
                return false;
            }
            
            // Validate form
            if (!formData.search_text.trim()) {
                showNotification('Please enter text to search for', 'error');
                return false;
            }
            
            if (formData.selected_tables.length === 0) {
                showNotification('Please select at least one table to search', 'error');
                return false;
            }
            
            // Show confirmation for non-dry-run operations
            if (!formData.dry_run) {
                const confirmMessage = 'This will modify your database. Are you sure you want to proceed?\n\nMake sure you have a backup of your database.';
                if (!confirm(confirmMessage)) {
                    return false;
                }
                // Add confirmation parameter for destructive operations
                formData.confirmed = 'true';
            }
            
            debug.log('Settings', 'Starting AJAX request');
            
            // Start search and replace
            isSearchReplaceRunning = true;
            $button.prop('disabled', true);
            $searchText.hide();
            $loadingText.show();
            $resultsContainer.empty();
            
            // Perform AJAX request
            $.ajax({
                url: slmmSettings.ajaxurl,
                type: 'POST',
                data: formData,
                timeout: 300000, // 5 minutes timeout
                beforeSend: function() {
                    debug.log('Settings', 'Sending AJAX request to: ' + slmmSettings.ajaxurl);
                    debug.log('Settings', 'Request data prepared', {
                        action: formData.action,
                        tablesCount: formData.selected_tables.length,
                        hasSearchText: !!formData.search_text
                    });
                },
                success: function(response) {
                    debug.success('Settings', 'AJAX request completed successfully', {
                        success: response.success,
                        totalReplacements: response.data?.total_replacements,
                        totalRowsAffected: response.data?.total_rows_affected
                    });
                    if (response.success) {
                        displaySearchReplaceResults(response.data);
                        const totalReplacements = response.data.total_replacements || 0;
                        const rowsAffected = response.data.total_rows_affected || 0;
                        if (response.data.dry_run) {
                            showNotification(`Dry run completed successfully. Found ${totalReplacements} potential replacements in ${rowsAffected} posts/items (excluding revisions).`, 'success');
                        } else {
                            showNotification(`Search and replace completed successfully! Made ${totalReplacements} replacements in ${rowsAffected} posts/items (excluding revisions).`, 'success');
                        }
                    } else {
                        debug.error('Settings', 'Server returned error', response.data);
                        // Handle error response - check if it's an object with a message property
                        const errorMessage = (response.data && typeof response.data === 'object' && response.data.message)
                            ? response.data.message
                            : (response.data || 'Unknown error occurred');
                        showNotification('Error: ' + errorMessage, 'error');
                        $resultsContainer.html('<div class="slmm-error">Error: ' + escapeHtml(errorMessage) + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    debug.error('Settings', 'AJAX error occurred', { 
                        status: status, 
                        error: error, 
                        xhrStatus: xhr.status,
                        hasResponseText: !!xhr.responseText 
                    });
                    
                    let errorMessage = 'An unexpected error occurred';
                    let showRetry = false;
                    
                    // Enhanced error handling based on different error types
                    if (status === 'timeout') {
                        errorMessage = 'Operation timed out. This usually happens with large databases. Try selecting fewer tables or contact support.';
                        showRetry = true;
                    } else if (status === 'abort') {
                        errorMessage = 'Request was cancelled. Please try again.';
                        showRetry = true;
                    } else if (xhr.status === 0) {
                        errorMessage = 'Network connection lost. Please check your internet connection and try again.';
                        showRetry = true;
                    } else if (xhr.status === 403) {
                        errorMessage = 'Permission denied. You may not have sufficient privileges to perform this operation.';
                    } else if (xhr.status === 404) {
                        errorMessage = 'Server endpoint not found. Please refresh the page and try again.';
                        showRetry = true;
                    } else if (xhr.status === 500) {
                        errorMessage = 'Server error occurred. Please try again with fewer tables or contact support.';
                        showRetry = true;
                    } else if (xhr.responseJSON && xhr.responseJSON.data) {
                        errorMessage = xhr.responseJSON.data;
                    } else if (xhr.responseText) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.data) {
                                errorMessage = response.data;
                            }
                        } catch (e) {
                            // If response is not JSON, show a generic error
                            errorMessage = 'Server returned an invalid response. Please try again.';
                            showRetry = true;
                        }
                    }
                    
                    showNotification('Error: ' + errorMessage, 'error');
                    
                    let errorHtml = '<div class="slmm-error">';
                    errorHtml += '<strong>Error:</strong> ' + errorMessage;
                    if (showRetry) {
                        errorHtml += '<br><br><button type="button" class="button button-secondary" onclick="$(\'.slmm-search-replace-btn\').click();">Try Again</button>';
                    }
                    errorHtml += '</div>';
                    
                    $resultsContainer.html(errorHtml);
                },
                complete: function() {
                    debug.log('Settings', 'AJAX request completed');
                    isSearchReplaceRunning = false;
                    $button.prop('disabled', false);
                    $searchText.show();
                    $loadingText.hide();
                }
            });
        });
        
        // Also prevent form submission as a backup
        $(document).on('submit', '#slmm-search-replace-form', function(e) {
            e.preventDefault();
            e.stopPropagation();
            debug.log('Settings', 'Form submission prevented');
            return false;
        });
        
        function loadDatabaseTables() {
            const $tableList = $('#table-list');
            
            if ($tableList.length === 0) {
                return;
            }
            
            $tableList.html('<div class="slmm-loading">Loading tables...</div>');
            
            $.ajax({
                url: slmmSettings.ajaxurl,
                type: 'POST',
                data: {
                    action: 'slmm_get_tables',
                    nonce: $('input[name="slmm_search_replace_nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        displayTableList(response.data.tables);
                    } else {
                        $tableList.html('<div class="slmm-error">Error loading tables: ' + response.data + '</div>');
                    }
                },
                error: function() {
                    $tableList.html('<div class="slmm-error">Error loading tables. Please refresh the page.</div>');
                }
            });
        }
        
        function displayTableList(tables) {
            const $tableList = $('#table-list');
            let html = '';

            // Frontend-only table patterns for default selection
            const frontendTablePatterns = ['posts', 'postmeta', 'options', 'terms', 'term_taxonomy', 'term_relationships', 'comments', 'commentmeta'];

            // Sort tables: selected first, then by name
            tables.sort(function(a, b) {
                // First, check if either table was previously selected
                const aSelected = $tableList.find('input[value="' + a.name + '"]').is(':checked');
                const bSelected = $tableList.find('input[value="' + b.name + '"]').is(':checked');

                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;

                // If both same selection state, sort by name
                return a.name.localeCompare(b.name);
            });
            
            tables.forEach(function(table) {
                const coreClass = table.is_core ? ' slmm-core-table' : '';
                const coreLabel = table.is_core ? ' (Core)' : '';

                // Check if this table should be selected by default (Frontend Only)
                const isFrontendTable = frontendTablePatterns.some(pattern =>
                    table.name.includes('_' + pattern) || table.name.endsWith('_' + pattern)
                );
                const checkedAttribute = isFrontendTable ? ' checked' : '';
                const selectedClass = isFrontendTable ? ' selected' : '';

                html += '<label class="slmm-table-checkbox slmm-table-item' + coreClass + selectedClass + '">';
                html += '<input type="checkbox" value="' + escapeHtml(table.name) + '" data-core="' + table.is_core + '"' + checkedAttribute + '>';
                html += '<span class="slmm-checkmark"></span>';
                html += '<span class="slmm-table-name">' + escapeHtml(table.name) + coreLabel + '</span>';
                html += '<span class="slmm-table-rows">(' + table.rows + ' rows)</span>';
                html += '</label>';
            });
            
            $tableList.html(html);
            
            // Re-bind checkbox change handlers for selection styling and sorting
            initializeTableSelection();
        }
        
        // Initialize table selection behavior
        function initializeTableSelection() {
            $('#table-list').off('change.table-selection').on('change.table-selection', 'input[type="checkbox"]', function() {
                const $label = $(this).closest('.slmm-table-checkbox');
                const $tableList = $('#table-list');
                
                // Update visual state
                if ($(this).is(':checked')) {
                    $label.addClass('selected');
                } else {
                    $label.removeClass('selected');
                }
                
                // Move selected items to top with animation
                setTimeout(function() {
                    sortTableList();
                }, 100);
            });
        }
        
        // Sort table list with selected items at top
        function sortTableList() {
            const $tableList = $('#table-list');
            const $labels = $tableList.find('.slmm-table-checkbox');
            
            // Convert to array and sort
            const labelsArray = $labels.toArray().sort(function(a, b) {
                const aSelected = $(a).find('input').is(':checked');
                const bSelected = $(b).find('input').is(':checked');
                const aName = $(a).find('.slmm-table-name').text();
                const bName = $(b).find('.slmm-table-name').text();
                
                // Selected items first
                if (aSelected && !bSelected) return -1;
                if (!aSelected && bSelected) return 1;
                
                // Then sort by name
                return aName.localeCompare(bName);
            });
            
            // Reorder with smooth animation
            $tableList.fadeOut(200, function() {
                $tableList.empty().append(labelsArray).fadeIn(200);
            });
        }
        
        function displaySearchReplaceResults(data) {
            const $resultsContainer = $('#slmm-search-replace-results');
            let html = '<div class="slmm-search-replace-results">';
            
            html += '<h3>Search and Replace Results</h3>';
            
            if (data.dry_run) {
                html += '<div class="slmm-dry-run-notice">🔍 <strong>Dry Run</strong> - No changes were made to the database</div>';
            }
            
            // Enhanced summary with detailed metrics
            html += '<div class="slmm-results-summary">';
            html += '<div class="slmm-summary-stats">';
            html += '<div class="slmm-stat"><strong>String Replacements:</strong> ' + data.total_replacements + '</div>';
            html += '<div class="slmm-stat"><strong>Rows Affected:</strong> ' + data.total_rows_affected + '</div>';
            html += '<div class="slmm-stat"><strong>Fields Modified:</strong> ' + data.total_field_replacements + '</div>';
            html += '<div class="slmm-stat"><strong>Tables Processed:</strong> ' + data.summary.tables_processed + '</div>';
            html += '</div>';
            html += '</div>';
            
            if (Object.keys(data.results).length === 0) {
                html += '<div class="slmm-no-results">No matches found in the selected tables.</div>';
            } else {
                // Table-by-table breakdown with Show button for matched rows
                html += '<div class="slmm-results-table">';
                html += '<h4>Tables Modified</h4>';
                html += '<table class="slmm-results-table-inner">';
                html += '<thead><tr><th>Table</th><th>String Replacements</th><th>Rows Affected</th><th>Fields Modified</th><th>Actions</th></tr></thead>';
                html += '<tbody>';

                Object.keys(data.results).forEach(function(tableName) {
                    const result = data.results[tableName];
                    html += '<tr>';
                    html += '<td>' + escapeHtml(tableName) + '</td>';
                    html += '<td>' + result.replacements + '</td>';
                    html += '<td>' + result.rows_affected + '</td>';
                    html += '<td>' + result.field_replacements + '</td>';
                    html += '<td>';
                    if (data.dry_run && data.search_params) {
                        html += '<button type="button" class="button button-secondary slmm-view-matched-rows slmm-toggle-button" ';
                        html += 'data-table="' + escapeHtml(tableName) + '" ';
                        html += 'data-search="' + escapeHtml(data.search_params.search_text) + '" ';
                        html += 'data-case-insensitive="' + data.search_params.case_insensitive + '" ';
                        html += 'data-whole-words="' + data.search_params.whole_words + '" ';
                        html += 'data-state="hidden">Show</button>';
                    }
                    html += '</td>';
                    html += '</tr>';
                });

                html += '</tbody></table>';
                html += '</div>';

                // Container for matched rows display
                html += '<div id="slmm-matched-rows-display"></div>';
                
                // Affected posts section (if any) - with edit/view buttons and approval checkboxes
                if (data.affected_posts && data.affected_posts.length > 0) {
                    html += '<div class="slmm-affected-posts">';
                    html += '<h4>Affected Posts/Items</h4>';

                    // Add search bar for filtering affected items
                    html += '<div class="slmm-affected-items-search">';
                    html += '<input type="text" id="affected-items-search" class="slmm-search-input" placeholder="Search affected items..." />';
                    html += '<span class="slmm-search-results-count">Showing all ' + data.affected_posts.length + ' items</span>';
                    html += '</div>';

                    // Add bulk selection controls
                    if (data.dry_run) {
                        html += '<div class="slmm-bulk-controls">';
                        html += '<button type="button" id="select-all-posts" class="button button-secondary slmm-bulk-action-button">Select All</button>';
                        html += '<button type="button" id="deselect-all-posts" class="button button-secondary slmm-bulk-action-button">Deselect All</button>';
                        html += '<button type="button" id="apply-selected-changes" class="button button-primary slmm-apply-changes-button" style="margin-left: 10px;">Apply Selected Changes</button>';
                        html += '</div>';
                    }

                    // Display ALL affected items uniformly
                    const allAffectedItems = data.affected_posts;

                    if (allAffectedItems.length > 0) {
                        html += '<div class="slmm-posts-table-wrapper">';
                        html += '<table class="slmm-posts-table">';
                        html += '<thead>';
                        html += '<tr>';
                        if (data.dry_run) {
                            html += '<th style="width: 40px;"><input type="checkbox" id="select-all-checkbox" title="Select/Deselect All"></th>';
                        }
                        html += '<th>Item Title/Name</th>';
                        html += '<th>Type</th>';
                        html += '<th>Status</th>';
                        html += '<th>Preview Changes</th>';
                        html += '<th>Actions</th>';
                        html += '</tr>';
                        html += '</thead>';
                        html += '<tbody>';

                        allAffectedItems.forEach(function(item) {
                            html += '<tr class="slmm-affected-item" data-search-text="' +
                                escapeHtml((item.post_title || item.option_name || item.primary_key_value || 'Unknown') + ' ' +
                                          (item.change_type || '') + ' ' + (item.table || '')) + '">';
                            if (data.dry_run) {
                                const itemId = item.ID || item.option_name || item.primary_key_value || 'unknown';
                                html += '<td><input type="checkbox" class="post-approval-checkbox" value="' + itemId + '" checked></td>';
                            }

                            // Item Title/Name
                            let itemTitle = 'Unknown Item';
                            if (item.post_title) {
                                itemTitle = item.post_title;
                            } else if (item.option_name) {
                                itemTitle = item.option_name;
                            } else if (item.primary_key_value) {
                                itemTitle = item.table + ' ID: ' + item.primary_key_value;
                            }
                            html += '<td class="slmm-post-title">' + escapeHtml(itemTitle) + '</td>';

                            // Type
                            html += '<td class="slmm-post-type">' + escapeHtml(item.change_type || item.post_type || 'Database Record') + '</td>';

                            // Status
                            html += '<td class="slmm-post-status">';
                            if (item.post_status) {
                                html += '<span class="slmm-status-badge slmm-status-' + item.post_status + '">';
                                html += escapeHtml(item.post_status.charAt(0).toUpperCase() + item.post_status.slice(1));
                                html += '</span>';
                            } else {
                                html += '<span class="slmm-status-badge slmm-status-active">Active</span>';
                            }
                            html += '</td>';

                            // Preview changes column
                            html += '<td class="slmm-preview-changes">';
                            if (item.column_changes && item.column_changes.length > 0) {
                                const itemId = item.ID || item.option_name || item.primary_key_value || 'unknown';
                                html += '<button type="button" class="slmm-preview-btn" data-post-id="' + itemId + '">View Changes (' + item.column_changes.length + ')</button>';
                                html += '<div class="slmm-preview-dropdown" id="preview-' + itemId + '" style="display: none;">';
                                item.column_changes.forEach(function(change) {
                                    html += '<div class="slmm-change-item">';
                                    html += '<strong>Column:</strong> ' + escapeHtml(change.column) + '<br>';

                                    // Create highlighted before preview
                                    if (change.before_text !== undefined) {
                                        const beforeHighlighted = escapeHtml(change.before_text) +
                                            '<span class="slmm-highlight-search">' + escapeHtml(change.match_text) + '</span>' +
                                            escapeHtml(change.after_text);
                                        html += '<div class="slmm-change-before"><strong>Before:</strong> ' + beforeHighlighted + '</div>';

                                        const afterHighlighted = escapeHtml(change.before_text_updated) +
                                            '<span class="slmm-highlight-replacement">' + escapeHtml(change.replacement_text) + '</span>' +
                                            escapeHtml(change.after_text_updated);
                                        html += '<div class="slmm-change-after"><strong>After:</strong> ' + afterHighlighted + '</div>';
                                    } else {
                                        // Fallback to old format if new data structure not available
                                        html += '<div class="slmm-change-before"><strong>Before:</strong> ' + escapeHtml(change.original_preview) + '</div>';
                                        html += '<div class="slmm-change-after"><strong>After:</strong> ' + escapeHtml(change.new_preview) + '</div>';
                                    }

                                    html += '</div>';
                                });
                                html += '</div>';
                            } else {
                                html += '<span class="slmm-no-preview">No changes</span>';
                            }
                            html += '</td>';

                            html += '<td class="slmm-post-actions">';

                            // Edit button (available for posts)
                            if (item.edit_url) {
                                html += '<a href="' + escapeHtml(item.edit_url) + '" class="slmm-action-link slmm-edit-link" target="_blank">Edit</a>';
                            }

                            // View button (for published posts)
                            if (item.view_url && item.post_status === 'publish') {
                                html += '<a href="' + escapeHtml(item.view_url) + '" class="slmm-action-link slmm-view-link" target="_blank">View</a>';
                            }
                            
                            html += '</td>';
                            html += '</tr>';
                        });
                        
                        html += '</tbody>';
                        html += '</table>';
                        html += '</div>';
                    }

                    html += '</div>';
                }
                
                // Column modification statistics
                if (data.modified_columns && Object.keys(data.modified_columns).length > 0) {
                    html += '<div class="slmm-column-stats">';
                    html += '<h4>Column Modification Statistics</h4>';
                    html += '<div class="slmm-column-list">';
                    
                    Object.keys(data.modified_columns).forEach(function(column) {
                        const count = data.modified_columns[column];
                        html += '<div class="slmm-column-stat">';
                        html += '<strong>' + escapeHtml(column) + ':</strong> ' + count + ' modifications';
                        html += '</div>';
                    });
                    
                    html += '</div>';
                    html += '</div>';
                }
            }
            
            html += '</div>';
            $resultsContainer.html(html);

            // Initialize action links after results are displayed
            initializeResultsActions();
            bindSearchReplaceResultsEvents();
        }
        
        // Initialize action links behavior (same as Lorem Ipsum Detector)
        function initializeResultsActions() {
            // Handle action link clicks
            $('.slmm-action-link').off('click.search-replace').on('click.search-replace', function(e) {
                // Links will open in new tabs as specified in HTML
                const action = $(this).hasClass('slmm-edit-link') ? 'edit' : 'view';
                const postTitle = $(this).closest('tr').find('.slmm-post-title').text();
                
                debug.log('Settings', 'Opening ' + action + ' for: ' + postTitle);
            });
            
            // Add hover effects to result tables
            $('.slmm-posts-table tbody tr').hover(
                function() {
                    $(this).addClass('hover');
                },
                function() {
                    $(this).removeClass('hover');
                }
            );
        }
        

        function bindSearchReplaceResultsEvents() {

            // Preview dropdown toggle
            $(document).off('click.preview').on('click.preview', '.slmm-preview-btn', function() {
                const postId = $(this).data('post-id');
                const $dropdown = $('#preview-' + postId);
                $('.slmm-preview-dropdown').not($dropdown).hide(); // Close other dropdowns
                $dropdown.toggle();
            });

            // Select/Deselect All functionality for results
            $(document).off('click.selectall').on('click.selectall', '#select-all-posts', function() {
                $('.post-approval-checkbox').prop('checked', true);
            });

            $(document).off('click.deselectall').on('click.deselectall', '#deselect-all-posts', function() {
                $('.post-approval-checkbox').prop('checked', false);
            });

            // Master checkbox toggle
            $(document).off('change.master').on('change.master', '#select-all-checkbox', function() {
                $('.post-approval-checkbox').prop('checked', $(this).is(':checked'));
            });

            // View matched rows functionality
            $(document).off('click.viewmatches').on('click.viewmatches', '.slmm-view-matched-rows', function() {
                const $button = $(this);
                const table = $button.data('table');
                const search = $button.data('search');
                const caseInsensitive = $button.data('case-insensitive');
                const wholeWords = $button.data('whole-words');
                const $display = $('#slmm-matched-rows-display');
                const currentState = $button.data('state') || 'hidden';

                // Toggle functionality
                if (currentState === 'visible') {
                    // Hide the matched rows
                    $display.slideUp(300, function() {
                        $display.empty();
                    });
                    $button.text('Show');
                    $button.data('state', 'hidden');
                    $button.removeClass('slmm-button-active');
                    return;
                }

                // Show loading
                $button.prop('disabled', true).text('Loading...');
                $display.html('<div class="slmm-loading">Loading matched rows...</div>').slideDown(300);

                // Make AJAX request to get matched rows
                $.ajax({
                    url: slmmSettings.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'slmm_get_matched_rows',
                        nonce: $('input[name="slmm_search_replace_nonce"]').val(),
                        table: table,
                        search: search,
                        case_insensitive: caseInsensitive,
                        whole_words: wholeWords
                    },
                    success: function(response) {
                        if (response.success) {
                            $display.html(response.data.html).show();
                            // Scroll to the matched rows display
                            $('html, body').animate({
                                scrollTop: $display.offset().top - 100
                            }, 500);

                            // Update button state to "visible"
                            $button.text('Hide');
                            $button.data('state', 'visible');
                            $button.addClass('slmm-button-active');
                        } else {
                            $display.html('<div class="slmm-error">Error loading matched rows: ' + response.data + '</div>').show();
                            $button.text('Show');
                            $button.data('state', 'hidden');
                        }
                    },
                    error: function() {
                        $display.html('<div class="slmm-error">Error loading matched rows. Please try again.</div>').show();
                        $button.text('Show');
                        $button.data('state', 'hidden');
                    },
                    complete: function() {
                        $button.prop('disabled', false);
                    }
                });
            });

            // Search functionality for affected items
            $(document).off('input.search').on('input.search', '#affected-items-search', function() {
                const searchTerm = $(this).val().toLowerCase();
                const $items = $('.slmm-affected-item');
                let visibleCount = 0;

                $items.each(function() {
                    const searchText = $(this).data('search-text').toLowerCase();
                    if (searchText.includes(searchTerm)) {
                        $(this).show();
                        visibleCount++;
                    } else {
                        $(this).hide();
                    }
                });

                $('.slmm-search-results-count').text(
                    searchTerm ? `Showing ${visibleCount} of ${$items.length} items` : `Showing all ${$items.length} items`
                );
            });

            // Apply selected changes
            $(document).off('click.apply').on('click.apply', '#apply-selected-changes', function() {
                const selectedPosts = [];
                $('.post-approval-checkbox:checked').each(function() {
                    selectedPosts.push(parseInt($(this).val()));
                });

                if (selectedPosts.length === 0) {
                    showNotification('Please select at least one post to apply changes to.', 'error');
                    return;
                }

                // Show confirmation for applying changes
                const confirmMessage = 'This will apply changes to ' + selectedPosts.length + ' selected post(s). Are you sure you want to proceed?\n\nMake sure you have a backup of your database.';
                if (!confirm(confirmMessage)) {
                    return;
                }

                // Get form data from the search form
                const $form = $('#slmm-search-replace-form');
                const $button = $(this);
                const $resultsContainer = $('#slmm-search-replace-results');

                // Build form data directly with all necessary parameters
                const formData = {
                    action: 'slmm_search_replace',
                    nonce: $form.find('input[name="slmm_search_replace_nonce"]').val(),
                    search_text: $form.find('#search_text').val(),
                    replace_text: $form.find('#replace_text').val(),
                    case_insensitive: $form.find('#case_insensitive').is(':checked'),
                    dry_run: false, // Always false for applying changes
                    whole_words: $form.find('#whole_words').is(':checked'),
                    page_content_only: $form.find('#page_content_only').is(':checked'),
                    selected_tables: [],
                    approved_posts: selectedPosts,
                    confirmed: 'true' // Add confirmation for destructive operations
                };

                // Get selected tables
                $form.find('#table-list input[type="checkbox"]:checked').each(function() {
                    formData.selected_tables.push($(this).val());
                });

                // Disable button and show loading state
                $button.prop('disabled', true).text('Applying changes...');

                // Submit directly via AJAX
                $.ajax({
                    url: slmmSettings.ajaxurl,
                    type: 'POST',
                    data: formData,
                    timeout: 300000, // 5 minutes timeout
                    success: function(response) {
                        if (response.success) {
                            displaySearchReplaceResults(response.data);
                            showNotification('Changes applied successfully!', 'success');
                        } else {
                            // Handle error response - check if it's an object with a message property
                            const errorMessage = (response.data && typeof response.data === 'object' && response.data.message)
                                ? response.data.message
                                : (response.data || 'Unknown error occurred');
                            showNotification('Error: ' + errorMessage, 'error');
                        }
                    },
                    error: function(xhr, status, error) {
                        if (status === 'timeout') {
                            showNotification('Request timed out. The operation may still be running in the background.', 'warning');
                        } else {
                            showNotification('Error applying changes: ' + error, 'error');
                        }
                    },
                    complete: function() {
                        $button.prop('disabled', false).text('Apply Selected Changes');
                    }
                });
            });
        }
        
        // Generate WordPress edit URL for posts (same pattern as Lorem Ipsum Detector)
        function generateEditUrl(postId, postType) {
            if (!postId) return null;
            
            // Use WordPress admin URL pattern
            const adminUrl = (typeof ajaxurl !== 'undefined') ? 
                ajaxurl.replace('/admin-ajax.php', '') : 
                '/wp-admin';
            
            return adminUrl + '/post.php?post=' + postId + '&action=edit';
        }
        
        // Generate WordPress view URL for published posts
        function generateViewUrl(postId) {
            if (!postId) return null;
            
            // For published posts, we can use the standard WordPress URL pattern
            // Note: This is a basic implementation - in a full implementation you might
            // want to make an AJAX call to get the actual permalink
            const siteUrl = window.location.origin;
            return siteUrl + '/?p=' + postId;
        }
        
        function escapeHtml(text) {
            const map = {
                '&': '&amp;',
                '<': '&lt;',
                '>': '&gt;',
                '"': '&quot;',
                "'": '&#039;'
            };
            return text.replace(/[&<>"']/g, function(m) { return map[m]; });
        }
    }

    // Utility functions
    function showNotification(message, type = 'success') {
        const $notification = $('<div class="slmm-notification slmm-notification-' + type + '">' + message + '</div>');
        
        $('body').append($notification);
        
        $notification.fadeIn(300).delay(3000).fadeOut(300, function() {
            $(this).remove();
        });
    }

    function debounce(func, wait, immediate) {
        let timeout;
        return function executedFunction() {
            const context = this;
            const args = arguments;
            const later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    // Search functionality for large option lists
    function initializeSearch() {
        $('.slmm-search-input').on('input', debounce(function() {
            const query = $(this).val().toLowerCase();
            const $target = $($(this).data('target'));
            
            $target.find('.slmm-option').each(function() {
                const text = $(this).text().toLowerCase();
                $(this).toggle(text.includes(query));
            });
        }, 300));
    }

    // Auto-save functionality for large forms
    let autoSaveTimeout;
    function initializeAutoSave() {
        $('.slmm-settings-form').on('change input', 'input, textarea, select', debounce(function() {
            if (typeof slmmSettings !== 'undefined' && slmmSettings.autoSave) {
                clearTimeout(autoSaveTimeout);
                autoSaveTimeout = setTimeout(function() {
                    // Auto-save implementation would go here
                    debug.log('Settings', 'Auto-saving...');
                }, 2000);
            }
        }, 500));
    }

    // Handle AJAX form submissions for individual sections
    function initializeAjaxForms() {
        $('.slmm-ajax-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $button = $form.find('button[type="submit"]');
            const originalText = $button.text();
            
            $button.text('Saving...').prop('disabled', true);
            
            $.ajax({
                url: slmmSettings.ajaxurl,
                type: 'POST',
                data: $form.serialize(),
                success: function(response) {
                    if (response.success) {
                        showNotification('Settings saved successfully!', 'success');
                        $form.removeClass('changed');
                    } else {
                        showNotification('Error saving settings: ' + response.data, 'error');
                    }
                },
                error: function() {
                    showNotification('Network error. Please try again.', 'error');
                },
                complete: function() {
                    $button.text(originalText).prop('disabled', false);
                }
            });
        });
    }

    // Initialize additional features
    initializeSearch();
    initializeAutoSave();
    initializeAjaxForms();

    // Global event handlers
    $(document).on('slmm:tabChanged', function(e, tabId) {
        // Update URL hash without triggering page scroll
        if (history.replaceState) {
            history.replaceState(null, null, '#' + tabId);
        }
        
        // Focus first input in new tab
        setTimeout(function() {
            $('#' + tabId).find('input, textarea, select').first().focus();
        }, 300);
    });

    // Handle responsive tab navigation
    function handleResponsiveTabs() {
        const $tabsNav = $('.slmm-tabs-nav');
        const $activeTab = $('.slmm-tab-button.active');
        
        if ($activeTab.length && $tabsNav.length) {
            const navScrollLeft = $tabsNav.scrollLeft();
            const navWidth = $tabsNav.outerWidth();
            const tabLeft = $activeTab.position().left;
            const tabWidth = $activeTab.outerWidth();
            
            if (tabLeft < 0) {
                $tabsNav.animate({ scrollLeft: navScrollLeft + tabLeft - 20 }, 300);
            } else if (tabLeft + tabWidth > navWidth) {
                $tabsNav.animate({ scrollLeft: navScrollLeft + (tabLeft + tabWidth - navWidth) + 20 }, 300);
            }
        }
    }

    // Call on tab change and window resize
    $(document).on('slmm:tabChanged', handleResponsiveTabs);
    $(window).on('resize', debounce(handleResponsiveTabs, 250));

    // Initialization complete
    debug.success('Settings', 'SLMM Settings initialized successfully');
}); 