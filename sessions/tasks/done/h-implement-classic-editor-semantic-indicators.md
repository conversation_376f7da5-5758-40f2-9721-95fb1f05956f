---
task: h-implement-classic-editor-semantic-indicators
branch: feature/classic-editor-semantic-indicators
status: pending
created: 2025-09-04
modules: [interlinking, classic-editor, semantic-indicators]
---

# Implement Classic Editor Semantic Indicators

## Problem/Goal
Extend the existing semantic indicator system to work in WordPress Classic Editor. Currently, the system only works in Direct Editor/Bricks Builder. Need to implement the same green highlighting system for `span.nav-link.clickable-url` elements in the SEO Overview sidebar when links are found in TinyMCE Classic Editor content.

## Success Criteria
- [ ] Scan TinyMCE HTML content in Classic Editor for matching links
- [ ] Apply green background to `span.nav-link.clickable-url` elements when links found
- [ ] Remove green background when links are removed from content
- [ ] Bidirectional real-time sync between Classic Editor and Direct Editor
- [ ] Use same scanning logic as existing Direct Editor system
- [ ] No visual artifacts or delays
- [ ] Work exactly like existing `div.silo-keyword-display.has-keyword.clickable-keyword` system

## Context Files
<!-- Added by context-gathering agent or manually -->
- @assets/js/slmm-direct-editor.js  # Existing semantic indicator implementation
- patterns/semantic-indicator-completion-tracking  # Current implementation patterns

## User Notes
Requirements:
- Work in Classic Editor TinyMCE environment
- Target `span.nav-link.clickable-url` elements in SEO Overview postbox
- Green background only (no checkmark ticks needed)
- Two-way AJAX communication with immediate updates
- Sync between Classic Editor ↔ Direct Editor seamlessly

Current working system:
- Direct Editor: `div.silo-keyword-display.has-keyword.clickable-keyword` ✅
- Classic Editor: `span.nav-link.clickable-url` ❌ (needs implementation)

## Work Log
- [2025-09-04] Task created - Classic Editor semantic indicators needed