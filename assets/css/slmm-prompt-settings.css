/* File: assets/css/slmm-prompt-settings.css */

/* SLMM Prompt Settings Dark Theme - Scoped Variables */
.slmm-prompts-dark-theme {
    --slmm-primary: #7C3AED;
    --slmm-primary-hover: #8B5CF6;
    --slmm-primary-light: rgba(124, 58, 237, 0.08);
    --slmm-primary-subtle: rgba(124, 58, 237, 0.04);
    --slmm-dark-bg: #0A0A0F;
    --slmm-dark-surface: #141419;
    --slmm-dark-surface-hover: #1A1A1F;
    --slmm-dark-border: #1F1F24;
    --slmm-dark-border-subtle: #16161B;
    --slmm-text-primary: #F1F5F9;
    --slmm-text-secondary: #94A3B8;
    --slmm-text-muted: #64748B;
    --slmm-text-dim: #475569;
    --slmm-success: #10B981;
    --slmm-warning: #F59E0B;
    --slmm-error: #EF4444;
    --slmm-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    --slmm-shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.4);
}

/* Main container styling */
.wrap {
    background: var(--slmm-dark-bg) !important;
    color: var(--slmm-text-primary) !important;
    margin: 0 -20px -10px !important;
    padding: 2rem !important;
    min-height: 100vh !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.wrap h1 {
    color: var(--slmm-text-primary) !important;
    font-size: 1.75rem !important;
    font-weight: 600 !important;
    margin-bottom: 2rem !important;
    padding-bottom: 1rem !important;
    border-bottom: 1px solid var(--slmm-dark-border) !important;
}

/* Container for all prompts */
#slmm-gpt-prompts-container {
    margin-bottom: 2rem;
}

/* Individual prompt box styling */
.slmm-gpt-prompt {
    background: var(--slmm-dark-surface) !important;
    border: 1px solid var(--slmm-dark-border) !important;
    border-radius: 12px !important;
    margin-bottom: 1.5rem !important;
    padding: 1.5rem !important;
    box-shadow: var(--slmm-shadow) !important;
    transition: all 0.3s ease !important;
}

.slmm-gpt-prompt:hover {
    border-color: var(--slmm-dark-border) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--slmm-shadow-lg) !important;
}

/* Prompt header styling */
.slmm-gpt-prompt-header {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 1rem !important;
    flex-wrap: wrap !important;
    gap: 0.75rem !important;
}

.slmm-gpt-prompt-handle {
    cursor: move !important;
    color: var(--slmm-text-muted) !important;
    padding: 0.25rem !important;
    border-radius: 4px !important;
    transition: color 0.2s ease !important;
}

.slmm-gpt-prompt-handle:hover {
    color: var(--slmm-primary) !important;
    background: var(--slmm-primary-light) !important;
}

.slmm-gpt-prompt-arrows {
    display: flex !important;
    flex-direction: column !important;
    gap: 2px !important;
}

.slmm-gpt-prompt-arrows .dashicons {
    cursor: pointer !important;
    color: var(--slmm-text-muted) !important;
    padding: 2px !important;
    border-radius: 3px !important;
    transition: all 0.2s ease !important;
    font-size: 16px !important;
}

.slmm-gpt-prompt-arrows .dashicons:hover {
    color: var(--slmm-primary) !important;
    background: var(--slmm-primary-light) !important;
}

/* Input field styling */
.slmm-gpt-prompt-title {
    flex: 1 1 200px !important;
    padding: 0.75rem 1rem !important;
    background: var(--slmm-dark-border-subtle) !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 8px !important;
    color: var(--slmm-text-primary) !important;
    font-size: 0.95rem !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
}

.slmm-gpt-prompt-title:focus {
    outline: none !important;
    border-color: var(--slmm-primary) !important;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
}

.slmm-gpt-prompt-title::placeholder {
    color: var(--slmm-text-muted) !important;
}

/* Model selector styling */
.slmm-model-selector {
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    flex-wrap: wrap !important;
}

.slmm-prompt-provider,
.slmm-gpt-prompt-model {
    padding: 0.5rem 0.75rem !important;
    background: var(--slmm-dark-border-subtle) !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 6px !important;
    color: var(--slmm-text-primary) !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
}

.slmm-prompt-provider {
    min-width: 100px !important;
}

.slmm-gpt-prompt-model {
    min-width: 200px !important;
}

.slmm-prompt-provider:focus,
.slmm-gpt-prompt-model:focus {
    outline: none !important;
    border-color: var(--slmm-primary) !important;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
}

/* Refresh button styling */
.slmm-refresh-prompt-models {
    background: var(--slmm-primary) !important;
    border: none !important;
    border-radius: 6px !important;
    color: white !important;
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    height: auto !important;
    min-height: 0 !important;
    line-height: 1 !important;
}

.slmm-refresh-prompt-models:hover {
    background: var(--slmm-primary-hover) !important;
    transform: translateY(-1px) !important;
}

.slmm-refresh-prompt-models:active {
    transform: translateY(0) !important;
}

/* Remove button styling */
.slmm-remove-prompt {
    background: var(--slmm-error) !important;
    border: none !important;
    border-radius: 6px !important;
    color: white !important;
    padding: 0.5rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.slmm-remove-prompt:hover {
    background: #dc2626 !important;
    transform: translateY(-1px) !important;
}

/* Textarea styling */
.slmm-gpt-prompt-content {
    width: 100% !important;
    min-height: 120px !important;
    padding: 1rem !important;
    background: var(--slmm-dark-border-subtle) !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 8px !important;
    color: var(--slmm-text-primary) !important;
    font-size: 0.95rem !important;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
    line-height: 1.5 !important;
    resize: vertical !important;
    margin-bottom: 1rem !important;
    transition: all 0.3s ease !important;
}

.slmm-gpt-prompt-content:focus {
    outline: none !important;
    border-color: var(--slmm-primary) !important;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
}

.slmm-gpt-prompt-content::placeholder {
    color: var(--slmm-text-muted) !important;
    font-family: inherit !important;
}

/* Footer styling */
.slmm-gpt-prompt-footer {
    display: flex !important;
    justify-content: space-between !important;
    flex-wrap: wrap !important;
    gap: 1rem !important;
}

.slmm-gpt-prompt-footer label {
    color: var(--slmm-text-primary) !important;
    font-weight: 600 !important;
    font-size: 0.875rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.slmm-gpt-prompt-temperature,
.slmm-gpt-prompt-max-tokens {
    width: 80px !important;
    padding: 0.5rem 0.75rem !important;
    background: var(--slmm-dark-border-subtle) !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 6px !important;
    color: var(--slmm-text-primary) !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
}

.slmm-gpt-prompt-temperature:focus,
.slmm-gpt-prompt-max-tokens:focus {
    outline: none !important;
    border-color: var(--slmm-primary) !important;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
}

/* Add new prompt button */
#slmm-add-prompt {
    background: var(--slmm-primary) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    padding: 0.875rem 1.5rem !important;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    margin-bottom: 2rem !important;
}

#slmm-add-prompt:hover {
    background: var(--slmm-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--slmm-shadow-lg) !important;
}

/* Save changes button */
#submit {
    background: var(--slmm-primary) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    padding: 0.875rem 2rem !important;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-shadow: none !important;
}

#submit:hover {
    background: var(--slmm-primary-hover) !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--slmm-shadow-lg) !important;
}

/* Form styling */
form {
    background: transparent !important;
}

/* Provider note styling */
.slmm-model-selector div[style*="font-size: 11px"] {
    color: var(--slmm-text-muted) !important;
    font-size: 0.8125rem !important;
    margin-top: 0.25rem !important;
    font-style: italic !important;
}

/* Animation for spinner */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 782px) {
    .wrap {
        padding: 1rem !important;
    }
    
    .slmm-gpt-prompt {
        padding: 1rem !important;
    }
    
    .slmm-gpt-prompt-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 0.75rem !important;
    }

    .slmm-gpt-prompt-title,
    .slmm-model-selector,
    .slmm-remove-prompt {
        width: 100% !important;
    }

    .slmm-model-selector {
        justify-content: flex-start !important;
    }

    .slmm-prompt-provider,
    .slmm-gpt-prompt-model {
        flex: 1 !important;
        min-width: 0 !important;
    }

    .slmm-gpt-prompt-footer {
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    .slmm-gpt-prompt-footer label {
        margin-bottom: 0 !important;
    }

    .slmm-gpt-prompt-temperature,
    .slmm-gpt-prompt-max-tokens {
        width: 100% !important;
    }
}

/* Override WordPress default styles */
.wrap input[type="text"],
.wrap input[type="number"],
.wrap select,
.wrap textarea {
    background: var(--slmm-dark-border-subtle) !important;
    color: var(--slmm-text-primary) !important;
    border: 2px solid var(--slmm-dark-border) !important;
    border-radius: 8px !important;
}

.wrap input[type="text"]:focus,
.wrap input[type="number"]:focus,
.wrap select:focus,
.wrap textarea:focus {
    border-color: var(--slmm-primary) !important;
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.2) !important;
    outline: none !important;
}

/* Loading state for disabled elements */
.wrap select:disabled,
.wrap button:disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
}

/* Notification styling (if any appear) */
.notice {
    background: var(--slmm-dark-surface) !important;
    color: var(--slmm-text-primary) !important;
    border-left-color: var(--slmm-primary) !important;
    border-radius: 8px !important;
}

.notice.notice-success {
    border-left-color: var(--slmm-success) !important;
}

.notice.notice-error {
    border-left-color: var(--slmm-error) !important;
}

/* Smooth transitions for all interactive elements */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease !important;
}   
