<?php
/**
 * Page Summary Manager
 * 
 * Handles database operations for page summaries with content change detection
 * 
 * @package SLMM_SEO_Bundle
 * @version 4.10.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SLMM_Page_Summary_Manager {

    private static $instance = null;
    private static $hooks_registered = false;
    private $table_name;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();

            // CRITICAL FIX: Only register hooks once, not every time getInstance is called
            if (!self::$hooks_registered) {
                add_action('plugins_loaded', array(self::$instance, 'maybe_create_table'));
                self::$hooks_registered = true;
            }
        }
        return self::$instance;
    }

    /**
     * Initialize the manager
     */
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'slmm_page_summaries';

        // REMOVED: Hook registration moved to get_instance() with duplicate prevention
    }
    
    /**
     * Create the summaries table if it doesn't exist
     */
    public function maybe_create_table() {
        global $wpdb;
        
        // Simple, reliable table existence check using SHOW TABLES
        $table_exists = ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") == $this->table_name);
        
        if (!$table_exists) {
            $this->create_table();
            error_log('[SLMM Page Summary Manager] Table creation triggered for: ' . $this->table_name);
            
            // Verify table was created successfully
            $table_created = ($wpdb->get_var("SHOW TABLES LIKE '{$this->table_name}'") == $this->table_name);
            if ($table_created) {
                error_log('[SLMM Page Summary Manager] Table created successfully: ' . $this->table_name);
            } else {
                error_log('[SLMM Page Summary Manager] ERROR: Table creation failed: ' . $this->table_name);
            }
        } else {
            error_log('[SLMM Page Summary Manager] Table already exists: ' . $this->table_name);
        }
    }
    
    /**
     * Create the summaries table using WordPress dbDelta for safety
     */
    public function create_table() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE {$this->table_name} (
            id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            url varchar(500) NOT NULL,
            content_hash varchar(64) NOT NULL,
            summary text NOT NULL,
            prompt_used text NOT NULL,
            model_used varchar(100) NOT NULL,
            provider_used varchar(50) NOT NULL,
            generated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY url_hash (url, content_hash),
            KEY url_index (url),
            KEY generated_at_index (generated_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        error_log('[SLMM Page Summary Manager] Table created: ' . $this->table_name);
    }
    
    /**
     * Generate content hash for change detection
     */
    public function generate_content_hash($content) {
        return hash('sha256', trim(strip_tags($content)));
    }
    
    /**
     * Check if summary exists for URL and content hash
     */
    public function get_summary($url, $content_hash = null) {
        global $wpdb;
        
        if ($content_hash) {
            // Check for specific content version
            $summary = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->table_name} 
                 WHERE url = %s AND content_hash = %s 
                 ORDER BY updated_at DESC LIMIT 1",
                $url,
                $content_hash
            ));
        } else {
            // Get latest summary for URL regardless of content hash
            $summary = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM {$this->table_name} 
                 WHERE url = %s 
                 ORDER BY updated_at DESC LIMIT 1",
                $url
            ));
        }
        
        return $summary;
    }
    
    /**
     * Save summary to database
     */
    public function save_summary($url, $content_hash, $summary, $prompt_used, $model_used, $provider_used) {
        global $wpdb;
        
        $result = $wpdb->replace(
            $this->table_name,
            array(
                'url' => $url,
                'content_hash' => $content_hash,
                'summary' => $summary,
                'prompt_used' => $prompt_used,
                'model_used' => $model_used,
                'provider_used' => $provider_used,
                'updated_at' => current_time('mysql')
            ),
            array(
                '%s', // url
                '%s', // content_hash
                '%s', // summary
                '%s', // prompt_used
                '%s', // model_used
                '%s', // provider_used
                '%s'  // updated_at
            )
        );
        
        if ($result === false) {
            error_log('[SLMM Page Summary Manager] Failed to save summary for URL: ' . $url);
            return false;
        }
        
        return $wpdb->insert_id ?: true;
    }
    
    /**
     * Check if content has changed since last summary
     */
    public function has_content_changed($url, $current_content_hash) {
        $existing_summary = $this->get_summary($url);
        
        if (!$existing_summary) {
            return true; // No summary exists
        }
        
        return $existing_summary->content_hash !== $current_content_hash;
    }
    
    /**
     * Get summary status for URL
     */
    public function get_summary_status($url, $content_hash = null) {
        $summary = $this->get_summary($url);
        
        if (!$summary) {
            return array(
                'status' => 'none',
                'summary' => null,
                'needs_update' => true
            );
        }
        
        $needs_update = false;
        if ($content_hash && $summary->content_hash !== $content_hash) {
            $needs_update = true;
        }
        
        return array(
            'status' => $needs_update ? 'outdated' : 'current',
            'summary' => $summary,
            'needs_update' => $needs_update
        );
    }
    
    /**
     * Delete old summaries for cleanup
     */
    public function cleanup_old_summaries($days_old = 30) {
        global $wpdb;
        
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days_old} days"));
        
        $deleted = $wpdb->query($wpdb->prepare(
            "DELETE FROM {$this->table_name} WHERE updated_at < %s",
            $cutoff_date
        ));
        
        if ($deleted !== false) {
            error_log("[SLMM Page Summary Manager] Cleaned up {$deleted} old summaries");
        }
        
        return $deleted;
    }
    
    /**
     * Get summary statistics
     */
    public function get_stats() {
        global $wpdb;
        
        $total_summaries = $wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        $unique_urls = $wpdb->get_var("SELECT COUNT(DISTINCT url) FROM {$this->table_name}");
        
        $recent_summaries = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE generated_at >= %s",
            date('Y-m-d H:i:s', strtotime('-7 days'))
        ));
        
        return array(
            'total_summaries' => (int) $total_summaries,
            'unique_urls' => (int) $unique_urls,
            'recent_summaries' => (int) $recent_summaries
        );
    }
    
    /**
     * Delete summary from database
     */
    public function delete_summary($url) {
        global $wpdb;
        
        $deleted = $wpdb->delete(
            $this->table_name,
            array('url' => $url),
            array('%s')
        );
        
        if ($deleted !== false && $deleted > 0) {
            error_log("[SLMM Page Summary Manager] Deleted summary for URL: {$url}");
        }
        
        return $deleted !== false && $deleted > 0;
    }
    
    /**
     * Get table name for external access
     */
    public function get_table_name() {
        return $this->table_name;
    }
}

// Initialize the manager
SLMM_Page_Summary_Manager::get_instance();