---
description: Interactive conversation history management - count, preview, and clean up old conversations
allowed-tools: Bash(*)
---

# Conversation History Manager

You are helping the user manage their Claude Code conversation history. 

## Current Status
[Error: <PERSON> requested permissions to use <PERSON><PERSON>, but you haven't granted it yet.]

## Your Task

1. **Show the current conversation count** (already shown above)
2. **Ask the user**: "How many of your most recent conversations would you like to keep?"
3. **When they provide a number (N)**, perform these steps:

   a) **Preview what will be deleted:**
   ```bash
   CONV_DIR=$(pwd | sed 's|/|-|g' | sed 's|^-||')
   CONV_DIR="$HOME/.claude/projects/$CONV_DIR"
   TOTAL=$(ls "$CONV_DIR"/*.jsonl 2>/dev/null | wc -l | tr -d ' ')
   TO_DELETE=$((TOTAL - N))
   if [ $TO_DELETE -gt 0 ]; then
       echo "🗑️  Files to be deleted ($TO_DELETE conversations):"
       ls -t "$CONV_DIR"/*.jsonl 2>/dev/null | tail -n +$((N+1)) | head -10
       if [ $TO_DELETE -gt 10 ]; then
           echo "... and $((TO_DELETE - 10)) more files"
       fi
   else
       echo "✅ No files to delete. You want to keep $N conversations and have $TOTAL total."
   fi
   ```

   b) **Ask for confirmation** before proceeding with deletion

   c) **If confirmed, perform the deletion:**
   ```bash
   CONV_DIR=$(pwd | sed 's|/|-|g' | sed 's|^-||')
   CONV_DIR="$HOME/.claude/projects/$CONV_DIR"
   TOTAL_BEFORE=$(ls "$CONV_DIR"/*.jsonl 2>/dev/null | wc -l | tr -d ' ')
   
   # Delete old conversations (keep newest N)
   ls -t "$CONV_DIR"/*.jsonl 2>/dev/null | tail -n +$((N+1)) | xargs rm -f 2>/dev/null
   
   TOTAL_AFTER=$(ls "$CONV_DIR"/*.jsonl 2>/dev/null | wc -l | tr -d ' ')
   DELETED=$((TOTAL_BEFORE - TOTAL_AFTER))
   
   echo "✅ Cleanup complete!"
   echo "🗑️ Deleted: $DELETED conversations" 
   echo "💾 Remaining: $TOTAL_AFTER conversations"
   ```

   d) **Show final summary**

## Important Notes
- Replace "N" in the commands with the actual number the user specifies
- Always preview before deleting
- Get explicit user confirmation before deletion
- Handle edge cases (like when no deletion is needed)
- Be clear about what's happening at each step