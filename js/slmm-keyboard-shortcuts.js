/*global jQuery*/

/**
 * SLMM Keyboard Shortcuts - Simple and Reliable
 * Supports standard Cmd+S (Mac) and Ctrl+S (Windows) shortcuts
 * Enhanced with BRICKS BUILDER support
 */
(function($, undefined) {
    'use strict';
    
    var isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    var doingClick = false;
    var isBricksContext = window.location.href.indexOf('?bricks=run') !== -1;

    /**
     * Initialize on document ready
     */
    $(function() {
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Keyboard Shortcuts initializing', {
                url: window.location.href,
                isBricksContext: isBricksContext,
                saveWithKeyboard: window.SaveWithKeyboard
            }, 'shortcuts');
        }
        
        // Early return if feature is not enabled
        if (!window.SaveWithKeyboard || window.SaveWithKeyboard.enabled !== '1') {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('SaveWithKeyboard feature not enabled, exiting', null, 'shortcuts');
            }
            return;
        }

        // Set up global keyboard shortcut
        $(document).on('keydown.slmm-shortcuts', handleKeydown);
        
        // Set up TinyMCE integration if available
        setupTinyMCE();
        
        if (typeof SlmmDebugLogger !== 'undefined') {
            SlmmDebugLogger.log('Keyboard Shortcuts initialized - ' + (isMac ? 'Cmd+S' : 'Ctrl+S') + ' to save', {
                isMac: isMac,
                bricksContext: isBricksContext
            }, 'shortcuts');
        }
    });

    /**
     * Handle keyboard shortcuts
     */
    function handleKeydown(e) {
        // Standard save shortcut: Cmd+S on Mac, Ctrl+S on Windows
        var isShortcut = isMac ? 
            (e.metaKey && !e.ctrlKey && !e.altKey && e.keyCode === 83) : // Cmd+S on Mac
            (e.ctrlKey && !e.metaKey && !e.altKey && e.keyCode === 83);  // Ctrl+S on Windows

        if (isShortcut) {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Save shortcut detected', {
                    shortcut: isMac ? 'Cmd+S' : 'Ctrl+S',
                    isMac: isMac
                }, 'shortcuts');
            }
            e.preventDefault();
            e.stopPropagation();
            performSave();
            return false;
        }
        
        // Debug GPT shortcuts specifically
        if ((e.metaKey && e.ctrlKey && e.keyCode >= 49 && e.keyCode <= 57) || 
            (e.ctrlKey && e.altKey && e.keyCode >= 49 && e.keyCode <= 57)) {
            var promptNumber = e.keyCode - 48;
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('GPT shortcut attempted', {
                    meta: e.metaKey,
                    ctrl: e.ctrlKey,
                    alt: e.altKey,
                    keyCode: e.keyCode,
                    promptNumber: promptNumber,
                    shouldTrigger: true
                }, 'shortcuts');
            }
        }
    }

    /**
     * Set up TinyMCE integration
     */
    function setupTinyMCE() {
        // Wait for TinyMCE to be available
        var checkTinyMCE = function() {
            if (typeof tinyMCE !== 'undefined' && tinyMCE.editors && tinyMCE.editors.length > 0) {
                // Add shortcut to all TinyMCE editors
                for (var i = 0; i < tinyMCE.editors.length; i++) {
                    var editor = tinyMCE.editors[i];
                    if (editor && editor.addShortcut) {
                        var shortcut = isMac ? 'meta+s' : 'ctrl+s';
                        editor.addShortcut(shortcut, 'Save Post', performSave);
                    }
                }
            }
            
            // Special handling for Bricks Builder
            if (isBricksContext) {
                setupBricksIntegration();
            }
        };

        // Check immediately and on TinyMCE init
        checkTinyMCE();
        $(document).on('tinymce-editor-init', function(event, editor) {
            if (editor && editor.addShortcut) {
                var shortcut = isMac ? 'meta+s' : 'ctrl+s';
                editor.addShortcut(shortcut, 'Save Post', performSave);
            }
        });
        
        // Poll for new editors (especially useful for Bricks)
        if (isBricksContext) {
            if (typeof SlmmDebugLogger !== 'undefined') {
                SlmmDebugLogger.log('Setting up Bricks editor polling', null, 'shortcuts');
            }
            var pollForNewEditors = function() {
                if (typeof tinyMCE !== 'undefined') {
                    var foundNewEditor = false;
                    for (var editorId in tinyMCE.editors) {
                        if (editorId.indexOf('brickswpeditor') !== -1) {
                            var editor = tinyMCE.editors[editorId];
                            if (editor && editor.initialized && !editor._slmmShortcutsAdded) {
                                foundNewEditor = true;
                                var shortcut = isMac ? 'meta+s' : 'ctrl+s';
                                if (typeof SlmmDebugLogger !== 'undefined') {
                                    SlmmDebugLogger.log('Adding save shortcut to Bricks editor', {editorId: editorId}, 'shortcuts');
                                }
                                try {
                                    editor.addShortcut(shortcut, 'Save Post', performSave);
                                    editor._slmmShortcutsAdded = true;
                                    if (typeof SlmmDebugLogger !== 'undefined') {
                                        SlmmDebugLogger.log('Successfully added shortcuts to Bricks editor', {editorId: editorId}, 'shortcuts');
                                    }
                                } catch (error) {
                                    if (typeof SlmmDebugLogger !== 'undefined') {
                                        SlmmDebugLogger.log('Error adding shortcuts to Bricks editor', {editorId: editorId, error: error}, 'shortcuts');
                                    }
                                }
                            }
                        }
                    }
                    if (foundNewEditor) {
                        if (typeof SlmmDebugLogger !== 'undefined') {
                            SlmmDebugLogger.log('Found and set up new Bricks editors', null, 'shortcuts');
                        }
                    }
                }
                // Continue polling but less frequently
                setTimeout(pollForNewEditors, 3000);
            };
            setTimeout(pollForNewEditors, 2000);
        }
    }

    /**
     * Set up Bricks Builder specific integration
     */
    function setupBricksIntegration() {
        // Monitor for Bricks editor initialization
        var observeBricksEditors = function() {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            // Check if this is a Bricks editor iframe
                            var iframes = node.querySelectorAll ? node.querySelectorAll('iframe[id*="brickswpeditor"]') : [];
                            if (node.tagName === 'IFRAME' && node.id && node.id.indexOf('brickswpeditor') !== -1) {
                                iframes = [node];
                            }
                            
                            for (var i = 0; i < iframes.length; i++) {
                                var iframe = iframes[i];
                                var editorId = iframe.id.replace('_ifr', '');
                                if (typeof SlmmDebugLogger !== 'undefined') {
                                    SlmmDebugLogger.log('Detected new Bricks editor iframe', {editorId: editorId}, 'shortcuts');
                                }
                                
                                // Wait a bit for the editor to initialize
                                setTimeout(function(id) {
                                    return function() {
                                        if (typeof tinyMCE !== 'undefined' && tinyMCE.editors[id]) {
                                            var editor = tinyMCE.editors[id];
                                            if (editor && editor.initialized && !editor._slmmShortcutsAdded) {
                                                var shortcut = isMac ? 'meta+s' : 'ctrl+s';
                                                editor.addShortcut(shortcut, 'Save Post', performSave);
                                                editor._slmmShortcutsAdded = true;
                                                if (typeof SlmmDebugLogger !== 'undefined') {
                                                    SlmmDebugLogger.log('Added shortcuts to new Bricks editor', {editorId: id}, 'shortcuts');
                                                }
                                            }
                                        }
                                    };
                                }(editorId), 1000);
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        };
        
        // Start observing
        if (typeof MutationObserver !== 'undefined') {
            observeBricksEditors();
        }
    }

    /**
     * Perform the save action
     */
    function performSave() {
        if (doingClick) {
            return false;
        }

        doingClick = true;

        try {
            // Save TinyMCE content first if available
            if (typeof tinyMCE !== 'undefined') {
                // Save all editors, including Bricks editors
                for (var editorId in tinyMCE.editors) {
                    var editor = tinyMCE.editors[editorId];
                    if (editor && editor.initialized) {
                        editor.save();
                    }
                }
                
                // Also save the active editor
                if (tinyMCE.activeEditor) {
                tinyMCE.activeEditor.save();
                }
            }

            // Find and click the appropriate save button
            var $saveButton = findSaveButton();
            
            if ($saveButton && $saveButton.length > 0 && $saveButton.is(':visible')) {
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('Triggering save via button', {
                        buttonId: $saveButton.attr('id'),
                        buttonClass: $saveButton.attr('class')
                    }, 'shortcuts');
                }
                $saveButton.click();
            } else {
                if (typeof SlmmDebugLogger !== 'undefined') {
                    SlmmDebugLogger.log('No visible save button found', null, 'shortcuts');
                }
                // Fallback: try to submit the main post form
                var $form = $('#post, #edittag, #the-list');
                if ($form.length > 0) {
                    $form.submit();
                }
            }
        } catch (error) {
            console.error('SLMM Keyboard Shortcuts error:', error);
        }

        // Reset the flag
        setTimeout(function() {
            doingClick = false;
        }, 1000);

        return false;
    }

    /**
     * Find the appropriate save button for the current page
     */
    function findSaveButton() {
        var $body = $(document.body);
        
        // Special handling for Bricks Builder
        if (isBricksContext) {
            // Look for Bricks specific save buttons
            var $bricksSave = $('.bricks-save, [data-balloon*="save" i], .bricks-panel-save, #bricks-save-button');
            if ($bricksSave.length > 0 && $bricksSave.is(':visible')) {
                return $bricksSave.first();
            }
        }
        
        // WordPress post editor (classic and Gutenberg)
        if ($body.hasClass('post-php') || $body.hasClass('post-new-php')) {
            // Check for Gutenberg editor first
            var $gutenbergSave = $('.editor-post-publish-button, .editor-post-save-draft');
            if ($gutenbergSave.length > 0 && $gutenbergSave.is(':visible')) {
                return $gutenbergSave.first();
            }
            
            // Classic editor - prioritize the main publish/update button
            var $publish = $('#publish');
            if ($publish.length > 0 && $publish.is(':visible')) {
                return $publish;
            }
            
            // Fall back to save draft
            var $saveDraft = $('#save-post');
            if ($saveDraft.length > 0 && $saveDraft.is(':visible')) {
                return $saveDraft;
            }
        }
        
        // Page editor
        if ($body.hasClass('page-php') || $body.hasClass('page-new-php')) {
            return $('#publish, #save-post').filter(':visible').first();
        }
        
        // Comments
        if ($body.hasClass('comment-php')) {
            return $('#save').filter(':visible').first();
        }
        
        // Widgets
        if ($body.hasClass('widgets-php')) {
            return $('.widget-control-save:visible').first();
        }
        
        // Customizer
        if ($body.hasClass('wp-customizer')) {
            return $('#save').filter(':visible').first();
        }
        
        // Menu editor
        if ($body.hasClass('nav-menus-php')) {
            return $('#save_menu_header').filter(':visible').first();
        }
        
        // Generic fallbacks
        var $genericButtons = $('#publish, #save-post, #save, #submit, .button-primary[type="submit"]');
        return $genericButtons.filter(':visible').first();
    }

    /**
     * Clean up on page unload
     */
    $(window).on('beforeunload.slmm-shortcuts', function() {
        $(document).off('keydown.slmm-shortcuts');
    });

})(jQuery); 