# Memory Bank System Integration

This document provides comprehensive guidance on integrating with the Memory Bank system for the SLMM SEO Bundle plugin development.

## Directory Structure & Usage

```
memory-bank/
├── README.md - System overview
├── current-session.md - Session context
├── decisions/ - Architectural decisions
├── patterns/ - Code patterns and best practices
├── issues/ - Known issues and solutions
├── features/ - Feature documentation
└── todo/ - Project todos and overviews
```

## Memory Bank Workflow

```javascript
// 1. Always check existing memories first
list_memories()

// 2. Read relevant memory files
read_memory("dual-system-architecture")
read_memory("data-localization-patterns")

// 3. Write new findings for future sessions
write_memory("new-feature-patterns", "# Feature Implementation\n...")
```

## Decision Documentation Pattern

```markdown
# Decision: [Title]
## Date: YYYY-MM-DD
## Context: [Situation requiring decision]
## Decision: [What was decided]
## Rationale: [Why this decision was made]
## Consequences: [Impact and implications]
## Status: [Active/Deprecated/Superseded]
```

## MANDATORY SERENA MEMORY REQUIREMENTS (CRITICAL)

### Context Window Protection Protocol (ABSOLUTE REQUIREMENT)

**BEFORE any complex implementation task, you MUST:**

1. **Create task PRD memory** with serena write_memory
2. **Document architectural decisions** in structured memory files
3. **Estimate file sizes** and document split strategies if needed
4. **Create implementation roadmap** with step-by-step breakdown
5. **Update progress memories** after each major milestone

### Mandatory Memory Creation Triggers

**You MUST create serena memory when:**
- Task has 3+ implementation steps
- Multiple files will be created/modified
- Complex integrations with existing systems required
- File size estimates approach 700+ lines
- Context window is >75% utilized
- Implementation will span multiple sessions

### PRD Memory Template (MANDATORY FORMAT)

**Every complex task requires a comprehensive PRD memory:**

```markdown
# Task: [Task Name] - Implementation PRD
## Date: [YYYY-MM-DD]
## Complexity: [Simple/Medium/Complex]
## Estimated Duration: [Sessions/Hours]
## File Size Estimates: [Per file, with totals]
## Architecture Overview: [High-level approach]
## Integration Points: [Existing code connections]
## Plugin.php Changes: [Required modifications]
## Implementation Steps:
1. [Step 1 with file size estimate]
2. [Step 2 with file size estimate]
3. [etc.]
## Testing Requirements: [Validation approach]
## Risk Factors: [Potential complications]
## Rollback Plan: [If implementation fails]
```

### Memory Naming Standards (ENFORCE STRICTLY)

**Use consistent naming for easy retrieval across sessions:**
- `task-[name]-prd` - Primary requirements document
- `task-[name]-architecture` - Architectural decisions and patterns
- `task-[name]-progress` - Implementation progress and milestones
- `task-[name]-issues` - Problems encountered and solutions
- `task-[name]-testing` - Testing protocols and results
- `file-size-analysis-[date]` - File size monitoring and split decisions

### Implementation Progress Memory Updates

**Update memory after EVERY major milestone:**

```javascript
// Template for progress updates
write_memory("task-[name]-progress", `
# Progress Update: [Date]
## Completed:
- [Completed items with file sizes]
## In Progress:
- [Current work with size estimates]
## Next Steps:
- [Planned work with size estimates]
## File Size Status:
- [Current line counts for each file]
## Architecture Decisions Made:
- [Any new patterns or decisions]
## Issues Encountered:
- [Problems and solutions]
`);
```

### Context Window Emergency Protocol

**When approaching context limit (>90% utilized):**
1. **IMMEDIATELY create emergency memory** with current state
2. **Document all file changes made** with line counts
3. **List remaining tasks** with size estimates
4. **Note integration points** completed/remaining
5. **Create handoff instructions** for next session

## Memory Bank Integration with Development Workflow

### Pre-Development Phase
- **Always check memory-bank/** before analyzing code
- **Use read_memory** for established patterns and decisions
- **Reference existing documentation** in memory-bank/patterns/
- **Follow memory management patterns** from memory-bank/memory-management-patterns.md

### During Development
- **Write new findings** to memory bank for future sessions
- **Document architectural decisions** as they're made
- **Update progress memories** after completing major milestones
- **Record integration patterns** for reuse

### Post-Development Phase
- **Document lessons learned** in appropriate memory files
- **Update architectural decision records** with outcomes
- **Create reusable patterns** documentation for similar future tasks
- **Archive completed task memories** with final status

## Best Practices for Memory Bank Usage

### Memory File Organization
- Keep memory files focused on single topics
- Use clear, descriptive naming conventions
- Include dates and version information
- Cross-reference related memories

### Content Quality Standards
- Include specific code examples and patterns
- Document both successful and failed approaches
- Provide context for future developers
- Keep information current and accurate

### Integration with Plugin Development
- Align memory structure with plugin architecture
- Reference memory bank patterns in code comments
- Use memory bank for onboarding new developers
- Maintain consistency across development sessions

## Memory Bank Maintenance

### Regular Reviews
- Review and update outdated memories
- Archive deprecated patterns
- Consolidate related memories
- Maintain searchable index

### Quality Control
- Verify code examples remain functional
- Update references to changed file locations
- Ensure consistent formatting
- Remove duplicate information

This memory bank integration system ensures continuity across development sessions and maintains institutional knowledge for the SLMM SEO Bundle plugin.