---
task: m-fix-semantic-indicator-removal
branch: fix/semantic-indicator-removal
status: pending
created: 2025-09-04
modules: [interlinking, semantic-indicators]
---

# Fix Semantic Indicator Green Highlight Removal

## Problem/Goal
The semantic interlinking sidebar correctly adds green highlights (`slmm-link-found-in-content` class) when links are inserted into TinyMCE content, but fails to remove these highlights when links are subsequently removed from the content. This creates a mismatch between the actual content state and the visual indicators.

## Success Criteria
- [ ] Green highlights are removed from sidebar items when corresponding links are deleted from content
- [ ] Green highlights are added when links are inserted (already working)
- [ ] Scanning system properly detects both additions and removals
- [ ] Visual indicators accurately reflect current content state
- [ ] No flashing or visual artifacts during updates

## Context Files
<!-- Added by context-gathering agent or manually -->
- @includes/interlinking/interlinking-suite.php  # Main interlinking controller
- patterns/semantic-indicator-completion-tracking  # Current implementation patterns

## User Notes
Current behavior:
1. Insert link → green highlight appears ✅ (works)
2. Remove link → green highlight persists ❌ (broken)

Expected behavior:
- Links should be scanned bidirectionally (add AND remove classes)
- Clean state management without orphaned highlights

## Work Log
- [2025-09-04] Task created - semantic indicator removal issue identified
- [2025-09-04] Fixed applyLinkTrackingIndicators function with bidirectional approach
- [2025-09-04] Implemented proper removal of orphaned green highlights
- [2025-09-04] Committed fix (bca387e) - ready for testing