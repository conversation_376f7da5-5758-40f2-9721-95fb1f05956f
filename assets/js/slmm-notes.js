/**
 * SLMM Notes Feature - JavaScript
 * 
 * Handles the notes popup functionality including opening/closing,
 * resizing, auto-save, and formatting toolbar.
 * 
 * @package SLMM_SEO_Bundle
 * @since 4.8.1
 */

(function($) {
    'use strict';

    let isPopupOpen = false;
    let isDirty = false;
    let notesPopup, notesOverlay, notesEditor;
    let lastSavedContent = '';
    let lastLoadedTimestamp = '';
    let saveInProgress = false;
    let conflictResolutionActive = false;

    // Debug helper methods
    const debug = {
        log: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            }
        },
        success: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.success(category, message, data);
            }
        },
        info: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.info(category, message, data);
            }
        },
        error: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            }
        },
        warn: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            }
        }
    };

    // Initialize on document ready
    $(document).ready(function() {
        debug.log('Notes', 'Initializing SLMM Notes...');
        
        // Wait for DOM elements to be available
        setTimeout(function() {
            initializeNotes();
        }, 100);
    });

    function initializeNotes() {
        // Initialize elements
        notesPopup = $('#slmm-notes-popup');
        notesOverlay = $('#slmm-notes-overlay');
        notesEditor = $('#slmm-notes-editor');

        // Verify elements exist
        if (notesPopup.length === 0 || notesOverlay.length === 0 || notesEditor.length === 0) {
            debug.error('Notes', 'Required elements not found in DOM');
            return;
        }

        // Log element discovery
        debug.log('Notes', 'Elements found', {
            popup: notesPopup.length,
            overlay: notesOverlay.length,
            editor: notesEditor.length
        });

        // Bind events
        bindEvents();

        // Load existing notes
        loadNotes();

        // Set up periodic sync checking to handle multiple tabs
        setInterval(function() {
            if (isPopupOpen && !saveInProgress && !conflictResolutionActive) {
                checkForUpdates();
            }
        }, 30000); // Check every 30 seconds when popup is open

        // Set up keyboard shortcuts for opening notes
        $(document).keydown(function(e) {
            // Ctrl+Shift+N (Windows) or Cmd+Shift+N (Mac)
            if (((e.ctrlKey || e.metaKey) && e.shiftKey && e.which === 78)) {
                e.preventDefault();
                debug.log('Notes', 'Notes shortcut triggered');
                toggleNotesPopup();
            }
        });

        // Create global debug object immediately
        window.slmmNotesDebug = {
            removeLinkAlternative: removeLinkAlternative,
            formatText: formatText,
            applyFormattingAlternative: applyFormattingAlternative,
            insertLinkAlternative: insertLinkAlternative,
            markDirty: markDirty,
            saveNotes: saveNotes,
            loadNotes: loadNotes
        };

        // Create global test functions immediately
        window.testBulletList = function() {
            debug.log('Notes', '🧪 Manual bullet list test...');
            notesEditor.focus();
            formatText('insertUnorderedList');
        };
        
        window.testUnlink = function() {
            debug.log('Notes', '🧪 Manual unlink test...');
            notesEditor.focus();
            removeLinkAlternative();
        };

        window.testButtonClick = function() {
            debug.log('Notes', '🧪 Testing direct button click...');
            const bulletBtn = $('.slmm-notes-format-btn[data-command="insertUnorderedList"]');
            const numberedBtn = $('.slmm-notes-format-btn[data-command="insertOrderedList"]');
            const unlinkBtn = $('.slmm-notes-format-btn[data-command="unlink"]');
            
            debug.log('Notes', '🧪 Found buttons', {
                bullet: bulletBtn.length,
                numbered: numberedBtn.length,
                unlink: unlinkBtn.length
            });
            
            if (bulletBtn.length > 0) {
                debug.log('Notes', '🧪 Simulating bullet list button click...');
                bulletBtn.first().trigger('click');
            }
        };

        debug.success('Notes', 'SLMM Notes initialized successfully');
        debug.info('Notes', 'Global debug functions created: testBulletList(), testUnlink(), testButtonClick()');
    }

    function bindEvents() {
        // Close popup when clicking overlay
        notesOverlay.on('click', function(e) {
            if (e.target === this) {
                closeNotesPopup();
            }
        });

        // Close popup when clicking close button
        $('.slmm-notes-close').on('click', closeNotesPopup);

        // Close popup on Escape key
        $(document).on('keydown', function(e) {
            if (e.which === 27 && isPopupOpen) { // Escape key
                closeNotesPopup();
            }
        });

        // Admin bar notes link
        $('#wp-admin-bar-slmm-notes a').on('click', function(e) {
            e.preventDefault();
            toggleNotesPopup();
        });

        // Format button handling - SIMPLIFIED WITHOUT MOUSEDOWN
        debug.log('Notes', '🔧 Setting up click event handler for .slmm-notes-format-btn');
        
        $(document).on('click', '.slmm-notes-format-btn', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const command = $(this).data('command');
            debug.log('Notes', '🎯 Button clicked! Command: ' + command);
            
            // Focus editor first
            notesEditor.focus();
            
            // Execute command
            formatText(command);
            
            return false;
        });

        // Editor event handlers
        notesEditor.on('input paste keyup DOMSubtreeModified', function() {
            markDirty();
            updateToolbarStates();
            // Update admin bar icon in real-time as user types
            const currentContent = notesEditor.html();
            updateAdminBarIcon(currentContent);
        });

        notesEditor.on('mouseup keyup', function() {
            setTimeout(updateToolbarStates, 10);
        });

        // Paste cleanup to remove unwanted formatting
        notesEditor.on('paste', function(e) {
            setTimeout(function() {
                // Clean up pasted content
                const content = notesEditor.html();
                const cleaned = content.replace(/<span[^>]*>/g, '').replace(/<\/span>/g, '');
                notesEditor.html(cleaned);
                markDirty();
            }, 10);
        });

        // Keyboard shortcuts for formatting
        notesEditor.on('keydown', function(e) {
            // Only handle keyboard shortcuts, not Enter key for regular text
            if (e.ctrlKey || e.metaKey) {
                switch(e.which) {
                    case 66: // B for bold
                        e.preventDefault();
                        formatText('bold');
                        break;
                    case 73: // I for italic
                        e.preventDefault();
                        formatText('italic');
                        break;
                    case 85: // U for underline
                        e.preventDefault();
                        formatText('underline');
                        break;
                    case 75: // K for link
                        e.preventDefault();
                        formatText('createLink');
                        break;
                }
            }
            
            // Allow normal Enter behavior for regular text (not in checkboxes)
            // The checkbox-specific Enter handler is bound separately below
        });
    }

    function toggleNotesPopup() {
        if (isPopupOpen) {
            closeNotesPopup();
        } else {
            openNotesPopup();
        }
    }

    function openNotesPopup() {
        debug.log('Notes', '📝 Opening notes popup');
        
        notesOverlay.fadeIn(200);
        notesPopup.fadeIn(200, function() {
            // Focus editor after popup is shown
            setTimeout(function() {
                notesEditor.focus();
            }, 50);
        });
        
        isPopupOpen = true;
        
        // Load notes content
        loadNotes();
    }

    function closeNotesPopup() {
        debug.log('Notes', '💾 Closing notes popup and saving');
        
        // Save notes when closing
        saveNotes();
        
        notesPopup.fadeOut(200);
        notesOverlay.fadeOut(200);
        isPopupOpen = false;
    }


    function formatText(command) {
        debug.log('Notes', '📝 Format command: ' + command);
        
        // Ensure editor has focus
        if (!notesEditor.is(':focus')) {
            notesEditor.focus();
        }

        // Handle special commands
        if (command === 'createLink') {
            const url = prompt('Enter URL:');
            if (url) {
                insertLinkAlternative(url);
            }
            return;
        }
        
        if (command === 'unlink') {
            removeLinkAlternative();
            return;
        }

        // Try execCommand first
        try {
            if (document.queryCommandSupported(command)) {
                const success = document.execCommand(command, false, null);
                debug.log('Notes', '📝 execCommand result for ' + command + ': ' + success);
                
                if (success) {
                    markDirty();
                    updateToolbarStates();
                    return;
                }
            }
        } catch (e) {
            debug.log('Notes', '📝 execCommand failed for ' + command + ': ' + e.message);
        }

        // Fallback to manual formatting
        debug.log('Notes', '📝 Using fallback formatting for: ' + command);
        applyFormattingAlternative(command);
    }

    // Lists are handled by standard execCommand - no custom function needed

    function insertLinkAlternative(url) {
        debug.log('Notes', '🔗 Inserting link: ' + url);
        
        try {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;
            
            const range = selection.getRangeAt(0);
            const selectedText = range.toString();
            
            const link = document.createElement('a');
            link.href = url;
            link.textContent = selectedText || url;
            
            range.deleteContents();
            range.insertNode(link);
            
            markDirty();
            debug.success('Notes', '🔗 Link inserted successfully');
            
        } catch (e) {
            debug.error('Notes', 'Error inserting link', e);
        }
    }

    function applyFormattingAlternative(command) {
        debug.log('Notes', '🎨 Applying alternative formatting: ' + command);
        
        try {
            const selection = window.getSelection();
            if (selection.rangeCount === 0) return;
            
            const range = selection.getRangeAt(0);
            const selectedText = range.toString();
            
            if (!selectedText) return;
            
            let wrapper;
            switch (command) {
                case 'bold':
                    wrapper = document.createElement('strong');
                    break;
                case 'italic':
                    wrapper = document.createElement('em');
                    break;
                case 'underline':
                    wrapper = document.createElement('u');
                    break;
                case 'strikeThrough':
                    wrapper = document.createElement('s');
                    break;
                default:
                    return;
            }
            
            wrapper.textContent = selectedText;
            range.deleteContents();
            range.insertNode(wrapper);
            
            markDirty();
            debug.success('Notes', '🎨 Alternative formatting applied successfully');
            
        } catch (e) {
            debug.error('Notes', 'Error applying alternative formatting', e);
        }
    }

    function removeLinkAlternative() {
        debug.log('Notes', '🚫 Attempting to remove link...');
        
        try {
            const selection = window.getSelection();
            
            if (selection.rangeCount === 0) {
                debug.log('Notes', '🚫 No selection found');
                return;
            }
            
            const range = selection.getRangeAt(0);
            let node = range.commonAncestorContainer;
            
            // Find the link element
            let linkElement = null;
            
            // Check if selection is inside a link
            while (node && node !== notesEditor[0]) {
                if (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'A') {
                    linkElement = node;
                    break;
                }
                node = node.parentNode;
            }
            
            // If no link found in parents, check if selection contains a link
            if (!linkElement && range.commonAncestorContainer.nodeType === Node.ELEMENT_NODE) {
                const links = range.commonAncestorContainer.querySelectorAll('a');
                if (links.length > 0) {
                    linkElement = links[0];
                }
            }
            
            if (linkElement) {
                debug.log('Notes', '🚫 Found link element', linkElement);
                
                // Replace link with its text content
                const textContent = linkElement.textContent;
                const textNode = document.createTextNode(textContent);
                
                linkElement.parentNode.replaceChild(textNode, linkElement);
                
                markDirty();
                debug.success('Notes', '🚫 Link removed successfully');
                
            } else {
                debug.log('Notes', '🚫 No link found to remove');
            }
            
        } catch (e) {
            debug.error('Notes', 'Error removing link', e);
        }
    }

    // No special event binding needed - using standard editor behavior

    function updateToolbarStates() {
        // Update toolbar button states based on current selection
        $('.slmm-notes-format-btn').removeClass('active');
        
        try {
            if (document.queryCommandState('bold')) {
                $('.slmm-notes-format-btn[data-command="bold"]').addClass('active');
            }
            if (document.queryCommandState('italic')) {
                $('.slmm-notes-format-btn[data-command="italic"]').addClass('active');
            }
            if (document.queryCommandState('underline')) {
                $('.slmm-notes-format-btn[data-command="underline"]').addClass('active');
            }
            if (document.queryCommandState('strikeThrough')) {
                $('.slmm-notes-format-btn[data-command="strikeThrough"]').addClass('active');
            }
        } catch (e) {
            // Ignore queryCommandState errors
        }
    }

    function markDirty() {
        isDirty = true;
        updateStatus('editing');
    }

    function saveNotes() {
        const content = notesEditor.html();
        
        if (content === lastSavedContent && !isDirty) {
            return; // No changes to save
        }
        
        // Prevent concurrent saves
        if (saveInProgress) {
            debug.log('Notes', '💾 Save already in progress, skipping...');
            return;
        }
        
        debug.log('Notes', '💾 Saving notes...');
        saveInProgress = true;
        updateStatus('saving');
        
        $.ajax({
            url: slmmNotes.ajaxurl,
            type: 'POST',
            data: {
                action: 'slmm_save_note',
                nonce: slmmNotes.nonce,
                note_content: content,
                client_timestamp: lastLoadedTimestamp // Send client timestamp for conflict detection
            },
            success: function(response) {
                saveInProgress = false;
                
                if (response.success) {
                    debug.success('Notes', '💾 Notes saved successfully');
                    
                    // Handle conflict resolution
                    if (response.data.conflict_resolved) {
                        debug.log('Notes', '⚠️ Conflict detected and resolved');
                        conflictResolutionActive = true;
                        
                        // Update editor with merged content
                        const mergedContent = response.data.content;
                        notesEditor.html(mergedContent);
                        lastSavedContent = mergedContent;
                        
                        // Show conflict resolution message
                        updateStatus('conflict-resolved');
                        
                        // Reset conflict flag after showing message
                        setTimeout(function() {
                            conflictResolutionActive = false;
                            updateStatus('saved');
                        }, 5000);
                        
                    } else {
                        lastSavedContent = content;
                        updateStatus('saved');
                    }
                    
                    // Update timestamp
                    lastLoadedTimestamp = response.data.timestamp;
                    isDirty = false;
                    
                    // Update admin bar icon color based on content
                    updateAdminBarIcon(content);
                    
                } else {
                    debug.error('Notes', 'Error saving note', response.data);
                    updateStatus('error');
                }
            },
            error: function(xhr, status, error) {
                debug.error('Notes', 'AJAX error saving note', error);
                saveInProgress = false;
                updateStatus('error');
            }
        });
    }

    function loadNotes() {
        debug.log('Notes', '📥 Loading notes...');
        
        $.ajax({
            url: slmmNotes.ajaxurl,
            type: 'POST',
            data: {
                action: 'slmm_get_note',
                nonce: slmmNotes.nonce
            },
            success: function(response) {
                if (response.success) {
                    const content = response.data.content || '';
                    const timestamp = response.data.timestamp || '';
                    
                    notesEditor.html(content);
                    lastSavedContent = content;
                    lastLoadedTimestamp = timestamp;
                    isDirty = false;
                    updateStatus('');
                    
                    // Update admin bar icon color based on loaded content
                    updateAdminBarIcon(content);
                    
                    debug.log('Notes', '📥 Notes loaded with timestamp: ' + timestamp);
                } else {
                    debug.error('Notes', 'Error loading note', response.data);
                }
            },
            error: function(xhr, status, error) {
                debug.error('Notes', 'AJAX error loading note', error);
            }
        });
    }

    function updateStatus(status) {
        const statusEl = $('.slmm-notes-status');
        
        statusEl.removeClass('saving saved error conflict-resolved sync-conflict synced');
        
        switch (status) {
            case 'saving':
                statusEl.addClass('saving').text(slmmNotes.strings.saving);
                break;
            case 'saved':
                statusEl.addClass('saved').text(slmmNotes.strings.saved);
                break;
            case 'error':
                statusEl.addClass('error').text(slmmNotes.strings.error);
                break;
            case 'conflict-resolved':
                statusEl.addClass('conflict-resolved').text('⚠️ Conflict resolved - versions merged');
                break;
            case 'sync-conflict':
                statusEl.addClass('sync-conflict').text('⚠️ Newer version available - will merge on save');
                break;
            case 'synced':
                statusEl.addClass('synced').text('🔄 Synced with server');
                setTimeout(function() {
                    statusEl.text('');
                    statusEl.removeClass('synced');
                }, 3000);
                break;
            case 'editing':
                statusEl.text('Modified');
                break;
            default:
                statusEl.text('');
        }
    }

    function checkForUpdates() {
        debug.log('Notes', '🔄 Checking for updates from other tabs...');
        
        $.ajax({
            url: slmmNotes.ajaxurl,
            type: 'POST',
            data: {
                action: 'slmm_get_note',
                nonce: slmmNotes.nonce
            },
            success: function(response) {
                if (response.success) {
                    const serverTimestamp = response.data.timestamp;
                    const serverContent = response.data.content || '';
                    
                    // Check if server has newer content
                    if (serverTimestamp && lastLoadedTimestamp) {
                        const serverTime = new Date(serverTimestamp).getTime();
                        const clientTime = new Date(lastLoadedTimestamp).getTime();
                        
                        if (serverTime > clientTime && serverContent !== lastSavedContent) {
                            debug.log('Notes', '⚠️ Newer version detected on server');
                            
                            // Show notification about newer version
                            const currentContent = notesEditor.html();
                            if (currentContent !== serverContent && isDirty) {
                                // There are local unsaved changes - prepare for conflict resolution
                                debug.log('Notes', '⚠️ Local changes detected - will merge on next save');
                                updateStatus('sync-conflict');
                                
                                // Update our timestamp but don't overwrite content yet
                                lastLoadedTimestamp = serverTimestamp;
                            } else if (!isDirty) {
                                // No local changes, safe to update
                                debug.log('Notes', '✅ Safe to update - no local changes');
                                notesEditor.html(serverContent);
                                lastSavedContent = serverContent;
                                lastLoadedTimestamp = serverTimestamp;
                                updateStatus('synced');
                            }
                        }
                    }
                }
            },
            error: function(xhr, status, error) {
                debug.error('Notes', 'Error checking for updates', error);
            }
        });
    }

    function updateAdminBarIcon(content) {
        const adminBarItem = $('#wp-admin-bar-slmm-notes');
        
        if (!adminBarItem.length) {
            debug.log('Notes', 'Admin bar notes item not found');
            return;
        }
        
        // Check if content has meaningful text (strip HTML and whitespace)
        const textContent = $('<div>').html(content).text().trim();
        const hasNotes = textContent.length > 0;
        
        if (hasNotes) {
            adminBarItem.addClass('slmm-notes-has-content');
            adminBarItem.find('.ab-item').attr('title', 'Notes available - Click to open');
            debug.log('Notes', '🟣 Admin bar icon updated to purple - notes exist');
        } else {
            adminBarItem.removeClass('slmm-notes-has-content');
            adminBarItem.find('.ab-item').attr('title', 'Click to open project notes');
            debug.log('Notes', '⚪ Admin bar icon updated to default - no notes');
        }
    }

    // Update toolbar states when selection changes (better approach)
    $(document).on('selectionchange', function() {
        if (isPopupOpen && notesEditor.length && notesEditor.is(':focus')) {
            setTimeout(updateToolbarStates, 50);
        }
    });

})(jQuery);

/*
===== SLMM NOTES DEBUG CONSOLE SCRIPT =====

Copy and paste this entire script into your browser console when the notes popup is open:

// SLMM Notes Debug Script
(function() {
    // Diagnostic debug function for this script
    function diagnosticLog(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.log('Notes Diagnostics', message, data);
        } else {
            console.log('[Notes Diagnostics] ' + message, data || '');
        }
    }
    
    function diagnosticError(message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.error('Notes Diagnostics', message, data);
        } else {
            console.error('[Notes Diagnostics] ' + message, data || '');
        }
    }
    
    diagnosticLog('🚀 ===== SLMM NOTES DIAGNOSTIC SCRIPT =====');
    
    // Check jQuery availability
    if (typeof jQuery === 'undefined') {
        console.error('❌ jQuery is not available');
        return;
    } else {
        console.log('✅ jQuery is available:', jQuery.fn.jquery);
    }
    
    // Check notes elements
    const popup = jQuery('#slmm-notes-popup');
    const overlay = jQuery('#slmm-notes-overlay');
    const editor = jQuery('#slmm-notes-editor');
    const buttons = jQuery('.slmm-notes-format-btn');
    
    console.log('📋 Elements found:', {
        popup: popup.length,
        overlay: overlay.length,
        editor: editor.length,
        buttons: buttons.length
    });
    
    if (popup.length === 0) {
        console.error('❌ Notes popup not found in DOM');
        return;
    }
    
    // Check button details
    buttons.each(function(index) {
        const btn = jQuery(this);
        diagnosticLog('🔘 Button ' + (index + 1), {
            element: this,
            command: btn.data('command'),
            text: btn.text().trim(),
            classes: btn.attr('class'),
            visible: btn.is(':visible')
        });
    });
    
    // Test button event binding
    diagnosticLog('🧪 Testing button event binding...');
    const testBtn = buttons.first();
    if (testBtn.length > 0) {
        diagnosticLog('🧪 Triggering click on first button...');
        testBtn.trigger('click');
    }
    
    // Test specific functions via debug object
    diagnosticLog('🧪 Testing debug object availability...');
    if (window.slmmNotesDebug) {
        diagnosticLog('✅ slmmNotesDebug object exists', window.slmmNotesDebug);
        
        if (window.slmmNotesDebug.insertCheckboxList) {
            diagnosticLog('✅ insertCheckboxList function exists in debug object');
        } else {
            diagnosticError('❌ insertCheckboxList function not found in debug object');
        }
        
        if (window.slmmNotesDebug.removeLinkAlternative) {
            diagnosticLog('✅ removeLinkAlternative function exists in debug object');
        } else {
            diagnosticError('❌ removeLinkAlternative function not found in debug object');
        }
    } else {
        diagnosticError('❌ slmmNotesDebug object not found');
    }
    
    // Test editor selection
    if (editor.length > 0) {
        editor.focus();
        const selection = window.getSelection();
        console.log('📝 Editor selection info:', {
            rangeCount: selection.rangeCount,
            isCollapsed: selection.rangeCount > 0 ? selection.getRangeAt(0).collapsed : 'no range',
            anchorNode: selection.anchorNode,
            focusNode: selection.focusNode
        });
    }
    
    diagnosticLog('🔍 Manual function tests:');
    
    // Create test buttons
    diagnosticLog('🧪 Creating manual test buttons...');
    
    window.testBulletList = function() {
        diagnosticLog('🧪 Manual bullet list test...');
        jQuery('#slmm-notes-editor').focus();
        document.execCommand('insertUnorderedList', false, null);
    };
    
    window.testNumberedList = function() {
        diagnosticLog('🧪 Manual numbered list test...');
        jQuery('#slmm-notes-editor').focus();
        document.execCommand('insertOrderedList', false, null);
    };
    
    window.testUnlink = function() {
        diagnosticLog('🧪 Manual unlink test...');
        if (window.slmmNotesDebug && window.slmmNotesDebug.removeLinkAlternative) {
            jQuery('#slmm-notes-editor').focus();
            window.slmmNotesDebug.removeLinkAlternative();
        } else {
            diagnosticError('❌ removeLinkAlternative function not available in debug object');
        }
    };
    
    diagnosticLog('✅ Debug script complete!');
    diagnosticLog('📋 Available manual tests:');
    diagnosticLog('   - testBulletList() - Test bullet list creation');
    diagnosticLog('   - testNumberedList() - Test numbered list creation');
    diagnosticLog('   - testUnlink() - Test link removal');
    diagnosticLog('   - testButtonClick() - Test button click simulation');
    diagnosticLog('   - Place cursor in editor and run these functions');
    
    // Create a direct button test
    window.testButtonClick = function() {
        diagnosticLog('🧪 Testing direct button click...');
        const bulletBtn = buttons.filter('[data-command="insertUnorderedList"]');
        const numberedBtn = buttons.filter('[data-command="insertOrderedList"]');
        const unlinkBtn = buttons.filter('[data-command="unlink"]');
        
        console.log('🧪 Found buttons:', {
            bullet: bulletBtn.length,
            numbered: numberedBtn.length,
            unlink: unlinkBtn.length
        });
        
        if (bulletBtn.length > 0) {
            diagnosticLog('🧪 Simulating bullet list button click...');
            bulletBtn.first().trigger('click');
        }
        
        if (numberedBtn.length > 0) {
            diagnosticLog('🧪 Simulating numbered list button click...');
            numberedBtn.first().trigger('click');
        }
        
        if (unlinkBtn.length > 0) {
            diagnosticLog('🧪 Simulating unlink button click...');
            unlinkBtn.first().trigger('click');
        }
    };
    
})();

===== END DEBUG SCRIPT =====
*/ 