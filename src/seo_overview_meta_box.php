<?php
/**
 * SEO Overview Meta Box
 * 
 * Registers a meta box in the WordPress editor that provides a comprehensive SEO overview.
 * The meta box is togglable from Screen Options and can be moved around the editor.
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_SEO_Overview_Meta_Box {
    
    /**
     * Initialize the class and set up hooks
     */
    public function __construct() {
        // Check if SEO Overview is enabled in the settings
        $options = get_option('chatgpt_generator_options', array());
        $seo_overview_enabled = isset($options['enable_seo_overview']) ? (bool)$options['enable_seo_overview'] : true;
        
        // Only register the meta box and related hooks if enabled
        if ($seo_overview_enabled) {
            add_action('add_meta_boxes', array($this, 'register_meta_boxes'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
            add_action('wp_ajax_get_hidden_div_count', array($this, 'get_hidden_div_count_ajax'));
            add_action('wp_ajax_get_silo_navigation', array($this, 'get_silo_navigation_ajax'));
            add_action('wp_ajax_get_priority_pages', array($this, 'get_priority_pages_ajax'));
        }
    }
    
    /**
     * Register the meta box
     */
    public function register_meta_boxes() {
        // Get allowed post types from settings
        $allowed_types = get_option('seo_overview_post_types', array('post', 'page'));
        $post_types = is_array($allowed_types) ? $allowed_types : array('post', 'page');
        // Allow other post types via filter
        $post_types = apply_filters('slmm_seo_overview_post_types', $post_types);
        foreach ($post_types as $post_type) {
            add_meta_box(
                'slmm-seo-overview',
                __('SEO Overview', 'slmm-seo-bundle'),
                array($this, 'render_meta_box'),
                $post_type,
                'side',
                'default', // Use default priority so it can be toggled in screen options
                null // No callback args
            );
            
            // Add to list of screen options that can be toggled
            add_filter('hidden_meta_boxes', array($this, 'ensure_meta_box_visible'), 10, 3);
        }
    }
    
    /**
     * Ensure the meta box is visible by default
     */
    public function ensure_meta_box_visible($hidden, $screen, $use_defaults) {
        // Make sure our meta box is not hidden when starting fresh
        if ($use_defaults) {
            $hidden = array_diff($hidden, array('slmm-seo-overview'));
        }
        return $hidden;
    }
    
    /**
     * Render the meta box content
     */
    public function render_meta_box($post) {
        // Get SILO STRUCTURE meta values for JavaScript access (energy conservative)
        $importance_rating = get_post_meta($post->ID, '_slmm_importance_rating', true);
        $difficulty_level = get_post_meta($post->ID, '_slmm_difficulty_level', true);
        $target_keyword = get_post_meta($post->ID, '_slmm_target_keyword', true);
        
        // Set defaults if not set
        if (empty($importance_rating)) $importance_rating = '3';
        if (empty($difficulty_level)) $difficulty_level = 'medium';
        if (empty($target_keyword)) $target_keyword = '';
        
        // Output the meta box container
        echo '<div id="seo-overview-content" class="slmm-seo-overview-content">';
        echo '<div class="seo-overview-loading">';
        echo '<div class="seo-overview-header">';
        echo '<span class="last-update">Loading...</span>';
        echo '<button type="button" id="refresh-seo-overview" class="button button-small" style="float:right;" disabled>';
        echo '<span class="dashicons dashicons-update" style="font-size:14px;line-height:1.4;"></span></button>';
        echo '</div>';
        echo '<div class="loading-spinner"><span class="spinner is-active"></span></div>';
        echo '</div>';
        echo '</div>';
        
        // Add hidden fields for JavaScript access to SILO STRUCTURE data (energy conservative)
        echo '<input type="hidden" name="_slmm_importance_rating" value="' . esc_attr($importance_rating) . '">';
        echo '<input type="hidden" name="_slmm_difficulty_level" value="' . esc_attr($difficulty_level) . '">';
        echo '<input type="hidden" name="_slmm_target_keyword" value="' . esc_attr($target_keyword) . '">';
        
        // Add nonce for security
        wp_nonce_field('slmm_seo_overview_nonce', 'slmm_seo_overview_nonce');
    }
    
    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts($hook) {
        // Only load on post editing screens
        if (!in_array($hook, array('post.php', 'post-new.php'))) {
            return;
        }
        
        // Ensure Dashicons are loaded
        wp_enqueue_style('dashicons');
        
        // Register and enqueue the script
        wp_enqueue_script(
            'slmm-seo-overview',
            plugin_dir_url(dirname(__FILE__)) . 'src/seo_overview_meta_box.js',
            array('jquery'),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'src/seo_overview_meta_box.js'),
            true
        );
        
        // Register and enqueue the stylesheet
        wp_enqueue_style(
            'slmm-seo-overview',
            plugin_dir_url(dirname(__FILE__)) . 'src/seo_overview_meta_box.css',
            array(),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'src/seo_overview_meta_box.css')
        );
        
        // Check if checklist is enabled
        $options = get_option('chatgpt_generator_options', array());
        $checklist_enabled = isset($options['enable_checklist']) ? (bool)$options['enable_checklist'] : false;
        
        // Pass AJAX URL, checklist status, interlinking nonce, semantic links nonce, and authorization status to script
        wp_localize_script('slmm-seo-overview', 'slmmSeoOverview', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'checklistEnabled' => $checklist_enabled,
            'interlinkingNonce' => wp_create_nonce('slmm_interlinking_nonce'), // Same nonce used by interlinking suite
            'semanticLinksNonce' => wp_create_nonce('slmm_semantic_links_nonce'), // Semantic links nonce
            'userAuthorized' => slmm_seo_check_visibility_authorization() // Authorization status for interlinking button
        ));
    }
    
    /**
     * Ajax handler for getting hidden div count
     */
    public function get_hidden_div_count_ajax() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'slmm_seo_overview_nonce')) {
            wp_send_json_error('Invalid nonce');
        }
        
        if (!isset($_POST['content'])) {
            wp_send_json_error('No content provided');
        }
        
        $content = $_POST['content'];
        $count = $this->count_hidden_divs($content);
        
        wp_send_json_success(array('count' => $count));
    }
    
    /**
     * Count hidden divs in content
     */
    private function count_hidden_divs($content) {
        $pattern = '/<div[^>]*style\s*=\s*["\'][^"\']*display\s*:\s*none[^"\']*["\'][^>]*>/i';
        preg_match_all($pattern, $content, $matches);
        return count($matches[0]);
    }
    
    /**
     * Ajax handler for getting silo navigation data (parents and siblings)
     */
    public function get_silo_navigation_ajax() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'slmm_seo_overview_nonce')) {
            wp_send_json_error('Invalid nonce');
        }
        
        if (!isset($_POST['post_id'])) {
            wp_send_json_error('No post ID provided');
        }
        
        $post_id = intval($_POST['post_id']);
        if ($post_id <= 0) {
            wp_send_json_error('Invalid post ID');
        }
        
        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
        }
        
        $navigation_data = $this->get_silo_navigation_data($post_id, $post->post_parent);
        
        wp_send_json_success($navigation_data);
    }
    
    /**
     * Get silo navigation data (parents and siblings)
     * Based on the method from interlinking-suite.php
     */
    private function get_silo_navigation_data($post_id, $parent_id) {
        $navigation_data = array(
            'parents' => array(),
            'children' => array(),
            'siblings' => array()
        );
        
        try {
            // Get parent hierarchy chain (up to 4 levels for reasonable display)
            if ($parent_id) {
                $ancestor_ids = get_post_ancestors($post_id);
                $ancestor_ids = array_slice($ancestor_ids, 0, 4); // Limit to 4 levels
                
                foreach ($ancestor_ids as $ancestor_id) {
                    $ancestor_post = get_post($ancestor_id);
                    if ($ancestor_post && $ancestor_post->post_status !== 'trash') {
                        $navigation_data['parents'][] = array(
                            'id' => $ancestor_id,
                            'title' => $ancestor_post->post_title,
                            'url' => get_permalink($ancestor_id),
                            'level' => count($navigation_data['parents']) + 1 // 1 = direct parent, 2 = grandparent, etc.
                        );
                    }
                }
            }
            
            // Get children (posts with current post as parent)
            $children = get_posts(array(
                'post_parent' => $post_id,
                'post_type' => array('post', 'page'),
                'post_status' => array('publish', 'draft', 'private'),
                'posts_per_page' => 10, // Reasonable limit for sidebar display
                'orderby' => 'title',
                'order' => 'ASC'
            ));
            
            foreach ($children as $child) {
                $navigation_data['children'][] = array(
                    'id' => $child->ID,
                    'title' => $child->post_title,
                    'url' => get_permalink($child->ID),
                    'status' => $child->post_status
                );
            }
            
            // Get siblings (posts with same parent, excluding current post)
            $siblings = get_posts(array(
                'post_parent' => $parent_id,
                'post_type' => array('post', 'page'),
                'post_status' => array('publish', 'draft', 'private'),
                'posts_per_page' => 10, // Reasonable limit for sidebar display
                'exclude' => array($post_id), // Exclude current post
                'orderby' => 'title',
                'order' => 'ASC'
            ));
            
            foreach ($siblings as $sibling) {
                $navigation_data['siblings'][] = array(
                    'id' => $sibling->ID,
                    'title' => $sibling->post_title,
                    'url' => get_permalink($sibling->ID),
                    'status' => $sibling->post_status
                );
            }
            
        } catch (Exception $e) {
            error_log('[SLMM SEO Overview] Error getting navigation data: ' . $e->getMessage());
        }
        
        return $navigation_data;
    }
    
    /**
     * Ajax handler for getting priority pages by importance level
     */
    public function get_priority_pages_ajax() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'slmm_seo_overview_nonce')) {
            wp_send_json_error('Invalid nonce');
        }
        
        if (!isset($_POST['priority_level'])) {
            wp_send_json_error('No priority level provided');
        }
        
        $priority_level = sanitize_text_field($_POST['priority_level']);
        if (!in_array($priority_level, array('1', '2', '3', '4', '5'))) {
            wp_send_json_error('Invalid priority level');
        }
        
        $priority_data = $this->get_priority_pages_data($priority_level);
        
        wp_send_json_success($priority_data);
    }
    
    /**
     * Get pages by priority/importance level
     * Similar to the interlinking suite's importance filtering
     */
    private function get_priority_pages_data($priority_level) {
        $priority_data = array(
            'pages' => array(),
            'count' => 0
        );
        
        try {
            // Query posts with specific importance rating
            $args = array(
                'post_type' => array('post', 'page'),
                'post_status' => array('publish', 'draft', 'private'),
                'posts_per_page' => 20, // Reasonable limit for accordion display
                'meta_query' => array(
                    array(
                        'key' => '_slmm_importance_rating',
                        'value' => $priority_level,
                        'compare' => '='
                    )
                ),
                'orderby' => 'title',
                'order' => 'ASC'
            );
            
            $query = new WP_Query($args);
            
            if ($query->have_posts()) {
                while ($query->have_posts()) {
                    $query->the_post();
                    $post_id = get_the_ID();
                    
                    // Get additional meta data
                    $difficulty = get_post_meta($post_id, '_slmm_difficulty_level', true);
                    $keyword = get_post_meta($post_id, '_slmm_target_keyword', true);
                    
                    $priority_data['pages'][] = array(
                        'id' => $post_id,
                        'title' => get_the_title(),
                        'url' => get_permalink($post_id),
                        'status' => get_post_status($post_id),
                        'difficulty' => $difficulty ?: 'medium',
                        'keyword' => $keyword ?: '',
                        'edit_url' => get_edit_post_link($post_id)
                    );
                }
                wp_reset_postdata();
            }
            
            $priority_data['count'] = $query->found_posts;
            
        } catch (Exception $e) {
            error_log('[SLMM SEO Overview] Error getting priority pages: ' . $e->getMessage());
        }
        
        return $priority_data;
    }
}

// Initialize the class
new SLMM_SEO_Overview_Meta_Box(); 