# ACF Direct Editor Integration Patterns

## Overview

This document captures the architectural patterns and implementation details for ACF title editing within the Direct Editor system, based on comprehensive codebase analysis conducted for task `m-improve-acf-title-direct-editor`.

## Current ACF Title System Architecture

### Display Implementation (`updatePostTitleDisplay`)
**Location**: `assets/js/slmm-direct-editor.js:1987-2020`

```javascript
updatePostTitleDisplay: function(titleData, acfData) {
    var $titleElement = this.currentModal.find('.slmm-direct-editor-post-info');
    
    // ACF title swapping logic
    if (acfData && acfData.acf_enabled && acfData.has_acf_content && acfData.display_title) {
        displayTitle = acfData.display_title;
        hasAcfContent = true;
        
        // Apply ACF data attributes and styling
        $titleElement.attr('data-acf-title', 'true');
        $titleElement.attr('data-original-title', originalTitle);
        $titleElement.addClass('slmm-acf-swapped-title');
    }
    
    $titleElement.text(displayTitle);
}
```

**Key Characteristics**:
- Uses `.slmm-direct-editor-post-info` as title element selector
- Applies `data-acf-title="true"` for ACF content identification
- Stores original title in `data-original-title` attribute
- Adds `slmm-acf-swapped-title` CSS class for visual differentiation
- Currently read-only implementation

### CSS Styling Infrastructure
**Location**: `assets/css/slmm-direct-editor.css:198-232`

```css
.slmm-direct-editor-post-info {
    font-size: 30px;
    color: #ffffff;
    margin-left: 10px;
}

.slmm-direct-editor-post-info.slmm-acf-swapped-title {
    color: #10b981; /* Green for ACF content */
    border-left: 3px solid #10b981;
    padding-left: 8px;
    position: relative;
}

.slmm-direct-editor-post-info.slmm-acf-swapped-title::after {
    content: "ACF";
    position: absolute;
    top: -8px;
    right: 0;
    background: #10b981;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.slmm-direct-editor-post-info[data-acf-title="true"] {
    cursor: help;
}
```

**Design Principles**:
- Green color scheme (`#10b981`) distinguishes ACF content
- Left border provides visual separation
- "ACF" badge in top-right corner for clear identification
- Help cursor indicates interactive potential
- Professional spacing and typography

## Dual-Update System Pattern

### Core Pattern Implementation
**Location**: `assets/js/slmm-direct-editor.js:2906+`

```javascript
makeNodeUpdateRequest: function(action, paramName, value, fieldName) {
    var self = this;
    
    // 1. IMMEDIATE visual update (DOM)
    this.updateTemporaryNodeData(fieldName, value);
    
    // 2. BACKGROUND database save (AJAX)
    var requestData = {
        action: action,
        post_id: this.currentPostId,
        [paramName]: value,
        nonce: slmmInterlinkingData.nonce
    };
    
    $.post(slmmInterlinkingData.ajax_url, requestData)
        .done(function(response) {
            if (response.success) {
                // 3. CONFIRM with fresh database data
                if (response.data.node_data) {
                    self.updateNodeWithFreshData(response.data.node_data);
                }
                
                // 4. SYNC canvas nodes if needed
                if (window.slmmInterLinkingSuite) {
                    window.slmmInterLinkingSuite.updateNodeInTree(
                        self.currentPostId, 
                        fieldName, 
                        value
                    );
                }
            } else {
                // 5. ROLLBACK on failure
                self.rollbackTemporaryUpdate(fieldName);
                console.error('Update failed:', response.data);
            }
        })
        .fail(function() {
            self.rollbackTemporaryUpdate(fieldName);
        });
}
```

**Pattern Characteristics**:
- **Immediate feedback**: DOM updates happen instantly
- **Background persistence**: AJAX saves happen asynchronously
- **Data consistency**: Fresh data confirms successful save
- **Canvas synchronization**: Updates propagate to visualization
- **Error handling**: Rollback mechanism for failed saves
- **User experience**: No blocking/waiting for database operations

### Existing Usage Examples
1. **Status Updates**: `updatePostStatus('published')` → `slmm_change_post_status`
2. **Slug Updates**: `updateSlug('new-slug')` → `slmm_change_slug`
3. **Keyword Updates**: Immediate D3 data update, no AJAX (performance optimization)

## Available AJAX Endpoints

### ACF Field Update Endpoint
**Location**: `includes/interlinking/interlinking-suite.php:86`
**Action**: `slmm_update_acf_field`

```php
public function ajax_update_acf_field() {
    // Security verification
    if (!check_ajax_referer('slmm_interlinking_nonce', 'nonce', false)) {
        wp_send_json_error('Security check failed');
    }
    
    // Capability check
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Insufficient permissions');
    }
    
    // Get parameters
    $post_id = intval($_POST['post_id']);
    $field_name = sanitize_text_field($_POST['field_name']);
    $field_value = sanitize_text_field($_POST['field_value']);
    
    // Update ACF field
    $success = update_field($field_name, $field_value, $post_id);
    
    if ($success) {
        wp_send_json_success(array(
            'message' => 'ACF field updated successfully',
            'post_id' => $post_id,
            'field_name' => $field_name,
            'new_value' => $field_value
        ));
    } else {
        wp_send_json_error('Failed to update ACF field');
    }
}
```

**Security Features**:
- Nonce verification with `slmm_interlinking_nonce`
- WordPress capability checks (`edit_posts`)
- Input sanitization for all parameters
- Proper JSON response format

## Canvas Node Synchronization

### Canvas Node Structure
**Canvas nodes are SVG text elements with ACF data attributes:**

```html
<text class="slmm-node-title" 
      y="-55" 
      style="opacity: 1;" 
      data-acf-title="true" 
      data-original-title="This is a really long title for SEO purposes">
    SEO optimised Titile&nbsp;
</text>
```

### ACF Integration System
**Location**: `assets/js/slmm-acf-integration.js`

```javascript
// Direct DOM manipulation for canvas nodes
function applyACFTitlesDirectlyToDOM() {
    // Find nodes with ACF content
    Object.keys(acfState.treeData).forEach(nodeId => {
        const acfNodeData = acfState.treeData[nodeId];
        
        if (acfNodeData.has_acf_content) {
            // Update title element
            titleElement.textContent = acfNodeData.display_title;
            titleElement.setAttribute('data-acf-title', 'true');
            titleElement.setAttribute('data-original-title', acfNodeData.title);
        }
    });
}
```

**Synchronization Requirements**:
- Canvas updates must happen after successful Direct Editor saves
- Both systems must maintain consistent `data-acf-title` attributes
- Visual indicators (green color, badges) must match across systems
- D3.js data binding requires fresh data after ACF field updates

## Inline Editing Infrastructure

### Contenteditable Exclusion System
**Location**: `assets/js/slmm-direct-editor.js:121-126`

```javascript
// Keyboard shortcut exclusions for editable fields
var target = $(e.target);
if (target.is('input, textarea, [contenteditable="true"]') || 
    target.closest('.mce-tinymce').length > 0) {
    return; // Don't process keyboard shortcuts in editable areas
}
```

**Pattern**: The system already excludes contenteditable elements from keyboard shortcuts, providing a safe pattern for inline editing implementation.

### Required Event Handlers for ACF Title Editing

```javascript
// Proposed implementation pattern
setupACFTitleInlineEditing: function($titleElement, acfData) {
    // Make element contenteditable
    $titleElement.attr('contenteditable', 'true');
    
    // Add editing event handlers
    $titleElement.on('keydown', this.handleACFTitleKeydown.bind(this));
    $titleElement.on('blur', this.handleACFTitleBlur.bind(this));
    $titleElement.on('focus', this.handleACFTitleFocus.bind(this));
    
    // Preserve ACF data for updates
    $titleElement.data('acf-field-name', acfData.field_name);
    $titleElement.data('original-title', acfData.original_title);
},

handleACFTitleKeydown: function(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        this.saveACFTitle($(event.target));
    } else if (event.key === 'Escape') {
        event.preventDefault();
        this.cancelACFTitleEdit($(event.target));
    }
}
```

## Implementation Strategy for Task

### Phase 1: Minimal Viable Edit Capability
1. **Modify `updatePostTitleDisplay`** to add `contenteditable="true"` for ACF titles
2. **Add basic keyboard handlers** for Enter (save) and Escape (cancel)
3. **Implement `updateACFTitle` method** following dual-update pattern
4. **Use existing `slmm_update_acf_field` endpoint** for AJAX saves

### Phase 2: Canvas Synchronization
1. **Add canvas node update logic** after successful AJAX saves
2. **Integrate with existing ACF integration system** for consistency
3. **Ensure D3.js data binding updates** with fresh ACF data

### Phase 3: Enhanced UX
1. **Visual editing state indicators** (editing, saving, saved)
2. **Auto-save functionality** with debouncing
3. **Comprehensive error handling** with user feedback
4. **Accessibility improvements** for screen reader support

## File Size Impact Analysis
- **Primary modifications**: `assets/js/slmm-direct-editor.js` (+~100 lines)
- **CSS enhancements**: `assets/css/slmm-direct-editor.css` (+~30 lines)
- **New functionality**: Inline editing, state management, error handling
- **Total estimated impact**: ~150 lines across existing files
- **Risk assessment**: Low risk, all within existing file size limits

## Architecture Compatibility
- **Dual-system pattern**: Fully compatible with existing status/slug updates
- **AJAX endpoints**: Existing ACF endpoint provides all required functionality
- **CSS infrastructure**: Current styling system supports new editing states
- **Keyboard shortcuts**: Existing exclusion system handles contenteditable areas
- **Canvas integration**: ACF system already handles DOM manipulation
- **Security**: All required security patterns already implemented

## Conclusion

The codebase provides comprehensive infrastructure for implementing ACF title inline editing:
- Established dual-update pattern for immediate feedback + persistence
- Existing AJAX endpoint with proper security and validation  
- CSS styling system with ACF-specific visual indicators
- Canvas synchronization system for visualization updates
- Keyboard shortcut exclusion system for contenteditable areas

Implementation can follow existing patterns with minimal architectural changes, ensuring consistency with the established codebase while delivering the requested functionality.