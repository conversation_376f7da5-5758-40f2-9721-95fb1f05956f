---
task: m-implement-silo-cpt-support
branch: feature/silo-cpt-support
status: in-progress
created: 2025-01-27
modules: [interlinking, classic-editor, post-types]
---

# Extend SILO STRUCTURE Shortcut to All Post Types

## Problem/Goal
The "SILO STRUCTURE" shortcut button currently only works for pages in the Classic Editor. This button provides a direct link to the interlinking suite with automatic highlighting of the current page in the tree structure. This valuable navigation feature should be extended to work with ALL custom post types and built-in post types (posts, products, events, etc.), not just pages.

Currently, the feature is limited to pages only, but users working with other post types (posts, products, services, etc.) would benefit from the same quick navigation to see their content's position in the site hierarchy.

## Success Criteria
- [ ] SILO STRUCTURE shortcut appears in Classic Editor for ALL public post types
- [ ] Shortcut correctly navigates to interlinking suite for any post type
- [ ] Current post/page is properly highlighted in the tree regardless of post type
- [ ] Feature works seamlessly across all custom post types (products, events, services, etc.)
- [ ] Maintains existing functionality for pages (no regression)
- [ ] Post type detection and URL generation works for all CPT permalink structures
- [ ] Tab switching in interlinking suite automatically selects correct post type tab

## Context Files
- @includes/interlinking/interlinking-suite.php  # Main interlinking suite controller
- @snippets/chat_gpt_title_and_description_generator_v2_0.php  # Likely contains classic editor integration
- @assets/css/slmm-admin.css  # Styling for SILO STRUCTURE button
- @assets/js/  # JavaScript handling for navigation and highlighting

## User Notes
- Feature currently visible in Classic Editor as "SILO STRUCTURE" button/widget (see screenshots)
- Should work with ALL post types, not just pages
- Must maintain existing page functionality while extending to CPTs
- Navigation should automatically switch to correct tab in interlinking suite
- Tree highlighting should work across all post type tabs

## Context Manifest

### How the SILO STRUCTURE Shortcut Currently Works: Pages-Only Navigation System

When a user is editing a page in the WordPress Classic Editor, the SEO Overview metabox displays prominently in the sidebar if enabled in settings (`chatgpt_generator_options['enable_seo_overview']`). This metabox contains four main sections: **SILO STRUCTURE**, **NAVIGATION**, **PRIORITY PAGES**, and **CONTENT/SEO ELEMENTS**. Within the SILO STRUCTURE section header, there's a small interlinking button that provides direct navigation to the interlinking suite.

The button is generated by `SEOOverviewMetaBox.getInterlinkingButtonHtml()` in `/src/seo_overview_meta_box.js` around line 1558. This function first checks authorization status via `slmmSeoOverview.userAuthorized` and returns an empty string if unauthorized, completely hiding the button. For authorized users, it generates a button with the class `seo-interlinking-btn` that includes an SVG icon and professional styling.

When the button is clicked, the event handler at line 324 triggers `SEOOverviewMetaBox.openInterlinkingSuite()`. This function first extracts the current post ID using `$('#post_ID').val() || $('#post-id').val() || 0` and attempts to get the current title from multiple sources: the title field (`#title`), block editor post title (`#post-title-0`), TinyMCE editor, or falls back to parsing the document title.

The system then makes an AJAX call to get the ACF title if configured, using the `slmm_load_acf_settings` action followed by `slmm_get_acf_titles_batch`. Once it has the final title (ACF title taking precedence), it constructs the interlinking suite URL by replacing `admin-ajax.php` with `admin.php?page=slmm-interlinking-suite` and appends a `search` parameter with the encoded title.

The crucial limitation is that this system works regardless of post type - it doesn't detect or pass the current post type to the interlinking suite. When the URL is opened via `window.open(finalUrl, '_blank')`, the interlinking suite loads with the search parameter but defaults to whatever tab was last active or the Pages tab as fallback.

### Current Interlinking Suite Tab System: Post Type-Aware Navigation

The interlinking suite displays tabs for each public post type, generated dynamically in `/includes/interlinking/interlinking-suite.php` around line 491. Each tab button has the class `slmm-tab-button` with a `data-tab` attribute containing the post type name (e.g., `data-tab="page"`, `data-tab="post"`, `data-tab="product"`).

When the interlinking suite receives a search parameter from the SEO Overview button, it stores it in `window.pendingSearchParam` around line 4641. The system listens for the `slmm_tree_rendering_complete` event, then automatically fills the search input (`#slmm-search-pages`) with the pending search term, triggers highlighting, and centers the result if it's a single match. This highlighting works by dispatching a spacebar keydown event that activates the existing search highlighting system.

The active tab detection system is critical throughout the interlinking suite and uses the pattern `$('.slmm-tab-button.active').data('tab')` to determine the current post type context. This is used for bulk creation, post type-specific queries, and maintaining context across different operations. The system falls back to `'page'` as the default post type when no active tab is detected.

However, the current search and highlighting system works across all tabs - when a search is performed, it finds and highlights matching content regardless of which post type tab is active. The issue is that the SEO Overview button doesn't communicate the current post type to automatically switch to the correct tab before performing the search.

### For SILO CPT Support Implementation: What Needs to Connect

Since we're implementing SILO STRUCTURE shortcut support for all post types, the key missing piece is post type detection and communication. The WordPress Classic Editor provides the current post type through several reliable methods that we can leverage in the SEO Overview metabox JavaScript.

The current post type can be detected using WordPress's built-in JavaScript variables. In the Classic Editor, `typenow` is a global variable containing the current post type, or we can extract it from the admin body classes which include `post-type-{type}`. Alternatively, we can use `$('#post_type').val()` which is a hidden field containing the post type.

The interlinking suite URL generation in `openInterlinkingSuite()` needs to be enhanced to include a `post_type` parameter alongside the existing `search` parameter. The URL would become: `/wp-admin/admin.php?page=slmm-interlinking-suite&search=TITLE&post_type=POST_TYPE`

The interlinking suite must be updated to handle the new `post_type` parameter. Around line 4637, where it currently checks for the `search` parameter, we need to also check for `post_type` using `urlParams.get('post_type')`. If a post type is provided, the system should automatically switch to that tab before performing the search and highlighting.

The tab switching logic already exists and is used in multiple places throughout the suite. The pattern is: find the target tab button using `$('.slmm-tab-button[data-tab="' + targetPostType + '"]')`, remove the `active` class from all tabs with `$('.slmm-tab-button').removeClass('active')`, add `active` to the target tab, and trigger the tab content update.

Since the search and highlighting system works across all post types already, we don't need to modify that functionality. The enhancement will ensure that when someone clicks the SILO STRUCTURE button while editing a product, for example, they land on the Products tab in the interlinking suite with their product already searched and highlighted.

### Technical Reference Details

#### Current Function Signatures & Integration Points

**SEO Overview Metabox (`/src/seo_overview_meta_box.js`):**
```javascript
// Button generation (line ~1558)
getInterlinkingButtonHtml: function()

// Navigation function (line ~1578)
openInterlinkingSuite: function()

// Current post detection
var postId = $('#post_ID').val() || $('#post-id').val() || 0;
```

**Interlinking Suite (`/includes/interlinking/interlinking-suite.php`):**
```javascript
// URL parameter handling (line ~4636)
const urlParams = new URLSearchParams(window.location.search);
const searchParam = urlParams.get('search');

// Tab switching pattern (multiple locations)
const activeTab = $('.slmm-tab-button.active').data('tab');
$('.slmm-tab-button').removeClass('active');
$targetTab.addClass('active');

// Search and highlighting system (line ~4645)
window.pendingSearchParam = searchParam;
$(document).on('slmm_tree_rendering_complete', function() { ... });
```

#### Post Type Detection Methods in WordPress Classic Editor

```javascript
// Method 1: Global variable (most reliable)
window.typenow  // 'page', 'post', 'product', etc.

// Method 2: Hidden form field
$('#post_type').val()

// Method 3: Body class extraction
$('body').attr('class').match(/post-type-(\w+)/)[1]

// Method 4: Admin screen detection
$('.post-type-' + postType).length > 0
```

#### URL Parameter Structure

**Current URL:**
```
/wp-admin/admin.php?page=slmm-interlinking-suite&search=ENCODED_TITLE
```

**Enhanced URL:**
```
/wp-admin/admin.php?page=slmm-interlinking-suite&search=ENCODED_TITLE&post_type=POST_TYPE
```

#### Implementation Requirements

1. **Modify `openInterlinkingSuite()` in `/src/seo_overview_meta_box.js`:**
   - Add post type detection logic
   - Include `post_type` parameter in URL construction
   - Maintain backward compatibility with search-only URLs

2. **Enhance URL parameter handling in `/includes/interlinking/interlinking-suite.php`:**
   - Add `post_type` parameter detection alongside existing search logic
   - Implement tab switching before search execution
   - Ensure proper fallback if post type tab doesn't exist

3. **Update SEO Overview metabox registration in `/src/seo_overview_meta_box.php`:**
   - Verify it's already registered for all desired post types via `$post_types` array
   - Current setting: `get_option('seo_overview_post_types', array('post', 'page'))`

#### File Locations

- **Primary implementation:** `/src/seo_overview_meta_box.js` (line ~1578 `openInterlinkingSuite` function)
- **Secondary implementation:** `/includes/interlinking/interlinking-suite.php` (line ~4636 URL parameter handling)
- **Configuration verification:** `/src/seo_overview_meta_box.php` (line ~38 post type registration)
- **No new files required:** This is an enhancement to existing functionality

## Work Log
- [2025-01-27] Task created - Extend SILO STRUCTURE shortcut to all post types