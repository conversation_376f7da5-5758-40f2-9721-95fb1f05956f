/**
 * Lorem Ipsum Detector CSS - Dark Theme
 * Styling for the Lorem Ipsum detection interface using dark theme variables
 */

/* Main detector container - inherits from settings wrap */
.slmm-settings-wrap .slmm-lorem-detector-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Scanner section */
.slmm-settings-wrap .slmm-lorem-scanner {
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--slmm-shadow);
}

.slmm-settings-wrap .slmm-lorem-info {
    margin-bottom: 20px;
}

.slmm-settings-wrap .slmm-lorem-info h3 {
    color: var(--slmm-text-primary);
    margin-bottom: 12px;
    font-size: 18px;
}

.slmm-settings-wrap .slmm-lorem-info p {
    color: var(--slmm-text-secondary);
    margin-bottom: 8px;
    line-height: 1.5;
}

.slmm-settings-wrap .slmm-lorem-controls {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;
}

/* Scan button styling */
.slmm-settings-wrap .slmm-lorem-scan-btn {
    position: relative;
    min-width: 120px;
    height: 40px;
    font-weight: 600;
    transition: all 0.3s ease;
    background: var(--slmm-primary);
    border-color: var(--slmm-primary);
    color: white;
}

.slmm-settings-wrap .slmm-lorem-scan-btn:hover {
    background: var(--slmm-primary-hover);
    border-color: var(--slmm-primary-hover);
}

.slmm-settings-wrap .slmm-lorem-scan-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.slmm-settings-wrap .slmm-lorem-scan-btn .slmm-scan-loading {
    display: none;
}

.slmm-settings-wrap .slmm-lorem-scan-btn:disabled .slmm-scan-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

/* Clear button styling */
.slmm-settings-wrap .slmm-lorem-clear-btn {
    background: var(--slmm-dark-surface-hover);
    border: 1px solid var(--slmm-dark-border);
    color: var(--slmm-text-secondary);
}

.slmm-settings-wrap .slmm-lorem-clear-btn:hover {
    background: var(--slmm-dark-border);
    color: var(--slmm-text-primary);
}

/* Spinner animation */
.slmm-settings-wrap .slmm-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: slmm-spin 1s linear infinite;
}

@keyframes slmm-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results container */
.slmm-settings-wrap .slmm-lorem-results-container {
    min-height: 100px;
}

/* Loading state */
.slmm-settings-wrap .slmm-lorem-loading {
    text-align: center;
    padding: 40px 20px;
    background: var(--slmm-dark-surface);
    border-radius: 8px;
    border: 1px solid var(--slmm-dark-border);
}

.slmm-settings-wrap .slmm-lorem-loading .slmm-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(124, 58, 237, 0.3);
    border-top: 3px solid var(--slmm-primary);
    margin-bottom: 16px;
}

.slmm-settings-wrap .slmm-lorem-loading p {
    color: var(--slmm-text-secondary);
    font-size: 16px;
    margin: 0;
}

/* No results state */
.slmm-settings-wrap .slmm-lorem-no-results {
    text-align: center;
    padding: 60px 20px;
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 8px;
}

.slmm-settings-wrap .slmm-lorem-no-results .slmm-lorem-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.slmm-settings-wrap .slmm-lorem-no-results h3 {
    color: var(--slmm-success);
    margin-bottom: 12px;
    font-size: 24px;
}

.slmm-settings-wrap .slmm-lorem-no-results p {
    color: var(--slmm-text-secondary);
    font-size: 16px;
    margin: 0;
}

/* Error state */
.slmm-settings-wrap .slmm-lorem-error {
    text-align: center;
    padding: 40px 20px;
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-error);
    border-radius: 8px;
}

.slmm-settings-wrap .slmm-lorem-error .slmm-lorem-icon.error {
    font-size: 48px;
    margin-bottom: 16px;
}

.slmm-settings-wrap .slmm-lorem-error h3 {
    color: var(--slmm-error);
    margin-bottom: 12px;
    font-size: 20px;
}

.slmm-settings-wrap .slmm-lorem-error p {
    color: var(--slmm-error);
    margin-bottom: 20px;
}

/* Results section */
.slmm-settings-wrap .slmm-lorem-results {
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--slmm-shadow);
}

.slmm-settings-wrap .slmm-lorem-summary {
    background: var(--slmm-dark-surface-hover);
    border-bottom: 1px solid var(--slmm-dark-border);
    padding: 20px 24px;
}

.slmm-settings-wrap .slmm-lorem-summary h3 {
    color: var(--slmm-warning);
    margin-bottom: 12px;
    font-size: 20px;
}

.slmm-settings-wrap .slmm-lorem-counts {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.slmm-settings-wrap .slmm-status-count {
    padding: 6px 12px;
    border-radius: 16px;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
}

.slmm-settings-wrap .slmm-status-count.slmm-status-publish {
    background: rgba(16, 185, 129, 0.15);
    color: var(--slmm-success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.slmm-settings-wrap .slmm-status-count.slmm-status-draft {
    background: rgba(245, 158, 11, 0.15);
    color: var(--slmm-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.slmm-settings-wrap .slmm-status-count.slmm-status-private {
    background: rgba(239, 68, 68, 0.15);
    color: var(--slmm-error);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.slmm-settings-wrap .slmm-status-count.slmm-status-total {
    background: rgba(124, 58, 237, 0.15);
    color: var(--slmm-primary);
    border: 1px solid rgba(124, 58, 237, 0.3);
    font-weight: 700;
}

/* Table styling */
.slmm-settings-wrap .slmm-lorem-table-wrapper {
    overflow-x: auto;
}

.slmm-settings-wrap .slmm-lorem-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.slmm-settings-wrap .slmm-lorem-table th,
.slmm-settings-wrap .slmm-lorem-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--slmm-dark-border);
}

.slmm-settings-wrap .slmm-lorem-table th {
    background: var(--slmm-dark-bg);
    font-weight: 600;
    color: var(--slmm-text-primary);
    cursor: pointer;
    user-select: none;
    position: relative;
    transition: background-color 0.2s ease;
}

.slmm-settings-wrap .slmm-lorem-table th:hover {
    background: var(--slmm-dark-border);
}

.slmm-settings-wrap .slmm-lorem-table th.sort-asc::after {
    content: ' ↑';
    color: var(--slmm-primary);
}

.slmm-settings-wrap .slmm-lorem-table th.sort-desc::after {
    content: ' ↓';
    color: var(--slmm-primary);
}

.slmm-settings-wrap .slmm-lorem-table tbody tr {
    transition: background-color 0.2s ease;
}

.slmm-settings-wrap .slmm-lorem-table tbody tr:hover,
.slmm-settings-wrap .slmm-lorem-table tbody tr.hover {
    background: var(--slmm-dark-surface-hover);
}

.slmm-settings-wrap .slmm-lorem-table tbody tr:nth-child(even) {
    background: var(--slmm-dark-surface);
}

.slmm-settings-wrap .slmm-lorem-table tbody tr:nth-child(even):hover,
.slmm-settings-wrap .slmm-lorem-table tbody tr:nth-child(even).hover {
    background: var(--slmm-dark-surface-hover);
}

/* Table cell specific styling */
.slmm-settings-wrap .slmm-lorem-title {
    font-weight: 600;
    color: var(--slmm-text-primary);
    max-width: 300px;
    word-wrap: break-word;
}

.slmm-settings-wrap .slmm-lorem-type {
    color: var(--slmm-text-secondary);
    font-size: 14px;
}

.slmm-settings-wrap .slmm-lorem-status {
    text-align: center;
}

.slmm-settings-wrap .slmm-status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.slmm-settings-wrap .slmm-status-badge.slmm-status-publish {
    background: rgba(16, 185, 129, 0.15);
    color: var(--slmm-success);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.slmm-settings-wrap .slmm-status-badge.slmm-status-draft {
    background: rgba(245, 158, 11, 0.15);
    color: var(--slmm-warning);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.slmm-settings-wrap .slmm-status-badge.slmm-status-private {
    background: rgba(239, 68, 68, 0.15);
    color: var(--slmm-error);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.slmm-settings-wrap .slmm-lorem-date {
    color: var(--slmm-text-secondary);
    font-size: 14px;
    white-space: nowrap;
}

.slmm-settings-wrap .slmm-lorem-actions {
    text-align: center;
    white-space: nowrap;
}

.slmm-settings-wrap .slmm-action-link {
    display: inline-block;
    padding: 4px 8px;
    margin: 0 2px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
}

.slmm-settings-wrap .slmm-edit-link {
    background: var(--slmm-primary);
    color: #ffffff;
}

.slmm-settings-wrap .slmm-edit-link:hover {
    background: var(--slmm-primary-hover);
    color: #ffffff;
}

.slmm-settings-wrap .slmm-view-link {
    background: var(--slmm-success);
    color: #ffffff;
}

.slmm-settings-wrap .slmm-view-link:hover {
    background: rgba(16, 185, 129, 0.8);
    color: #ffffff;
}

/* Notification styling */
.slmm-lorem-notification {
    display: none;
    background: var(--slmm-dark-surface);
    border-left: 4px solid var(--slmm-primary);
    box-shadow: var(--slmm-shadow-lg);
    border-radius: 4px;
    padding: 16px;
    max-width: 400px;
    z-index: 999999;
    border: 1px solid var(--slmm-dark-border);
}

.slmm-lorem-notification-success {
    border-left-color: var(--slmm-success);
}

.slmm-lorem-notification-warning {
    border-left-color: var(--slmm-warning);
}

.slmm-lorem-notification-error {
    border-left-color: var(--slmm-error);
}

.slmm-notification-icon::before {
    font-size: 16px;
    margin-right: 8px;
}

.slmm-lorem-notification-success .slmm-notification-icon::before {
    content: '✅';
}

.slmm-lorem-notification-warning .slmm-notification-icon::before {
    content: '⚠️';
}

.slmm-lorem-notification-error .slmm-notification-icon::before {
    content: '❌';
}

.slmm-notification-text {
    flex: 1;
    color: var(--slmm-text-primary);
    font-weight: 500;
}

.slmm-notification-close {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--slmm-text-muted);
    cursor: pointer;
    padding: 0;
    margin-left: 12px;
    line-height: 1;
}

.slmm-notification-close:hover {
    color: var(--slmm-text-secondary);
}

/* Responsive design */
@media (max-width: 768px) {
    .slmm-settings-wrap .slmm-lorem-counts {
        justify-content: center;
    }
    
    .slmm-settings-wrap .slmm-lorem-table-wrapper {
        margin: 0 -24px;
    }
    
    .slmm-settings-wrap .slmm-lorem-table th,
    .slmm-settings-wrap .slmm-lorem-table td {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .slmm-settings-wrap .slmm-lorem-title {
        max-width: 200px;
    }
    
    .slmm-settings-wrap .slmm-lorem-controls {
        justify-content: center;
    }
    
    .slmm-lorem-notification {
        max-width: calc(100vw - 40px);
        margin: 0 20px;
    }
}

@media (max-width: 480px) {
    .slmm-settings-wrap .slmm-lorem-scanner {
        padding: 16px;
    }
    
    .slmm-settings-wrap .slmm-lorem-summary {
        padding: 16px;
    }
    
    .slmm-settings-wrap .slmm-lorem-counts {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }
    
    .slmm-settings-wrap .slmm-lorem-table th,
    .slmm-settings-wrap .slmm-lorem-table td {
        padding: 6px 8px;
        font-size: 13px;
    }
    
    .slmm-settings-wrap .slmm-lorem-title {
        max-width: 150px;
    }
    
    .slmm-settings-wrap .slmm-lorem-controls {
        flex-direction: column;
        gap: 8px;
    }
    
    .slmm-settings-wrap .slmm-lorem-scan-btn,
    .slmm-settings-wrap .slmm-lorem-clear-btn {
        width: 100%;
        max-width: 200px;
    }
}