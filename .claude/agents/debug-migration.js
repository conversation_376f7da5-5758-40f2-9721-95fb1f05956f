#!/usr/bin/env node

/**
 * SLMM Debug Migration Agent
 * Safely migrates console.log statements to the SLMM debug logging system
 * 
 * Usage: node debug-migration.js <file-path> [options]
 * Options:
 *   --category=<name>  Override default category detection
 *   --dry-run          Show what would be changed without modifying files
 *   --emergency        Extra safety checks for critical files
 */

const fs = require('fs');
const path = require('path');
const { execSync, spawn } = require('child_process');

class DebugMigrationAgent {
    constructor(options = {}) {
        this.options = {
            dryRun: options.dryRun || false,
            emergency: options.emergency || false,
            categoryOverride: options.category || null,
            ...options
        };
        
        this.branchName = null;
        this.backupFile = null;
        this.changes = [];
        
        // Available debug categories from slmm-debug-logger.js
        this.availableCategories = [
            'Direct Editor', 'Dashboard', 'ACF Title', 'Regular Title',
            'Link Tracking', 'Important Pages', 'Silo Nav', 'Copy Links',
            'Delete Semantic', 'Importance', 'Copy', 'Popup Management',
            'Resize', 'ACF Direct Editor', 'Link Popup', 'ACF Integration',
            'Initialization', 'Tree Loading', 'Mouseover', 'Completion',
            'Surgical Update', 'Link Overlay', 'Notes', 'Visual Highlighting',
            'Link Connections', 'Fallback Matching', 'Debug Analysis'
        ];
        
        // File-based category mapping
        this.categoryMappings = {
            'editor': 'Direct Editor',
            'ajax': 'AJAX',
            'link': 'Link Tracking',
            'popup': 'Popup Management',
            'resize': 'Resize',
            'dashboard': 'Dashboard',
            'acf': 'ACF Integration',
            'navigation': 'Silo Nav',
            'copy': 'Copy',
            'delete': 'Delete Semantic'
        };
        
        // Callback context patterns that require 'var self = this;'
        this.callbackPatterns = [
            /\$\([^)]+\)\.(each|on|click|change|submit|ready)/g,
            /\.forEach\s*\(/g,
            /\.map\s*\(/g,
            /\.filter\s*\(/g,
            /\$\.ajax\s*\(/g,
            /setTimeout\s*\(/g,
            /setInterval\s*\(/g,
            /\.addEventListener\s*\(/g
        ];
    }
    
    async migrate(filePath) {
        try {
            console.log(`🔧 Starting debug migration for: ${filePath}`);
            
            // Phase 1: Safety Setup
            await this.setupSafety(filePath);
            
            // Phase 2: Analysis
            const analysis = await this.analyzeFile(filePath);
            
            // Phase 3: Migration Planning
            const migrationPlan = await this.createMigrationPlan(analysis);
            
            if (this.options.dryRun) {
                this.reportDryRun(migrationPlan);
                return;
            }
            
            // Phase 4: Safe Migration
            await this.executeMigration(filePath, migrationPlan);
            
            // Phase 5: Validation
            await this.validateMigration(filePath);
            
            // Phase 6: Report (NO AUTO-COMMIT)
            await this.reportCompletion();
            
        } catch (error) {
            console.error(`❌ Migration failed: ${error.message}`);
            await this.emergencyRollback(filePath);
            throw error;
        }
    }
    
    async setupSafety(filePath) {
        console.log('📋 Phase 1: Setting up safety protocols');
        
        // Create new git branch
        const fileName = path.basename(filePath, '.js');
        this.branchName = `debug-migration/${fileName}-${Date.now()}`;
        
        try {
            execSync(`git checkout -b ${this.branchName}`, { stdio: 'inherit' });
            console.log(`✅ Created safety branch: ${this.branchName}`);
        } catch (error) {
            throw new Error(`Failed to create git branch: ${error.message}`);
        }
        
        // Create backup
        this.backupFile = `${filePath}.backup-${Date.now()}`;
        fs.copyFileSync(filePath, this.backupFile);
        console.log(`✅ Created backup: ${this.backupFile}`);
        
        // Verify file exists and is readable
        if (!fs.existsSync(filePath)) {
            throw new Error(`File not found: ${filePath}`);
        }
        
        console.log('✅ Safety setup complete');
    }
    
    async analyzeFile(filePath) {
        console.log('🔍 Phase 2: Analyzing file structure');
        
        const content = fs.readFileSync(filePath, 'utf8');
        const lines = content.split('\n');
        
        const analysis = {
            filePath,
            content,
            lines,
            consoleStatements: [],
            callbackContexts: [],
            hasDebugHelper: false,
            objectPatterns: [],
            suggestedCategory: this.detectCategory(filePath, content)
        };
        
        // Find console statements
        this.findConsoleStatements(analysis);
        
        // Detect callback contexts
        this.detectCallbackContexts(analysis);
        
        // Check for existing debug helper
        analysis.hasDebugHelper = /debug:\s*{/.test(content);
        
        // Detect object patterns
        this.detectObjectPatterns(analysis);
        
        console.log(`✅ Found ${analysis.consoleStatements.length} console statements`);
        console.log(`✅ Found ${analysis.callbackContexts.length} callback contexts`);
        console.log(`✅ Suggested category: ${analysis.suggestedCategory}`);
        
        return analysis;
    }
    
    findConsoleStatements(analysis) {
        const consoleRegex = /console\.(log|warn|error|info)\s*\([^)]*\);?/g;
        let match;
        
        while ((match = consoleRegex.exec(analysis.content)) !== null) {
            const lineNumber = this.getLineNumber(analysis.content, match.index);
            const statement = match[0];
            const method = match[1];
            
            // Parse the arguments
            const argsMatch = statement.match(/console\.\w+\s*\(([^)]*)\)/);
            const args = argsMatch ? this.parseConsoleArgs(argsMatch[1]) : [];
            
            analysis.consoleStatements.push({
                line: lineNumber,
                statement,
                method,
                args,
                index: match.index,
                context: this.analyzeStatementContext(analysis.content, match.index)
            });
        }
    }
    
    detectCallbackContexts(analysis) {
        this.callbackPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(analysis.content)) !== null) {
                const lineNumber = this.getLineNumber(analysis.content, match.index);
                const context = this.getCallbackContext(analysis.content, match.index);
                
                analysis.callbackContexts.push({
                    line: lineNumber,
                    pattern: pattern.source,
                    match: match[0],
                    context,
                    needsSelfReference: this.needsSelfReference(context)
                });
            }
        });
    }
    
    detectObjectPatterns(analysis) {
        // Detect object literal patterns and constructor functions
        const objectPatterns = [
            /var\s+(\w+)\s*=\s*{/g,
            /(\w+)\.prototype\s*=/g,
            /function\s+(\w+)\s*\(/g
        ];
        
        objectPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(analysis.content)) !== null) {
                const objectName = match[1];
                analysis.objectPatterns.push({
                    name: objectName,
                    type: this.getObjectType(match[0]),
                    line: this.getLineNumber(analysis.content, match.index)
                });
            }
        });
    }
    
    detectCategory(filePath, content) {
        if (this.options.categoryOverride) {
            return this.options.categoryOverride;
        }
        
        const fileName = path.basename(filePath).toLowerCase();
        
        // Check file name patterns
        for (const [keyword, category] of Object.entries(this.categoryMappings)) {
            if (fileName.includes(keyword)) {
                return category;
            }
        }
        
        // Check content patterns
        if (content.includes('TinyMCE') || content.includes('editor')) {
            return 'Direct Editor';
        }
        
        if (content.includes('$.ajax') || content.includes('XMLHttpRequest')) {
            return 'AJAX';
        }
        
        if (content.includes('link') || content.includes('url')) {
            return 'Link Tracking';
        }
        
        // Default category
        return 'General';
    }
    
    async createMigrationPlan(analysis) {
        console.log('📋 Phase 3: Creating migration plan');
        
        const plan = {
            analysis,
            needsDebugHelper: !analysis.hasDebugHelper,
            needsSelfReferences: [],
            replacements: []
        };
        
        // Plan debug helper addition
        if (plan.needsDebugHelper) {
            plan.debugHelperLocation = this.findBestDebugHelperLocation(analysis);
        }
        
        // Plan self reference additions
        analysis.callbackContexts.forEach(context => {
            if (context.needsSelfReference) {
                plan.needsSelfReferences.push(context);
            }
        });
        
        // Plan console replacements
        analysis.consoleStatements.forEach(stmt => {
            const replacement = this.createReplacement(stmt, analysis);
            plan.replacements.push(replacement);
        });
        
        console.log(`✅ Plan created: ${plan.replacements.length} replacements, ${plan.needsSelfReferences.length} self references`);
        
        return plan;
    }
    
    createReplacement(statement, analysis) {
        const category = analysis.suggestedCategory;
        const method = this.mapConsoleMethod(statement.method);
        const isInCallback = this.isInCallbackContext(statement, analysis);
        const debugPrefix = isInCallback ? 'self.debug' : 'this.debug';
        
        // Parse console arguments
        const { message, data } = this.parseConsoleStatement(statement);
        
        // Create replacement
        let replacement;
        if (data) {
            replacement = `${debugPrefix}.${method}('${category}', ${message}, ${data});`;
        } else {
            replacement = `${debugPrefix}.${method}('${category}', ${message});`;
        }
        
        return {
            original: statement.statement,
            replacement,
            line: statement.line,
            isInCallback,
            category
        };
    }
    
    async executeMigration(filePath, plan) {
        console.log('🔧 Phase 4: Executing safe migration');
        
        let content = fs.readFileSync(filePath, 'utf8');
        
        // Step 1: Add debug helper if needed
        if (plan.needsDebugHelper) {
            content = this.addDebugHelper(content, plan.debugHelperLocation);
            this.changes.push('Added debug helper object');
        }
        
        // Step 2: Add self references where needed
        for (const selfRef of plan.needsSelfReferences) {
            content = this.addSelfReference(content, selfRef);
            this.changes.push(`Added 'var self = this;' at line ${selfRef.line}`);
        }
        
        // Step 3: Replace console statements (in reverse order to preserve line numbers)
        plan.replacements.sort((a, b) => b.line - a.line);
        
        for (const replacement of plan.replacements) {
            const before = replacement.original;
            const after = `// ${before}\n        ${replacement.replacement}`;
            
            content = content.replace(replacement.original, replacement.replacement);
            this.changes.push(`Line ${replacement.line}: ${replacement.original} → ${replacement.replacement}`);
        }
        
        // Write updated content
        fs.writeFileSync(filePath, content, 'utf8');
        
        console.log(`✅ Migration executed: ${this.changes.length} changes made`);
    }
    
    async validateMigration(filePath) {
        console.log('✅ Phase 5: Validating migration');
        
        // Syntax check
        try {
            execSync(`node -c ${filePath}`, { stdio: 'pipe' });
            console.log('✅ Syntax validation passed');
        } catch (error) {
            throw new Error(`Syntax error in migrated file: ${error.message}`);
        }
        
        // Linter check (if available)
        try {
            execSync(`npx eslint ${filePath} --format compact`, { stdio: 'pipe' });
            console.log('✅ ESLint validation passed');
        } catch (error) {
            // Non-critical - just warn
            console.log('⚠️  ESLint warnings (non-critical):', error.message.substring(0, 200));
        }
        
        // Check file size increase is reasonable
        const originalSize = fs.statSync(this.backupFile).size;
        const newSize = fs.statSync(filePath).size;
        const increase = ((newSize - originalSize) / originalSize) * 100;
        
        if (increase > 50) {
            throw new Error(`File size increased by ${increase.toFixed(1)}% - this seems excessive`);
        }
        
        console.log(`✅ File size change: +${increase.toFixed(1)}%`);
    }
    
    async reportCompletion() {
        console.log('\n🎉 Migration completed successfully!');
        console.log('\n📋 Changes Summary:');
        this.changes.forEach((change, i) => {
            console.log(`  ${i + 1}. ${change}`);
        });
        
        console.log('\n🔍 Git Status:');
        try {
            const gitStatus = execSync('git status --porcelain', { encoding: 'utf8' });
            console.log(gitStatus);
        } catch (error) {
            console.log('Could not get git status');
        }
        
        console.log('\n📄 Git Diff Preview:');
        try {
            const gitDiff = execSync('git diff HEAD --stat', { encoding: 'utf8' });
            console.log(gitDiff);
        } catch (error) {
            console.log('Could not get git diff');
        }
        
        console.log('\n⚠️  IMPORTANT: Changes are NOT committed!');
        console.log('To review changes: git diff');
        console.log('To commit changes: git add . && git commit -m "Migrate console logging to debug system"');
        console.log('To rollback: git checkout main && git branch -D ' + this.branchName);
        
        console.log('\n🧪 Testing Recommendations:');
        console.log('1. Test with debug logging ENABLED: ?slmm_debug=true');
        console.log('2. Test with debug logging DISABLED (default)');  
        console.log('3. Check browser console for any JavaScript errors');
        console.log('4. Verify all original functionality still works');
        
        console.log(`\n✅ Backup available at: ${this.backupFile}`);
    }
    
    reportDryRun(plan) {
        console.log('\n🔍 DRY RUN - Preview of changes:');
        console.log(`\nFile: ${plan.analysis.filePath}`);
        console.log(`Suggested Category: ${plan.analysis.suggestedCategory}`);
        
        if (plan.needsDebugHelper) {
            console.log('\n➕ Would add debug helper object');
        }
        
        if (plan.needsSelfReferences.length > 0) {
            console.log(`\n📌 Would add ${plan.needsSelfReferences.length} 'var self = this;' references:`);
            plan.needsSelfReferences.forEach(ref => {
                console.log(`  Line ${ref.line}: ${ref.match}`);
            });
        }
        
        console.log(`\n🔄 Would replace ${plan.replacements.length} console statements:`);
        plan.replacements.forEach(replacement => {
            console.log(`  Line ${replacement.line}:`);
            console.log(`    OLD: ${replacement.original}`);
            console.log(`    NEW: ${replacement.replacement}`);
        });
        
        console.log('\n💡 Run without --dry-run to execute migration');
    }
    
    async emergencyRollback(filePath) {
        console.log('🚨 Emergency rollback initiated');
        
        if (this.backupFile && fs.existsSync(this.backupFile)) {
            fs.copyFileSync(this.backupFile, filePath);
            console.log('✅ File restored from backup');
        }
        
        try {
            execSync('git checkout main', { stdio: 'inherit' });
            execSync(`git branch -D ${this.branchName}`, { stdio: 'inherit' });
            console.log('✅ Git branch cleaned up');
        } catch (error) {
            console.log('⚠️  Manual git cleanup may be needed');
        }
    }
    
    // Helper methods
    getLineNumber(content, index) {
        return content.substring(0, index).split('\n').length;
    }
    
    parseConsoleArgs(argsString) {
        // Simple argument parsing - could be enhanced
        return argsString.split(',').map(arg => arg.trim());
    }
    
    analyzeStatementContext(content, index) {
        const before = content.substring(Math.max(0, index - 100), index);
        const after = content.substring(index, Math.min(content.length, index + 100));
        return { before, after };
    }
    
    getCallbackContext(content, index) {
        const start = Math.max(0, index - 200);
        const end = Math.min(content.length, index + 200);
        return content.substring(start, end);
    }
    
    needsSelfReference(context) {
        return context.includes('function(') && !context.includes('var self = this');
    }
    
    getObjectType(pattern) {
        if (pattern.includes('var')) return 'variable';
        if (pattern.includes('prototype')) return 'prototype';
        if (pattern.includes('function')) return 'constructor';
        return 'unknown';
    }
    
    mapConsoleMethod(method) {
        const mapping = {
            'log': 'log',
            'warn': 'warn', 
            'error': 'error',
            'info': 'info'
        };
        return mapping[method] || 'log';
    }
    
    isInCallbackContext(statement, analysis) {
        return analysis.callbackContexts.some(ctx => 
            Math.abs(ctx.line - statement.line) < 5
        );
    }
    
    parseConsoleStatement(statement) {
        const args = statement.args;
        if (args.length === 1) {
            return { message: args[0], data: null };
        } else if (args.length >= 2) {
            return { message: args[0], data: args.slice(1).join(', ') };
        }
        return { message: "'Empty message'", data: null };
    }
    
    addDebugHelper(content, location) {
        const debugHelper = `
    // Debug helper - ALWAYS include this pattern
    debug: {
        log: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            }
        },
        success: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.success(category, message, data);
            }
        },
        warn: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            }
        },
        error: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            }
        }
    },
`;
        
        // Find best insertion point (after first opening brace)
        const insertIndex = content.indexOf('{') + 1;
        return content.slice(0, insertIndex) + debugHelper + content.slice(insertIndex);
    }
    
    addSelfReference(content, selfRef) {
        const lines = content.split('\n');
        const targetLine = selfRef.line - 1;
        
        // Add var self = this; before the function
        if (!lines[targetLine - 1] || !lines[targetLine - 1].includes('var self = this')) {
            const indentation = this.getIndentation(lines[targetLine]);
            lines.splice(targetLine, 0, `${indentation}var self = this;`);
        }
        
        return lines.join('\n');
    }
    
    getIndentation(line) {
        const match = line.match(/^(\s*)/);
        return match ? match[1] : '';
    }
    
    findBestDebugHelperLocation(analysis) {
        // Find the best place to insert debug helper (after first opening brace)
        return analysis.content.indexOf('{') + 1;
    }
}

// CLI Interface
function main() {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log(`
🔧 SLMM Debug Migration Agent

Usage: node debug-migration.js <file-path> [options]

Options:
  --category=<name>  Override default category detection
  --dry-run          Show what would be changed without modifying files
  --emergency        Extra safety checks for critical files

Examples:
  node debug-migration.js assets/js/slmm-direct-editor.js
  node debug-migration.js assets/js/slmm-link-popup.js --category="Link Popup"
  node debug-migration.js assets/js/file.js --dry-run
        `);
        process.exit(1);
    }
    
    const filePath = args[0];
    const options = {};
    
    args.slice(1).forEach(arg => {
        if (arg === '--dry-run') {
            options.dryRun = true;
        } else if (arg === '--emergency') {
            options.emergency = true;
        } else if (arg.startsWith('--category=')) {
            options.category = arg.split('=')[1];
        }
    });
    
    const agent = new DebugMigrationAgent(options);
    
    agent.migrate(filePath)
        .then(() => {
            process.exit(0);
        })
        .catch(error => {
            console.error(`\n❌ Migration failed: ${error.message}`);
            process.exit(1);
        });
}

if (require.main === module) {
    main();
}

module.exports = DebugMigrationAgent;