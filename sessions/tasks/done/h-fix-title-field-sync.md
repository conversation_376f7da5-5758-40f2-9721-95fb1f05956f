---
task: h-fix-title-field-sync
branch: fix/title-field-sync
status: completed
created: 2025-01-10
completed: 2025-01-10
modules: [slmm-direct-editor, canvas-nodes, acf-title-system]
---

# CRITICAL: Fix Title Field Synchronization and TinyMCE Unlinking

## Problem/Goal

**CRITICAL BUG**: The title field editing is causing two major issues:
1. **TinyMCE Content Erasure**: Editing the title field deletes everything in the TinyMCE editor and populates it with title content - THIS SHOULD NOT HAPPEN
2. **ACF Title Sync Broken**: ACF title field edits are no longer syncing to the canvas nodes in real-time

### Affected Components
- `@assets/js/slmm-direct-editor.js` - Title field handling (lines ~1987-2000+)
- Canvas nodes: `<text class="slmm-node-title" data-acf-title="true">` elements
- ACF title field: `.slmm-direct-editor-post-info.slmm-acf-swapped-title`

### User Impact
- **DATA LOSS**: Users lose their entire TinyMCE content when editing titles
- **No Real-time Updates**: Title changes don't reflect on canvas immediately
- **Broken Workflow**: Users can't safely edit titles without losing work

## Success Criteria
- [x] **CRITICAL**: Title field editing NEVER affects TinyMCE editor content
- [x] **CRITICAL**: TinyMCE content and title fields are completely isolated  
- [x] ACF title edits sync immediately to canvas nodes (like regular title behavior)
- [x] Real-time frontend updates work without requiring backend save
- [x] AJAX backend updates happen after frontend updates (existing pattern)
- [x] Title field changes trigger canvas node refresh via existing functions

## Context Files
- @assets/js/slmm-direct-editor.js:1987-2100  # Title display update function
- @assets/js/slmm-direct-editor.js:2000-2100  # ACF title handling logic
- @commits/bdcacbac2a25bfc060f26c3e1ef6a5af043ee159  # Potential fix location
- @commits/0337a655392409184d1d98f1d97d14fb50e8c61e  # Alternative fix location

## User Notes
**FROM USER**:
- Issue appeared during recent updates 
- Title field and TinyMCE editor are now incorrectly linked
- ACF title field (`slmm-acf-swapped-title`) not syncing to canvas nodes
- Canvas nodes have `data-acf-title="true"` and `data-original-title` attributes
- Regular title updates work immediately on frontend, ACF titles should work the same way
- Title content was NEVER supposed to be linked to TinyMCE editor content

## Context Manifest

### 🔍 COMPREHENSIVE SYSTEM ANALYSIS

#### Title Field Synchronization Architecture
Based on codebase analysis, there are **TWO DISTINCT SYSTEMS** that handle titles:

1. **ACF Direct Editor Mode** (`acfMode.isEditingACF = true`)
   - Purpose: Edit ACF field content directly in TinyMCE editor
   - Trigger: When ACF mode is enabled AND validated AND field name exists
   - Functions: `loadACFEditorModal()` → `renderACFEditor()` → loads ACF field content into TinyMCE
   - Backend: `ajax_load_acf_editor` in `interlinking-suite.php:11746`
   - Content: ACF field value becomes TinyMCE editor content

2. **Regular Direct Editor with ACF Title Swapping** (`acfMode.isEditingACF = false`)
   - Purpose: Edit post content normally, but display ACF title if available
   - Trigger: Regular post editing with optional ACF title overlay
   - Functions: `updatePostTitleDisplay()` with ACF data for visual title swapping
   - Backend: Regular post content loading with ACF title metadata
   - Content: Post content in TinyMCE, title visual only

#### 🚨 ROOT CAUSE ANALYSIS

**The Problem**: These two systems are getting confused/mixed up, causing:

1. **TinyMCE Content Erasure**: 
   - When user edits title in Regular mode, system incorrectly triggers ACF mode
   - `renderACFEditor()` gets called instead of title-only update
   - `$content.html(data.editor_html)` at line 3607 replaces TinyMCE content
   - `data.editor_html` contains ACF field content (title) instead of post content

2. **ACF Title Sync Broken**:
   - `syncCanvasNodeTitle()` function exists but canvas updates not working
   - Canvas nodes: `<text class="slmm-node-title" data-acf-title="true">` 
   - D3 selector: `.node[data-post-id="postId"] .slmm-node-title`
   - Update method: `nodeText.text(newTitle)` at line 2229

#### 🔧 CRITICAL CODE LOCATIONS

**Frontend JavaScript** (`assets/js/slmm-direct-editor.js`):
- Line 1988: `updatePostTitleDisplay()` - Handles ACF title swapping display
- Line 2056: `makeACFTitleEditable()` - Enables title editing with ACF support
- Line 2157: `saveACFTitleChange()` - AJAX save for title changes
- Line 2219: `syncCanvasNodeTitle()` - Canvas node update (working code)
- Line 3512: `acfMode.isEditingACF` - Mode detection logic
- Line 3531: `loadEditorModalWithACF()` - Mode routing decision
- Line 3607: `$content.html(data.editor_html)` - **POTENTIAL PROBLEM LOCATION**

**Backend PHP** (`includes/interlinking/interlinking-suite.php`):
- Line 11746: `ajax_load_acf_editor()` - Loads ACF field as editor content
- Line 11822: Returns `editor_html` with ACF field content
- Line 86: `slmm_update_acf_field` - Title update AJAX handler

#### 🎯 SOLUTION ARCHITECTURE

**The Fix Strategy**:
1. **Isolate Systems**: Ensure title editing NEVER triggers ACF editor mode
2. **Preserve TinyMCE**: Title changes must be completely separate from editor content
3. **Fix Canvas Sync**: Ensure title changes trigger proper canvas updates
4. **Validate Mode Detection**: Fix `acfMode.isEditingACF` logic to prevent confusion

**Key Distinction**:
- **ACF Title Swapping**: Visual title replacement only (should not affect TinyMCE)
- **ACF Direct Editor**: Complete TinyMCE content replacement (separate use case)

#### 🔍 EXISTING PATTERNS FOR REAL-TIME UPDATES

**Canvas Update Pattern** (Line 2219-2244):
```javascript
syncCanvasNodeTitle: function(postId, newTitle) {
    var nodeText = d3.select('.node[data-post-id="' + postId + '"] .slmm-node-title');
    if (!nodeText.empty()) {
        nodeText.text(newTitle);
        nodeText.attr('data-acf-title', 'true');
    }
}
```

**AJAX Backend Update Pattern** (Line 2170-2212):
```javascript
$.ajax({
    url: slmmDirectEditorData.ajax_url,
    action: 'slmm_update_acf_field',
    post_id: this.currentPostId,
    field_name: acfData.acf_field_name,
    new_value: newTitle
})
```

#### 🧪 TESTING REQUIREMENTS

**Critical Test Cases**:
1. Edit ACF title in regular post editor → TinyMCE content must remain unchanged
2. Edit ACF title → Canvas node updates immediately 
3. Edit ACF title → Backend save completes successfully
4. Edit regular title → Normal title behavior preserved
5. ACF Direct Editor mode → Should work independently without interference

**Validation Points**:
- `acfMode.isEditingACF` state accurate
- TinyMCE content isolation verified
- Canvas D3 updates functional
- AJAX save endpoints working
- No system cross-contamination

## Work Log
- [2025-01-10] Task created - CRITICAL priority due to data loss risk
- [2025-01-10] Context manifest created - Root cause identified in mode detection logic
- [2025-01-10] **COMPLETED** - All critical fixes implemented and committed

### ✅ CRITICAL FIXES IMPLEMENTED:

1. **ACF Mode Detection Fixed** (Lines 3512-3542)
   - Added `explicitACFRequest` flag to prevent accidental ACF mode triggering
   - Enhanced mode validation with triple-check system
   - Added explicit mode reset for regular Direct Editor usage

2. **TinyMCE Content Protection** (Lines 3547-3561, 3707-3720)
   - Implemented complete isolation between title editing and TinyMCE content
   - Added safety guards in `loadEditorModalWithACF` and `saveAndCloseWithACF`
   - Prevents line 3607 content replacement during regular title editing

3. **Canvas Sync Restoration** (Lines 2169-2172, 2187)
   - Added immediate frontend sync before AJAX save (matches regular title behavior)
   - Preserved existing `syncCanvasNodeTitle` functionality
   - Ensured canvas updates happen immediately for better UX

4. **API Methods Added** (Lines 3414-3439)
   - `openACFFieldEditor(postId, fieldName)` - Explicit ACF mode
   - `openRegularEditor(postId)` - Explicit regular mode  
   - Clear separation prevents accidental mode confusion

**Result**: Title editing is now completely isolated from TinyMCE editor content. Users can safely edit titles without any risk of data loss, and ACF titles sync immediately to canvas nodes.