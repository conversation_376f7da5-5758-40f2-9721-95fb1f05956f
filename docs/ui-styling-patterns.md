# UI Styling & Design Patterns (v4.10.0)

This document contains the current UI and styling patterns for the SLMM SEO Bundle WordPress plugin. These patterns ensure consistency across all features and maintain a professional appearance within the WordPress admin environment.

## WordPress Admin Button Standardization

The plugin follows a 40px minimum height standard for all buttons to ensure consistent visual hierarchy and accessibility.

```css
/* 40px minimum height standard (from slmm-admin.css) */
.wp-core-ui .button,
.wp-core-ui .button-primary,
.wp-core-ui .button-secondary {
    min-height: 40px !important;
    line-height: 38px !important;
    padding: 0 12px !important;
}

/* Proper vertical alignment */
#custom-editor-buttons .button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}
```

## Enhanced Security UI (v4.10.0 Dark Theme)

The plugin features an elegant dark theme design for security-related UI elements, providing clear visual distinction for important warnings and notifications.

```css
/* New warning box styling - elegant dark surface */
.slmm-warning-box {
    background-color: #1a1a1a; /* Dark surface */
    border: 4px dashed #f97316; /* Orange dashed border */
    color: #d1d5db; /* Light text */
    padding: 20px;
    border-radius: 8px;
}

/* Code element styling with proper contrast */
.slmm-warning-box code {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
}
```

## Professional Checkbox Design (v4.10.0)

Custom checkbox styling specifically designed for search and replace forms, providing a modern and accessible interface.

```css
/* Custom checkbox styling for search and replace forms */
.slmm-checkbox {
    appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    cursor: pointer;
}

.slmm-checkbox:checked {
    background: #3b82f6;
    border-color: #3b82f6;
}

.slmm-checkbox:hover {
    border-color: #6b7280;
}
```

## Icon System Consistency

Professional icon integration system ensuring consistent sizing and visual treatment across all plugin interfaces.

```css
/* Professional icon integration across all tabs */
.slmm-tab-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Hover and active states for navigation clarity */
.slmm-tab.active .slmm-tab-icon {
    opacity: 1;
    filter: brightness(1.2);
}
```

## Usage Guidelines

### Button Implementation
- All buttons must use the standardized 40px minimum height
- Use flexbox alignment for proper vertical centering
- Maintain consistent padding and line-height values

### Warning and Security Elements
- Use the dark theme warning box for security-related notifications
- Ensure proper contrast ratios for accessibility
- Apply consistent border radius and padding

### Form Elements
- Implement custom checkbox styling for better user experience
- Use consistent hover and active states
- Maintain accessibility standards with cursor pointer

### Icon Standards
- Keep icon sizes consistent at 20px × 20px
- Apply proper spacing with margin-right: 8px
- Use brightness filters for active state indication

## Implementation Notes

- All styles are scoped with plugin-specific classes to avoid conflicts
- CSS uses `!important` declarations where necessary to override WordPress defaults
- Color values follow a consistent design system with proper contrast ratios
- Responsive considerations are built into the flexible layout patterns

## Version History

- **v4.10.0**: Initial standardization of UI patterns
  - Implemented 40px button standard
  - Added dark theme security UI
  - Created professional checkbox design
  - Established icon system consistency