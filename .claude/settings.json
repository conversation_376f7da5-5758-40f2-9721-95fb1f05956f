{"hooks": {"UserPromptSubmit": [{"hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/user-messages.py"}]}], "PreToolUse": [{"matcher": "Write|Edit|MultiEdit|Task|Bash", "hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/sessions-enforce.py"}]}, {"matcher": "Task", "hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/task-transcript-link.py"}]}], "PostToolUse": [{"hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/post-tool-use.py"}]}], "SessionStart": [{"matcher": "startup|clear", "hooks": [{"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/hooks/session-start.py"}]}]}, "statusLine": {"type": "command", "command": "$CLAUDE_PROJECT_DIR/.claude/statusline-script.sh", "padding": 0}}