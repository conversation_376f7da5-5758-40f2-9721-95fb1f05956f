<?php
// File: includes/interlinking/grid-generator.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Grid Generator - Advanced spatial optimization system for silo visualization
 * 
 * Handles:
 * - Dynamic grid generation with spatial intelligence
 * - Optimal placement algorithms for pages and links
 * - Visual hierarchy optimization
 * - Collision detection and auto-adjustment
 */
class SLMM_Grid_Generator {
    
    private $default_grid_size = 50; // Grid unit size in pixels
    private $min_spacing = 2; // Minimum spacing between elements
    private $canvas_padding = 100; // Canvas edge padding
    
    /**
     * Generate optimized grid layout for silo visualization
     * 
     * @param array $silo_data Silo structure with pages and links
     * @param array $canvas_config Canvas configuration
     * @return array Complete grid layout with positioned elements
     */
    public function generate_silo_grid($silo_data, $canvas_config = array()) {
        // Merge with default canvas config
        $canvas_config = wp_parse_args($canvas_config, array(
            'width' => 1200,
            'height' => 800,
            'grid_size' => $this->default_grid_size,
            'auto_layout' => true,
            'hierarchy_mode' => 'authority', // 'authority', 'topical', 'custom'
            'spacing_factor' => 1.0,
            'visual_clustering' => true
        ));
        
        if (!isset($silo_data['pages']) || empty($silo_data['pages'])) {
            return array('error' => 'No pages to layout');
        }
        
        $pages = $silo_data['pages'];
        $links = isset($silo_data['links']) ? $silo_data['links'] : array();
        
        // Initialize grid system
        $grid_info = $this->initialize_grid($canvas_config);
        
        // Analyze page relationships and hierarchy
        $hierarchy_data = $this->analyze_page_hierarchy($pages, $links, $canvas_config);
        
        // Generate optimal placement positions
        $placement_data = $this->calculate_optimal_positions($hierarchy_data, $grid_info, $canvas_config);
        
        // Apply clustering and grouping
        if ($canvas_config['visual_clustering']) {
            $placement_data = $this->apply_visual_clustering($placement_data, $hierarchy_data, $grid_info);
        }
        
        // Resolve collisions and optimize spacing
        $final_positions = $this->resolve_spatial_conflicts($placement_data, $grid_info);
        
        // Generate link routing paths
        $link_paths = $this->generate_link_paths($final_positions, $links, $grid_info);
        
        $result = array(
            'grid_info' => $grid_info,
            'positions' => $final_positions,
            'links' => $link_paths,
            'hierarchy' => $hierarchy_data,
            'metadata' => array(
                'total_pages' => count($pages),
                'total_links' => count($links),
                'canvas_utilization' => $this->calculate_canvas_utilization($final_positions, $canvas_config),
                'layout_efficiency' => $this->calculate_layout_efficiency($final_positions, $link_paths)
            )
        );
        
        error_log('[SLMM Grid Generator] Generated grid layout for ' . count($pages) . ' pages');
        
        return $result;
    }
    
    /**
     * Initialize grid system with spatial intelligence
     * 
     * @param array $canvas_config
     * @return array Grid initialization data
     */
    private function initialize_grid($canvas_config) {
        $grid_size = $canvas_config['grid_size'];
        $width = $canvas_config['width'];
        $height = $canvas_config['height'];
        
        $cols = floor(($width - ($this->canvas_padding * 2)) / $grid_size);
        $rows = floor(($height - ($this->canvas_padding * 2)) / $grid_size);
        
        // Create occupancy grid
        $occupancy_grid = array();
        for ($row = 0; $row < $rows; $row++) {
            for ($col = 0; $col < $cols; $col++) {
                $occupancy_grid[$row][$col] = false;
            }
        }
        
        return array(
            'grid_size' => $grid_size,
            'cols' => $cols,
            'rows' => $rows,
            'canvas_width' => $width,
            'canvas_height' => $height,
            'padding' => $this->canvas_padding,
            'occupancy' => $occupancy_grid,
            'reserved_zones' => array() // For special positioning
        );
    }
    
    /**
     * Analyze page hierarchy and relationships for optimal positioning
     * 
     * @param array $pages
     * @param array $links
     * @param array $canvas_config
     * @return array Hierarchy analysis data
     */
    private function analyze_page_hierarchy($pages, $links, $canvas_config) {
        $hierarchy_mode = $canvas_config['hierarchy_mode'];
        
        // Build connection matrix
        $connections = $this->build_connection_matrix($pages, $links);
        
        // Calculate hierarchy scores based on selected mode
        $hierarchy_scores = array();
        
        switch ($hierarchy_mode) {
            case 'authority':
                $hierarchy_scores = $this->calculate_authority_hierarchy($pages, $connections);
                break;
            case 'topical':
                $hierarchy_scores = $this->calculate_topical_hierarchy($pages, $connections);
                break;
            case 'custom':
                $hierarchy_scores = $this->extract_custom_hierarchy($pages);
                break;
            default:
                $hierarchy_scores = $this->calculate_authority_hierarchy($pages, $connections);
        }
        
        // Identify page clusters and groups
        $clusters = $this->identify_page_clusters($pages, $connections, $hierarchy_scores);
        
        // Determine layout tiers (top, middle, bottom)
        $tiers = $this->organize_into_tiers($hierarchy_scores, 3);
        
        return array(
            'scores' => $hierarchy_scores,
            'connections' => $connections,
            'clusters' => $clusters,
            'tiers' => $tiers,
            'mode' => $hierarchy_mode
        );
    }
    
    /**
     * Build connection matrix between pages
     * 
     * @param array $pages
     * @param array $links
     * @return array Connection matrix
     */
    private function build_connection_matrix($pages, $links) {
        $matrix = array();
        
        // Initialize matrix
        foreach ($pages as $page_id => $page_data) {
            $matrix[$page_id] = array(
                'inbound' => array(),
                'outbound' => array(),
                'strength' => 0
            );
        }
        
        // Populate connections from links
        foreach ($links as $link) {
            if (!isset($link['from']) || !isset($link['to'])) {
                continue;
            }
            
            $from = $link['from'];
            $to = $link['to'];
            
            if (isset($matrix[$from]) && isset($matrix[$to])) {
                $matrix[$from]['outbound'][$to] = isset($link['weight']) ? $link['weight'] : 1.0;
                $matrix[$to]['inbound'][$from] = isset($link['weight']) ? $link['weight'] : 1.0;
            }
        }
        
        // Calculate connection strength
        foreach ($matrix as $page_id => &$data) {
            $inbound_strength = array_sum($data['inbound']);
            $outbound_strength = array_sum($data['outbound']);
            $data['strength'] = $inbound_strength + ($outbound_strength * 0.5);
        }
        
        return $matrix;
    }
    
    /**
     * Calculate authority-based hierarchy scores
     * 
     * @param array $pages
     * @param array $connections
     * @return array Authority hierarchy scores
     */
    private function calculate_authority_hierarchy($pages, $connections) {
        $scores = array();
        
        foreach ($pages as $page_id => $page_data) {
            $base_score = 50; // Base score for all pages
            
            // Add inbound link authority
            if (isset($connections[$page_id]['inbound'])) {
                $inbound_count = count($connections[$page_id]['inbound']);
                $inbound_weight = array_sum($connections[$page_id]['inbound']);
                $base_score += ($inbound_count * 10) + ($inbound_weight * 5);
            }
            
            // Add content quality factors
            if (isset($page_data['content'])) {
                $word_count = str_word_count(strip_tags($page_data['content']));
                if ($word_count > 1500) $base_score += 20;
                elseif ($word_count > 1000) $base_score += 15;
                elseif ($word_count > 500) $base_score += 10;
            }
            
            // Add keyword targeting bonus
            if (isset($page_data['target_keywords']) && !empty($page_data['target_keywords'])) {
                $base_score += count($page_data['target_keywords']) * 2;
            }
            
            $scores[$page_id] = $base_score;
        }
        
        return $scores;
    }
    
    /**
     * Calculate topical hierarchy based on content relationships
     * 
     * @param array $pages
     * @param array $connections
     * @return array Topical hierarchy scores
     */
    private function calculate_topical_hierarchy($pages, $connections) {
        $scores = array();
        
        // Extract all topics/keywords
        $all_topics = array();
        foreach ($pages as $page_id => $page_data) {
            if (isset($page_data['target_keywords'])) {
                $all_topics = array_merge($all_topics, $page_data['target_keywords']);
            }
        }
        
        $topic_frequency = array_count_values(array_map('strtolower', $all_topics));
        
        foreach ($pages as $page_id => $page_data) {
            $base_score = 50;
            
            // Score based on topic centrality
            if (isset($page_data['target_keywords'])) {
                foreach ($page_data['target_keywords'] as $keyword) {
                    $keyword_lower = strtolower($keyword);
                    if (isset($topic_frequency[$keyword_lower])) {
                        $base_score += $topic_frequency[$keyword_lower] * 5;
                    }
                }
            }
            
            // Bonus for topic breadth (covers multiple related topics)
            $topic_count = isset($page_data['target_keywords']) ? count($page_data['target_keywords']) : 0;
            $base_score += min(30, $topic_count * 5);
            
            $scores[$page_id] = $base_score;
        }
        
        return $scores;
    }
    
    /**
     * Extract custom hierarchy from page data
     * 
     * @param array $pages
     * @return array Custom hierarchy scores
     */
    private function extract_custom_hierarchy($pages) {
        $scores = array();
        
        foreach ($pages as $page_id => $page_data) {
            // Check for custom hierarchy indicators
            $custom_score = 50; // Default
            
            if (isset($page_data['hierarchy_level'])) {
                $custom_score = intval($page_data['hierarchy_level']);
            } elseif (isset($page_data['priority'])) {
                $priority = strtolower($page_data['priority']);
                switch ($priority) {
                    case 'high': $custom_score = 90; break;
                    case 'medium': $custom_score = 70; break;
                    case 'low': $custom_score = 40; break;
                }
            }
            
            $scores[$page_id] = $custom_score;
        }
        
        return $scores;
    }
    
    /**
     * Identify clusters of related pages
     * 
     * @param array $pages
     * @param array $connections
     * @param array $hierarchy_scores
     * @return array Page clusters
     */
    private function identify_page_clusters($pages, $connections, $hierarchy_scores) {
        $clusters = array();
        $assigned_pages = array();
        $cluster_id = 0;
        
        // Sort pages by hierarchy score (process highest first)
        $sorted_pages = $hierarchy_scores;
        arsort($sorted_pages);
        
        foreach ($sorted_pages as $page_id => $score) {
            if (isset($assigned_pages[$page_id])) {
                continue; // Already assigned to a cluster
            }
            
            // Start a new cluster
            $current_cluster = array(
                'id' => $cluster_id++,
                'center_page' => $page_id,
                'pages' => array($page_id),
                'avg_score' => $score,
                'topic_keywords' => array()
            );
            
            // Add strongly connected pages to cluster
            if (isset($connections[$page_id])) {
                $connected_pages = array_merge(
                    array_keys($connections[$page_id]['outbound']),
                    array_keys($connections[$page_id]['inbound'])
                );
                
                foreach ($connected_pages as $connected_id) {
                    if (!isset($assigned_pages[$connected_id]) && 
                        abs($hierarchy_scores[$connected_id] - $score) < 30) {
                        
                        $current_cluster['pages'][] = $connected_id;
                        $assigned_pages[$connected_id] = $current_cluster['id'];
                    }
                }
            }
            
            // Calculate cluster metadata
            $total_score = 0;
            $cluster_keywords = array();
            
            foreach ($current_cluster['pages'] as $cluster_page_id) {
                $total_score += $hierarchy_scores[$cluster_page_id];
                
                if (isset($pages[$cluster_page_id]['target_keywords'])) {
                    $cluster_keywords = array_merge($cluster_keywords, $pages[$cluster_page_id]['target_keywords']);
                }
                
                $assigned_pages[$cluster_page_id] = $current_cluster['id'];
            }
            
            $current_cluster['avg_score'] = $total_score / count($current_cluster['pages']);
            $current_cluster['topic_keywords'] = array_unique($cluster_keywords);
            
            $clusters[] = $current_cluster;
        }
        
        return $clusters;
    }
    
    /**
     * Organize pages into visual tiers for layout
     * 
     * @param array $hierarchy_scores
     * @param int $tier_count Number of tiers to create
     * @return array Tier organization
     */
    private function organize_into_tiers($hierarchy_scores, $tier_count = 3) {
        $scores = array_values($hierarchy_scores);
        sort($scores);
        
        $tiers = array();
        $score_range = max($scores) - min($scores);
        
        if ($score_range == 0) {
            // All pages have same score, put all in middle tier
            $tiers[1] = array_keys($hierarchy_scores);
            return $tiers;
        }
        
        $tier_size = $score_range / $tier_count;
        
        for ($i = 0; $i < $tier_count; $i++) {
            $tiers[$i] = array();
        }
        
        foreach ($hierarchy_scores as $page_id => $score) {
            $tier_index = min($tier_count - 1, floor(($score - min($scores)) / $tier_size));
            $tiers[$tier_index][] = $page_id;
        }
        
        return $tiers;
    }
    
    /**
     * Calculate optimal positions for all elements
     * 
     * @param array $hierarchy_data
     * @param array $grid_info
     * @param array $canvas_config
     * @return array Position data for all elements
     */
    private function calculate_optimal_positions($hierarchy_data, $grid_info, $canvas_config) {
        $positions = array();
        $tiers = $hierarchy_data['tiers'];
        $clusters = $hierarchy_data['clusters'];
        
        // Calculate tier positioning (vertical distribution)
        $tier_heights = $this->calculate_tier_heights($tiers, $grid_info);
        
        foreach ($tiers as $tier_index => $tier_pages) {
            if (empty($tier_pages)) continue;
            
            $tier_y = $tier_heights[$tier_index];
            
            // Distribute pages horizontally within tier
            $horizontal_positions = $this->distribute_horizontally($tier_pages, $grid_info, $clusters);
            
            foreach ($tier_pages as $page_index => $page_id) {
                $positions[$page_id] = array(
                    'x' => $horizontal_positions[$page_index]['x'],
                    'y' => $tier_y,
                    'grid_x' => $horizontal_positions[$page_index]['grid_x'],
                    'grid_y' => floor($tier_y / $grid_info['grid_size']),
                    'tier' => $tier_index,
                    'cluster' => $this->find_page_cluster($page_id, $clusters)
                );
            }
        }
        
        return $positions;
    }
    
    /**
     * Calculate vertical positions for tiers
     * 
     * @param array $tiers
     * @param array $grid_info
     * @return array Tier Y positions
     */
    private function calculate_tier_heights($tiers, $grid_info) {
        $tier_count = count($tiers);
        if ($tier_count == 0) return array();
        
        $available_height = $grid_info['canvas_height'] - ($grid_info['padding'] * 2);
        
        // Start root node near top (10% down from available height)
        $root_offset = $available_height * 0.1;
        
        // Space remaining tiers evenly in remaining area
        $remaining_height = $available_height - $root_offset;
        $tier_spacing = $tier_count > 1 ? $remaining_height / ($tier_count - 1) : 0;
        
        $heights = array();
        for ($i = 0; $i < $tier_count; $i++) {
            if ($i == 0) {
                // Root tier positioned near top
                $heights[$i] = $grid_info['padding'] + $root_offset;
            } else {
                // Subsequent tiers spaced evenly below
                $heights[$i] = $grid_info['padding'] + $root_offset + ($tier_spacing * ($i - 1));
            }
        }
        
        return $heights;
    }
    
    /**
     * Distribute pages horizontally within a tier
     * 
     * @param array $tier_pages
     * @param array $grid_info
     * @param array $clusters
     * @return array Horizontal positions
     */
    private function distribute_horizontally($tier_pages, $grid_info, $clusters) {
        $page_count = count($tier_pages);
        if ($page_count == 0) return array();
        
        $available_width = $grid_info['canvas_width'] - ($grid_info['padding'] * 2);
        
        // Group pages by cluster for better positioning
        $clustered_pages = $this->group_pages_by_cluster($tier_pages, $clusters);
        
        $positions = array();
        $current_x = $grid_info['padding'];
        
        if ($page_count == 1) {
            // Center single page
            $positions[0] = array(
                'x' => $grid_info['canvas_width'] / 2,
                'grid_x' => floor($grid_info['cols'] / 2)
            );
        } else {
            // Distribute multiple pages
            $spacing = $available_width / ($page_count + 1);
            
            foreach ($tier_pages as $index => $page_id) {
                $x_pos = $grid_info['padding'] + ($spacing * ($index + 1));
                $grid_x = floor(($x_pos - $grid_info['padding']) / $grid_info['grid_size']);
                
                $positions[$index] = array(
                    'x' => $x_pos,
                    'grid_x' => max(0, min($grid_info['cols'] - 1, $grid_x))
                );
            }
        }
        
        return $positions;
    }
    
    /**
     * Group pages by their cluster membership
     * 
     * @param array $pages
     * @param array $clusters
     * @return array Clustered pages
     */
    private function group_pages_by_cluster($pages, $clusters) {
        $grouped = array();
        
        foreach ($pages as $page_id) {
            $cluster_id = $this->find_page_cluster($page_id, $clusters);
            if ($cluster_id !== null) {
                if (!isset($grouped[$cluster_id])) {
                    $grouped[$cluster_id] = array();
                }
                $grouped[$cluster_id][] = $page_id;
            } else {
                // Unclustered page
                $grouped['unclustered'][] = $page_id;
            }
        }
        
        return $grouped;
    }
    
    /**
     * Find which cluster a page belongs to
     * 
     * @param string $page_id
     * @param array $clusters
     * @return int|null Cluster ID or null
     */
    private function find_page_cluster($page_id, $clusters) {
        foreach ($clusters as $cluster) {
            if (in_array($page_id, $cluster['pages'])) {
                return $cluster['id'];
            }
        }
        return null;
    }
    
    /**
     * Apply visual clustering adjustments
     * 
     * @param array $placement_data
     * @param array $hierarchy_data
     * @param array $grid_info
     * @return array Adjusted placement data
     */
    private function apply_visual_clustering($placement_data, $hierarchy_data, $grid_info) {
        $clusters = $hierarchy_data['clusters'];
        $adjusted_positions = $placement_data;
        
        // For each cluster, try to bring pages closer together
        foreach ($clusters as $cluster) {
            if (count($cluster['pages']) < 2) continue;
            
            $cluster_pages = $cluster['pages'];
            $cluster_positions = array();
            
            // Collect current positions of cluster pages
            foreach ($cluster_pages as $page_id) {
                if (isset($adjusted_positions[$page_id])) {
                    $cluster_positions[$page_id] = $adjusted_positions[$page_id];
                }
            }
            
            if (count($cluster_positions) < 2) continue;
            
            // Calculate cluster centroid
            $centroid_x = 0;
            $centroid_y = 0;
            foreach ($cluster_positions as $pos) {
                $centroid_x += $pos['x'];
                $centroid_y += $pos['y'];
            }
            $centroid_x /= count($cluster_positions);
            $centroid_y /= count($cluster_positions);
            
            // Adjust positions to be closer to centroid (but maintain grid alignment)
            $pull_factor = 0.3; // How much to pull towards centroid
            
            foreach ($cluster_positions as $page_id => $pos) {
                $new_x = $pos['x'] + (($centroid_x - $pos['x']) * $pull_factor);
                $new_grid_x = round($new_x / $grid_info['grid_size']);
                $new_grid_x = max(0, min($grid_info['cols'] - 1, $new_grid_x));
                
                $adjusted_positions[$page_id]['x'] = $new_grid_x * $grid_info['grid_size'] + $grid_info['padding'];
                $adjusted_positions[$page_id]['grid_x'] = $new_grid_x;
            }
        }
        
        return $adjusted_positions;
    }
    
    /**
     * Resolve spatial conflicts and optimize spacing
     * 
     * @param array $placement_data
     * @param array $grid_info
     * @return array Conflict-free positions
     */
    private function resolve_spatial_conflicts($placement_data, $grid_info) {
        $occupancy = $grid_info['occupancy'];
        $resolved_positions = array();
        
        // Sort by priority (higher tier pages get preference)
        $sorted_pages = array();
        foreach ($placement_data as $page_id => $pos_data) {
            $sorted_pages[] = array('page_id' => $page_id, 'data' => $pos_data);
        }
        
        usort($sorted_pages, function($a, $b) {
            return $a['data']['tier'] - $b['data']['tier']; // Lower tier number = higher priority
        });
        
        // Place pages, resolving conflicts as we go
        foreach ($sorted_pages as $page_info) {
            $page_id = $page_info['page_id'];
            $desired_pos = $page_info['data'];
            
            $grid_x = $desired_pos['grid_x'];
            $grid_y = $desired_pos['grid_y'];
            
            // Check if desired position is available
            if (!isset($occupancy[$grid_y][$grid_x]) || !$occupancy[$grid_y][$grid_x]) {
                // Position is available
                $occupancy[$grid_y][$grid_x] = true;
                $resolved_positions[$page_id] = $desired_pos;
            } else {
                // Find nearest available position
                $new_position = $this->find_nearest_available_position($grid_x, $grid_y, $occupancy, $grid_info);
                
                if ($new_position) {
                    $occupancy[$new_position['grid_y']][$new_position['grid_x']] = true;
                    
                    $resolved_positions[$page_id] = array_merge($desired_pos, array(
                        'x' => $new_position['grid_x'] * $grid_info['grid_size'] + $grid_info['padding'],
                        'y' => $new_position['grid_y'] * $grid_info['grid_size'] + $grid_info['padding'],
                        'grid_x' => $new_position['grid_x'],
                        'grid_y' => $new_position['grid_y']
                    ));
                }
            }
        }
        
        return $resolved_positions;
    }
    
    /**
     * Find nearest available grid position
     * 
     * @param int $preferred_x
     * @param int $preferred_y
     * @param array $occupancy
     * @param array $grid_info
     * @return array|false New position or false if none found
     */
    private function find_nearest_available_position($preferred_x, $preferred_y, $occupancy, $grid_info) {
        $max_search_radius = 5;
        
        for ($radius = 1; $radius <= $max_search_radius; $radius++) {
            // Check positions in expanding square around preferred position
            for ($dx = -$radius; $dx <= $radius; $dx++) {
                for ($dy = -$radius; $dy <= $radius; $dy++) {
                    // Skip positions we've already checked in smaller radius
                    if (abs($dx) < $radius && abs($dy) < $radius) continue;
                    
                    $check_x = $preferred_x + $dx;
                    $check_y = $preferred_y + $dy;
                    
                    // Check bounds
                    if ($check_x >= 0 && $check_x < $grid_info['cols'] && 
                        $check_y >= 0 && $check_y < $grid_info['rows']) {
                        
                        if (!isset($occupancy[$check_y][$check_x]) || !$occupancy[$check_y][$check_x]) {
                            return array('grid_x' => $check_x, 'grid_y' => $check_y);
                        }
                    }
                }
            }
        }
        
        return false; // No position found
    }
    
    /**
     * Generate optimized link paths between positioned elements
     * 
     * @param array $positions
     * @param array $links
     * @param array $grid_info
     * @return array Link path data
     */
    private function generate_link_paths($positions, $links, $grid_info) {
        $link_paths = array();
        
        foreach ($links as $link_index => $link) {
            if (!isset($link['from']) || !isset($link['to']) || 
                !isset($positions[$link['from']]) || !isset($positions[$link['to']])) {
                continue;
            }
            
            $from_pos = $positions[$link['from']];
            $to_pos = $positions[$link['to']];
            
            // Calculate path points
            $path = $this->calculate_link_path($from_pos, $to_pos, $link, $grid_info);
            
            $link_paths[$link_index] = array(
                'from' => $link['from'],
                'to' => $link['to'],
                'path' => $path,
                'type' => isset($link['type']) ? $link['type'] : 'default',
                'weight' => isset($link['weight']) ? $link['weight'] : 1.0,
                'style' => $this->determine_link_style($link, $from_pos, $to_pos)
            );
        }
        
        return $link_paths;
    }
    
    /**
     * Calculate optimal path for a link
     * 
     * @param array $from_pos
     * @param array $to_pos
     * @param array $link_data
     * @param array $grid_info
     * @return array Path coordinates
     */
    private function calculate_link_path($from_pos, $to_pos, $link_data, $grid_info) {
        // Simple path for now - can be enhanced with pathfinding algorithms
        $path = array(
            array('x' => $from_pos['x'], 'y' => $from_pos['y']),
            array('x' => $to_pos['x'], 'y' => $to_pos['y'])
        );
        
        // Add curved path for better visualization
        if (abs($from_pos['x'] - $to_pos['x']) > $grid_info['grid_size'] * 2 || 
            abs($from_pos['y'] - $to_pos['y']) > $grid_info['grid_size'] * 2) {
            
            $mid_x = ($from_pos['x'] + $to_pos['x']) / 2;
            $mid_y = ($from_pos['y'] + $to_pos['y']) / 2;
            
            // Add control point for curved line
            $path = array(
                array('x' => $from_pos['x'], 'y' => $from_pos['y']),
                array('x' => $mid_x, 'y' => $mid_y, 'control' => true),
                array('x' => $to_pos['x'], 'y' => $to_pos['y'])
            );
        }
        
        return $path;
    }
    
    /**
     * Determine visual style for link based on properties
     * 
     * @param array $link_data
     * @param array $from_pos
     * @param array $to_pos
     * @return array Style properties
     */
    private function determine_link_style($link_data, $from_pos, $to_pos) {
        $style = array(
            'color' => '#666666',
            'width' => 2,
            'dash' => 'solid',
            'opacity' => 0.7
        );
        
        // Adjust style based on link weight
        if (isset($link_data['weight'])) {
            $weight = $link_data['weight'];
            $style['width'] = max(1, min(5, $weight * 2));
            $style['opacity'] = max(0.3, min(1.0, 0.5 + ($weight * 0.3)));
        }
        
        // Adjust style based on link type
        if (isset($link_data['type'])) {
            switch ($link_data['type']) {
                case 'contextual':
                    $style['color'] = '#2563eb';
                    break;
                case 'navigational':
                    $style['color'] = '#059669';
                    $style['dash'] = 'dashed';
                    break;
                case 'related':
                    $style['color'] = '#7c3aed';
                    break;
                case 'external':
                    $style['color'] = '#dc2626';
                    $style['dash'] = 'dotted';
                    break;
            }
        }
        
        return $style;
    }
    
    /**
     * Calculate canvas utilization percentage
     * 
     * @param array $positions
     * @param array $canvas_config
     * @return float Utilization percentage
     */
    private function calculate_canvas_utilization($positions, $canvas_config) {
        if (empty($positions)) return 0.0;
        
        $min_x = min(array_column($positions, 'x'));
        $max_x = max(array_column($positions, 'x'));
        $min_y = min(array_column($positions, 'y'));
        $max_y = max(array_column($positions, 'y'));
        
        $used_width = $max_x - $min_x;
        $used_height = $max_y - $min_y;
        
        $total_area = $canvas_config['width'] * $canvas_config['height'];
        $used_area = $used_width * $used_height;
        
        return min(100, ($used_area / $total_area) * 100);
    }
    
    /**
     * Calculate layout efficiency score
     * 
     * @param array $positions
     * @param array $link_paths
     * @return float Efficiency score (0-100)
     */
    private function calculate_layout_efficiency($positions, $link_paths) {
        if (empty($positions) || empty($link_paths)) return 50.0;
        
        $total_efficiency = 0.0;
        $link_count = 0;
        
        // Measure average link distance (shorter = more efficient)
        foreach ($link_paths as $link_path) {
            if (!isset($positions[$link_path['from']]) || !isset($positions[$link_path['to']])) {
                continue;
            }
            
            $from_pos = $positions[$link_path['from']];
            $to_pos = $positions[$link_path['to']];
            
            $distance = sqrt(
                pow($to_pos['x'] - $from_pos['x'], 2) + 
                pow($to_pos['y'] - $from_pos['y'], 2)
            );
            
            // Shorter distances get higher efficiency scores
            $link_efficiency = max(10, 100 - ($distance / 10));
            $total_efficiency += $link_efficiency;
            $link_count++;
        }
        
        if ($link_count == 0) return 50.0;
        
        return $total_efficiency / $link_count;
    }
}