/**
 * SLMM Page Summarization - Frontend JavaScript
 * 
 * Handles button events, keyboard shortcuts, and summary popups
 * for the D3.js-integrated page summarization buttons
 * 
 * @package SLMM_SEO_Bundle
 * @version 1.0.0
 */

(function($) {
    'use strict';

    window.SLMMPageSummarization = {
        
        // Configuration
        config: {
            keyPressed: false,
            currentHoveredNode: null,
            popupTimeout: null,
            popupDelay: 500
        },

        // Initialize when DOM is ready
        init: function() {
            $(document).ready(() => {
                this.bindEvents();
            });
            
            // Only initialize when tree rendering is actually complete
            $(document).on('slmm_tree_rendering_complete', () => {
                // Tree rendering complete - checking existing summaries
                // Add delay to ensure D3.js elements are fully bound
                setTimeout(() => {
                    this.checkExistingSummaries();
                }, 500);
            });
            
            // Page Summarization system initialized
        },

        // Bind event handlers
        bindEvents: function() {
            // Keyboard shortcuts (still needed for global functionality)
            $(document).on('keydown', this.handleKeyDown.bind(this));
            $(document).on('keyup', this.handleKeyUp.bind(this));

            // Track hovered nodes for keyboard shortcuts (legacy support)
            $(document).on('mouseenter', '.slmm-tree-node', (e) => {
                this.config.currentHoveredNode = $(e.currentTarget);
            });

            $(document).on('mouseleave', '.slmm-tree-node', () => {
                this.config.currentHoveredNode = null;
            });
        },

        // D3.js-based click handler (called from interlinking-suite.php)
        triggerSummarizationD3: function(nodeData, buttonElement) {
            console.log('[Page Summarization] D3.js click handler triggered for:', nodeData.data.name);
            
            const url = this.getUrlFromNodeData(nodeData);
            const postId = nodeData.data.id;
            
            if (!url || !postId) {
                console.error('[Page Summarization] Missing URL or Post ID from node data');
                return;
            }

            this.updateButtonStateD3(buttonElement, 'generating');
            this.generateSummary(url, postId, buttonElement, nodeData);
        },

        // D3.js-based SHIFT+hover handler
        handleShiftHoverD3: function(nodeData, buttonElement) {
            console.log('[Page Summarization] SHIFT+hover handler for:', nodeData.data.name);
            
            const url = this.getUrlFromNodeData(nodeData);
            
            // Check cached summary first
            const cachedSummary = window.SLMMPageSummaries?.getSummary(url);
            if (cachedSummary) {
                // Ensure cached data has post_id
                if (!cachedSummary.post_id) {
                    cachedSummary.post_id = nodeData.data.id;
                }
                this.displayPopupD3(buttonElement, cachedSummary.summary, cachedSummary);
                return;
            }
            
            // Fetch from database
            this.fetchExistingSummaryD3(url, nodeData, buttonElement);
        },

        // D3.js-based normal hover handler
        handleHoverStartD3: function(nodeData, buttonElement) {
            // Clear any existing timeout
            if (this.config.popupTimeout) {
                clearTimeout(this.config.popupTimeout);
            }
            
            const url = this.getUrlFromNodeData(nodeData);
            
            // Set timeout to show existing summary if available
            this.config.popupTimeout = setTimeout(() => {
                this.fetchExistingSummaryD3(url, nodeData, buttonElement, false);
            }, this.config.popupDelay);
        },

        // D3.js-based hover end handler
        handleHoverEndD3: function() {
            // Clear popup timeout only - don't auto-hide popup
            if (this.config.popupTimeout) {
                clearTimeout(this.config.popupTimeout);
                this.config.popupTimeout = null;
            }
            
            // Popup stays open - only closes via close button
        },

        // Extract URL from D3.js node data
        getUrlFromNodeData: function(nodeData) {
            // Use permalink if available, otherwise construct from data
            if (nodeData.data.permalink) {
                return nodeData.data.permalink;
            }
            
            // Fallback URL construction (same logic as getNodeData)
            const protocol = window.location.protocol;
            const hostname = window.location.hostname;
            const port = window.location.port ? ':' + window.location.port : '';
            const slug = nodeData.data.post_name || nodeData.data.name.toLowerCase().replace(/[^a-z0-9]+/g, '-');
            
            return `${protocol}//${hostname}${port}/${slug}/`;
        },

        // Update button state for D3.js elements
        updateButtonStateD3: function(buttonElement, state) {
            const $button = $(buttonElement);
            
            // Remove all existing state classes
            $button.removeClass('has-summary generating error');
            
            // Apply state-specific styling with !important to override CSS
            switch(state) {
                case 'generating':
                    $button.addClass('generating');
                    $button.attr('fill', '#f59e0b'); // Orange
                    $button.attr('stroke', '#d97706');
                    $button.attr('stroke-width', '2');
                    break;
                case 'completed':
                    $button.addClass('has-summary');
                    $button.attr('fill', '#10b981'); // Green for has-summary
                    $button.attr('stroke', '#059669'); // Darker green border
                    $button.attr('stroke-width', '2');
                    // Force visual update
                    $button.css('fill', '#10b981');
                    $button.css('stroke', '#059669');
                    break;
                case 'error':
                    $button.addClass('error');
                    $button.attr('fill', '#ef4444'); // Red
                    $button.attr('stroke', '#dc2626');
                    $button.attr('stroke-width', '2');
                    break;
                default: // 'default'
                    $button.attr('fill', '#6b7280'); // Gray
                    $button.attr('stroke', '#4b5563');
                    $button.attr('stroke-width', '1');
            }
            
            console.log('[Page Summarization] Button state updated to:', state, 'for element:', buttonElement);
        },

        // Fetch existing summary for D3.js hover
        fetchExistingSummaryD3: function(url, nodeData, buttonElement, showIfEmpty = true) {
            $.ajax({
                url: slmmPageSummarizationData.ajax_url,
                type: 'POST',
                data: {
                    action: 'slmm_check_summary',
                    url: url,
                    post_id: nodeData.data.id,
                    nonce: slmmPageSummarizationData.nonce
                },
                success: (response) => {
                    if (response.success && response.data.exists) {
                        // Update button state and show popup
                        this.updateButtonStateD3(buttonElement, 'completed');
                        
                        if (showIfEmpty || response.data.summary) {
                            const summaryData = {
                                title: nodeData.data.name,
                                url: url,
                                summary: response.data.summary,
                                post_id: nodeData.data.id  // CRITICAL: Include post_id for delete functionality
                            };
                            this.displayPopupD3(buttonElement, response.data.summary, summaryData);
                        }
                        
                        // Store in global cache with post_id
                        this.storeSummaryGlobally(url, nodeData.data.name, response.data.summary, nodeData.data.id);
                    } else if (showIfEmpty) {
                        this.updateButtonStateD3(buttonElement, 'default');
                    }
                },
                error: () => {
                    console.error('[Page Summarization] Error fetching existing summary');
                    if (showIfEmpty) {
                        this.updateButtonStateD3(buttonElement, 'default');
                    }
                }
            });
        },

        // Display popup for D3.js elements
        displayPopupD3: function(buttonElement, summaryText, summaryData) {
            const $button = $(buttonElement);
            const buttonRect = buttonElement.getBoundingClientRect();
            
            this.displayPopup($button, summaryText, summaryData, buttonRect);
        },

        // Generate summary for a page (D3.js only)
        generateSummary: function(url, postId, buttonElement, nodeData) {
            // Ensure we have a D3 element
            const d3Button = buttonElement.jquery ? buttonElement[0] : buttonElement;
            this.updateButtonStateD3(d3Button, 'generating');
            
            $.ajax({
                url: slmmPageSummarizationData.ajax_url,
                type: 'POST',
                data: {
                    action: 'slmm_summarize_page',
                    url: url,
                    post_id: postId,
                    nonce: slmmPageSummarizationData.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.updateButtonStateD3(d3Button, 'completed');
                        
                        // Use node_data from response if available, otherwise create it
                        const nodeUpdate = response.data?.node_data || { has_summary: true };
                        
                        // Apply surgical update using refreshNodeWithD3Rebind if available
                        if (typeof window.refreshNodeWithD3Rebind === 'function' && response.data?.node_data) {
                            window.refreshNodeWithD3Rebind(postId, nodeUpdate);
                            console.log('[Page Summarization] Applied surgical update via refreshNodeWithD3Rebind for post:', postId);
                        } else {
                            // Fallback to manual update
                            this.addSurgicalUpdate(postId, nodeUpdate);
                        }
                        
                        this.showNotification('Summary generated successfully!', 'success');
                        
                        // Store the summary globally for quick access
                        const pageTitle = nodeData?.data?.name || nodeData?.name || 'Unknown Page';
                        const nodePostId = nodeData?.data?.id || postId;
                        this.storeSummaryGlobally(url, pageTitle, response.data.summary, nodePostId);
                        
                        console.log('[Page Summarization] Summary generated successfully for:', url);
                    } else {
                        this.updateButtonStateD3(d3Button, 'error');
                        this.showNotification('Error generating summary: ' + (response.data || 'Unknown error'), 'error');
                        console.error('[Page Summarization] Error generating summary:', response.data);
                    }
                },
                error: (xhr, status, error) => {
                    this.updateButtonStateD3(d3Button, 'error');
                    this.showNotification('Network error generating summary', 'error');
                    console.error('[Page Summarization] AJAX error generating summary:', error);
                }
            });
        },

        // Add surgical update for persistent button states 
        addSurgicalUpdate: function(postId, updates) {
            if (!window.surgicallyUpdatedNodes) {
                window.surgicallyUpdatedNodes = new Set();
            }
            
            // Mark node as surgically updated to prevent backend override
            window.surgicallyUpdatedNodes.add(String(postId));
            
            // Find the node data and update it
            if (window.treeGroup) {
                window.treeGroup.selectAll('.slmm-tree-node').each(function(d) {
                    if (d && d.data && String(d.data.id) === String(postId)) {
                        // Update node data with has_summary field
                        if (updates.has_summary !== undefined) {
                            d.data.has_summary = updates.has_summary;
                        }
                        
                        // Use surgical refresh to persist the change
                        if (typeof window.refreshNodeWithD3Rebind === 'function') {
                            window.refreshNodeWithD3Rebind(postId, d.data);
                            console.log('[Page Summarization] Applied surgical update via refreshNodeWithD3Rebind for post:', postId);
                        }
                    }
                });
            }
            
            console.log('[Page Summarization] Added surgical update for post:', postId, updates);
        },

        // Check existing summaries and update button states (D3.js only)
        checkExistingSummaries: function() {
            // Only proceed if D3 tree is actually ready
            if (!window.treeGroup || typeof d3 === 'undefined') {
                return; // Silently skip if not ready
            }
            
            console.log('[Page Summarization] Checking existing summaries from node data...');
            
            // DEBUG: First check what the tree data structure looks like
            let sampleNode = null;
            window.treeGroup.selectAll('.slmm-tree-node').each(function(d) {
                if (!sampleNode && d && d.data) {
                    sampleNode = d.data;
                    console.log('[Page Summarization] DEBUG - Sample node data structure:', {
                        id: d.data.id,
                        name: d.data.name,
                        has_summary: d.data.has_summary,
                        has_summary_exists: 'has_summary' in d.data,
                        has_summary_type: typeof d.data.has_summary,
                        all_keys: Object.keys(d.data)
                    });
                }
            });
            
            let buttonCount = 0;
            let summaryCount = 0;
            
            window.treeGroup.selectAll('.slmm-node-summary-button').each(function(nodeData) {
                buttonCount++;
                const buttonElement = this;
                
                // DEBUG: Log each node's has_summary value
                if (nodeData && nodeData.data) {
                    console.log(`[Page Summarization] Node ${nodeData.data.id} (${nodeData.data.name}): has_summary=${nodeData.data.has_summary}`);
                }
                
                // Check if node has has_summary field from backend
                if (nodeData && nodeData.data && nodeData.data.has_summary === true) {
                    // Update button state based on backend data
                    window.SLMMPageSummarization.updateButtonStateD3(buttonElement, 'completed');
                    summaryCount++;
                    console.log(`[Page Summarization] ✅ Node ${nodeData.data.id} has summary (from backend)`);
                } else {
                    // Set default state for nodes without summaries
                    window.SLMMPageSummarization.updateButtonStateD3(buttonElement, 'default');
                }
            });
            
            console.log(`[Page Summarization] Checked ${buttonCount} buttons, found ${summaryCount} with existing summaries`);
        },


        // Handle keyboard down events - USES MAIN TREE HOVER TRACKING (same as semantic linking)
        handleKeyDown: function(event) {
            // 't' or 'T' key for page summarization - USES MAIN TREE HOVER TRACKING
            if (event.key === 't' || event.key === 'T') {
                // Only trigger if not typing in an input field and hovering over a node
                if (!$(event.target).is('input, textarea, [contenteditable]') && window.currentHoveredNode) {
                    event.preventDefault();
                    
                    const nodeData = window.currentHoveredNode;
                    if (nodeData && nodeData.data.id && nodeData.data.post_type !== 'site') {
                        console.log('[Page Summarization] Keyboard shortcut triggered for node:', nodeData.data.name);
                        
                        // Get the summary button from the hovered element
                        const buttonElement = $(window.hoveredNodeElement).find('.slmm-node-summary-button')[0];
                        const url = this.getUrlFromNodeData(nodeData);
                        const postId = nodeData.data.id;
                        
                        if (url && postId && buttonElement) {
                            this.generateSummary(url, postId, buttonElement, nodeData);
                        }
                    }
                }
            }
        },

        // Handle keyboard up events
        handleKeyUp: function(event) {
            if (event.key === 't' || event.key === 'T') {
                this.config.keyPressed = false;
            }
        },






        // Store summary in global cache for access by other components
        storeSummaryGlobally: function(url, title, summary, postId) {
            // Initialize global cache if not exists
            if (typeof window.SLMMPageSummaries === 'undefined') {
                window.SLMMPageSummaries = {
                    cache: {},
                    getSummary: function(url) {
                        return this.cache[url] || null;
                    },
                    setSummary: function(url, data) {
                        this.cache[url] = data;
                    },
                    getAllSummaries: function() {
                        return this.cache;
                    }
                };
            }
            
            // Store summary data with post_id
            window.SLMMPageSummaries.setSummary(url, {
                title: title,
                url: url,
                summary: summary,
                post_id: postId,  // Store post_id for delete operations
                timestamp: new Date().toISOString()
            });
            
            console.log('[Page Summarization] Stored summary globally for:', url, 'with post_id:', postId);
        },

        // Clear cached summary for a URL
        clearCachedSummary: function(url) {
            if (window.SLMMPageSummaries && window.SLMMPageSummaries.cache) {
                delete window.SLMMPageSummaries.cache[url];
                console.log('[Page Summarization] Cleared cached summary for:', url);
            }
        },

        // Handle rescan (regenerate) summary request
        handleRescanSummary: function(buttonElement, summaryData) {
            if (!confirm('Regenerate summary for this page?')) {
                return;
            }
            
            this.hideSummaryPopup();
            
            // Get the actual D3 element if jQuery wrapped
            const d3Button = buttonElement.jquery ? buttonElement[0] : buttonElement;
            
            // Update button state to show it's generating
            this.updateButtonStateD3(d3Button, 'generating');
            
            // Make AJAX request with force_regenerate flag
            $.ajax({
                url: slmmPageSummarizationData.ajax_url,
                type: 'POST',
                data: {
                    action: 'slmm_summarize_page',
                    url: summaryData.url,
                    post_id: summaryData.post_id || 0,
                    force_regenerate: 'true',
                    nonce: slmmPageSummarizationData.nonce
                },
                success: (response) => {
                    if (response.success) {
                        console.log('[Page Summarization] Summary regenerated successfully');
                        
                        // Update button state
                        this.updateButtonStateD3(d3Button, 'completed');
                        
                        // Add surgical update for persistent state
                        if (summaryData.post_id) {
                            this.addSurgicalUpdate(summaryData.post_id, { has_summary: true });
                        }
                        
                        // Update global cache
                        this.storeSummaryGlobally(
                            summaryData.url, 
                            summaryData.title || 'Unknown Page', 
                            response.data.summary,
                            summaryData.post_id
                        );
                        
                        this.showNotification('Summary regenerated successfully!', 'success');
                    } else {
                        console.error('[Page Summarization] Regeneration failed:', response.data);
                        this.updateButtonStateD3(d3Button, 'error');
                        this.showNotification('Failed to regenerate summary: ' + (response.data || 'Unknown error'), 'error');
                    }
                },
                error: (xhr, status, error) => {
                    console.error('[Page Summarization] Regeneration AJAX error:', error);
                    this.updateButtonStateD3(d3Button, 'error');
                    this.showNotification('Error regenerating summary: ' + error, 'error');
                }
            });
        },

        // Display popup for summary (unified method)
        displayPopup: function($button, summaryText, summaryData, buttonRect) {
            // Remove any existing popup first
            this.hideSummaryPopup();
            
            // Create popup HTML with dark theme styling
            const popupHtml = `
                <div class="slmm-summary-popup" style="display: none;">
                    <div class="slmm-summary-popup-header">
                        <span class="slmm-summary-popup-title">${summaryData.title || 'Page Summary'}</span>
                        <button class="slmm-summary-popup-close" aria-label="Close">×</button>
                    </div>
                    <div class="slmm-summary-popup-content">
                        ${summaryText || 'No summary available'}
                    </div>
                    <div class="slmm-summary-popup-footer">
                        <button class="slmm-summary-popup-regenerate" style="background-color: #3b82f6; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;">Regenerate</button>
                        <button class="slmm-summary-popup-delete" style="background-color: #dc2626; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-right: 8px;">Delete</button>
                        <button class="slmm-summary-popup-copy" style="background-color: #6b7280; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">Copy</button>
                    </div>
                    <div class="slmm-summary-popup-meta">
                        <div style="margin-bottom: 4px;">URL: <a href="${summaryData.url}" target="_blank" style="color: #3b82f6; text-decoration: none;">${summaryData.url}</a></div>
                        Generated: ${new Date().toLocaleString()}
                    </div>
                </div>
            `;
            
            // Append to body
            const $popup = $(popupHtml).appendTo('body');
            
            // Store data on popup for later use
            $popup.data('summaryData', summaryData);
            $popup.data('$button', $button);
            
            // Position popup near button
            const rect = buttonRect || ($button[0] ? $button[0].getBoundingClientRect() : { top: 100, left: 100 });
            const popupWidth = 480;
            const maxPopupHeight = Math.min(window.innerHeight * 0.8, 600);
            
            // Calculate position (prefer right side, fallback to left)
            let left = rect.right + 10;
            let top = rect.top;
            
            // Check if popup would go off screen
            if (left + popupWidth > window.innerWidth) {
                left = rect.left - popupWidth - 10;
            }
            
            // Ensure popup stays on screen vertically
            if (top + maxPopupHeight > window.innerHeight) {
                top = window.innerHeight - maxPopupHeight - 10;
            }
            
            // Apply positioning and dark theme styling
            $popup.css({
                position: 'fixed',
                left: left + 'px',
                top: top + 'px',
                zIndex: 100002,
                width: popupWidth + 'px',
                maxHeight: maxPopupHeight + 'px',
                minHeight: '250px',
                backgroundColor: '#1a1a1a',
                border: '1px solid #374151',
                borderRadius: '8px',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.5)',
                color: '#d1d5db',
                fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
                overflow: 'hidden',
                display: 'flex',
                flexDirection: 'column'
            });
            
            // Style header
            $popup.find('.slmm-summary-popup-header').css({
                padding: '12px 16px',
                borderBottom: '1px solid #374151',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
            });
            
            // Style title
            $popup.find('.slmm-summary-popup-title').css({
                fontSize: '16px',
                fontWeight: '600',
                color: '#f3f4f6'
            });
            
            // Style close button
            $popup.find('.slmm-summary-popup-close').css({
                background: 'transparent',
                border: 'none',
                color: '#9ca3af',
                fontSize: '24px',
                cursor: 'pointer',
                padding: '0',
                width: '30px',
                height: '30px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            });
            
            // Style content
            $popup.find('.slmm-summary-popup-content').css({
                padding: '16px',
                overflowY: 'auto',
                fontSize: '14px',
                lineHeight: '1.6',
                color: '#d1d5db',
                backgroundColor: '#1a1a1a',
                flex: '1',
                minHeight: '120px'
            });
            
            // Style TL;DR label
            $popup.find('.slmm-summary-tldr').css({
                color: '#9ca3af',
                marginBottom: '8px',
                fontSize: '12px',
                fontWeight: '500'
            });
            
            // Style footer buttons
            $popup.find('.slmm-summary-popup-footer').css({
                padding: '12px 16px',
                borderTop: '1px solid #374151',
                display: 'flex',
                gap: '8px',
                backgroundColor: '#1a1a1a',
                flexShrink: '0'
            });
            
            $popup.find('.slmm-summary-popup-footer button').css({
                padding: '6px 12px',
                backgroundColor: '#374151',
                border: '1px solid #4b5563',
                borderRadius: '6px',
                color: '#e5e7eb',
                cursor: 'pointer',
                fontSize: '13px',
                fontWeight: '500',
                transition: 'all 0.2s'
            }).hover(
                function() { $(this).css({ backgroundColor: '#4b5563', borderColor: '#6b7280' }); },
                function() { $(this).css({ backgroundColor: '#374151', borderColor: '#4b5563' }); }
            );
            
            // Style meta info
            $popup.find('.slmm-summary-popup-meta').css({
                padding: '8px 16px',
                fontSize: '11px',
                color: '#6b7280',
                borderTop: '1px solid #374151',
                backgroundColor: '#1a1a1a',
                flexShrink: '0'
            });
            
            // Bind popup events
            this.bindPopupEvents($popup, summaryData);
            
            // Show with animation
            $popup.fadeIn(200);
            
            console.log('[Page Summarization] Popup displayed for:', summaryData.title);
        },
        
        // Hide/remove summary popup
        hideSummaryPopup: function() {
            $('.slmm-summary-popup').fadeOut(200, function() {
                $(this).remove();
            });
        },
        
        // Bind events to popup
        bindPopupEvents: function($popup, summaryData) {
            const self = this;
            
            // Close button
            $popup.find('.slmm-summary-popup-close').on('click', function(e) {
                e.preventDefault();
                self.hideSummaryPopup();
            });
            
            // Regenerate button
            $popup.find('.slmm-summary-popup-regenerate').on('click', function(e) {
                e.preventDefault();
                const $button = $popup.data('$button');
                self.handleRescanSummary($button, summaryData);
            });
            
            // Delete button
            $popup.find('.slmm-summary-popup-delete').on('click', function(e) {
                e.preventDefault();
                const $button = $popup.data('$button');
                
                // Pass the node element (parent of button) and URL
                const $node = $button.closest('.slmm-tree-node');
                self.handleDeleteSummary($node, summaryData.url);
            });
            
            // Copy button
            $popup.find('.slmm-summary-popup-copy').on('click', function(e) {
                e.preventDefault();
                self.copyToClipboard(summaryData.summary);
                self.showNotification('Summary copied to clipboard!', 'success');
            });
            
            // Click outside to close
            $(document).on('click.slmm-summary-popup', function(e) {
                if (!$(e.target).closest('.slmm-summary-popup').length && 
                    !$(e.target).closest('.slmm-node-summary-button').length) {
                    self.hideSummaryPopup();
                    $(document).off('click.slmm-summary-popup');
                }
            });
            
            // ESC key to close
            $(document).on('keydown.slmm-summary-popup', function(e) {
                if (e.keyCode === 27) {
                    self.hideSummaryPopup();
                    $(document).off('keydown.slmm-summary-popup');
                }
            });
        },
        
        // Show notification message
        showNotification: function(message, type) {
            // Remove any existing notification
            $('.slmm-notification').remove();
            
            // Create notification HTML
            const notificationHtml = `
                <div class="slmm-notification slmm-notification-${type}">
                    ${message}
                </div>
            `;
            
            // Append to body
            const $notification = $(notificationHtml).appendTo('body');
            
            // Position at top center
            $notification.css({
                position: 'fixed',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                zIndex: 100003,
                padding: '12px 24px',
                borderRadius: '4px',
                backgroundColor: type === 'success' ? '#10b981' : '#ef4444',
                color: 'white',
                fontSize: '14px',
                fontWeight: '500',
                boxShadow: '0 4px 6px rgba(0,0,0,0.1)'
            });
            
            // Auto-hide after 3 seconds
            setTimeout(function() {
                $notification.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 3000);
        },
        
        // Copy text to clipboard
        copyToClipboard: function(text) {
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text);
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.opacity = '0';
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                } catch (err) {
                    console.error('[Page Summarization] Could not copy text:', err);
                }
                document.body.removeChild(textArea);
            }
        },

        // Handle delete summary request
        handleDeleteSummary: function($node, pageUrl) {
            if (!confirm('Delete summary for this page? This action cannot be undone.')) {
                return;
            }
            
            this.hideSummaryPopup();
            
            // Extract URL and post ID from multiple sources
            let url = pageUrl;
            let postId = null;
            
            // Try to get data from node if available
            if ($node && $node.length) {
                // Extract data directly from DOM
                const nodeUrl = $node.data('url') || $node.attr('data-url');
                const nodePostId = $node.data('post-id') || $node.attr('data-post-id');
                
                if (nodeUrl && nodeUrl !== 'Unknown URL') {
                    url = nodeUrl;
                }
                if (nodePostId) {
                    postId = nodePostId;
                }
            }
            
            // Get post ID from URL if not found
            if (!postId && url) {
                // Try to extract from global cache or popup data
                const cachedSummary = window.SLMMPageSummaries?.getSummary(url);
                if (cachedSummary && cachedSummary.post_id) {
                    postId = cachedSummary.post_id;
                }
            }
            
            console.log('[Page Summarization] Delete attempt - URL:', url, 'Post ID:', postId);
            
            if (!url || !postId) {
                console.error('[Page Summarization] Cannot delete - missing URL or post ID');
                return;
            }
            
            const $button = $node ? $node.find('.slmm-node-summary-button') : null;
            
            // Make AJAX request to delete summary
            $.ajax({
                url: slmmPageSummarizationData.ajax_url,
                type: 'POST',
                data: {
                    action: 'slmm_delete_summary',
                    url: url,
                    post_id: postId,
                    nonce: slmmPageSummarizationData.nonce
                },
                success: (response) => {
                    console.log('[Page Summarization] Delete AJAX response:', response);
                    if (response.success) {
                        console.log('[Page Summarization] Summary deleted successfully for URL:', url);
                        if ($button) {
                            // Convert jQuery button to D3 element if needed
                            const d3Button = $button.jquery ? $button[0] : $button;
                            this.updateButtonStateD3(d3Button, 'default');
                        }
                        
                        // Use node_data from response if available for surgical update
                        if (postId) {
                            const nodeUpdate = response.data?.node_data || { has_summary: false };
                            
                            // Apply surgical update using refreshNodeWithD3Rebind if available
                            if (typeof window.refreshNodeWithD3Rebind === 'function' && response.data?.node_data) {
                                window.refreshNodeWithD3Rebind(postId, nodeUpdate);
                                console.log('[Page Summarization] Applied surgical delete update via refreshNodeWithD3Rebind for post:', postId);
                            } else {
                                // Fallback to manual update
                                this.addSurgicalUpdate(postId, nodeUpdate);
                            }
                        }
                        
                        // Remove from global cache
                        if (window.SLMMPageSummaries && window.SLMMPageSummaries.cache) {
                            delete window.SLMMPageSummaries.cache[url];
                        }
                        // Also remove any cached entries by URL
                        this.clearCachedSummary(url);
                    } else {
                        console.error('[Page Summarization] Delete failed:', response.data);
                    }
                },
                error: (xhr, status, error) => {
                    console.error('[Page Summarization] Delete AJAX error:', error);
                }
            });
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        if (typeof slmmPageSummarizationData !== 'undefined') {
            SLMMPageSummarization.init();
        }
    });

})(jQuery);