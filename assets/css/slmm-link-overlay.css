/**
 * SLMM Link Overlay Styles
 * CSS for the link visualization system
 * 
 * @version 1.0.0
 * <AUTHOR> SEO Bundle
 */

/* CSS Variables for Link Overlay */
:root {
    --slmm-internal-link-color: #3b82f6;      /* Blue for internal links */
    --slmm-external-link-color: #f59e0b;      /* Orange for external links */
    --slmm-link-hover-opacity: 0.8;
    --slmm-link-default-opacity: 0.6;
    --slmm-link-stroke-width: 1.5px;
    --slmm-link-stroke-width-hover: 2.5px;
    --slmm-primary: #3b82f6;                   /* Primary blue */
    --slmm-warning: #f59e0b;                   /* Warning orange */
    --slmm-dark-surface: #1e293b;             /* Dark surface */
    --slmm-dark-border: #334155;              /* Dark border */
    --slmm-dark-hover: #475569;               /* Dark hover */
    --slmm-text-primary: #f1f5f9;             /* Light text */
}

/* Link Overlay Container */
.slmm-link-overlay-group {
    pointer-events: none; /* Allow tree interactions underneath */
}

.slmm-link-overlay-group .slmm-link-overlay {
    pointer-events: visibleStroke; /* Allow hover on actual link paths */
    cursor: pointer;
}

/* Internal Link Styles */
.slmm-internal-link {
    fill: none;
    stroke: var(--slmm-internal-link-color);
    stroke-width: var(--slmm-link-stroke-width);
    opacity: var(--slmm-link-default-opacity);
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: all 0.2s ease;
}

.slmm-internal-link:hover {
    opacity: var(--slmm-link-hover-opacity);
    stroke-width: var(--slmm-link-stroke-width-hover);
    filter: drop-shadow(0 0 3px var(--slmm-internal-link-color));
}

/* External Link Styles */
.slmm-external-link {
    fill: none;
    stroke: var(--slmm-external-link-color);
    stroke-width: var(--slmm-link-stroke-width);
    stroke-dasharray: 4,2;
    opacity: var(--slmm-link-default-opacity);
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: all 0.2s ease;
}

.slmm-external-link:hover {
    opacity: var(--slmm-link-hover-opacity);
    stroke-width: var(--slmm-link-stroke-width-hover);
    filter: drop-shadow(0 0 3px var(--slmm-external-link-color));
}

/* Link Toggle Switch Container - In Controls Area */
.slmm-link-toggle-container {
    position: relative;
    display: flex;
    align-items: center;
    margin-left: 0.75rem; /* Space from previous button */
    gap: 0.5rem;
}

/* Professional Toggle Switch */
.slmm-toggle-switch {
    position: relative;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    user-select: none;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;
    height: 40px; /* Match button height for consistency */
    box-sizing: border-box;
}

.slmm-toggle-switch:hover {
    background: var(--slmm-dark-surface-hover);
}

/* Hide the default checkbox */
.slmm-toggle-input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

/* Toggle Slider */
.slmm-toggle-slider {
    position: relative;
    width: 44px;
    height: 24px;
    background: var(--slmm-dark-border);
    border-radius: 12px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 2px;
}

.slmm-toggle-input:checked + .slmm-toggle-slider {
    background: var(--slmm-primary);
}

/* Toggle Icon Container */
.slmm-toggle-icon {
    position: absolute;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    left: 2px;
    top: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slmm-toggle-input:checked + .slmm-toggle-slider .slmm-toggle-icon {
    transform: translateX(20px);
}

.slmm-toggle-icon .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    color: var(--slmm-dark-surface);
}

.slmm-toggle-input:checked + .slmm-toggle-slider .slmm-toggle-icon .dashicons {
    color: var(--slmm-primary);
}

/* Toggle Label */
.slmm-toggle-label {
    color: var(--slmm-text-secondary);
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.slmm-toggle-input:checked + .slmm-toggle-slider + .slmm-toggle-label {
    color: var(--slmm-text-primary);
}

/* Loading State */
.slmm-toggle-switch.loading {
    opacity: 0.7;
    pointer-events: none;
}

.slmm-toggle-switch.loading .slmm-toggle-icon::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    border: 1px solid var(--slmm-primary);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Link Controls Panel (Collapsible) */
.slmm-link-controls-panel {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 180px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
}

.slmm-link-controls-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
    display: block !important;
}

/* Link Control Groups */
.slmm-link-control-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.slmm-link-control-group:last-child {
    margin-bottom: 0;
}

.slmm-link-control-group label {
    color: var(--slmm-text-primary);
    font-size: 12px;
    font-weight: 500;
}

/* Color Inputs */
.slmm-color-input {
    width: 30px;
    height: 20px;
    border: 1px solid var(--slmm-dark-border);
    border-radius: 3px;
    cursor: pointer;
    background: none;
    padding: 0;
}

.slmm-color-input::-webkit-color-swatch-wrapper {
    padding: 0;
}

.slmm-color-input::-webkit-color-swatch {
    border: none;
    border-radius: 2px;
}

/* Checkbox in controls */
.slmm-link-control-group input[type="checkbox"] {
    margin-right: 6px;
    transform: scale(0.9);
}

/* Legacy Link Controls Container (for compatibility) */
.slmm-link-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
}

.slmm-link-controls .button {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    min-height: 32px;
    padding: 0 12px;
}

.slmm-link-controls .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Color Control Interface (for future enhancement) */
.slmm-link-color-controls {
    display: none; /* Hidden initially */
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    min-width: 200px;
}

.slmm-link-color-controls.visible {
    display: block;
}

.slmm-color-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.slmm-color-control label {
    flex: 1;
    color: var(--slmm-text-primary);
    font-size: 12px;
}

.slmm-color-picker {
    width: 30px;
    height: 20px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

/* Link Legend */
.slmm-link-legend {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 11px;
    display: none;
    z-index: 100;
}

.slmm-link-legend.visible {
    display: block;
}

.slmm-legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
}

.slmm-legend-item:last-child {
    margin-bottom: 0;
}

.slmm-legend-line {
    width: 20px;
    height: 2px;
    display: inline-block;
}

.slmm-legend-line.internal {
    background: var(--slmm-internal-link-color);
}

.slmm-legend-line.external {
    background: var(--slmm-external-link-color);
    background-image: linear-gradient(to right, var(--slmm-external-link-color) 60%, transparent 60%);
    background-size: 6px 2px;
}

/* Simple Tooltip for Links */
.slmm-link-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    pointer-events: none;
    z-index: 10000;
    max-width: 250px;
    line-height: 1.3;
}

.slmm-link-tooltip strong {
    display: block;
    margin-bottom: 4px;
    color: #fff;
}

/* Animation Keyframes */
@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes linkFadeIn {
    from {
        opacity: 0;
        stroke-width: 0;
    }
    to {
        opacity: var(--slmm-link-default-opacity);
        stroke-width: var(--slmm-link-stroke-width);
    }
}

/* Link Animation */
.slmm-link-overlay {
    animation: linkFadeIn 0.3s ease-out;
}

/* Dark Theme Adjustments */
.slmm-interlinking-theme .slmm-link-controls .button-primary {
    background: var(--slmm-primary) !important;
    border-color: var(--slmm-primary) !important;
}

.slmm-interlinking-theme .slmm-link-controls .button-secondary {
    background: var(--slmm-dark-surface) !important;
    border-color: var(--slmm-dark-border) !important;
    color: var(--slmm-text-primary) !important;
}

.slmm-interlinking-theme .slmm-link-controls .button-secondary:hover {
    background: var(--slmm-dark-hover) !important;
}

/* Performance Optimizations */
.slmm-link-overlay-group {
    will-change: transform;
}

.slmm-link-overlay {
    vector-effect: non-scaling-stroke;
    shape-rendering: optimizeSpeed;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .slmm-link-controls {
        margin-right: 0.5rem;
    }
    
    .slmm-link-controls .button {
        min-height: 28px;
        padding: 0 8px;
    }
    
    .slmm-link-tooltip {
        max-width: 200px;
        font-size: 11px;
    }
}

/* Link Thickness Control Slider */
.slmm-thickness-control-container {
    display: flex;
    align-items: center;
    margin-left: 1rem; /* Space from link toggle */
    gap: 0.5rem;
    height: 40px; /* Match other controls */
    padding: 0 0.5rem;
}

.slmm-thickness-label {
    color: var(--slmm-text-secondary);
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.slmm-thickness-slider-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Professional Range Slider Styling */
.slmm-thickness-slider {
    width: 80px;
    height: 4px;
    background: var(--slmm-dark-border);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    appearance: none;
    -webkit-appearance: none;
}

.slmm-thickness-slider::-webkit-slider-thumb {
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--slmm-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slmm-thickness-slider::-webkit-slider-thumb:hover {
    background: var(--slmm-primary-hover);
    transform: scale(1.1);
}

.slmm-thickness-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--slmm-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slmm-thickness-slider::-moz-range-thumb:hover {
    background: var(--slmm-primary-hover);
    transform: scale(1.1);
}

.slmm-thickness-slider::-moz-range-track {
    height: 4px;
    background: var(--slmm-dark-border);
    border-radius: 2px;
    border: none;
}

/* Thickness Value Display */
.slmm-thickness-value {
    color: var(--slmm-text-primary);
    font-size: 11px;
    font-weight: 500;
    min-width: 24px;
    text-align: center;
    background: var(--slmm-dark-surface);
    border: 1px solid var(--slmm-dark-border);
    border-radius: 3px;
    padding: 2px 4px;
}

/* Responsive Adjustments for Thickness Slider */
@media (max-width: 768px) {
    .slmm-thickness-control-container {
        margin-left: 0.5rem;
        gap: 0.25rem;
    }
    
    .slmm-thickness-slider {
        width: 60px;
    }
    
    .slmm-thickness-label {
        font-size: 11px;
    }
}