# Essential Development Patterns

## Plugin Architecture (v4.10.0)
- **Entry Point**: `plugin.php` → loads `slmm-seo-plugin.php`
- **Initialization**: `plugins_loaded` hook with visibility check first
- **Settings Structure**: Modular settings in `includes/settings/`
- **AI Integrations**: Provider-specific classes in `includes/ai-integration/`
- **Utilities**: Feature-specific utilities in `includes/utils/`
- **Frontend Assets**: Version-controlled loading in `assets/`

### Core Feature Architecture
```php
// Standard feature initialization pattern
function slmm_seo_plugin_init() {
    // Visibility check FIRST
    if (!slmm_seo_check_visibility_authorization()) {
        return;
    }

    // Initialize settings
    $general_settings = new SLMM_General_Settings();
    $general_settings->init();

    // Initialize feature classes
    SLMM_Protected_Words::get_instance();
    new SLMM_Lorem_Ipsum_Detector();
    (new SLMM_Prompt_Settings())->init();
}
```

## Multi-Instance Support Pattern
```php
// Static counter pattern for unique IDs (critical for multi-instance support)
static $instance_counter = 0;
$instance_counter++;
$unique_id = 'element-' . $instance_counter;
```

## AJAX Integration Pattern
```php
// Standard AJAX handler pattern
add_action('wp_ajax_slmm_action_name', 'slmm_handle_action');

function slmm_handle_action() {
    // Nonce verification
    if (!wp_verify_nonce($_POST['nonce'], 'slmm_action_nonce')) {
        wp_die('Security check failed');
    }

    // Capability check
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions');
    }

    // Input sanitization
    $input = sanitize_textarea_field($_POST['input']);

    // Process and return JSON
    wp_send_json_success($result);
}
```

## Asset Loading Pattern (v4.10.0 Updates)
```php
// Conditional asset loading with Bricks Builder detection
function slmm_enqueue_scripts() {
    $is_bricks = isset($_GET['bricks']) && $_GET['bricks'] === 'run';

    wp_enqueue_script(
        'slmm-script',
        SLMM_SEO_PLUGIN_URL . 'assets/js/script.js',
        array('jquery'),
        SLMM_SEO_VERSION,
        true
    );

    // Always localize data for dual-system support
    wp_localize_script('slmm-script', 'slmmGptPromptData', array(
        'prompts' => get_option('slmm_gpt_prompts', array()),
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
    ));
}
```

## Plugin.php Integration Protocols (MANDATORY FOR ALL NEW FILES)

### New File Registration Process (ABSOLUTE REQUIREMENT)
**EVERY new PHP file MUST be properly registered in plugin.php:**

1. **File Inclusion Order** - Add requires in logical dependency order
2. **Class Existence Check** - Always verify class exists before instantiation
3. **Proper Initialization** - Follow established initialization patterns
4. **Version Tracking** - Ensure new files inherit version constants

### File Registration Template (MANDATORY FORMAT)
```php
// In plugin.php - Add new file includes in dependency order
require_once __DIR__ . '/includes/utils/class-helper-functions.php';          // Utilities first
require_once __DIR__ . '/includes/settings/class-new-settings.php';          // Settings second
require_once __DIR__ . '/includes/features/class-new-feature.php';           // Features third
require_once __DIR__ . '/includes/ai-integration/class-new-provider.php';    // AI integrations last
```

### Initialization Pattern (ENFORCE STRICTLY)
```php
// In slmm-seo-plugin.php - Initialize new classes in plugin init function
function slmm_seo_plugin_init() {
    // Visibility check FIRST (never skip this)
    if (!slmm_seo_check_visibility_authorization()) {
        return;
    }

    // Initialize utilities (no dependencies)
    if (class_exists('SLMM_Helper_Functions')) {
        SLMM_Helper_Functions::get_instance();
    }

    // Initialize settings (may depend on utilities)
    if (class_exists('SLMM_New_Settings')) {
        (new SLMM_New_Settings())->init();
    }

    // Initialize features (may depend on settings)
    if (class_exists('SLMM_New_Feature')) {
        new SLMM_New_Feature();
    }

    // Initialize AI integrations (may depend on settings and features)
    if (class_exists('SLMM_New_Provider')) {
        SLMM_New_Provider::get_instance();
    }
}
```

### File Size Validation Integration
**Before adding ANY file to plugin.php:**
```bash
# Check file size before registration
wc -l includes/new-feature/class-new-feature.php

# If file exceeds 800 lines, MUST split before registration
if [[ $(wc -l < file.php) -gt 800 ]]; then
    echo "ERROR: File exceeds 800 lines - split required before plugin.php integration"
    exit 1
fi
```

### Directory Structure Compliance (MANDATORY)
**New files MUST follow established directory patterns:**
```php
// CORRECT - Follow established patterns
require_once __DIR__ . '/includes/settings/class-new-settings.php';
require_once __DIR__ . '/includes/features/search-replace/class-search-engine.php';
require_once __DIR__ . '/includes/features/search-replace/class-replace-engine.php';
require_once __DIR__ . '/includes/utils/class-file-size-monitor.php';

// WRONG - Random placement breaks organization
require_once __DIR__ . '/random-file.php';
require_once __DIR__ . '/includes/mixed-purposes.php';
```

### Dependency Management (CRITICAL)
**Ensure proper loading order to prevent fatal errors:**
```php
// Base utilities and interfaces first
require_once __DIR__ . '/includes/interfaces/interface-provider.php';
require_once __DIR__ . '/includes/utils/class-base-utility.php';

// Settings that may use utilities
require_once __DIR__ . '/includes/settings/class-provider-settings.php';

// Features that may use settings and utilities
require_once __DIR__ . '/includes/features/class-advanced-feature.php';

// Providers that implement interfaces
require_once __DIR__ . '/includes/ai-integration/class-concrete-provider.php';
```

### Integration Validation Checklist (MANDATORY VERIFICATION)
**Before committing any plugin.php changes:**
- [ ] File size under 800 lines verified
- [ ] Proper directory structure followed
- [ ] Dependency order respected
- [ ] Class existence checks implemented
- [ ] Initialization follows established patterns
- [ ] No fatal errors on plugin activation
- [ ] Dual-system compatibility maintained
- [ ] Authorization system integration preserved