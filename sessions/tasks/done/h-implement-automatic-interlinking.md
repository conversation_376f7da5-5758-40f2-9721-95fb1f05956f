---
task: h-implement-automatic-interlinking
branch: feature/automatic-interlinking
status: in-progress
created: 2025-01-16
modules: [interlinking, settings, classic-editor-integration]
---

# Automatic Interlinking System for CPT Siblings

## Problem/Goal
Create an automatic interlinking system for Custom Post Type siblings that inserts a configurable section at the bottom of posts, linking to related content in a closed loop to ensure complete interconnection.

## Success Criteria
- [ ] Settings box in existing settings page (`#interlinking-suite`) with matching dark UI styling
- [ ] "Interlinking Section text" input field for custom text (e.g., "Check out our other articles")
- [ ] Heading size selector (H3, H4, H5) that renders properly in Classic Editor
- [ ] Article count selector (1-10 articles to display)
- [ ] Closed-loop linking logic ensuring NEXT article in sequence is always linked
- [ ] Last article links back to FIRST article (complete loop coverage)
- [ ] Auto-insertion at bottom of posts for sibling CPT content
- [ ] Integration with existing Classic Editor workflow
- [ ] CPT sibling detection and filtering system

## Detailed Requirements
### Settings Interface
- Location: http://localhost:8884/wp-admin/admin.php?page=chatgpt-generator-settings#interlinking-suite
- Dark UI styling matching existing app design
- "Interlinking Section text" input field
- Heading size dropdown (H3, H4, H5)
- Article count selector (1-10)

### Linking Logic
- Always link to NEXT article in CPT sibling sequence
- Last article must link back to FIRST article (closed loop)
- Insert at very bottom of post content
- Handle cases with many posts intelligently

### Technical Integration
- Classic Editor compatibility required
- CPT sibling detection system
- Auto-insertion mechanism at post bottom

## Context Files
<!-- Added by context-gathering agent or manually -->

## User Notes
- High priority implementation
- Must integrate with existing settings page structure
- UI must match current dark theme styling
- Closed loop linking is critical for SEO coverage

## Work Log
<!-- Updated as work progresses -->
- [2025-01-16] Created high-priority task for automatic interlinking system