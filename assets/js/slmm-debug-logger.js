/**
 * SLMM Debug Logger
 * Centralized logging utility to control console output
 * 
 * Usage:
 * - SLMM.debug.log('Category', 'Message', data);
 * - SLMM.debug.warn('Category', 'Warning message');
 * - SLMM.debug.error('Category', 'Error message', error);
 */

(function(window) {
    'use strict';

    // Initialize SLMM namespace if it doesn't exist
    if (typeof window.SLMM === 'undefined') {
        window.SLMM = {};
    }

    // Debug configuration
    const debugConfig = {
        enabled: false, // Default to disabled
        categories: {
            'Direct Editor': true,
            'Dashboard': true,
            'ACF Title': true,
            'Regular Title': true,
            'Link Tracking': true,
            'Important Pages': true,
            'Silo Nav': true,
            'Copy Links': true,
            'Delete Semantic': true,
            'Importance': true,
            'Copy': true,
            'Popup Management': true,
            'Resize': true,
            'ACF Direct Editor': true,
            'Link Popup': true,
            'ACF Integration': true,
            'Initialization': true,
            'Tree Loading': true,
            'Mouseover': true,
            'Completion': true,
            'Surgical Update': true,
            'Link Overlay': true,
            'Notes': true,
            'Visual Highlighting': true,
            'Link Connections': true,
            'Fallback Matching': true,
            'Debug Analysis': true,
            'Lorem Detector': true,
            'Bricks Builder': true,
            'GPT Prompt': true,
            'Broken Link Detector': true,
            'SEO Overview': true,
            'SEO Tools': true,
            'Prompt Execution': true,
            'Notes Diagnostics': true,
            'QuickBulk D3': true,
            'URL Renderer': true
        },
        logLevels: {
            log: true,
            warn: true,
            error: true,
            info: true
        }
    };

    // Check for debug logging setting from plugin options (WordPress sends '1' as string)
    if (typeof slmmDirectEditorData !== 'undefined' && 
        (slmmDirectEditorData.debug_logging_enabled === true || 
         slmmDirectEditorData.debug_logging_enabled === '1' || 
         slmmDirectEditorData.debug_logging_enabled === 1)) {
        debugConfig.enabled = true;
        
        // Override categories with WordPress admin settings if available
        if (slmmDirectEditorData.debug_categories && typeof slmmDirectEditorData.debug_categories === 'object') {
            debugConfig.categories = slmmDirectEditorData.debug_categories;
        }
    }
    
    // Also check slmmLinkCheck data for broken link detector
    if (typeof slmmLinkCheck !== 'undefined' && 
        (slmmLinkCheck.debug_logging_enabled === true || 
         slmmLinkCheck.debug_logging_enabled === '1' || 
         slmmLinkCheck.debug_logging_enabled === 1)) {
        debugConfig.enabled = true;
        
        // Override categories with WordPress admin settings if available
        if (slmmLinkCheck.debug_categories && typeof slmmLinkCheck.debug_categories === 'object') {
            debugConfig.categories = slmmLinkCheck.debug_categories;
        }
    }
    
    // Check for global debug mode from WordPress (fallback) - ONLY if plugin debug is not explicitly disabled
    var hasDirectEditorDebugSetting = typeof slmmDirectEditorData !== 'undefined' && slmmDirectEditorData.hasOwnProperty('debug_logging_enabled');
    var hasLinkCheckDebugSetting = typeof slmmLinkCheck !== 'undefined' && slmmLinkCheck.hasOwnProperty('debug_logging_enabled');
    
    if (typeof slmmDirectEditorData !== 'undefined' && 
        slmmDirectEditorData.debug_mode && 
        !hasDirectEditorDebugSetting && !hasLinkCheckDebugSetting) {
        // Only use WP_DEBUG as fallback when plugin debug setting is not available anywhere
        debugConfig.enabled = true;
    }
    
    if (typeof slmmLinkCheck !== 'undefined' && 
        slmmLinkCheck.debug_mode && 
        !hasDirectEditorDebugSetting && !hasLinkCheckDebugSetting) {
        // Only use WP_DEBUG as fallback when plugin debug setting is not available anywhere
        debugConfig.enabled = true;
    }

    // Check for URL parameter override
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('slmm_debug') === 'true') {
        debugConfig.enabled = true;
    }

    // Console override for emergency debugging
    if (window.location.search.includes('slmm_debug=console')) {
        debugConfig.enabled = true;
        // Enable all categories for emergency debugging
        Object.keys(debugConfig.categories).forEach(cat => {
            debugConfig.categories[cat] = true;
        });
    }

    /**
     * Main debug logger class
     */
    class DebugLogger {
        constructor() {
            this.config = debugConfig;
        }

        /**
         * Check if logging is enabled for a category
         */
        isEnabled(category = 'General') {
            if (!this.config.enabled) return false;
            if (!this.config.categories.hasOwnProperty(category)) return true;
            return this.config.categories[category];
        }

        /**
         * Format log message with timestamp and category
         */
        formatMessage(category, message) {
            const timestamp = new Date().toTimeString().split(' ')[0];
            return `[${timestamp}] [SLMM ${category}] ${message}`;
        }

        /**
         * Main log method
         */
        log(category, message, data = null) {
            if (!this.isEnabled(category) || !this.config.logLevels.log) return;
            
            const formattedMessage = this.formatMessage(category, message);
            if (data !== null) {
                console.log(formattedMessage, data);
            } else {
                console.log(formattedMessage);
            }
        }

        /**
         * Warning log method
         */
        warn(category, message, data = null) {
            if (!this.isEnabled(category) || !this.config.logLevels.warn) return;
            
            const formattedMessage = this.formatMessage(category, message);
            if (data !== null) {
                console.warn(formattedMessage, data);
            } else {
                console.warn(formattedMessage);
            }
        }

        /**
         * Error log method
         */
        error(category, message, data = null) {
            if (!this.isEnabled(category) || !this.config.logLevels.error) return;
            
            const formattedMessage = this.formatMessage(category, message);
            if (data !== null) {
                console.error(formattedMessage, data);
            } else {
                console.error(formattedMessage);
            }
        }

        /**
         * Info log method
         */
        info(category, message, data = null) {
            if (!this.isEnabled(category) || !this.config.logLevels.info) return;
            
            const formattedMessage = this.formatMessage(category, message);
            if (data !== null) {
                console.info(formattedMessage, data);
            } else {
                console.info(formattedMessage);
            }
        }

        /**
         * Success log method (uses console.log with success prefix)
         */
        success(category, message, data = null) {
            if (!this.isEnabled(category) || !this.config.logLevels.log) return;
            
            const formattedMessage = this.formatMessage(category, `✅ ${message}`);
            if (data !== null) {
                console.log(formattedMessage, data);
            } else {
                console.log(formattedMessage);
            }
        }

        /**
         * Group log methods
         */
        group(category, title) {
            if (!this.isEnabled(category)) return;
            console.group(this.formatMessage(category, title));
        }

        groupEnd() {
            console.groupEnd();
        }

        /**
         * Configuration methods
         */
        enable() {
            this.config.enabled = true;
        }

        disable() {
            this.config.enabled = false;
        }

        enableCategory(category) {
            this.config.categories[category] = true;
        }

        disableCategory(category) {
            this.config.categories[category] = false;
        }

        /**
         * Get current configuration
         */
        getConfig() {
            return { ...this.config };
        }

        /**
         * Console debugging helper
         */
        showHelp() {
            console.log(`
SLMM Debug Logger Help:
======================
- SLMM.debug.enable() - Enable all logging
- SLMM.debug.disable() - Disable all logging
- SLMM.debug.enableCategory('Category Name') - Enable specific category
- SLMM.debug.disableCategory('Category Name') - Disable specific category
- SLMM.debug.getConfig() - Show current configuration
- URL parameter: ?slmm_debug=true - Enable debug mode
- URL parameter: ?slmm_debug=console - Enable emergency console debug mode

Available categories:
${Object.keys(this.config.categories).map(cat => `- ${cat}`).join('\n')}
            `);
        }
    }

    // Initialize the debug logger
    window.SLMM.debug = new DebugLogger();

    // Re-check debug settings after DOM is ready (in case wp_localize_script loads after this script)
    if (typeof jQuery !== 'undefined') {
        jQuery(document).ready(function() {
            // Double-check debug settings in case they weren't available during initial load
            if (!debugConfig.enabled && typeof slmmDirectEditorData !== 'undefined' && 
                (slmmDirectEditorData.debug_logging_enabled === true || 
                 slmmDirectEditorData.debug_logging_enabled === '1' || 
                 slmmDirectEditorData.debug_logging_enabled === 1)) {
                debugConfig.enabled = true;
                
                // Override categories with WordPress admin settings if available
                if (slmmDirectEditorData.debug_categories && typeof slmmDirectEditorData.debug_categories === 'object') {
                    debugConfig.categories = slmmDirectEditorData.debug_categories;
                }
                
                window.SLMM.debug.config = debugConfig;
                console.log('[SLMM Debug Logger] Debug logging enabled via WordPress settings. Type SLMM.debug.showHelp() for commands.');
            }
        });
    }

    // Console debugging helpers (only in debug mode)
    if (debugConfig.enabled) {
        console.log('[SLMM Debug Logger] Debug logging enabled. Type SLMM.debug.showHelp() for commands.');
    }

})(window);