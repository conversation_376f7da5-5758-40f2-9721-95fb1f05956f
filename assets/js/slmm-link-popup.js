/**
 * SLMM Link Popup JavaScript
 * 
 * Handles hover events on internal/external link indicators and displays
 * popups with link details including anchor text and URLs.
 * 
 * USAGE:
 * - Hover over I/O indicators: Shows popup with link details
 * - SHIFT + Hover over I indicators: Shows only visual highlighting (no popup)
 * 
 * @package SLMM_SEO_Bundle
 * @since 4.10.1
 */

(function($, d3) {
    'use strict';
    
    // Ensure dependencies are available
    if (typeof $ === 'undefined' || typeof d3 === 'undefined') {
        // Early error before debug system is available
        console.error('SLMM Link Popup: Missing dependencies (jQuery or D3.js)');
        return;
    }
    
    /**
     * SLMM Link Popup Manager Class
     */
    class SLMM_LinkPopup {
        
        constructor() {
            this.popup = null;
            this.currentPopup = null;
            this.hoverTimeout = null;
            this.hideTimeout = null;
            this.highlightTimeout = null; // Timeout for highlight clearing
            this.currentHighlighting = null; // Current highlighting state
            this.isVisualizationMode = false; // Track if in SHIFT+hover mode
            this.isInitialized = false;
            
            // Configuration
            this.config = {
                showDelay: 300,      // ms delay before showing popup
                hideDelay: 100,      // ms delay before hiding popup
                highlightClearDelay: 150, // ms delay before clearing highlighting
                maxRetries: 2        // max AJAX retries
            };
            
            // Initialize debug helpers
            this.debug = {
                log: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.log(category, message, data);
                    }
                },
                success: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.success(category, message, data);
                    }
                },
                warn: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.warn(category, message, data);
                    }
                },
                error: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.error(category, message, data);
                    }
                },
                info: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.info(category, message, data);
                    }
                }
            };

            this.init();
        }
        
        /**
         * Initialize the link popup system
         */
        init() {
            // Wait for DOM and D3 to be ready
            $(document).ready(() => {
                this.setupPopupContainer();
                this.bindGlobalEvents();
                this.isInitialized = true;
                this.debug.success('Link Popup', 'Initialized successfully');
            });
        }
        
        /**
         * Setup popup HTML container
         */
        setupPopupContainer() {
            // Remove any existing popup to avoid duplicates
            $('.slmm-link-popup').remove();
            
            // Create popup HTML structure
            const popupHTML = `
                <div class="slmm-link-popup" id="slmm-link-popup">
                    <div class="slmm-link-popup-header">
                        <div class="slmm-link-popup-title">
                            <div class="slmm-link-popup-icon">
                                <span class="slmm-link-popup-icon-text"></span>
                            </div>
                            <span class="slmm-link-popup-title-text">Links</span>
                        </div>
                        <button class="slmm-link-popup-close" title="Close">×</button>
                    </div>
                    <div class="slmm-link-popup-content">
                        <!-- Content will be dynamically inserted here -->
                    </div>
                    <div class="slmm-link-popup-actions">
                        <!-- Action buttons will be dynamically inserted here -->
                    </div>
                </div>
            `;
            
            // Add popup to body
            $('body').append(popupHTML);
            this.popup = $('#slmm-link-popup');
            
            // Bind popup events
            this.popup.find('.slmm-link-popup-close').on('click', () => {
                this.hidePopup();
            });
            
            // Prevent popup from hiding when hovering over it
            this.popup.on('mouseenter', () => {
                this.clearHideTimeout();
                this.clearHighlightTimeout(); // Don't clear highlighting when hovering over popup
            });
            
            this.popup.on('mouseleave', () => {
                this.scheduleHide();
                this.scheduleHighlightClear(); // Clear highlighting when leaving popup
            });
        }
        
        /**
         * Bind global events
         */
        bindGlobalEvents() {
            // Close popup when clicking outside
            $(document).on('click', (e) => {
                if (!$(e.target).closest('.slmm-link-popup, .slmm-node-link-indicator').length) {
                    this.hidePopup(); // This will also clear highlighting
                }
            });
            
            // Close popup on escape key
            $(document).on('keydown', (e) => {
                if (e.key === 'Escape' && (this.isVisible() || this.currentHighlighting)) {
                    this.hidePopup(); // This will also clear highlighting
                }
            });
        }
        
        /**
         * Attach hover handlers to D3.js link indicators
         * This is called from the interlinking suite after nodes are rendered
         * ENHANCED: Now handles re-attachments properly to fix intermittent behavior
         */
        attachToD3Indicators(nodeSelection) {
            if (!nodeSelection || !this.isInitialized) {
                this.debug.warn('Link Popup', 'Cannot attach to D3 indicators - not ready');
                return false;
            }
            
            try {
                // Find all internal and external link indicators
                const indicators = nodeSelection.selectAll('.slmm-node-link-indicator.internal, .slmm-node-link-indicator.external');
                
                if (indicators.empty()) {
                    this.debug.warn('Link Popup', 'No indicators found to attach to');
                    return false;
                }
                
                // CRITICAL FIX: Remove existing event handlers before adding new ones to prevent conflicts
                indicators
                    .on('mouseenter', null)
                    .on('mouseleave', null);
                
                // Attach fresh event handlers
                indicators
                    .on('mouseenter', (event, d) => {
                        this.handleIndicatorHover(event, d);
                    })
                    .on('mouseleave', (event, d) => {
                        this.handleIndicatorLeave(event, d);
                    });
                
                this.debug.success('Link Popup', `Successfully attached to ${indicators.size()} indicators`);
                return true;
                
            } catch (error) {
                this.debug.error('Link Popup', 'Error during attachment', error);
                return false;
            }
        }
        
        /**
         * Handle hover on link indicator
         */
        handleIndicatorHover(event, nodeData) {
            this.clearHideTimeout();
            this.clearHighlightTimeout(); // Don't clear highlighting when hovering
            
            // Determine link type from event target classes
            const target = event.target;
            const isInternal = target.classList.contains('internal');
            const isExternal = target.classList.contains('external');
            
            if (!isInternal && !isExternal) {
                return; // Not a valid link indicator
            }
            
            const linkType = isInternal ? 'internal' : 'external';
            const pageId = nodeData.data.id;
            
            if (!pageId) {
                return; // No page ID available
            }
            
            // Check if SHIFT key is held down
            const isShiftHeld = event.shiftKey;
            
            if (isShiftHeld) {
                // SHIFT + HOVER = Visualization only (no popup)
                if (isInternal) {
                    this.debug.log('Visual Highlighting', 'SHIFT+hover detected - showing visualization only');
                    this.enableLinkHighlighting(pageId, nodeData);
                    this.isVisualizationMode = true; // Track that we're in viz mode
                }
                // Don't show popup when SHIFT is held
                return;
            }
            
            // Regular hover behavior - POPUP ONLY (no highlighting)
            this.isVisualizationMode = false;
            
            // Schedule popup show with delay for all link types (I and O)
            this.hoverTimeout = setTimeout(() => {
                this.showPopup(event, pageId, linkType, nodeData.data.name);
            }, this.config.showDelay);
        }
        
        /**
         * Handle mouse leave on link indicator
         */
        handleIndicatorLeave(event, nodeData) {
            // Always schedule popup hide
            this.scheduleHide();
            
            // Always schedule highlighting clear
            this.scheduleHighlightClear();
            
            // Reset visualization mode
            this.isVisualizationMode = false;
        }
        
        /**
         * Enable link relationship highlighting for internal links
         * ENHANCED: Now also shows actual link connections like the Links toggle view
         */
        async enableLinkHighlighting(pageId, sourceNodeData) {
            try {
                // Fetch link data to get target post IDs (this will use cache if popup already fetched it)
                const linkData = await this.fetchLinkData(pageId);
                const internalLinks = linkData.internal.links || [];
                
                this.debug.info('Link Tracking', `Processing ${internalLinks.length} internal links for page ${pageId}`);
                this.debug.info('Link Tracking', 'Internal links data (first 10)', internalLinks.slice(0, 10));
                
                // Enhanced link processing with multi-strategy matching
                const linkAnalysis = this.analyzeLinkTargets(internalLinks);
                
                this.debug.info('Fallback Matching', 'Link analysis results', linkAnalysis);
                this.debug.log('Fallback Matching', `Valid post IDs: ${linkAnalysis.validPostIds.length}, URL fallbacks: ${linkAnalysis.urlFallbacks.length}`);
                
                if (linkAnalysis.validPostIds.length === 0 && linkAnalysis.urlFallbacks.length === 0) {
                    this.debug.log('Fallback Matching', 'No valid targets found for highlighting');
                    return; // No internal links to highlight
                }
                
                // Apply multi-strategy highlighting using D3.js
                this.applyAdvancedLinkHighlighting(pageId, linkAnalysis);
                
                // NEW: Also show actual link connections (like Links toggle view)
                await this.enableTemporaryLinkConnections(pageId);
                
                this.debug.success('Visual Highlighting', `Applied advanced highlighting AND link connections for page ${pageId}`);
                
            } catch (error) {
                this.debug.warn('Visual Highlighting', 'Failed to enable link highlighting', error);
                // Don't let highlighting errors affect popup functionality
            }
        }
        
        /**
         * Enable temporary link connections visualization (like Links toggle view)
         * This creates SVG paths showing actual link connections during SHIFT+hover
         * FIXED: Now uses page-specific link data to show ONLY DIRECT internal link connections
         */
        async enableTemporaryLinkConnections(pageId) {
            try {
                // Ensure we have access to D3.js and SVG elements
                if (!d3 || !d3.select('#slmm-tree-svg').node()) {
                    this.debug.warn('Link Connections', 'D3.js or SVG not available for temporary link connections');
                    return;
                }
                
                // Get SVG and tree group
                const svg = d3.select('#slmm-tree-svg');
                const treeGroup = svg.select('.slmm-tree-group');
                
                if (treeGroup.empty()) {
                    this.debug.warn('Link Connections', 'Tree group not found for temporary link connections');
                    return;
                }
                
                // Create or get temporary link group (inserted before tree nodes for proper z-order)
                let tempLinkGroup = treeGroup.select('.slmm-temp-link-group');
                if (tempLinkGroup.empty()) {
                    tempLinkGroup = treeGroup.insert('g', ':first-child')
                        .attr('class', 'slmm-temp-link-group')
                        .style('opacity', 0);
                }
                
                // Clear any existing temporary links
                tempLinkGroup.selectAll('*').remove();
                
                // CRITICAL FIX: Use page-specific link data instead of full site data
                this.debug.log('Link Connections', `Fetching page-specific link data for page ${pageId}...`);
                const pageSpecificLinkData = await this.fetchLinkData(pageId);
                
                // Update node positions cache
                const nodePositions = this.updateNodePositionsCache();
                
                // Prepare link data for rendering using page-specific internal links only
                const renderableLinks = this.prepareDirectInternalLinksOnly(pageSpecificLinkData, pageId, nodePositions);
                
                if (renderableLinks.length === 0) {
                    this.debug.log('Link Connections', 'No direct internal links for temporary connections');
                    return;
                }
                
                this.debug.log('Link Connections', `Rendering ${renderableLinks.length} DIRECT internal link connections`);
                
                // Setup link generator (same as Link Overlay system)
                const linkGenerator = d3.linkHorizontal()
                    .x(d => d.x)
                    .y(d => d.y);
                
                // Render only internal links (external links not relevant for this issue)
                const internalLinks = renderableLinks.filter(l => l.type === 'internal');
                if (internalLinks.length > 0) {
                    this.renderTemporaryInternalLinks(tempLinkGroup, internalLinks, linkGenerator);
                }
                
                // Animate links in (same as Link Overlay system)
                tempLinkGroup
                    .transition()
                    .duration(300)
                    .style('opacity', 1);
                
                this.debug.success('Link Connections', `Successfully created ${internalLinks.length} DIRECT internal link connections`);
                
            } catch (error) {
                this.debug.warn('Link Connections', 'Failed to create temporary link connections', error);
            }
        }
        
        /**
         * Fetch full link data using same endpoint as Link Overlay system
         */
        async fetchFullLinkData() {
            const activeTab = $('.slmm-tab-button.active').data('tab') || 'page';
            
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: slmmInterlinkingData.ajax_url || ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'slmm_generate_silo_grid',
                        nonce: slmmInterlinkingData.nonce,
                        post_type_filter: activeTab,
                        include_links: 'true' // Request link data
                    },
                    timeout: 10000,
                    success: (response) => {
                        if (response.success && response.data) {
                            const internalCount = (response.data.internal_links || []).length;
                            const externalCount = (response.data.external_links || []).length;
                            this.debug.info('Link Tracking', `Full link data loaded - ${internalCount} internal, ${externalCount} external`);
                            resolve({
                                internal: response.data.internal_links || [],
                                external: response.data.external_links || []
                            });
                        } else {
                            reject(new Error(response.data || 'Failed to load full link data'));
                        }
                    },
                    error: (xhr, status, error) => {
                        reject(new Error(`AJAX error loading full link data: ${status} - ${error}`));
                    }
                });
            });
        }
        
        /**
         * Prepare DIRECT internal links only from page-specific link data
         * This fixes the issue where SHIFT+hover was showing all child nodes instead of only actual internal links
         */
        prepareDirectInternalLinksOnly(pageSpecificLinkData, sourcePageId, nodePositions) {
            const renderableLinks = [];
            const sourceId = String(sourcePageId);
            
            this.debug.log('Link Connections', `Preparing DIRECT internal links for page ${sourceId}`);
            this.debug.info('Link Connections', 'Page-specific internal links (first 10)', (pageSpecificLinkData.internal.links || []).slice(0, 10));
            
            // Get actual internal links with valid target_post_id values
            const internalLinks = pageSpecificLinkData.internal.links || [];
            
            internalLinks.forEach((link, index) => {
                this.debug.log('Link Connections', `Processing internal link ${index}`, {
                    url: link.url,
                    anchor: link.anchor,
                    target_post_id: link.target_post_id,
                    target_page: link.target_page
                });
                
                // Only process links that have a valid target_post_id (actual direct internal links)
                const targetPostId = link.target_post_id;
                if (!targetPostId || targetPostId === '0' || targetPostId === 0) {
                    this.debug.log('Link Connections', `Skipping link ${index} - no valid target_post_id (${targetPostId})`);
                    return;
                }
                
                const targetId = String(targetPostId);
                const sourcePos = nodePositions.get(sourceId);
                const targetPos = nodePositions.get(targetId);
                
                if (sourcePos && targetPos) {
                    renderableLinks.push({
                        ...link,
                        source: sourcePos,
                        target: targetPos,
                        type: 'internal',
                        from: sourceId,
                        to: targetId
                    });
                    this.debug.success('Link Connections', `Added DIRECT internal link: ${sourceId} -> ${targetId} (${link.anchor || 'no anchor'})`);
                } else {
                    this.debug.warn('Link Connections', `Skipping internal link ${sourceId} -> ${targetId} - missing positions`, { 
                        sourcePos: !!sourcePos, 
                        targetPos: !!targetPos,
                        availableNodes: Array.from(nodePositions.keys()).slice(0, 10) // Show first 10 for debugging
                    });
                }
            });
            
            this.debug.success('Link Connections', `Prepared ${renderableLinks.length} DIRECT internal link connections (no hierarchy-based connections)`);
            return renderableLinks;
        }
        
        /**
         * Prepare filtered renderable links using Link Overlay logic (same as working system)
         */
        prepareFilteredRenderableLinksFromFullData(fullLinkData, sourcePageId, nodePositions) {
            const renderableLinks = [];
            const sourceId = String(sourcePageId);
            
            this.debug.log('Link Tracking', `Filtering links FROM node ${sourceId} using Link Overlay logic`);
            this.debug.info('Link Tracking', `Full data - ${fullLinkData.internal.length} internal, ${fullLinkData.external.length} external`);
            
            // Process internal links (same logic as Link Overlay system)
            fullLinkData.internal.forEach((link, index) => {
                // Use same field mapping as Link Overlay
                const fromId = String(link.from || link.source_id || link.post_from);
                const toId = String(link.to || link.target_id || link.post_to);
                
                // Only include links FROM our source node
                if (fromId === sourceId) {
                    const sourcePos = nodePositions.get(fromId);
                    const targetPos = nodePositions.get(toId);
                    
                    if (sourcePos && targetPos) {
                        renderableLinks.push({
                            ...link,
                            source: sourcePos,
                            target: targetPos,
                            type: 'internal'
                        });
                        this.debug.log('Link Tracking', `Added internal link: ${fromId} -> ${toId}`);
                    } else {
                        this.debug.warn('Link Tracking', `Skipping internal link ${fromId} -> ${toId} - missing positions`, { sourcePos: !!sourcePos, targetPos: !!targetPos });
                    }
                }
            });
            
            // Process external links (same logic as Link Overlay system)
            fullLinkData.external.forEach((link, index) => {
                const fromId = String(link.from || link.source_id || link.post_from);
                
                // Only include links FROM our source node
                if (fromId === sourceId) {
                    const sourcePos = nodePositions.get(fromId);
                    
                    if (sourcePos) {
                        renderableLinks.push({
                            ...link,
                            source: sourcePos,
                            type: 'external'
                        });
                        this.debug.log('Link Tracking', `Added external link FROM ${fromId}`);
                    } else {
                        this.debug.warn('Link Tracking', `Skipping external link FROM ${fromId} - missing position`);
                    }
                }
            });
            
            this.debug.success('Link Tracking', `Prepared ${renderableLinks.length} filtered renderable links from full data`);
            return renderableLinks;
        }
        
        /**
         * Update and cache current node positions from D3.js tree
         */
        updateNodePositionsCache() {
            const nodePositions = new Map();
            
            // Cache all visible node positions
            d3.selectAll('.slmm-tree-node').each((d) => {
                if (d && d.data && d.data.id) {
                    const nodeId = String(d.data.id);
                    nodePositions.set(nodeId, {
                        x: d.x || 0,
                        y: d.y || 0,
                        visible: true
                    });
                }
            });
            
            this.debug.log('Link Connections', `Cached ${nodePositions.size} node positions for temporary links`);
            return nodePositions;
        }
        
        /**
         * Render temporary internal links as curved SVG paths (same as Link Overlay system)
         */
        renderTemporaryInternalLinks(linkGroup, links, linkGenerator) {
            const internalGroup = linkGroup.selectAll('.slmm-temp-internal-links')
                .data([null])
                .join('g')
                .attr('class', 'slmm-temp-internal-links');
                
            const paths = internalGroup.selectAll('.slmm-temp-internal-link')
                .data(links)
                .join('path')
                .attr('class', 'slmm-temp-internal-link')
                .attr('d', d => linkGenerator(d))
                .style('fill', 'none')
                .style('stroke', 'var(--slmm-internal-link-color, #3b82f6)')
                .style('stroke-width', 'var(--slmm-link-stroke-width, 1.5px)')
                .style('opacity', 'var(--slmm-link-default-opacity, 0.6)')
                .style('stroke-linecap', 'round')
                .style('stroke-linejoin', 'round')
                .style('pointer-events', 'none'); // No interaction needed for temporary links
                
            this.debug.log('Link Connections', `Rendered ${paths.size()} temporary internal link paths`);
        }
        
        /**
         * Render temporary external links as dashed arcs with endpoints
         */
        renderTemporaryExternalLinks(linkGroup, links) {
            const externalGroup = linkGroup.selectAll('.slmm-temp-external-links')
                .data([null])
                .join('g')
                .attr('class', 'slmm-temp-external-links');
                
            // Create arc paths for external links
            const paths = externalGroup.selectAll('.slmm-temp-external-link')
                .data(links)
                .join('path')
                .attr('class', 'slmm-temp-external-link')
                .attr('d', d => this.generateExternalLinkPath(d))
                .style('fill', 'none')
                .style('stroke', 'var(--slmm-external-link-color, #f59e0b)')
                .style('stroke-width', 'var(--slmm-link-stroke-width, 1.2px)')
                .style('stroke-dasharray', '4,2')
                .style('opacity', 'var(--slmm-link-default-opacity, 0.4)')
                .style('stroke-linecap', 'round')
                .style('stroke-linejoin', 'round')
                .style('pointer-events', 'none');
                
            // Create endpoint nodes for external links
            const nodes = externalGroup.selectAll('.slmm-temp-external-node')
                .data(links)
                .join('circle')
                .attr('class', 'slmm-temp-external-node')
                .attr('cx', d => {
                    const angle = d.visual_angle ? (d.visual_angle * Math.PI / 180) : 0;
                    const distance = d.visual_distance || 80;
                    return d.source.x + Math.cos(angle) * distance;
                })
                .attr('cy', d => {
                    const angle = d.visual_angle ? (d.visual_angle * Math.PI / 180) : 0;
                    const distance = d.visual_distance || 80;
                    return d.source.y + Math.sin(angle) * distance;
                })
                .attr('r', 4)
                .style('fill', 'var(--slmm-external-link-color, #f59e0b)')
                .style('stroke', 'white')
                .style('stroke-width', 1)
                .style('opacity', 'var(--slmm-link-default-opacity, 0.6)')
                .style('pointer-events', 'none');
                
            this.debug.log('Link Connections', `Rendered ${paths.size()} temporary external links with ${nodes.size()} endpoint nodes`);
        }
        
        /**
         * Generate path for external links (same logic as Link Overlay system)
         */
        generateExternalLinkPath(d) {
            const source = d.source;
            const angle = d.visual_angle ? (d.visual_angle * Math.PI / 180) : 0;
            const distance = d.visual_distance || 80;
            
            // Calculate target position
            const targetX = source.x + Math.cos(angle) * distance;
            const targetY = source.y + Math.sin(angle) * distance;
            
            // Create curved arc path
            const controlDistance = distance * 0.6;
            const controlX = source.x + Math.cos(angle) * controlDistance;
            const controlY = source.y + Math.sin(angle) * controlDistance - 15;
            
            return `M${source.x},${source.y} Q${controlX},${controlY} ${targetX},${targetY}`;
        }
        
        /**
         * Clear temporary link connections
         */
        clearTemporaryLinkConnections() {
            const svg = d3.select('#slmm-tree-svg');
            if (!svg.empty()) {
                const tempLinkGroup = svg.select('.slmm-temp-link-group');
                if (!tempLinkGroup.empty()) {
                    // Animate out and remove
                    tempLinkGroup
                        .transition()
                        .duration(300)
                        .style('opacity', 0)
                        .on('end', () => {
                            tempLinkGroup.remove();
                        });
                }
            }
            this.debug.log('Link Popup', '🔗 Temporary link connections cleared');
        }
        
        /**
         * Analyze link targets and separate valid post IDs from URL fallbacks
         */
        analyzeLinkTargets(internalLinks) {
            const validPostIds = [];
            const urlFallbacks = [];
            const analysis = {
                validPostIds: [],
                urlFallbacks: [],
                debugInfo: []
            };
            
            internalLinks.forEach((link, index) => {
                const debugEntry = {
                    linkIndex: index,
                    url: link.url,
                    anchor: link.anchor,
                    target_page: link.target_page,
                    raw_target_post_id: link.target_post_id,
                    resolution_status: 'unknown'
                };
                
                // Try to get valid post ID
                const postId = link.target_post_id || link.post_id || link.id || null;
                const intId = parseInt(postId, 10);
                
                if (intId && intId > 0) {
                    // Valid post ID found
                    analysis.validPostIds.push(intId);
                    debugEntry.resolution_status = 'post_id_valid';
                    debugEntry.resolved_post_id = intId;
                } else {
                    // No valid post ID - add to URL fallback list
                    const extractedSlug = this.extractSlugFromUrl(link.url);
                    const fallbackData = {
                        url: link.url,
                        anchor: link.anchor,
                        target_page: link.target_page,
                        slug: extractedSlug
                    };
                    analysis.urlFallbacks.push(fallbackData);
                    debugEntry.resolution_status = 'post_id_failed_using_url_fallback';
                    debugEntry.extracted_slug = extractedSlug;
                    
                    // Removed verbose URL fallback debugging
                }
                
                analysis.debugInfo.push(debugEntry);
            });
            
            this.debug.info('Debug Analysis', 'Detailed link analysis', analysis.debugInfo);
            return analysis;
        }
        
        /**
         * Apply advanced multi-strategy highlighting to D3.js nodes
         */
        applyAdvancedLinkHighlighting(sourcePageId, linkAnalysis) {
            // Find the D3.js tree group
            let treeGroup = d3.select('.slmm-tree-svg .slmm-tree-group');
            
            if (treeGroup.empty()) {
                treeGroup = d3.select('svg g.slmm-tree-group');
            }
            
            if (treeGroup.empty()) {
                treeGroup = d3.select('.slmm-tree-group');
            }
            
            if (treeGroup.empty()) {
                this.debug.warn('Visual Highlighting', 'D3.js tree group not found for advanced highlighting');
                return;
            }
            
            // Get all tree nodes
            const allNodes = treeGroup.selectAll('.slmm-tree-node');
            this.debug.log('Visual Highlighting', `Found ${allNodes.size()} tree nodes for advanced highlighting`);
            
            if (allNodes.empty()) {
                this.debug.warn('Visual Highlighting', 'No tree nodes found for highlighting');
                return;
            }
            
            // Build comprehensive node data for matching
            const availableNodes = [];
            const self = this; // Store class reference
            allNodes.each(function(d) {
                if (d && d.data) {
                    availableNodes.push({
                        element: this, // DOM element (from D3 each context)
                        id: d.data.id,
                        name: d.data.name,
                        title: d.data.title || d.data.name,
                        // Extract potential URL patterns from node data
                        possibleSlugs: self.generateNodeSlugs(d.data) // Use stored class reference
                    });
                }
            });
            
            // COMPREHENSIVE DEBUG: Log complete node data structure
            // Removed verbose available nodes debugging - use debug system if needed
            
            // First, dim all nodes
            allNodes.classed('slmm-link-dimmed', true)
                    .classed('slmm-link-highlighted', false)
                    .classed('slmm-link-source', false);
            
            // Highlight source node
            const sourceNodes = allNodes.filter(d => {
                const nodeId = parseInt(d.data.id, 10);
                const sourceId = parseInt(sourcePageId, 10);
                return nodeId === sourceId;
            });
            sourceNodes.classed('slmm-link-dimmed', false)
                       .classed('slmm-link-source', true);
            
            this.debug.log('Visual Highlighting', `Highlighted ${sourceNodes.size()} source node(s)`);
            
            // Strategy 1: Match by valid post IDs
            let matchedByPostId = 0;
            if (linkAnalysis.validPostIds.length > 0) {
                // Use debug system for post ID lookup if needed
                
                const postIdNodes = allNodes.filter(d => {
                    const nodeId = parseInt(d.data.id, 10);
                    const isMatch = linkAnalysis.validPostIds.includes(nodeId);
                    
                    if (isMatch) {
                        // Removed verbose post ID match debugging
                    }
                    
                    return isMatch;
                });
                
                // Removed verbose nodes found debugging
                
                postIdNodes.each(function(d) {
                    const element = this;
                    // Removed verbose highlighting application debugging
                });
                
                postIdNodes.classed('slmm-link-dimmed', false)
                          .classed('slmm-link-highlighted', true);
                
                // Removed verbose after highlighting debugging loop
                
                matchedByPostId = postIdNodes.size();
                this.debug.log('Link Popup', `Matched ${matchedByPostId} nodes by post ID`);
            }
            
            // Strategy 2: Match by URL fallbacks (slug, title, etc.)
            let matchedByFallback = 0;
            if (linkAnalysis.urlFallbacks.length > 0) {
                // Removed verbose URL fallback matching debugging
                
                linkAnalysis.urlFallbacks.forEach((fallback, index) => {
                    // Removed verbose fallback processing debugging
                    const matchedNodes = this.findNodesByFallback(allNodes, fallback);
                    
                    if (matchedNodes.size() > 0) {
                        matchedNodes.classed('slmm-link-dimmed', false)
                                   .classed('slmm-link-highlighted', true);
                        matchedByFallback += matchedNodes.size();
                        this.debug.success('Fallback Matching', `Fallback match found for ${fallback.url} - matched ${matchedNodes.size()} node(s)`);
                    } else {
                        this.debug.warn('Fallback Matching', `No fallback match for ${fallback.url} (slug: ${fallback.slug}, anchor: ${fallback.anchor})`);
                    }
                });
            } else {
                // No URL fallbacks to process
            }
            
            const totalMatched = matchedByPostId + matchedByFallback;
            this.debug.log('Link Popup', `Total highlighting results - Post ID matches: ${matchedByPostId}, URL fallback matches: ${matchedByFallback}, Total: ${totalMatched}`);
            
            // Store highlighting state for cleanup
            this.currentHighlighting = {
                sourcePageId: sourcePageId,
                totalMatched: totalMatched,
                timestamp: Date.now()
            };
        }
        
        /**
         * Generate possible slugs for a node based on its data
         */
        generateNodeSlugs(nodeData) {
            const slugs = [];
            
            // Removed verbose slug generation debugging - use debug system if needed
            
            if (nodeData.name) {
                const nameVariations = this.createSlugVariations(nodeData.name);
                slugs.push(...nameVariations);
                // Removed verbose name variations debugging
            }
            
            if (nodeData.title && nodeData.title !== nodeData.name) {
                const titleVariations = this.createSlugVariations(nodeData.title);
                slugs.push(...titleVariations);
                this.debug.log('Fallback Matching', 'Added title variations', titleVariations);
            }
            
            // Remove duplicates and empty strings
            const cleanSlugs = [...new Set(slugs)].filter(slug => slug && slug.length > 0);
            // Removed verbose final slugs debugging
            
            return cleanSlugs;
        }

        /**
         * Create comprehensive slug variations from text
         * Handles PascalCase, camelCase, spaces, and various delimiters
         */
        createSlugVariations(text) {
            if (!text || typeof text !== 'string') {
                return [];
            }
            
            const variations = [];
            const originalText = text.trim();
            
            // Basic lowercase conversion
            variations.push(originalText.toLowerCase());
            
            // Handle PascalCase/camelCase conversion (key fix for ProtectedWords → protected-word)
            const kebabFromPascal = originalText
                .replace(/([a-z])([A-Z])/g, '$1-$2') // Insert dash before uppercase letters
                .replace(/([A-Z])([A-Z][a-z])/g, '$1-$2') // Handle sequences like "XMLParser"
                .toLowerCase();
            if (kebabFromPascal !== originalText.toLowerCase()) {
                variations.push(kebabFromPascal);
                // Also try without the last 's' for plural matching (protected-words → protected-word)
                if (kebabFromPascal.endsWith('s') && kebabFromPascal.length > 1) {
                    variations.push(kebabFromPascal.slice(0, -1));
                }
            }
            
            // Handle spaces and various delimiters
            const spaceToDash = originalText.toLowerCase().replace(/\s+/g, '-');
            const spaceToUnderscore = originalText.toLowerCase().replace(/\s+/g, '_');
            const noSpaces = originalText.toLowerCase().replace(/\s+/g, '');
            
            variations.push(spaceToDash, spaceToUnderscore, noSpaces);
            
            // Clean up non-alphanumeric characters
            const cleanDashed = originalText.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-');
            const cleanNoDashes = originalText.toLowerCase().replace(/[^a-z0-9]/g, '');
            
            variations.push(cleanDashed, cleanNoDashes);
            
            // Add singular/plural variations
            variations.forEach(variant => {
                if (variant.endsWith('s') && variant.length > 1) {
                    variations.push(variant.slice(0, -1)); // Remove 's'
                } else if (!variant.endsWith('s')) {
                    variations.push(variant + 's'); // Add 's'
                }
            });
            
            // Handle word variations (for cases like "form-test" vs "form_test")
            variations.forEach(variant => {
                if (variant.includes('-')) {
                    variations.push(variant.replace(/-/g, '_')); // dash to underscore
                    variations.push(variant.replace(/-/g, '')); // remove dashes
                }
                if (variant.includes('_')) {
                    variations.push(variant.replace(/_/g, '-')); // underscore to dash
                    variations.push(variant.replace(/_/g, '')); // remove underscores
                }
            });
            
            // Remove duplicates and empty strings
            return [...new Set(variations)].filter(v => v && v.length > 0);
        }
        
        /**
         * Find D3 nodes that match URL fallback criteria
         */
        findNodesByFallback(allNodes, fallback) {
            this.debug.log('Fallback Matching', 'findNodesByFallback called with', fallback);
            
            const matchedNodes = [];
            let totalNodesChecked = 0;
            let debugInfo = {
                strategy1_attempts: 0,
                strategy1_matches: 0,
                strategy2_attempts: 0,
                strategy2_matches: 0,
                strategy3_attempts: 0,
                strategy3_matches: 0,
                strategy4_attempts: 0,
                strategy4_matches: 0,
                strategy5_attempts: 0,
                strategy5_matches: 0
            };
            
            const result = allNodes.filter(d => {
                totalNodesChecked++;
                if (!d || !d.data) {
                    this.debug.warn('Fallback Matching', `Node ${totalNodesChecked} has no data`, d);
                    return false;
                }
                
                this.debug.log('Fallback Matching', `Checking node ${totalNodesChecked}`, {
                    id: d.data.id,
                    name: d.data.name,
                    title: d.data.title
                });
                
                // Strategy 1: Direct slug match (enhanced)
                if (fallback.slug) {
                    debugInfo.strategy1_attempts++;
                    const possibleSlugs = this.generateNodeSlugs(d.data);
                    this.debug.log('Fallback Matching', `Node ${d.data.id} possible slugs`, possibleSlugs);
                    this.debug.log('Fallback Matching', `Comparing against fallback slug: ${fallback.slug.toLowerCase()}`);
                    
                    if (possibleSlugs.some(slug => slug === fallback.slug.toLowerCase())) {
                        debugInfo.strategy1_matches++;
                        this.debug.success('Fallback Matching', `STRATEGY 1 MATCH - Node ${d.data.id} matches slug ${fallback.slug}`);
                        return true;
                    }
                }
                
                // Strategy 2: Reverse slug matching (generate variations of URL slug and match against node name)
                if (fallback.slug) {
                    debugInfo.strategy2_attempts++;
                    const urlSlugVariations = this.createSlugVariations(fallback.slug);
                    const nodeNameLower = (d.data.name || '').toLowerCase();
                    const nodeTitleLower = (d.data.title || '').toLowerCase();
                    
                    this.debug.log('Fallback Matching', 'Strategy 2 - URL slug variations', urlSlugVariations.slice(0, 10));
                    this.debug.log('Fallback Matching', `Strategy 2 - Node name/title: "${nodeNameLower}" / "${nodeTitleLower}"`);
                    
                    const reverseMatch = urlSlugVariations.some(variation => 
                        variation === nodeNameLower || 
                        variation === nodeTitleLower ||
                        nodeNameLower.includes(variation) ||
                        nodeTitleLower.includes(variation)
                    );
                    
                    if (reverseMatch) {
                        debugInfo.strategy2_matches++;
                        this.debug.success('Fallback Matching', `STRATEGY 2 MATCH - URL slug variations match node ${d.data.id}`);
                        return true;
                    }
                }
                
                // Strategy 3: Fuzzy slug matching (Levenshtein distance)
                if (fallback.slug && d.data.name) {
                    debugInfo.strategy3_attempts++;
                    const slugLower = fallback.slug.toLowerCase();
                    const nameLower = d.data.name.toLowerCase();
                    const distance = this.calculateLevenshteinDistance(slugLower, nameLower);
                    const maxLength = Math.max(slugLower.length, nameLower.length);
                    const similarity = 1 - (distance / maxLength);
                    
                    this.debug.log('Fallback Matching', `Strategy 3 - Fuzzy match "${slugLower}" vs "${nameLower}": distance=${distance}, similarity=${similarity.toFixed(2)}`);
                    
                    if (similarity >= 0.6) { // 60% similarity threshold
                        debugInfo.strategy3_matches++;
                        this.debug.success('Fallback Matching', `STRATEGY 3 MATCH - Fuzzy match for node ${d.data.id} (${(similarity * 100).toFixed(1)}% similar)`);
                        return true;
                    }
                }
                
                // Strategy 4: Anchor text match (enhanced)
                if (fallback.anchor && d.data.name) {
                    debugInfo.strategy4_attempts++;
                    const anchorVariations = this.createSlugVariations(fallback.anchor);
                    const nameVariations = this.createSlugVariations(d.data.name);
                    
                    this.debug.log('Fallback Matching', 'Strategy 4 - Anchor variations', anchorVariations.slice(0, 5));
                    this.debug.log('Fallback Matching', 'Strategy 4 - Name variations', nameVariations.slice(0, 5));
                    
                    const anchorMatch = anchorVariations.some(anchor => 
                        nameVariations.some(name => anchor === name || anchor.includes(name) || name.includes(anchor))
                    );
                    
                    if (anchorMatch) {
                        debugInfo.strategy4_matches++;
                        this.debug.success('Fallback Matching', `STRATEGY 4 MATCH - Node ${d.data.id} matches enhanced anchor text`);
                        return true;
                    }
                }
                
                // Strategy 5: Target page match (enhanced)
                if (fallback.target_page && d.data.name) {
                    debugInfo.strategy5_attempts++;
                    const targetVariations = this.createSlugVariations(fallback.target_page);
                    const nameVariations = this.createSlugVariations(d.data.name);
                    
                    this.debug.log('Fallback Matching', 'Strategy 5 - Target variations', targetVariations.slice(0, 5));
                    this.debug.log('Fallback Matching', 'Strategy 5 - Name variations', nameVariations.slice(0, 5));
                    
                    const targetMatch = targetVariations.some(target => 
                        nameVariations.some(name => target === name || target.includes(name) || name.includes(target))
                    );
                    
                    if (targetMatch) {
                        debugInfo.strategy5_matches++;
                        this.debug.success('Fallback Matching', `STRATEGY 5 MATCH - Node ${d.data.id} matches enhanced target page`);
                        return true;
                    }
                }
                
                return false;
            });
            
            this.debug.info('Debug Analysis', 'Fallback matching complete', {
                totalNodesChecked,
                matchesFound: result.size(),
                debugInfo
            });
            
            return result;
        }

        /**
         * Calculate Levenshtein distance between two strings (for fuzzy matching)
         */
        calculateLevenshteinDistance(str1, str2) {
            const matrix = [];
            
            // Initialize matrix
            for (let i = 0; i <= str2.length; i++) {
                matrix[i] = [i];
            }
            for (let j = 0; j <= str1.length; j++) {
                matrix[0][j] = j;
            }
            
            // Fill matrix
            for (let i = 1; i <= str2.length; i++) {
                for (let j = 1; j <= str1.length; j++) {
                    if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                        matrix[i][j] = matrix[i - 1][j - 1];
                    } else {
                        matrix[i][j] = Math.min(
                            matrix[i - 1][j - 1] + 1, // substitution
                            matrix[i][j - 1] + 1,     // insertion
                            matrix[i - 1][j] + 1      // deletion
                        );
                    }
                }
            }
            
            return matrix[str2.length][str1.length];
        }
        
        /**
         * Extract slug from URL for fallback matching
         */
        extractSlugFromUrl(url) {
            this.debug.log('Fallback Matching', `extractSlugFromUrl called with: ${url}`);
            
            try {
                const urlObj = new URL(url, window.location.origin);
                this.debug.log('Fallback Matching', 'Parsed URL object', {
                    pathname: urlObj.pathname,
                    origin: urlObj.origin,
                    href: urlObj.href
                });
                
                const pathSegments = urlObj.pathname.split('/').filter(segment => segment.length > 0);
                this.debug.log('Fallback Matching', 'Path segments', pathSegments);
                
                const extractedSlug = pathSegments[pathSegments.length - 1] || '';
                this.debug.log('Fallback Matching', `Extracted slug: "${extractedSlug}"`);
                
                return extractedSlug;
            } catch (e) {
                this.debug.warn('Fallback Matching', 'URL parsing failed, using fallback', e);
                
                // Fallback for relative URLs or parsing errors
                const pathSegments = url.split('/').filter(segment => segment.length > 0);
                this.debug.log('Fallback Matching', 'Fallback path segments', pathSegments);
                
                const extractedSlug = pathSegments[pathSegments.length - 1] || '';
                this.debug.log('Fallback Matching', `Fallback extracted slug: "${extractedSlug}"`);
                
                return extractedSlug;
            }
        }
        
        /**
         * Apply visual highlighting to D3.js nodes (legacy method - keeping for compatibility)
         */
        applyLinkHighlighting(sourcePageId, targetPostIds) {
            // Find the D3.js tree group with multiple selector attempts
            let treeGroup = d3.select('.slmm-tree-svg .slmm-tree-group');
            
            if (treeGroup.empty()) {
                // Try alternative selectors
                treeGroup = d3.select('svg g.slmm-tree-group');
            }
            
            if (treeGroup.empty()) {
                treeGroup = d3.select('.slmm-tree-group');
            }
            
            if (treeGroup.empty()) {
                this.debug.warn('Visual Highlighting', 'D3.js tree group not found for highlighting', {
                    svgElements: d3.selectAll('svg').size(),
                    treeNodes: d3.selectAll('.slmm-tree-node').size()
                });
                return;
            }
            
            // Get all tree nodes
            const allNodes = treeGroup.selectAll('.slmm-tree-node');
            
            this.debug.log('Visual Highlighting', `Found ${allNodes.size()} tree nodes for highlighting`);
            
            if (allNodes.empty()) {
                this.debug.warn('Visual Highlighting', 'No tree nodes found for highlighting');
                return;
            }
            
            // Debug: Log all available node IDs to understand the data structure
            const availableNodeIds = [];
            allNodes.each(function(d) {
                if (d && d.data && d.data.id !== undefined) {
                    availableNodeIds.push({
                        raw: d.data.id,
                        type: typeof d.data.id,
                        parsed: parseInt(d.data.id, 10)
                    });
                }
            });
            this.debug.info('Debug Analysis', 'Available node IDs in tree', availableNodeIds.slice(0, 20)); // Show first 20 for debugging
            this.debug.info('Debug Analysis', 'Target post IDs to highlight', targetPostIds);
            
            // First, dim all nodes
            allNodes.classed('slmm-link-dimmed', true)
                    .classed('slmm-link-highlighted', false)
                    .classed('slmm-link-source', false);
            
            // Highlight the source node (the one being hovered) with improved ID matching
            const sourceNodes = allNodes.filter(d => {
                if (!d || !d.data || d.data.id === undefined) return false;
                const nodeId = parseInt(d.data.id, 10);
                const sourceId = parseInt(sourcePageId, 10);
                return nodeId === sourceId;
            });
            sourceNodes.classed('slmm-link-dimmed', false)
                       .classed('slmm-link-source', true);
            
            this.debug.log('Visual Highlighting', `Highlighted ${sourceNodes.size()} source node(s) for page ${sourcePageId}`);
            
            // Highlight target nodes (the ones being linked to) with improved ID matching
            const targetNodes = allNodes.filter(d => {
                if (!d || !d.data || d.data.id === undefined) return false;
                
                // Convert node ID to integer for comparison
                const nodeId = parseInt(d.data.id, 10);
                
                // Check if this node ID is in our target post IDs array
                const isTarget = targetPostIds.some(targetId => {
                    const targetIdInt = parseInt(targetId, 10);
                    return nodeId === targetIdInt;
                });
                
                if (isTarget) {
                    const matchedTargetId = targetPostIds.find(id => parseInt(id, 10) === nodeId);
                    this.debug.success('Visual Highlighting', `Matching node found - Node ID: ${nodeId} (type: ${typeof d.data.id}) matches target ID: ${matchedTargetId}`);
                }
                
                return isTarget;
            });
            
            targetNodes.classed('slmm-link-dimmed', false)
                       .classed('slmm-link-highlighted', true);
            
            this.debug.log('Visual Highlighting', `Highlighted ${targetNodes.size()} target node(s) from ${targetPostIds.length} target IDs`, targetPostIds);
            
            // If no target nodes were highlighted, provide detailed debugging
            if (targetNodes.size() === 0 && targetPostIds.length > 0) {
                this.debug.warn('Visual Highlighting', 'No target nodes were highlighted despite having target IDs');
                this.debug.warn('Debug Analysis', 'ID matching issue detected', {
                    targetPostIds,
                    availableNodeIds: availableNodeIds.slice(0, 10) // Show first 10 for debugging
                });
            }
            
            // Store current highlighting state for cleanup
            this.currentHighlighting = {
                sourcePageId,
                targetPostIds
            };
        }
        
        /**
         * Clear link relationship highlighting
         * ENHANCED: Now also clears temporary link connections
         */
        clearLinkHighlighting() {
            const treeGroup = d3.select('.slmm-tree-svg .slmm-tree-group');
            
            if (!treeGroup.empty()) {
                treeGroup.selectAll('.slmm-tree-node')
                         .classed('slmm-link-dimmed', false)
                         .classed('slmm-link-highlighted', false)
                         .classed('slmm-link-source', false);
            }
            
            // NEW: Also clear temporary link connections
            this.clearTemporaryLinkConnections();
            
            this.currentHighlighting = null;
            this.debug.log('Link Popup', '🔗 Link highlighting AND temporary connections cleared');
        }
        
        /**
         * Schedule link highlighting clear with delay
         */
        scheduleHighlightClear() {
            this.clearHighlightTimeout();
            this.highlightTimeout = setTimeout(() => {
                this.clearLinkHighlighting();
            }, this.config.highlightClearDelay);
        }
        
        /**
         * Clear highlight timeout
         */
        clearHighlightTimeout() {
            if (this.highlightTimeout) {
                clearTimeout(this.highlightTimeout);
                this.highlightTimeout = null;
            }
        }
        
        /**
         * Show popup with link data
         */
        async showPopup(event, pageId, linkType, pageTitle) {
            if (!this.popup) {
                return;
            }
            
            // Position popup near cursor
            this.positionPopup(event);
            
            // Set loading state
            this.setLoadingState(linkType, pageTitle);
            
            // Show popup
            this.popup.addClass('visible');
            this.currentPopup = { pageId, linkType };
            
            try {
                // Fetch link data
                const linkData = await this.fetchLinkData(pageId);
                
                // Only update if this is still the current popup
                if (this.currentPopup && this.currentPopup.pageId === pageId) {
                    this.populatePopup(linkData, linkType, pageTitle);
                }
                
            } catch (error) {
                this.debug.error('Link Popup', 'Error fetching link data', error);
                
                // Only show error if this is still the current popup
                if (this.currentPopup && this.currentPopup.pageId === pageId) {
                    this.showError('Failed to load link data');
                }
            }
        }
        
        /**
         * Hide popup
         */
        hidePopup() {
            if (!this.popup) {
                return;
            }
            
            this.clearHoverTimeout();
            this.clearHideTimeout();
            
            this.popup.removeClass('visible');
            this.currentPopup = null;
            
            // Schedule highlighting clear (don't clear immediately to avoid conflicts)
            this.scheduleHighlightClear();
        }
        
        /**
         * Schedule popup hide
         */
        scheduleHide() {
            this.hideTimeout = setTimeout(() => {
                this.hidePopup();
            }, this.config.hideDelay);
        }
        
        /**
         * Position popup near the cursor/element
         */
        positionPopup(event) {
            const mouseX = event.pageX || event.clientX + window.pageXOffset;
            const mouseY = event.pageY || event.clientY + window.pageYOffset;
            
            // Get popup dimensions
            const popupWidth = 320; // min-width from CSS
            const popupHeight = 200; // estimated height
            
            // Calculate position with viewport boundaries
            let left = mouseX + 15;
            let top = mouseY - popupHeight / 2;
            
            // Adjust for right edge
            if (left + popupWidth > $(window).width()) {
                left = mouseX - popupWidth - 15;
            }
            
            // Adjust for top/bottom edges
            if (top < $(window).scrollTop()) {
                top = $(window).scrollTop() + 10;
            } else if (top + popupHeight > $(window).scrollTop() + $(window).height()) {
                top = $(window).scrollTop() + $(window).height() - popupHeight - 10;
            }
            
            this.popup.css({
                left: left + 'px',
                top: top + 'px'
            });
        }
        
        /**
         * Set loading state
         */
        setLoadingState(linkType, pageTitle) {
            // Update header
            const icon = this.popup.find('.slmm-link-popup-icon');
            const iconText = this.popup.find('.slmm-link-popup-icon-text');
            const titleText = this.popup.find('.slmm-link-popup-title-text');
            
            icon.removeClass('internal external').addClass(linkType);
            iconText.text(linkType === 'internal' ? 'I' : 'O');
            titleText.text(`${linkType === 'internal' ? 'Internal' : 'External'} Links`);
            
            // Show loading content
            const content = this.popup.find('.slmm-link-popup-content');
            content.html(`
                <div style="display: flex; align-items: center; justify-content: center; padding: 32px 16px; color: var(--slmm-text-secondary, #94A3B8); font-size: 12px;">
                    <div class="slmm-link-loading-spinner"></div>
                    Loading links...
                </div>
            `);
            
            // Clear actions
            this.popup.find('.slmm-link-popup-actions').empty();
            
            this.popup.addClass('loading');
        }
        
        /**
         * Populate popup with link data
         */
        populatePopup(linkData, linkType, pageTitle) {
            this.popup.removeClass('loading');
            
            const links = linkData[linkType];
            const content = this.popup.find('.slmm-link-popup-content');
            const actions = this.popup.find('.slmm-link-popup-actions');
            
            if (!links || links.count === 0) {
                this.showEmpty(linkType);
                return;
            }
            
            // Build content HTML
            let contentHTML = `
                <div class="slmm-link-summary">
                    <div class="slmm-link-count">
                        Found <span class="slmm-link-count-number">${links.count}</span> ${linkType} link${links.count !== 1 ? 's' : ''}
                        ${links.count > 10 ? ' (showing first 10)' : ''}
                    </div>
                </div>
                <ul class="slmm-link-list">
            `;
            
            links.links.forEach(link => {
                contentHTML += `
                    <li class="slmm-link-item">
                        <div class="slmm-link-bullet"></div>
                        <div class="slmm-link-details">
                            <div class="slmm-link-anchor">${this.escapeHtml(link.anchor)}</div>
                            <div class="slmm-link-url">${this.escapeHtml(link.url)}</div>
                            ${link.target_page ? `<div class="slmm-link-target">→ ${this.escapeHtml(link.target_page)}</div>` : ''}
                            ${link.domain ? `<div class="slmm-link-target">→ ${this.escapeHtml(link.domain)}</div>` : ''}
                        </div>
                    </li>
                `;
            });
            
            contentHTML += '</ul>';
            content.html(contentHTML);
            
            // Add action buttons
            if (links.links && links.links.length > 0) {
                // Prepare ALL URLs for copying
                const allUrls = links.links.map(link => link.url).join('\n');
                
                // Prepare 2-column format for Google Sheets (URL + Anchor Text)
                const sheetsFormat = links.links.map(link => `${link.url}\t${link.anchor || ''}`).join('\n');
                
                actions.html(`
                    <button class="slmm-link-popup-btn" data-action="copy-all-urls" data-text="${this.escapeHtml(allUrls)}">
                        📋 Copy URL
                    </button>
                    <button class="slmm-link-popup-btn" data-action="copy-sheets-format" data-text="${this.escapeHtml(sheetsFormat)}">
                        📝 Copy Anchors and URLs
                    </button>
                `);
                
                // Bind copy actions
                actions.find('[data-action="copy-all-urls"], [data-action="copy-sheets-format"]').on('click', (e) => {
                    const text = $(e.target).data('text');
                    this.copyToClipboard(text);
                });
            }
            
            // Note: Visual state management is handled by backend system, not popup
        }
        
        /**
         * Show empty state
         */
        showEmpty(linkType) {
            const content = this.popup.find('.slmm-link-popup-content');
            const actions = this.popup.find('.slmm-link-popup-actions');
            
            content.html(`
                <div class="slmm-link-popup-empty">
                    <div class="slmm-link-popup-empty-icon">🔗</div>
                    <div>No ${linkType} links found</div>
                </div>
            `);
            
            actions.empty();
            
            // Note: Visual state management is handled by backend system, not popup
        }
        
        /**
         * Show error state
         */
        showError(message) {
            this.popup.removeClass('loading');
            
            const content = this.popup.find('.slmm-link-popup-content');
            const actions = this.popup.find('.slmm-link-popup-actions');
            
            content.html(`
                <div class="slmm-link-popup-error">
                    ⚠️ ${this.escapeHtml(message)}
                </div>
            `);
            
            actions.empty();
            
            // Note: Visual state management is handled by backend system, not popup
        }
        
        /**
         * Fetch link data via AJAX (NO CACHING - always fresh)
         */
        async fetchLinkData(pageId) {
            this.debug.log('Link Popup', `Fetching FRESH link data for page ${pageId} (no caching)`);
            
            // Always fetch fresh from server with cache-busting parameter
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: slmmInterlinkingData.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'slmm_get_page_links',
                        page_id: pageId,
                        nonce: slmmInterlinkingData.nonce,
                        _cache_bust: Date.now() // Force fresh data
                    },
                    timeout: 10000,
                    success: (response) => {
                        if (response.success && response.data) {
                            this.debug.success('Link Popup', `Fresh link data received for page ${pageId}`);
                            this.debug.info('Link Tracking', 'Link data structure', {
                                internalCount: response.data.internal?.count || 0,
                                externalCount: response.data.external?.count || 0
                            });
                            resolve(response.data);
                        } else {
                            reject(new Error(response.data || 'Unknown error'));
                        }
                    },
                    error: (xhr, status, error) => {
                        reject(new Error(`AJAX error: ${status} - ${error}`));
                    }
                });
            });
        }
        
        /**
         * Copy text to clipboard
         */
        async copyToClipboard(text) {
            try {
                if (navigator.clipboard && navigator.clipboard.writeText) {
                    await navigator.clipboard.writeText(text);
                    this.showToast('Copied to clipboard');
                } else {
                    // Fallback for older browsers
                    const textarea = document.createElement('textarea');
                    textarea.value = text;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    this.showToast('Copied to clipboard');
                }
            } catch (error) {
                this.debug.error('Link Popup', 'Failed to copy to clipboard', error);
                this.showToast('Failed to copy');
            }
        }
        
        /**
         * Show toast notification
         */
        showToast(message) {
            // Simple toast implementation
            const toast = $(`<div style="position: fixed; bottom: 20px; right: 20px; background: var(--slmm-primary, #7C3AED); color: white; padding: 8px 16px; border-radius: 4px; z-index: 100100; font-size: 12px;">${this.escapeHtml(message)}</div>`);
            $('body').append(toast);
            
            setTimeout(() => {
                toast.fadeOut(200, () => toast.remove());
            }, 2000);
        }
        
        
        
        /**
         * REMOVED: updateIndicatorVisualState method
         * Visual state management is now handled exclusively by backend system.
         * Popup system is read-only and does not modify indicator states.
         */
        
        /**
         * Utility functions
         */
        clearHoverTimeout() {
            if (this.hoverTimeout) {
                clearTimeout(this.hoverTimeout);
                this.hoverTimeout = null;
            }
        }
        
        clearHideTimeout() {
            if (this.hideTimeout) {
                clearTimeout(this.hideTimeout);
                this.hideTimeout = null;
            }
        }
        
        isVisible() {
            return this.popup && this.popup.hasClass('visible');
        }
        
        escapeHtml(text) {
            if (typeof text !== 'string') return '';
            return text
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#x27;');
        }
    }
    
    // Initialize when DOM is ready
    $(document).ready(() => {
        // Create global instance
        window.slmmLinkPopup = new SLMM_LinkPopup();
        
        // Link popup system ready - using debug system if needed
    });
    
})(jQuery, d3);