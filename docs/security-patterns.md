# Security Implementation Patterns

**Defense-in-depth remains MANDATORY for plugin integrity.**

## 1. MANDATORY NONCE VERIFICATION (ABSOLUTE REQUIREMENT)

### AJAX Handler Nonce Pattern (CRITICAL - NEVER SKIP)
```php
// MANDATORY for ALL AJAX handlers - no exceptions
function slmm_handle_ajax_action() {
    // STEP 1: Verify nonce FIRST - before ANY processing
    if (!wp_verify_nonce($_POST['nonce'], 'slmm_action_nonce')) {
        wp_die('Security check failed', 'Security Error', array('response' => 403));
    }

    // STEP 2: Additional capability check
    if (!current_user_can('manage_options')) {
        wp_die('Insufficient permissions', 'Authorization Error', array('response' => 403));
    }

    // STEP 3: Authorization system integration
    if (!slmm_seo_check_visibility_authorization()) {
        wp_die('Plugin access denied', 'Authorization Error', array('response' => 403));
    }

    // Now safe to process request
    $sanitized_input = sanitize_textarea_field($_POST['input']);

    // Always return structured JSON response
    wp_send_json_success($result);
}
```

### Form Nonce Pattern (MANDATORY)
```php
// Creating forms with nonces
function slmm_render_settings_form() {
    echo '<form method="post" action="">';
    wp_nonce_field('slmm_settings_save', 'slmm_settings_nonce');
    // Form fields...
    echo '</form>';
}

// Processing form submissions
function slmm_process_settings_form() {
    if (isset($_POST['submit'])) {
        // MANDATORY nonce verification
        if (!wp_verify_nonce($_POST['slmm_settings_nonce'], 'slmm_settings_save')) {
            wp_die('Security check failed');
        }

        // Additional checks...
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        // Process form...
    }
}
```

### JavaScript Nonce Integration (REQUIRED)
```javascript
// Always include nonce in AJAX requests
jQuery.post(slmmGptPromptData.ajax_url, {
    action: 'slmm_ajax_action',
    nonce: slmmGptPromptData.nonce,  // MANDATORY - always include
    data: sanitizedData
}, function(response) {
    if (response.success) {
        // Handle success
    } else {
        // Handle error - could be security failure
        console.error('Request failed:', response.data);
    }
});
```

## 2. REQUIRED CAPABILITY CHECKING (ENFORCE STRICTLY)

### Standard Capability Pattern (MANDATORY)
```php
// ALWAYS check capabilities before ANY admin functionality
function slmm_admin_functionality() {
    // Primary capability check - manage_options required
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }

    // Secondary authorization check via plugin system
    if (!slmm_seo_check_visibility_authorization()) {
        wp_die('Plugin access denied by authorization system.');
    }

    // Proceed with admin functionality
}
```

### Menu Registration Capability Control (REQUIRED)
```php
// Proper capability control in menu registration
add_action('admin_menu', 'slmm_add_admin_menu');

function slmm_add_admin_menu() {
    add_options_page(
        'SLMM SEO Settings',
        'SLMM SEO',
        'manage_options',  // MANDATORY - restrict to admin users
        'slmm-seo-settings',
        'slmm_settings_page_callback'
    );
}

function slmm_settings_page_callback() {
    // MANDATORY - double-check capabilities in callback
    if (!current_user_can('manage_options')) {
        wp_die('Access denied');
    }

    // Additional plugin authorization
    if (!slmm_seo_check_visibility_authorization()) {
        wp_die('Plugin access denied');
    }

    // Render settings page
}
```

## 3. SQL INJECTION PREVENTION (ABSOLUTE REQUIREMENT)

### Database Query Pattern (MANDATORY - USE ONLY THIS APPROACH)
```php
// CORRECT - Always use prepared statements
function slmm_get_posts_by_content($search_term) {
    global $wpdb;

    // MANDATORY - Use prepared statements for ALL queries
    $results = $wpdb->get_results($wpdb->prepare(
        "SELECT ID, post_title, post_content
         FROM {$wpdb->posts}
         WHERE post_content LIKE %s
         AND post_status = 'publish'",
        '%' . $wpdb->esc_like($search_term) . '%'
    ));

    return $results;
}

// CORRECT - Multiple parameter preparation
function slmm_search_and_replace_content($search, $replace, $post_types) {
    global $wpdb;

    // Prepare IN clause safely
    $placeholders = implode(',', array_fill(0, count($post_types), '%s'));

    $query = $wpdb->prepare(
        "SELECT ID, post_content
         FROM {$wpdb->posts}
         WHERE post_content LIKE %s
         AND post_type IN ($placeholders)
         AND post_status = 'publish'",
        '%' . $wpdb->esc_like($search) . '%',
        ...$post_types
    );

    return $wpdb->get_results($query);
}
```

### NEVER DO (FORBIDDEN PATTERNS)
```php
// FORBIDDEN - Direct string interpolation
$results = $wpdb->get_results("SELECT * FROM {$wpdb->posts} WHERE post_title = '$user_input'");

// FORBIDDEN - sprintf without prepare
$query = sprintf("SELECT * FROM {$wpdb->posts} WHERE ID = %d", $id);
$results = $wpdb->get_results($query);

// FORBIDDEN - Building queries with concatenation
$query = "SELECT * FROM {$wpdb->posts} WHERE post_content LIKE '%" . $search . "%'";
```

## 4. XSS PREVENTION REQUIREMENTS (MANDATORY OUTPUT ESCAPING)

### Output Escaping Patterns (REQUIRED FOR ALL OUTPUT)
```php
// HTML content escaping (most common)
echo '<h2>' . esc_html($page_title) . '</h2>';
echo '<p>' . esc_html__('Description text', 'slmm-seo') . '</p>';

// Attribute escaping in HTML tags
echo '<input type="text" value="' . esc_attr($form_value) . '" />';
echo '<div class="' . esc_attr($css_class) . '">';

// URL escaping for links
echo '<a href="' . esc_url($external_link) . '">Link Text</a>';

// JavaScript data (use wp_localize_script preferred)
echo '<script>var data = ' . wp_json_encode($data) . ';</script>';

// For admin URLs (WordPress internal)
echo '<a href="' . esc_url(admin_url('admin.php?page=slmm-settings')) . '">Settings</a>';
```

### Safe Data Localization Pattern (PREFERRED METHOD)
```php
// CORRECT - Use wp_localize_script for JavaScript data
wp_localize_script('slmm-admin-script', 'slmmSecureData', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_admin_action'),
    'messages' => array(
        'success' => esc_html__('Operation completed successfully', 'slmm-seo'),
        'error' => esc_html__('An error occurred', 'slmm-seo')
    ),
    'settings' => array(
        'max_length' => absint($max_length),
        'enable_feature' => (bool) $enable_feature
    )
));
```

### Text Domain Requirements (MANDATORY)
```php
// ALWAYS use text domain for translatable strings
echo esc_html__('Settings saved successfully', 'slmm-seo');
echo esc_html_e('Error occurred during processing', 'slmm-seo');

// For sprintf patterns
echo sprintf(
    esc_html__('Processing %d of %d items', 'slmm-seo'),
    absint($current),
    absint($total)
);
```

## 5. AUTHORIZATION SYSTEM INTEGRATION (CRITICAL)

### Plugin Authorization Pattern (MANDATORY FOR ALL FEATURES)
```php
// MANDATORY initialization check in ALL feature classes
class SLMM_New_Feature {
    public function __construct() {
        // CRITICAL - Check authorization before ANY initialization
        if (!slmm_seo_check_visibility_authorization()) {
            return; // Silent fail - do not initialize
        }

        // Safe to initialize feature
        add_action('admin_init', array($this, 'init'));
        add_action('wp_ajax_slmm_new_feature', array($this, 'handle_ajax'));
    }

    public function init() {
        // Additional capability check in methods
        if (!current_user_can('manage_options')) {
            return;
        }

        // Initialize feature functionality
    }

    public function handle_ajax() {
        // Triple security layer for AJAX
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_new_feature_nonce')) {
            wp_die('Nonce verification failed');
        }

        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }

        if (!slmm_seo_check_visibility_authorization()) {
            wp_die('Plugin access denied');
        }

        // Safe to process AJAX request
    }
}
```

### Asset Loading Authorization (REQUIRED)
```php
// MANDATORY - Only load assets for authorized users
function slmm_enqueue_admin_assets() {
    // Check authorization before loading ANY assets
    if (!slmm_seo_check_visibility_authorization()) {
        return; // Do not load assets for unauthorized users
    }

    if (!current_user_can('manage_options')) {
        return; // Additional capability check
    }

    // Safe to load admin assets
    wp_enqueue_script(
        'slmm-admin-script',
        SLMM_SEO_PLUGIN_URL . 'assets/js/admin-script.js',
        array('jquery'),
        SLMM_SEO_VERSION,
        true
    );

    // MANDATORY - Include nonce in localized data
    wp_localize_script('slmm-admin-script', 'slmmAdminData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_admin_action'),
        'user_authorized' => slmm_seo_check_visibility_authorization()
    ));
}
add_action('admin_enqueue_scripts', 'slmm_enqueue_admin_assets');
```

## 6. CLIENT-SIDE API KEY HANDLING (ADMIN-CONTEXT GUIDELINES)

### Acceptable Patterns in Admin Context
```php
// ACCEPTABLE - API keys in admin-only, whitelisted environment
function slmm_localize_ai_settings() {
    // Only for authorized admin users
    if (!slmm_seo_check_visibility_authorization() || !current_user_can('manage_options')) {
        return;
    }

    wp_localize_script('slmm-ai-script', 'slmmAiConfig', array(
        'openai_api_key' => get_option('slmm_openai_api_key', ''),
        'anthropic_api_key' => get_option('slmm_anthropic_api_key', ''),
        // ACCEPTABLE because:
        // 1. Admin-only access (manage_options)
        // 2. Additional authorization layer (plugin whitelist)
        // 3. No public-facing exposure
        // 4. Required for client-side AI integration
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_ai_action')
    ));
}
```

### Security Notes for API Keys
```php
// RECOMMENDED - Additional protection measures
function slmm_protect_api_keys() {
    // 1. Option-level protection
    add_filter('pre_option_slmm_openai_api_key', function($value) {
        if (!slmm_seo_check_visibility_authorization()) {
            return ''; // Return empty for unauthorized users
        }
        return $value;
    });

    // 2. REST API protection (if used)
    add_filter('rest_authentication_errors', function($result) {
        if (strpos($_SERVER['REQUEST_URI'], '/slmm/') !== false) {
            if (!slmm_seo_check_visibility_authorization()) {
                return new WP_Error('rest_forbidden', 'Access denied', array('status' => 403));
            }
        }
        return $result;
    });
}
```

## 7. SECURITY VALIDATION CHECKLIST (MANDATORY VERIFICATION)

### Pre-Implementation Security Review (REQUIRED)
**BEFORE implementing any new feature, verify ALL items:**

#### Input Security Checklist
- [ ] **All user inputs sanitized** using appropriate WordPress functions
- [ ] **SQL queries use prepared statements** - NO string concatenation
- [ ] **Nonce verification implemented** for all forms and AJAX calls
- [ ] **Capability checks enforced** (`manage_options` minimum)
- [ ] **Plugin authorization integrated** (`slmm_seo_check_visibility_authorization()`)

#### Output Security Checklist
- [ ] **All output escaped** using `esc_html()`, `esc_attr()`, `esc_url()`
- [ ] **JavaScript data properly localized** using `wp_localize_script()`
- [ ] **Text domains used consistently** for all translatable strings
- [ ] **JSON data encoded safely** using `wp_json_encode()`
- [ ] **Admin URLs escaped** using `esc_url(admin_url())`

#### Authorization Security Checklist
- [ ] **Feature initialization protected** by authorization check
- [ ] **Asset loading restricted** to authorized users only
- [ ] **AJAX endpoints secured** with triple security layer
- [ ] **Menu items capability-controlled** (`manage_options`)
- [ ] **Settings pages protected** with double capability checks

#### WordPress Integration Checklist
- [ ] **Hooks used correctly** with proper priority and parameters
- [ ] **Options stored securely** with appropriate sanitization
- [ ] **Transients properly prefixed** and secured if used
- [ ] **Cron jobs authorized** if scheduled tasks implemented
- [ ] **Plugin deactivation handled** securely if cleanup needed

### Security Testing Protocol (MANDATORY)
```php
// REQUIRED - Test each security layer independently
function slmm_security_test_suite() {
    // Test 1: Nonce verification
    // - Submit AJAX without nonce → Should fail
    // - Submit with invalid nonce → Should fail
    // - Submit with valid nonce → Should succeed

    // Test 2: Capability checks
    // - Access as subscriber → Should fail
    // - Access as editor → Should fail
    // - Access as admin → Should proceed to authorization check

    // Test 3: Authorization system
    // - Access as non-whitelisted admin → Should fail
    // - Access as whitelisted admin → Should succeed

    // Test 4: Input sanitization
    // - Submit malicious scripts → Should be escaped/sanitized
    // - Submit SQL injection attempts → Should be parameterized
    // - Submit XSS payloads → Should be escaped in output
}
```

## 8. REQUIRED SECURITY TESTING PATTERNS (VALIDATION REQUIREMENTS)

### Browser Console Security Tests (MANDATORY)
```javascript
// Test 1: Verify authorization data availability
console.log('User authorized:', slmmAdminData?.user_authorized);
console.log('Nonce available:', slmmAdminData?.nonce);

// Test 2: Test AJAX security enforcement
jQuery.post(slmmAdminData.ajax_url, {
    action: 'slmm_test_action',
    // Deliberately omit nonce to test security
    test_data: 'security_test'
}, function(response) {
    console.log('No nonce test:', response); // Should fail
});

// Test 3: Test with invalid nonce
jQuery.post(slmmAdminData.ajax_url, {
    action: 'slmm_test_action',
    nonce: 'invalid_nonce_test',
    test_data: 'security_test'
}, function(response) {
    console.log('Invalid nonce test:', response); // Should fail
});

// Test 4: Test with valid nonce
jQuery.post(slmmAdminData.ajax_url, {
    action: 'slmm_test_action',
    nonce: slmmAdminData.nonce,
    test_data: 'security_test'
}, function(response) {
    console.log('Valid request test:', response); // Should succeed if authorized
});
```

### PHP Security Validation Functions (REQUIRED HELPERS)
```php
// MANDATORY - Include in all new feature files
function slmm_validate_security_context($nonce_action = '') {
    $errors = array();

    // Check 1: WordPress capability
    if (!current_user_can('manage_options')) {
        $errors[] = 'Insufficient WordPress capabilities';
    }

    // Check 2: Plugin authorization
    if (!slmm_seo_check_visibility_authorization()) {
        $errors[] = 'Plugin authorization failed';
    }

    // Check 3: Nonce verification (if provided)
    if (!empty($nonce_action) && !wp_verify_nonce($_POST['nonce'], $nonce_action)) {
        $errors[] = 'Nonce verification failed';
    }

    // Check 4: AJAX context validation
    if (wp_doing_ajax() && empty($_POST['nonce'])) {
        $errors[] = 'AJAX request missing nonce';
    }

    return empty($errors) ? true : $errors;
}

// Usage in feature methods
function slmm_feature_ajax_handler() {
    $security_check = slmm_validate_security_context('slmm_feature_action');

    if ($security_check !== true) {
        wp_send_json_error(array(
            'message' => 'Security validation failed',
            'errors' => $security_check
        ));
    }

    // Safe to proceed with feature logic
}
```

### Security Documentation Requirements (MANDATORY)
**Every new feature MUST include security documentation:**
```php
/**
 * Feature Security Documentation
 *
 * Security Measures Implemented:
 * 1. Nonce verification: [action_name]
 * 2. Capability requirement: manage_options
 * 3. Plugin authorization: slmm_seo_check_visibility_authorization()
 * 4. Input sanitization: [list sanitization functions used]
 * 5. Output escaping: [list escaping functions used]
 *
 * Tested Attack Vectors:
 * - XSS payload injection: PROTECTED via esc_html()
 * - SQL injection attempts: PROTECTED via $wpdb->prepare()
 * - CSRF attacks: PROTECTED via wp_nonce_field()
 * - Unauthorized access: PROTECTED via authorization layers
 *
 * Admin-Context Justification:
 * - Feature restricted to manage_options capability
 * - Additional plugin-level authorization required
 * - No public-facing endpoints exposed
 * - Client-side API keys acceptable in this secured context
 */
```

## SECURITY IMPLEMENTATION PRIORITY ORDER (MANDATORY SEQUENCE)

### Phase 1: Foundation Security (IMPLEMENT FIRST)
1. **Authorization checks** - Plugin and WordPress capability verification
2. **Nonce verification** - All forms and AJAX endpoints
3. **Input sanitization** - All user data processing

### Phase 2: Data Security (IMPLEMENT SECOND)
1. **SQL injection prevention** - Prepared statements for all queries
2. **Output escaping** - All data display and HTML generation
3. **Safe data localization** - JavaScript data passing

### Phase 3: Integration Security (IMPLEMENT THIRD)
1. **Asset loading protection** - Authorized users only
2. **Menu and page access control** - Capability enforcement
3. **Settings storage security** - Proper option handling

### Phase 4: Testing and Validation (IMPLEMENT LAST)
1. **Security test implementation** - Browser and PHP validation
2. **Documentation completion** - Security measures documentation
3. **Attack vector testing** - Manual security validation

**🚨 CRITICAL REMINDER: In this admin-only, whitelisted environment, security measures protect plugin integrity and prevent privilege escalation rather than public-facing attacks. However, ALL security patterns remain MANDATORY for defense-in-depth and code quality.**