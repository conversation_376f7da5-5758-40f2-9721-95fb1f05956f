---
task: h-implement-content-segmentation
branch: feature/content-segmentation
status: pending
created: 2025-01-09
modules: [slmm-direct-editor, tinymce-integration, ai-processing, url-renderer]
---

# Content Segmentation System for AI-Driven Link Suggestions

## Problem/Goal
Implement a visual content segmentation system in the TinyMCE editor that divides articles into three distinct sections (TOP, MIDDLE, BOTTOM) to enable AI-powered link suggestions that focus only on specific content portions. This addresses performance and relevance issues by preventing AI from processing entire articles when only specific sections are needed for link analysis.

## Success Criteria
- [x] Visual content segmentation with two user-insertable divider lines
- [x] TOP section: 50% yellow overlay for high-priority/parent links (enhanced visibility)
- [x] MIDDLE section: 50% red overlay for child/semantic links (enhanced visibility)
- [x] BOTTOM section: neutral (no overlay) for sibling/external links
- [x] TinyMCE toolbar button for inserting segmentation markers (replaced with sidebar button)
- [x] Cursor-position-based marker insertion functionality
- [x] Robust containerization system that reliably isolates content sections
- [x] AI integration points that can access specific sections independently
- [x] Flexible and minimal-overhead implementation
- [x] Visual feedback system showing section boundaries
- [x] Content parsing system that extracts specific segments for AI processing

## Context Files & Analysis

### TinyMCE Integration Patterns
- @assets/js/slmm-direct-editor.js:2168-2267 # TinyMCE initialization with dark theme
- @snippets/seo_text_helper_2_3.php:117-135 # Custom editor buttons injection pattern
- @src/contentDuplication.js:91-98 # Toolbar button addition pattern
- @src/brokenLinkDetector.js:132-139 # Professional toolbar button implementation

### Direct Editor Architecture
- @assets/js/slmm-direct-editor.js:1139-1398 # Modal structure with resizable layout
- @assets/js/slmm-direct-editor.js:415-474 # Event handler setup patterns
- @assets/js/slmm-direct-editor.js:944-984 # Button injection and DOM manipulation
- @assets/css/slmm-direct-editor.css:1-100 # Modal overlay and layout styling

### AJAX Handler Patterns
- @includes/ajax/url-renderer-handler.php:49-100 # Security validation pattern
- @includes/ai-integration/openai-integration.php:15-47 # AJAX action registration
- @includes/bulk-creation/class-slmm-quickbulk-ajax-handler.php # Content processing

### AI Integration Points
- @includes/ai-integration/openai-integration.php:49+ # Content generation methods
- @includes/ai-integration/anthropic-integration.php # Claude integration pattern
- @includes/ai-integration/openrouter-integration.php # Multi-provider support

### Visual Styling Patterns
- @assets/css/slmm-settings.css:2070-2074 # Professional notice styling with rgba overlays
- @assets/css/lorem-detector.css:220-240 # Status badge patterns with background overlays
- @assets/css/slmm-direct-editor.css:23-27 # Modal overlay with backdrop-filter
- @assets/css/quickbulk-canvas.css:568-580 # Interactive element hover states

## CRITICAL IMPLEMENTATION DETAILS

### Comment-Based Marker System (USER REQUIREMENT)
**IMPORTANT**: Content segmentation markers must be HTML comments that are:
- Only visible to @assets/js/slmm-direct-editor.js for processing
- NOT rendered in final HTML output  
- Used purely for backend content parsing and visual editor guidance

```html
<!-- SLMM_SEGMENT_TOP_START -->
Content for TOP section (yellow overlay)
<!-- SLMM_SEGMENT_TOP_END -->
<!-- SLMM_SEGMENT_MIDDLE_START -->  
Content for MIDDLE section (red overlay)
<!-- SLMM_SEGMENT_MIDDLE_END -->
<!-- SLMM_SEGMENT_BOTTOM_START -->
Content for BOTTOM section (no overlay)
<!-- SLMM_SEGMENT_BOTTOM_END -->
```

### Existing TinyMCE Integration Architecture

#### TinyMCE Initialization Pattern (Line 2168-2267)
```javascript
tinymce.init({
    selector: '#' + editorId,
    height: 400,
    menubar: false,
    toolbar1: this.config.tinymce_config.toolbar1,
    toolbar2: this.config.tinymce_config.toolbar2,
    plugins: 'lists,link,textcolor,paste,tabfocus,wordpress',
    body_class: 'slmm-direct-editor-content',
    base_z_index: 10010,
    content_style: `/* Dark theme CSS */`,
    setup: function(editor) {
        // Content change tracking
        editor.on('input change paste keyup', function() {
            self.onContentChange();
        });
        editor.on('init', function() {
            self.updateStatus('ready', 'Ready to edit');
        });
    }
});
```

#### Custom Toolbar Button Pattern (seo_text_helper_2_3.php:117-135)  
```javascript
if ($('#custom-editor-buttons').length === 0) {
    var buttonHTML = '\
    <div id="custom-editor-buttons" style="margin-bottom: 10px; display: inline-block;">\
        <button id="clean-and-title" class="button custom-button">Clean&Title</button>\
        <!-- Add segmentation button here -->\
    </div>';
    $('#wp-content-editor-tools').append(buttonHTML);
}
```

#### Professional Button Implementation (brokenLinkDetector.js:132-139)
```javascript
addToolbarButton: function() {
    setTimeout(function() {
        if ($('#broken-links-button').length === 0 && $('#custom-editor-buttons').length > 0) {
            var buttonHTML = '<button id="broken-links-button" class="button custom-button">Links<span class="broken-links-indicator active valid" style="background-color: #00a32a;"></span><span class="broken-links-count"></span></button>';
            $('#custom-editor-buttons').append(buttonHTML);
        }
    }, 1000);
}
```

### AJAX Security Pattern (url-renderer-handler.php:49-100)
```php
public function handle_fetch_url_content() {
    // Nonce verification  
    if (!wp_verify_nonce($_POST['nonce'], 'slmm_direct_editor_nonce')) {
        wp_send_json_error(array(
            'message' => 'Security check failed',
            'code' => 'invalid_nonce'
        ));
        return;
    }
    
    // Capability check
    if (!current_user_can('edit_posts')) {
        wp_send_json_error(array(
            'message' => 'Insufficient permissions',
            'code' => 'insufficient_permissions'
        ));
        return;
    }
    
    // Input sanitization
    $content = sanitize_textarea_field($_POST['content']);
}
```

### Modal Architecture Pattern (slmm-direct-editor.js:1139-1398)
```javascript  
createModalStructure: function(postId) {
    var modalHtml = `
        <div class="slmm-direct-editor-overlay">
            <div class="slmm-direct-editor-modal">
                <div class="slmm-direct-editor-header">
                    <h2 class="slmm-direct-editor-title">
                        <span class="slmm-direct-editor-post-info">Loading...</span>
                    </h2>
                    <div class="slmm-direct-editor-controls">
                        <div class="slmm-direct-editor-status">Loading...</div>
                        <button class="slmm-direct-editor-close">&times;</button>
                    </div>
                </div>
                <div class="slmm-direct-editor-main">
                    <div class="slmm-direct-editor-editor-area">
                        <!-- TinyMCE editor container -->
                    </div>
                    <div class="slmm-resize-handle"></div>  
                    <div class="slmm-direct-editor-sidebar">
                        <!-- AI tools and controls -->
                    </div>
                </div>
            </div>
        </div>`;
}
```

### Professional Overlay Styling Pattern
```css
/* Based on existing rgba overlay patterns */
.slmm-content-section-top {
    background: rgba(255, 255, 0, 0.1); /* Yellow 10% opacity */
    position: relative;
    border-left: 3px solid #fbbf24;
}

.slmm-content-section-middle {  
    background: rgba(255, 0, 0, 0.1); /* Red 10% opacity */
    position: relative;
    border-left: 3px solid #ef4444;
}

.slmm-content-divider {
    background: rgba(59, 130, 246, 0.1); /* Blue accent */
    border: 1px dashed #3b82f6;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 16px 0;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
    color: #3b82f6;
    user-select: none;
}
```

### Event Handler Setup Pattern (slmm-direct-editor.js:415-474)
```javascript
setupUrlRendererHandlers: function() {
    var self = this;
    
    // Segmentation button click
    $(document).on('click', '#slmm-segment-btn', function(e) {
        e.preventDefault();
        e.stopPropagation();
        self.handleSegmentInsertion();
    });
    
    // Segment overlay toggle
    $(document).on('click', '.slmm-segment-toggle', function(e) {
        e.preventDefault();
        self.toggleSectionOverlays();
    });
}
```

### AI Integration Architecture (openai-integration.php:15-47)
```php
public function initialize() {
    add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    add_action('wp_ajax_slmm_extract_content_section', array($this, 'ajax_extract_section'));
    add_action('wp_ajax_slmm_process_section_ai', array($this, 'ajax_process_section_ai'));
}

public function enqueue_scripts($hook) {
    if ('post.php' != $hook && 'post-new.php' != $hook) {
        return;
    }
    
    $localized_data = array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_content_segmentation'),
        'segment_prompts' => array(
            'top' => 'Focus on parent/high-priority links for this TOP section',
            'middle' => 'Suggest child/semantic links for this MIDDLE section', 
            'bottom' => 'Recommend sibling/external links for this BOTTOM section'
        )
    );
}
```

### Content Processing Pattern
Based on URL renderer content extraction, section processing will follow:
```php
class SLMM_Content_Segmentation {
    public function extract_section($content, $section_type) {
        $pattern = '/<!-- SLMM_SEGMENT_' . strtoupper($section_type) . '_START -->(.*?)<!-- SLMM_SEGMENT_' . strtoupper($section_type) . '_END -->/s';
        preg_match($pattern, $content, $matches);
        return isset($matches[1]) ? trim($matches[1]) : '';
    }
    
    public function apply_visual_overlays($content) {
        // Replace comment markers with visual dividers for editor display only
        $content = preg_replace('/<!-- SLMM_SEGMENT_TOP_START -->/', '<div class="slmm-content-section-top">', $content);
        $content = preg_replace('/<!-- SLMM_SEGMENT_TOP_END -->/', '</div>', $content);
        // Similar for MIDDLE and BOTTOM sections
        return $content;
    }
}
```

## Technical Requirements

### Content Segmentation Architecture (FOCUSED SCOPE)
1. **Visual Segmentation System**
   - Two insertable divider lines creating three sections
   - Section 1 (TOP): Yellow overlay (rgba(255, 255, 0, 0.1))
   - Section 2 (MIDDLE): Red overlay (rgba(255, 0, 0, 0.1))  
   - Section 3 (BOTTOM): No overlay (neutral)
   - Clean visual boundaries that don't interfere with content editing

2. **TinyMCE Integration**
   - Custom toolbar button for marker insertion
   - Cursor-position-aware insertion logic
   - Non-intrusive marker elements that don't break content flow
   - Proper marker management (prevent duplicates, allow repositioning)

3. **Content Parsing System**
   - Reliable extraction of content by section
   - HTML-aware parsing that preserves formatting
   - Content isolation that prevents cross-section bleeding
   - Simple PHP class methods for section extraction

4. **Basic Integration Hooks**
   - Content segmentation available to existing AI system
   - Section extraction methods callable from existing `executePromptDirectly()` function
   - No changes to existing slmmGptPromptData system

### Implementation Strategy

#### Phase 1: Visual Segmentation Foundation
1. **Marker System Design**
   ```html
   <!-- Divider markers with unique IDs and visual styling -->
   <div class="slmm-content-divider slmm-divider-top" data-section-boundary="top">
       <div class="slmm-divider-line"></div>
       <div class="slmm-divider-label">TOP SECTION</div>
   </div>
   
   <div class="slmm-content-divider slmm-divider-bottom" data-section-boundary="bottom">
       <div class="slmm-divider-line"></div>
       <div class="slmm-divider-label">BOTTOM SECTION</div>
   </div>
   ```

2. **CSS Overlay System**
   ```css
   .slmm-content-section-top {
       background: rgba(255, 255, 0, 0.1);
       position: relative;
   }
   .slmm-content-section-middle {
       background: rgba(255, 0, 0, 0.1);
       position: relative;
   }
   ```

#### Phase 2: TinyMCE Toolbar Integration
1. **Custom Toolbar Button**
   - Add button to TinyMCE toolbar
   - Icon: Segmentation/divider symbol
   - Tooltip: "Insert Content Divider"
   - Keyboard shortcut: Ctrl+Shift+D

2. **Insertion Logic**
   - Detect cursor position in content
   - Insert appropriate marker (top or bottom based on existing markers)
   - Prevent invalid marker placement
   - Auto-apply section overlays after insertion

#### Phase 3: Content Extraction System
1. **Section Parser Class**
   ```php
   class SLMM_Content_Segmentation {
       public function extract_section($content, $section) {
           // Extract TOP, MIDDLE, or BOTTOM section
           // Return clean HTML for AI processing
       }
       
       public function get_section_boundaries($content) {
           // Identify marker positions
           // Return section start/end points
       }
   }
   ```

2. **AJAX Endpoints**
   - `slmm_get_section_content` - Extract specific section
   - `slmm_save_segmented_content` - Save with markers
   - `slmm_validate_markers` - Ensure proper marker placement

#### Phase 4: Basic Integration Hooks
1. **Content Section Extraction**
   - Add section parameter to content extraction methods
   - Simple PHP functions that can be called from existing AI handlers
   - Return clean HTML sections for existing prompt execution system

2. **Integration Points**
   - Hook into existing `executePromptDirectly()` function
   - Optional section parameter for AI processing
   - No changes to current slmmGptPromptData structure

### File Structure and Size Management (MINIMAL APPROACH)
```
includes/
├── content-segmentation/
│   ├── class-content-segmentation.php        (~350 lines) - Core segmentation logic
│   └── class-tinymce-segmentation.php        (~300 lines) - TinyMCE button/markers  
├── ajax/
│   └── segmentation-handler.php              (~150 lines) - Basic AJAX endpoints
assets/
├── js/
│   └── tinymce-segmentation.js               (~400 lines) - Frontend functionality
└── css/
    └── content-segmentation.css              (~100 lines) - Visual overlays
```

### Integration Points with Existing System (ZERO CHANGES TO EXISTING CODE)
1. **ADDITIVE ONLY Integration**
   - ADD segmentation toolbar button WITHOUT modifying existing buttons
   - ADD new event handlers WITHOUT touching existing handlers  
   - ADD visual overlay CSS WITHOUT modifying existing styles

2. **COMPLETE Existing AI System Preservation**
   - ALL existing `executePromptDirectly()` functionality UNCHANGED
   - ALL existing slmmGptPromptData structure UNTOUCHED
   - ALL existing keyboard shortcuts PRESERVED exactly as-is
   - NEW section extraction functions are OPTIONAL additions only

3. **NON-INVASIVE TinyMCE Integration**
   - ADD new toolbar button using existing registration patterns
   - PRESERVE all existing TinyMCE initialization
   - NEW plugin loads ALONGSIDE existing functionality - NO conflicts

## Risk Factors and Mitigation
1. **Content Integrity Risk**: Markers interfering with content
   - Mitigation: Use non-printing, CSS-based markers
   - Fallback: Clean marker removal on content save

2. **Performance Risk**: Additional overhead from overlays
   - Mitigation: CSS-only overlays, minimal DOM manipulation
   - Monitoring: Performance benchmarks before/after

3. **TinyMCE Compatibility Risk**: Plugin conflicts
   - Mitigation: Namespace all custom functionality
   - Testing: Comprehensive plugin compatibility testing

## User Experience Flow
1. User opens Direct Editor for post
2. Clicks segmentation button in TinyMCE toolbar
3. Cursor-positioned marker insertion divides content
4. Visual overlays immediately show section boundaries
5. User positions second marker to complete segmentation
6. AI link suggestions work on individual sections
7. Content hierarchy guides appropriate link placement

## Testing Requirements
1. **Marker Insertion Testing**
   - Various cursor positions
   - Content with different HTML structures
   - Edge cases (empty content, complex formatting)

2. **Content Extraction Testing**
   - Verify clean section separation
   - HTML preservation during parsing
   - Performance with large content blocks

3. **AI Integration Testing**
   - Section-specific AI responses
   - Content accuracy in extracted sections
   - Integration with existing prompt system

## Future Enhancement Possibilities
1. **Dynamic Section Sizing**: Adjustable overlay intensities
2. **Section Analytics**: Track link performance by section
3. **Advanced Segmentation**: More than three sections
4. **Template System**: Predefined segmentation patterns
5. **Collaborative Editing**: Multi-user segment assignment

## WordPress Integration Requirements (PURELY ADDITIVE - NO EXISTING CODE CHANGES)

### Plugin.php Integration Pattern (ADD ONLY - MODIFY NOTHING)
```php
// ONLY ADD these new file includes AFTER all existing includes
require_once __DIR__ . '/includes/content-segmentation/class-content-segmentation.php';
require_once __DIR__ . '/includes/content-segmentation/class-tinymce-segmentation.php';
require_once __DIR__ . '/includes/ajax/segmentation-handler.php';

// ONLY ADD these initializations AFTER existing initializations in slmm_seo_plugin_init()
// DO NOT modify the existing function signature or any existing code within it
function slmm_seo_plugin_init() {
    // ... ALL EXISTING CODE REMAINS EXACTLY THE SAME ...
    
    // ONLY ADD these lines at the very end of the existing function:
    if (class_exists('SLMM_Content_Segmentation')) {
        new SLMM_Content_Segmentation();
    }
    if (class_exists('SLMM_TinyMCE_Segmentation')) {
        new SLMM_TinyMCE_Segmentation();
    }
    if (class_exists('SLMM_Segmentation_Handler')) {
        new SLMM_Segmentation_Handler();
    }
}
```

### Asset Enqueue Pattern (Following Direct Editor Integration)
```php
// In slmm-seo-plugin.php enqueue functions
function slmm_enqueue_direct_editor_assets() {
    // Existing assets...
    
    // Content segmentation assets
    wp_enqueue_script(
        'slmm-content-segmentation',
        SLMM_SEO_PLUGIN_URL . 'assets/js/content-segmentation.js',
        array('jquery', 'slmm-direct-editor'),
        SLMM_SEO_VERSION,
        true
    );
    
    wp_enqueue_style(
        'slmm-content-segmentation',
        SLMM_SEO_PLUGIN_URL . 'assets/css/content-segmentation.css',
        array('slmm-direct-editor'),
        SLMM_SEO_VERSION
    );
    
    // Localize segmentation data
    wp_localize_script('slmm-content-segmentation', 'slmmSegmentationData', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_content_segmentation'),
        'overlays' => array(
            'top' => array(
                'color' => 'rgba(255, 255, 0, 0.1)',
                'border' => '#fbbf24',
                'label' => 'TOP SECTION'
            ),
            'middle' => array(
                'color' => 'rgba(255, 0, 0, 0.1)', 
                'border' => '#ef4444',
                'label' => 'MIDDLE SECTION'
            ),
            'bottom' => array(
                'color' => 'transparent',
                'border' => 'transparent', 
                'label' => 'BOTTOM SECTION'
            )
        )
    ));
}
```

### Integration with Existing Direct Editor Architecture

#### TinyMCE Plugin Registration (Extending slmm-direct-editor.js:2168+)
```javascript
// Add to existing TinyMCE plugins configuration
plugins: 'lists,link,textcolor,paste,tabfocus,wordpress,slmm_segmentation',

// Extend toolbar configuration  
toolbar1: this.config.tinymce_config.toolbar1 + ' | slmm_segment_button',

// Add to setup function after existing event handlers
setup: function(editor) {
    // ... existing setup code ...
    
    // Register segmentation plugin
    editor.addCommand('slmmInsertSegment', function() {
        self.insertContentSegment(editor);
    });
    
    // Register toolbar button
    editor.addButton('slmm_segment_button', {
        text: 'Segment',
        icon: 'wp_code', // WordPress dashicons
        tooltip: 'Insert Content Divider',
        cmd: 'slmmInsertSegment'
    });
    
    // Monitor content changes for overlay updates
    editor.on('NodeChange', function() {
        self.updateSegmentOverlays(editor);
    });
}
```

### URL Renderer Enhancement Pattern
```php  
// Extend includes/ajax/url-renderer-handler.php
class SLMM_URL_Renderer_Handler {
    // Add to existing constructor
    public function __construct() {
        // ... existing actions ...
        add_action('wp_ajax_slmm_process_content_section', array($this, 'handle_section_processing'));
    }
    
    public function handle_section_processing() {
        // Follow existing security pattern
        if (!wp_verify_nonce($_POST['nonce'], 'slmm_direct_editor_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));  
            return;
        }
        
        $content = sanitize_textarea_field($_POST['content']);
        $section = sanitize_text_field($_POST['section']); // 'top', 'middle', 'bottom'
        
        // Extract specific section using segmentation parser
        $parser = new SLMM_Content_Segmentation();
        $section_content = $parser->extract_section($content, $section);
        
        wp_send_json_success(array(
            'section' => $section,
            'content' => $section_content,
            'length' => strlen($section_content)
        ));
    }
}
```

### AI Integration Enhancement (Following openai-integration.php:15-47)
```php
// Extend AI providers with section-aware processing
class SLMM_OpenAI_Integration {
    public function initialize() {
        // ... existing actions ...
        add_action('wp_ajax_slmm_generate_section_links', array($this, 'ajax_generate_section_links'));
    }
    
    public function ajax_generate_section_links() {
        // Security checks following existing pattern...
        
        $content = sanitize_textarea_field($_POST['content']);
        $section = sanitize_text_field($_POST['section']);
        
        // Section-specific prompts
        $prompts = array(
            'top' => 'Analyze this TOP section and suggest high-priority parent page links',
            'middle' => 'Analyze this MIDDLE section and suggest child/semantic links',
            'bottom' => 'Analyze this BOTTOM section and suggest sibling/external links'
        );
        
        $prompt = $prompts[$section] . "\n\nContent: " . $content;
        $result = $this->generate_content($prompt, 'gpt-4o', 500);
        
        wp_send_json_success(array(
            'section' => $section,
            'suggestions' => $result,
            'prompt_used' => $prompt
        ));
    }
}
```

## Architectural Constraints & Integration Points

### Critical Requirements (Following CLAUDE.md Guidelines)
1. **File Size Limit**: Each PHP file MUST be under 800 lines
2. **Plugin.php Integration**: All new files properly registered  
3. **Dual-System Compatibility**: Works with both button and shortcut systems
4. **Authorization Integration**: Respects slmm_seo_check_visibility_authorization()
5. **WordPress Security**: Nonces, capability checks, input sanitization
6. **Asset Loading**: Proper dependency management and version control

### TinyMCE Dark Theme Compatibility
Must integrate with existing content_style configuration (lines 2182-2235):
```css
/* Content segmentation overlays must work with dark theme */
.slmm-content-section-top {
    background: rgba(255, 255, 0, 0.1) !important;
    border-left: 3px solid #fbbf24;
}

.slmm-content-section-middle {
    background: rgba(255, 0, 0, 0.1) !important; 
    border-left: 3px solid #ef4444;
}

/* Ensure overlays don't interfere with text selection */
::selection { background: #8b5cf6 !important; color: white !important; }
```

### Performance Considerations
- **Lazy Loading**: Initialize segmentation only when Direct Editor opens
- **Event Delegation**: Use existing $(document).on() pattern from line 420+
- **Memory Efficiency**: Reuse existing modal and sidebar infrastructure
- **Processing Optimization**: Cache segment extraction results

### Error Handling Pattern (Following url-renderer-handler.php)
```javascript
// Client-side error handling
function handleSegmentationError(xhr, textStatus, errorThrown) {
    console.error('Segmentation Error:', {
        status: xhr.status,
        statusText: textStatus,
        error: errorThrown,
        response: xhr.responseJSON
    });
    
    // User-friendly error display
    self.updateStatus('error', 'Segmentation failed - ' + 
        (xhr.responseJSON?.message || 'Unknown error'));
}
```

## Development Priority: HIGH
This feature directly supports the AI-driven link suggestion system and addresses performance concerns with processing large content blocks. The visual segmentation system will significantly improve AI accuracy and user workflow efficiency.

## FINAL SCOPE CLARIFICATION
**THIS TASK IS ONLY ABOUT:**
1. Adding visual divider lines in TinyMCE content
2. Adding colored section overlays (yellow, red, neutral)
3. Adding a toolbar button to insert dividers
4. Adding PHP functions to extract content by section
5. Making segmented content available to existing AI system

**THIS TASK DOES NOT:**
1. Modify any existing AI integration code
2. Change existing keyboard shortcuts or executePromptDirectly()
3. Alter existing slmmGptPromptData structure  
4. Modify existing TinyMCE initialization
5. Change any existing CSS styles or JavaScript handlers
6. Modify any existing file in any way

**INTEGRATION APPROACH:**
- Existing AI system can optionally use new section extraction functions
- All existing functionality remains 100% unchanged and functional
- New system runs completely independently alongside existing features

## IMPLEMENTATION COMPLETED ✅

### Enhanced Button Visibility Solution
The overlay toggle button (👁️) was initially invisible due to styling issues. **Fixed with enhanced visibility:**

**Changes Made:**
1. **More prominent styling** - Added `rgba(255,255,255,0.1)` background and border instead of transparent
2. **Better color contrast** - Using `#f0f0f1` instead of `#999` for better visibility in dark themes
3. **Larger size** - Added `min-width: 28px; min-height: 28px` for touch-friendly interaction
4. **Emoji eye icon** - Using `👁️` emoji instead of tiny SVG for better visibility across all displays
5. **Forced display** - Added `!important` to `inline-flex` to prevent CSS conflicts with theme styles

**Why This Works:**
- **Visibility Issue Solved**: The previous button was too small (tiny SVG) and too transparent (no background, light gray)
- **Cross-Platform Compatibility**: Emoji icons render consistently across all browsers and devices
- **Dark Theme Optimized**: Proper contrast ratios ensure visibility in Direct Editor's dark interface
- **CSS Conflict Resolution**: `!important` declarations prevent theme styles from hiding the button

**Testing Verification:**
```javascript
// Verify button exists and is clickable
$('.slmm-overlay-toggle').click();
// Should toggle overlays and show status message
```

### TinyMCE Protection Issue Resolution
**Problem:** HTML comments were being wrapped in `<!--mce:protected %3C%21--%20SLMM_SEGMENT_[...]%20--%3E-->` making them unusable.

**Root Cause:** TinyMCE's `insertContent()` method treats HTML comments as potentially unsafe content and wraps them with protection.

**Solution Implemented:**
1. **Direct Content Manipulation** - Replaced `editor.insertContent()` with `editor.setContent()` using string manipulation
2. **Raw HTML Insertion** - Used `editor.execCommand('mceInsertRawHTML')` for click-based insertion to bypass protection
3. **Comprehensive Cleanup** - Enhanced clearing function to remove both clean and protected markers

**Technical Implementation:**
```javascript
// OLD (problematic): TinyMCE protection
editor.insertContent('<!-- SLMM_SEGMENT_TOP_END -->');

// NEW (clean): Direct insertion without protection
editor.execCommand('mceInsertRawHTML', false, '<!-- SLMM_SEGMENT_TOP_END -->');
```

### Click-Based Insertion System
**Problem:** Markers were being inserted at random positions instead of where user clicked.

**Solution:** Implemented proper click-to-cursor positioning:
- TinyMCE automatically positions cursor at click location
- Using `execCommand('mceInsertRawHTML')` inserts exactly at cursor
- Eliminated complex cursor position calculation that was causing random placement

### Console Spam Cleanup
**Removed all debug logging for production:**
- ✅ Initialization messages
- ✅ Button setup confirmations  
- ✅ Header detection logs
- ✅ Overlay creation measurements
- ✅ Status update spam

**Result:** Silent operation with clean console output while maintaining full functionality.

### Files Successfully Implemented
```
✅ includes/content-segmentation/class-content-segmentation.php (747 lines)
✅ includes/ajax/segmentation-handler.php (374 lines) 
✅ assets/js/content-segmentation-simple.js (693 lines)
✅ assets/css/content-segmentation.css (728 lines)
✅ Integration in slmm-seo-plugin.php (properly registered)
```

### Current System Features
1. **3-Click Workflow**: Button → Click divider 1 → Click divider 2 → Complete
2. **Visual Overlays**: 50% opacity yellow (TOP) and red (MIDDLE) sections with clean boundaries  
3. **Smart Insertion**: Markers inserted exactly where user clicks in content
4. **Comprehensive Cleanup**: Removes all marker variations including TinyMCE-protected ones
5. **Silent Operation**: No console spam, professional user experience
6. **Eye Toggle Button**: Clearly visible 👁️ button next to "Content Segmentation" label

**Status: FULLY IMPLEMENTED AND FUNCTIONAL** 🎉