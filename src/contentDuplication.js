(function($) {
    'use strict';

    var ContentDuplication = {
        duplicates: [],
        dialog: null,
        settings: {
            wordThreshold: 5 // Default minimum word count to consider as duplicate
        },
        // Track which instance of each duplicate we've visited
        visitedInstances: {},
        
        init: function() {
            // Load saved settings if available
            this.loadSettings();
            
            // Create dialog container
            this.createDialog();
            
            // Initialize TinyMCE integration
            this.initTinyMCE();
            
            // Add the duplication button to the toolbar
            this.addToolbarButton();
        },

        // Load settings from localStorage
        loadSettings: function() {
            var savedSettings = localStorage.getItem('contentDuplicationSettings');
            if (savedSettings) {
                try {
                    var parsedSettings = JSON.parse(savedSettings);
                    if (parsedSettings && typeof parsedSettings === 'object') {
                        this.settings = $.extend({}, this.settings, parsedSettings);
                    }
                } catch (e) {
                    console.error('Error loading content duplication settings', e);
                }
            }
        },

        // Save settings to localStorage
        saveSettings: function() {
            localStorage.setItem('contentDuplicationSettings', JSON.stringify(this.settings));
        },

        initTinyMCE: function() {
            var self = this;

            // Add button to editor toolbar if TinyMCE is active
            if (typeof tinymce !== 'undefined') {
                tinymce.PluginManager.add('content_duplication', function(editor) {
                    editor.addButton('duplication', {
                        icon: 'duplicate',
                        tooltip: 'Check for duplicate content',
                        onPostRender: function() {
                            var btn = this;
                            var indicator = $('<span class="duplication-indicator"></span>');
                            
                            // Add the indicator next to the button
                            editor.on('init', function() {
                                setTimeout(function() {
                                    $('.mce-i-duplicate').closest('.mce-btn').append(indicator);
                                    
                                    // Initial check for duplicates
                                    self.checkForDuplicatesInBackground(editor);
                                    
                                    // Setup interval for background checks
                                    setInterval(function() {
                                        self.checkForDuplicatesInBackground(editor);
                                    }, 5000); // Check every 5 seconds
                                }, 500);
                            });
                        },
                        onclick: function() {
                            self.checkForDuplicates(editor);
                        }
                    });
                    
                    // Listen for content changes to update indicator
                    editor.on('change keyup', function() {
                        self.debounce(function() {
                            self.checkForDuplicatesInBackground(editor);
                        }, 1000)();
                    });
                });
            }
        },
        
        // Add a duplication button to the custom toolbar in WordPress
        addToolbarButton: function() {
            var self = this;
            
            $(document).ready(function() {
                if ($('#content-duplication').length === 0 && $('#custom-editor-buttons').length > 0) {
                    var buttonHTML = '<button id="content-duplication" class="button custom-button">Duplicates<span class="duplication-indicator"></span><span class="duplication-count"></span></button>';
                    $('#custom-editor-buttons').append(buttonHTML);
                    
                    // Add click handler
                    $('#content-duplication').on('click', function(e) {
                        e.preventDefault();
                        if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                            self.checkForDuplicates(tinymce.activeEditor);
                        }
                    });
                    
                    // Initial check
                    setTimeout(function() {
                        if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                            self.checkForDuplicatesInBackground(tinymce.activeEditor);
                        }
                    }, 1000);
                }
            });
        },

        createDialog: function() {
            // Add overlay element
            var overlayHTML = '<div id="duplication-overlay" style="display:none;"></div>';
            
            var dialogHTML = '\
                <div id="duplication-dialog" style="display:none;" class="wp-core-ui">\
                    <div class="duplication-content">\
                        <div class="dialog-header">\
                            <div class="title-group">\
                                <span class="dialog-title">Duplicate Content</span>\
                                <span id="duplication-counter"></span>\
                            </div>\
                            <button type="button" class="close-button" aria-label="Close">&times;</button>\
                        </div>\
                        <div class="settings-panel">\
                            <div class="settings-row">\
                                <label for="duplicate-word-threshold">Minimum words to consider as duplicate:</label>\
                                <input type="number" id="duplicate-word-threshold" min="2" max="50" value="' + this.settings.wordThreshold + '">\
                                <button type="button" class="button button-small" id="save-threshold">Save</button>\
                            </div>\
                        </div>\
                        <div class="tabs">\
                            <button class="tab-button active" data-tab="list">List View</button>\
                            <button class="tab-button" data-tab="content">Content View</button>\
                        </div>\
                        <div class="tab-content" id="list-view">\
                            <div id="duplicates-list"></div>\
                        </div>\
                        <div class="tab-content" id="content-view" style="display:none;">\
                            <div id="duplicates-content"></div>\
                        </div>\
                    </div>\
                </div>';

            // Remove any existing dialog and overlay
            $('#duplication-dialog, #duplication-overlay').remove();
            
            // Add new elements
            $('body').append(overlayHTML + dialogHTML);

            // Style the dialog and overlay
            $('<style>').text(`
                #duplication-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.2);
                    z-index: 159998;
                }
                #duplication-dialog {
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: #ffffff;
                    padding: 20px;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    z-index: 159999;
                    width: 700px;
                    max-width: 90vw;
                    max-height: 80vh;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                }
                .duplication-content {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    height: 100%;
                    max-height: calc(80vh - 40px);
                }
                .dialog-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 15px;
                }
                .title-group {
                    display: flex;
                    align-items: flex-end;
                    gap: 8px;
                }
                .dialog-title {
                    font-size: 16px;
                    font-weight: 600;
                    line-height: 1;
                }
                #duplication-counter {
                    color: #000;
                    font-size: 11px;
                    font-weight: 600;
                    line-height: 1;
                    margin-bottom: 1px;
                }
                .close-button {
                    background: none;
                    border: none;
                    font-size: 24px;
                    line-height: 1;
                    padding: 0;
                    cursor: pointer;
                    color: #666;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border-radius: 50%;
                }
                .close-button:hover {
                    background: #f0f0f0;
                    color: #000;
                }
                .tabs {
                    display: flex;
                    margin-bottom: 15px;
                    border-bottom: 1px solid #ddd;
                }
                .tab-button {
                    padding: 8px 15px;
                    background: transparent;
                    border: none;
                    border-bottom: 2px solid transparent;
                    cursor: pointer;
                }
                .tab-button.active {
                    border-bottom: 2px solid #2271b1;
                    font-weight: 600;
                }
                .tab-content {
                    flex: 1;
                    overflow-y: auto;
                    padding-right: 5px;
                }
                #duplicates-list {
                    margin-bottom: 20px;
                }
                .duplicate-group {
                    margin-bottom: 15px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                }
                .duplicate-header {
                    background: #f8f8f8;
                    padding: 10px;
                    font-weight: 600;
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .duplicate-instances {
                    padding: 0;
                    margin: 0;
                    max-height: 0;
                    overflow: hidden;
                    transition: max-height 0.3s ease-out;
                }
                .duplicate-group.expanded .duplicate-instances {
                    max-height: 300px;
                    overflow-y: auto;
                }
                .duplicate-instance {
                    padding: 8px 10px;
                    border-top: 1px solid #eee;
                    cursor: pointer;
                }
                .duplicate-instance:hover {
                    background: #f0f7fc;
                }
                .duplication-indicator {
                    display: inline-block;
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                    margin-left: 5px;
                    vertical-align: middle;
                }
                .duplication-indicator.active {
                    background-color: #d63638;
                }
                .duplication-count {
                    margin-left: 3px;
                    font-size: 0.8em;
                    vertical-align: middle;
                }
                .duplicate-content {
                    background: #f8f8f8;
                    padding: 15px;
                    margin-bottom: 15px;
                    border-radius: 4px;
                    border-left: 3px solid #d63638;
                }
                .duplicate-label {
                    font-weight: 600;
                    margin-bottom: 5px;
                    color: #d63638;
                }
                .goto-button {
                    cursor: pointer;
                    color: #2271b1;
                    background: none;
                    border: none;
                    padding: 0;
                    text-decoration: underline;
                }
                .duplicate-highlight {
                    background-color: #ffeb3b;
                }
                .settings-panel {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    background: #f8f8f8;
                    border-radius: 4px;
                    margin-bottom: 15px;
                }
                .settings-row {
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }
                #duplicate-word-threshold {
                    width: 60px;
                }
            `).appendTo('head');

            this.bindDialogEvents();
        },

        showDialog: function() {
            $('#duplication-overlay').show();
            $('#duplication-dialog').show();
        },

        hideDialog: function() {
            $('#duplication-overlay').hide();
            $('#duplication-dialog').hide();
            
            // Clear any highlights in the editor
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                this.clearHighlights(tinymce.activeEditor);
            }
        },

        bindDialogEvents: function() {
            var self = this;

            // Close button click
            $(document).on('click', '#duplication-dialog .close-button', function() {
                self.hideDialog();
            });

            // Escape key press
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#duplication-dialog').is(':visible')) {
                    self.hideDialog();
                }
            });

            // Close dialog when clicking overlay
            $(document).on('click', '#duplication-overlay', function() {
                self.hideDialog();
            });
            
            // Tab switching
            $(document).on('click', '.tab-button', function() {
                var tab = $(this).data('tab');
                $('.tab-button').removeClass('active');
                $(this).addClass('active');
                $('.tab-content').hide();
                
                if (tab === 'list') {
                    $('#list-view').show();
                } else {
                    $('#content-view').show();
                }
            });
            
            // Duplicate group expansion
            $(document).on('click', '.duplicate-header', function() {
                var group = $(this).closest('.duplicate-group');
                group.toggleClass('expanded');
            });
            
            // Jump to duplicate instance
            $(document).on('click', '.duplicate-instance, .goto-button', function() {
                var index = $(this).data('index');
                self.jumpToDuplicate(index);
            });
            
            // Save threshold from settings panel
            $(document).on('click', '#save-threshold', function() {
                var threshold = parseInt($('#duplicate-word-threshold').val());
                if (!isNaN(threshold) && threshold >= 2) {
                    self.settings.wordThreshold = threshold;
                    self.saveSettings();
                    if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                        self.checkForDuplicates(tinymce.activeEditor);
                    }
                }
            });
            
            // Duplicate word threshold input validation
            $(document).on('input', '#duplicate-word-threshold', function() {
                var val = parseInt($(this).val());
                if (isNaN(val) || val < 2) {
                    $(this).val(2);
                } else if (val > 50) {
                    $(this).val(50);
                }
            });
        },
        
        // Debounce function to limit how often a function is called
        debounce: function(func, wait) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(function() {
                    func.apply(context, args);
                }, wait);
            };
        },
        
        // Background check without showing dialog
        checkForDuplicatesInBackground: function(editor) {
            if (!editor) return;
            
            var content = editor.getContent({format: 'html'});
            var duplicates = this.findDuplicates(content);
            
            // Update indicator
            this.updateIndicator(duplicates.length > 0, duplicates.length);
            
            // Store the duplicates for later use
            this.duplicates = duplicates;
        },
        
        // Check for duplicates and show dialog if found
        checkForDuplicates: function(editor) {
            if (!editor) return;
            
            var content = editor.getContent({format: 'html'});
            var duplicates = this.findDuplicates(content);
            
            // Update indicator
            this.updateIndicator(duplicates.length > 0, duplicates.length);
            
            // Store the duplicates for later use
            this.duplicates = duplicates;
            
            // Show dialog with results
            this.showDuplicatesDialog(duplicates, editor);
        },
        
        // Find duplicate content in the editor
        findDuplicates: function(content) {
            // Create a temporary div to parse the HTML
            var tempDiv = document.createElement('div');
            tempDiv.innerHTML = content;
            
            // Get current word threshold setting
            var wordThreshold = this.settings.wordThreshold || 5;
            
            // Collect paragraphs, list items, and headings
            var elements = [];
            var potentialDuplicates = [];
            
            // Function to check if text has enough words
            function hasEnoughWords(text, threshold) {
                // Count words by splitting on whitespace and filtering out empty strings
                var wordCount = text.split(/\s+/).filter(function(word) {
                    return word.length > 0;
                }).length;
                return wordCount >= threshold;
            }
            
            // Add paragraphs
            var paragraphs = tempDiv.querySelectorAll('p');
            for (var i = 0; i < paragraphs.length; i++) {
                var text = paragraphs[i].textContent.trim();
                if (hasEnoughWords(text, wordThreshold)) {
                    elements.push({
                        type: 'p',
                        content: paragraphs[i].innerHTML,
                        text: text,
                        node: paragraphs[i]
                    });
                }
            }
            
            // Add list items
            var listItems = tempDiv.querySelectorAll('li');
            for (var i = 0; i < listItems.length; i++) {
                var text = listItems[i].textContent.trim();
                if (hasEnoughWords(text, wordThreshold)) {
                    elements.push({
                        type: 'li',
                        content: listItems[i].innerHTML,
                        text: text,
                        node: listItems[i]
                    });
                }
            }
            
            // Add headings
            var headings = tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6');
            for (var i = 0; i < headings.length; i++) {
                var text = headings[i].textContent.trim();
                if (hasEnoughWords(text, wordThreshold)) {
                    elements.push({
                        type: 'h',
                        content: headings[i].innerHTML,
                        text: text,
                        node: headings[i]
                    });
                }
            }
            
            // Find duplicates
            var duplicates = [];
            var seen = {};
            
            for (var i = 0; i < elements.length; i++) {
                var text = elements[i].text;
                
                if (!seen[text]) {
                    seen[text] = [i];
                } else {
                    seen[text].push(i);
                }
            }
            
            // Collect groups with more than one instance
            for (var text in seen) {
                if (seen[text].length > 1) {
                    var instances = seen[text].map(function(index) {
                        return {
                            index: index,
                            type: elements[index].type,
                            html: elements[index].content
                        };
                    });
                    
                    duplicates.push({
                        text: text,
                        instances: instances
                    });
                }
            }
            
            return duplicates;
        },
        
        // Update the indicator dot
        updateIndicator: function(hasDuplicates, count) {
            var tinymceIndicator = $('.mce-i-duplicate').closest('.mce-btn').find('.duplication-indicator');
            var customIndicator = $('#content-duplication').find('.duplication-indicator');
            var countSpan = $('#content-duplication').find('.duplication-count');
            
            if (hasDuplicates) {
                tinymceIndicator.addClass('active');
                customIndicator.addClass('active');
                countSpan.text(count > 1 ? ` x ${count}` : '');
            } else {
                tinymceIndicator.removeClass('active');
                customIndicator.removeClass('active');
                countSpan.text('');
            }
        },
        
        // Show dialog with duplicate content
        showDuplicatesDialog: function(duplicates, editor) {
            if (duplicates.length === 0) {
                alert('No duplicate content found.');
                return;
            }
            
            // Update counter
            $('#duplication-counter').text(duplicates.length + ' duplicate' + (duplicates.length > 1 ? 's' : '') + ' found');
            
            // Generate list view
            var listHTML = '';
            duplicates.forEach(function(dup, groupIndex) {
                listHTML += '<div class="duplicate-group">';
                listHTML += '<div class="duplicate-header">';
                listHTML += '<span>' + this.truncateText(dup.text, 50) + '</span>';
                listHTML += '<span>' + dup.instances.length + ' instances</span>';
                listHTML += '</div>';
                listHTML += '<div class="duplicate-instances">';
                
                dup.instances.forEach(function(instance, instanceIndex) {
                    var nodeType = instance.type === 'h' ? 'Heading' : (instance.type === 'p' ? 'Paragraph' : 'List item');
                    listHTML += '<div class="duplicate-instance" data-index="' + instance.index + '">';
                    listHTML += '<span>' + nodeType + ' ' + (instanceIndex + 1) + '</span>';
                    listHTML += '<button class="goto-button" data-index="' + instance.index + '">Jump to duplicate</button>';
                    listHTML += '</div>';
                });
                
                listHTML += '</div>'; // Close instances
                listHTML += '</div>'; // Close group
            }.bind(this));
            
            $('#duplicates-list').html(listHTML);
            
            // Generate content view
            var contentHTML = '';
            duplicates.forEach(function(dup) {
                contentHTML += '<div class="duplicate-content">';
                contentHTML += '<div class="duplicate-label">Found ' + dup.instances.length + ' instances of:</div>';
                contentHTML += '<div class="duplicate-text">' + dup.text + '</div>';
                
                // Add jump buttons for each instance
                contentHTML += '<div class="duplicate-actions">';
                dup.instances.forEach(function(instance, index) {
                    contentHTML += '<button class="goto-button" data-index="' + instance.index + '">Jump to instance ' + (index + 1) + '</button> ';
                });
                contentHTML += '</div>';
                
                contentHTML += '</div>';
            });
            
            $('#duplicates-content').html(contentHTML);
            
            // Show dialog
            this.showDialog();
        },
        
        // Jump to a specific duplicate
        jumpToDuplicate: function(index) {
            var editor = tinymce.activeEditor;
            if (!editor) return;
            
            // Clear previous highlights
            this.clearHighlights(editor);
            
            // Get the duplicate text and instance info we're looking for
            var duplicateText = '';
            var dupGroupIndex = -1;
            var instanceIndexInGroup = -1;
            
            // Loop through our stored duplicates to find the text for this index
            for (var i = 0; i < this.duplicates.length; i++) {
                var duplicate = this.duplicates[i];
                for (var j = 0; j < duplicate.instances.length; j++) {
                    var instance = duplicate.instances[j];
                    if (instance.index === index) {
                        duplicateText = duplicate.text;
                        dupGroupIndex = i;
                        instanceIndexInGroup = j;
                        break;
                    }
                }
                if (duplicateText) break;
            }
            
            if (!duplicateText || dupGroupIndex === -1) {
                console.error('Could not find duplicate text for index', index);
                return;
            }
            
            // Initialize tracking for this duplicate group if not already tracked
            var trackingKey = 'dup_' + dupGroupIndex;
            if (!this.visitedInstances[trackingKey]) {
                this.visitedInstances[trackingKey] = {
                    currentInstance: 0,
                    totalInstances: this.duplicates[dupGroupIndex].instances.length,
                    lastVisitTime: 0
                };
            }
            
            var tracking = this.visitedInstances[trackingKey];
            var currentTime = new Date().getTime();
            
            // Reset counter if it's been a while since last visit (5+ seconds)
            if (currentTime - tracking.lastVisitTime > 5000) {
                tracking.currentInstance = 0;
            }
            
            // If clicking on a specific instance button, go directly to that instance
            if (instanceIndexInGroup !== -1 && currentTime - tracking.lastVisitTime < 3000) {
                // Only set to the specific instance if we're in the middle of browsing
                tracking.currentInstance = instanceIndexInGroup;
            }
            
            // Update last visit time
            tracking.lastVisitTime = currentTime;
            
            // Focus the editor
            editor.focus();
            
            // Get editor content and body
            var editorBody = editor.getBody();
            
            // Function to find all matching nodes in the editor
            function findAllMatchingNodes(node, searchText, matches) {
                if (!matches) matches = [];
                
                if (node.nodeType === 3) { // Text node
                    // Check if this text node contains our text
                    if (node.textContent.trim() === searchText.trim()) {
                        matches.push(node);
                    }
                } else if (node.nodeType === 1) { // Element node
                    // Skip link elements (a tags)
                    if (node.nodeName.toLowerCase() === 'a') {
                        return matches;
                    }
                    
                    // Check if this node's complete text content matches
                    if (node.textContent.trim() === searchText.trim()) {
                        // If it's a paragraph, heading, or list item, add to matches
                        if (/^(p|h[1-6]|li)$/i.test(node.nodeName)) {
                            matches.push(node);
                            return matches; // Don't search children if we matched the parent
                        }
                    }
                    
                    // Check child nodes
                    for (var i = 0; i < node.childNodes.length; i++) {
                        findAllMatchingNodes(node.childNodes[i], searchText, matches);
                    }
                }
                return matches;
            }
            
            // Find all matches of our text in the editor
            var allMatches = findAllMatchingNodes(editorBody, duplicateText);
            
            if (allMatches.length === 0) {
                console.error('Could not find any matching nodes for text:', duplicateText);
                return;
            }
            
            // Make sure our index is within bounds
            if (tracking.currentInstance >= allMatches.length) {
                tracking.currentInstance = 0;
            }
            
            // Get the current match
            var matchingNode = allMatches[tracking.currentInstance];
            
            // Increment for next time
            tracking.currentInstance = (tracking.currentInstance + 1) % allMatches.length;
            
            // Get the parent element if the match is a text node
            var targetElement = matchingNode.nodeType === 3 ? matchingNode.parentNode : matchingNode;
            
            // Add highlight class to the target element
            editor.dom.addClass(targetElement, 'duplicate-highlight');
            
            // Select the target element
            editor.selection.select(targetElement);
            editor.selection.collapse(true);
            
            // Scroll the element into view - place it at 1/4 from the top of the editor
            var editorWin = editor.getWin();
            var targetRect = targetElement.getBoundingClientRect();
            var editorHeight = editorWin.innerHeight;
            
            // Make element appear about 1/4 down from the top
            var scrollPosition = targetRect.top + editorWin.pageYOffset - (editorHeight / 4);
            
            // Scroll with timeout to ensure rendering is complete
            setTimeout(function() {
                try {
                    editorWin.scrollTo(0, scrollPosition);
                } catch (e) {
                    console.log('Primary scroll method failed:', e);
                    try {
                        // Fallback to the native method
                        targetElement.scrollIntoView();
                        
                        // Then adjust to get better positioning
                        setTimeout(function() {
                            var currentScroll = editorWin.pageYOffset;
                            editorWin.scrollTo(0, currentScroll - (editorHeight / 4));
                        }, 50);
                    } catch (e2) {
                        console.error('All scroll methods failed:', e2);
                    }
                }
            }, 50);
            
            // Set a timeout to remove the highlight after 3 seconds
            setTimeout(function() {
                var elements = editor.dom.select('.duplicate-highlight');
                for (var i = 0; i < elements.length; i++) {
                    editor.dom.removeClass(elements[i], 'duplicate-highlight');
                }
            }, 3000);
        },
        
        // Clear highlights in editor
        clearHighlights: function(editor) {
            var elements = editor.dom.select('.duplicate-highlight');
            for (var i = 0; i < elements.length; i++) {
                editor.dom.removeClass(elements[i], 'duplicate-highlight');
            }
        },
        
        // Helper to truncate text
        truncateText: function(text, maxLength) {
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength - 3) + '...';
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        ContentDuplication.init();
    });

})(jQuery); 