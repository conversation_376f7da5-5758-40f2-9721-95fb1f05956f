# CSV Import to D3 Tree Update Process

## Overview
This document details the complete process flow for CSV import functionality in the SLMM Interlinking Suite, from user file upload through database updates to D3 tree visualization refresh.

## Process Flow Diagram

```ascii
CSV IMPORT TO D3 TREE UPDATE PROCESS MAP
=====================================================

FRONTEND (JavaScript) - interlinking-suite.php
┌─────────────────────────────────────────────────────────────────────┐
│ 1. USER UPLOADS CSV FILE                                            │
│    ↓                                                                │
│ 2. handleFileUpload() - reads file content                         │
│    ↓                                                                │
│ 3. $('#slmm-import-csv-data').click() - triggers import            │
│    ↓                                                                │
│ 4. AJAX Call #1: Initialize Import                                 │
│    POST to ajax_import_silo_data                                    │
│    Data: {                                                          │
│      action: 'slmm_import_silo_data',                              │
│      action_type: 'initialize',                                     │
│      csv_content: [RAW CSV TEXT],                                   │
│      nonce: slmmInterlinkingData.nonce                             │
│    }                                                                │
└─────────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────────┐
│ BACKEND (PHP) - SLMM_Interlinking_Suite::ajax_import_silo_data()   │
│                                                                     │
│ 5. Security Checks:                                                │
│    - check_ajax_referer('slmm_interlinking_nonce')                 │
│    - current_user_can('edit_posts'/'edit_pages')                   │
│    ↓                                                                │
│ 6. Switch on action_type = 'initialize'                            │
│    ↓                                                                │
│ 7. initialize_csv_import():                                        │
│    - parse_csv_content() - converts CSV text to array              │
│    - Validates headers against expected format                     │
│    - Creates session_id = 'slmm_csv_import_[timestamp]_[rand]'     │
│    - set_transient($session_id, $import_data, HOUR_IN_SECONDS)     │
│    - Returns: { session_id, total_rows, message }                  │
└─────────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────────┐
│ FRONTEND (JavaScript) - Batch Processing Loop                      │
│                                                                     │
│ 8. Receives session_id from initialization                         │
│    ↓                                                                │
│ 9. LOOP: While processed < totalRows                               │
│    ↓                                                                │
│ 10. AJAX Call #2+: Process Batch                                   │
│     POST to ajax_import_silo_data                                   │
│     Data: {                                                         │
│       action: 'slmm_import_silo_data',                             │
│       action_type: 'process_batch',                                 │
│       session_id: [SESSION_ID],                                     │
│       batch_size: 10,                                               │
│       nonce: slmmInterlinkingData.nonce                            │
│     }                                                               │
└─────────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────────┐
│ BACKEND (PHP) - process_csv_batch()                                │
│                                                                     │
│ 11. get_transient($session_id) - retrieve CSV data                 │
│     ↓                                                               │
│ 12. Extract batch_size (default 10) rows from csv_data             │
│     ↓                                                               │
│ 13. FOR EACH ROW in batch:                                         │
│     ↓                                                               │
│ 14. process_single_csv_row($row_data):                             │
│     - Extract: ID, title, difficulty, importance, etc.             │
│     - Update WordPress post via wp_update_post()                   │
│     - Update ACF fields via update_field()                         │
│     - Update custom meta via update_post_meta()                    │
│     ↓                                                               │
│ 15. Update session data:                                           │
│     - increment processed_rows                                      │
│     - add any errors to errors array                               │
│     - set_transient() with updated data                            │
│     ↓                                                               │
│ 16. Return batch response:                                         │
│     {                                                               │
│       success: true,                                                │
│       processed: [TOTAL_PROCESSED],                                 │
│       total: [TOTAL_ROWS],                                          │
│       status: 'processing'/'completed',                             │
│       processed_posts: [ARRAY_OF_UPDATED_POST_IDS]                 │
│     }                                                               │
└─────────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────────┐
│ FRONTEND (JavaScript) - Batch Response Handling                    │
│                                                                     │
│ 17. Receive batch response                                          │
│     ↓                                                               │
│ 18. Update progress bar: processed/total                           │
│     ↓                                                               │
│ 19. IF status === 'completed':                                     │
│     ↓                                                               │
│ 20. TRIGGER D3 CANVAS UPDATE:                                      │
│     ↓                                                               │
│ 21. AJAX Call #3: Get Fresh Data                                   │
│     POST to slmm_generate_silo_grid                                 │
│     Data: {                                                         │
│       action: 'slmm_generate_silo_grid',                           │
│       nonce: slmmInterlinkingData.nonce,                           │
│       tree_mode: true,                                              │
│       fresh_analysis: 'true',                                       │
│       post_type_filter: [ACTIVE_TAB],                              │
│       silo_root_id: window.currentSiloRootId,                      │
│       max_depth: 5,                                                 │
│       include_posts: true,                                          │
│       bypass_cache: true                                            │
│     }                                                               │
└─────────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────────┐
│ BACKEND (PHP) - SLMM_Interlinking_Suite::ajax_generate_silo_grid() │
│                                                                     │
│ 22. Security checks (nonce, capabilities)                          │
│     ↓                                                               │
│ 23. Query WordPress database for FRESH data:                       │
│     - get_posts() with post_type filter                            │
│     - get_post_meta() for all custom fields                        │
│     - get_field() for ACF values                                   │
│     - Build hierarchical structure                                 │
│     ↓                                                               │
│ 24. Return fresh tree data:                                        │
│     {                                                               │
│       success: true,                                                │
│       data: {                                                       │
│         pages: {                                                    │
│           [POST_ID]: {                                              │
│             id: [ID],                                               │
│             title: [UPDATED_TITLE],                                 │
│             difficulty_level: [UPDATED_DIFFICULTY],                 │
│             importance_rating: [UPDATED_IMPORTANCE],                │
│             parent_id: [PARENT],                                    │
│             // ... all fresh database values                       │
│           }                                                         │
│         }                                                           │
│       }                                                             │
│     }                                                               │
└─────────────────────────────────────────────────────────────────────┘
                                    ↓
┌─────────────────────────────────────────────────────────────────────┐
│ FRONTEND (JavaScript) - D3 Tree Visual Update                     │
│                                                                     │
│ 25. Receive fresh database data                                    │
│     ↓                                                               │
│ 26. convertToD3TreeFormat(refreshResponse.data):                   │
│     - Convert WordPress hierarchy to D3.js tree structure          │
│     - Create parent-child relationships                            │
│     - Preserve all meta data (difficulty, importance, etc.)        │
│     ↓                                                               │
│ 27. renderD3Tree(treeHierarchy):                                   │
│     - clearCanvas() - remove existing SVG elements                 │
│     - d3.hierarchy(rootData) - create D3 hierarchy                 │
│     - treeLayout(treeData) - calculate node positions              │
│     - CREATE SVG ELEMENTS:                                         │
│       • Links: svg.selectAll('.slmm-tree-link')                   │
│       • Nodes: svg.selectAll('.slmm-tree-node')                   │
│       • Text: node.append('text') with fresh titles               │
│       • Difficulty circles: node.append('circle').attr('class',   │
│         'slmm-difficulty-dropdown ' + d.data.difficulty_level)     │
│       • Importance badges: node.append('text').text(              │
│         d.data.importance_rating)                                   │
│     ↓                                                               │
│ 28. Apply D3 transitions and positioning                           │
│     ↓                                                               │
│ 29. updateSiloStats(refreshResponse.data) - update counters        │
│     ↓                                                               │
│ 30. updateStatusMessage() - show completion message                │
└─────────────────────────────────────────────────────────────────────┘

VISUAL UPDATE VERIFICATION (Lines 13744-13760):
┌─────────────────────────────────────────────────────────────────────┐
│ 31. setTimeout(() => {                                              │
│     window.treeGroup.selectAll('.slmm-tree-node').each(d => {      │
│       // Verify difficulty visual matches database                 │
│       const difficultyCircle = nodeElement.select(                 │
│         '.slmm-difficulty-dropdown');                               │
│       difficultyCircle.attr('class',                               │
│         `slmm-difficulty-dropdown ${d.data.difficulty_level}`);     │
│                                                                     │
│       // Verify importance visual matches database                 │
│       const importanceSymbol = nodeElement.select(                 │
│         '.slmm-importance-dropdown-symbol');                        │
│       importanceSymbol.text(d.data.importance_rating);             │
│     });                                                             │
│   }, 1000);                                                        │
└─────────────────────────────────────────────────────────────────────┘
```

## Critical Functions

### Backend PHP Functions (SLMM_Interlinking_Suite class)

1. **ajax_import_silo_data()** - Main AJAX handler
   - Location: `includes/interlinking/interlinking-suite.php:17557`
   - Purpose: Routes CSV import requests to appropriate handlers

2. **initialize_csv_import()** - Import initialization
   - Location: `includes/interlinking/interlinking-suite.php:17591`
   - Purpose: Parse CSV, validate headers, create session

3. **process_csv_batch()** - Batch processor
   - Location: `includes/interlinking/interlinking-suite.php:17642`
   - Purpose: Process batches of CSV rows

4. **process_single_csv_row()** - Individual row processor
   - **STATUS**: Missing - needs to be created
   - Purpose: Update WordPress post and meta data from CSV row

5. **parse_csv_content()** - CSV parser
   - **STATUS**: Needs verification
   - Purpose: Convert CSV text to array structure

### Frontend JavaScript Functions

1. **handleFileUpload()** - File reader
   - Purpose: Read uploaded CSV file content

2. **convertToD3TreeFormat()** - Data converter
   - Location: `includes/interlinking/interlinking-suite.php:~5800`
   - Purpose: Convert WordPress hierarchy to D3 format

3. **renderD3Tree()** - Tree renderer
   - Location: `includes/interlinking/interlinking-suite.php:5819`
   - Purpose: Render D3 tree visualization

4. **clearCanvas()** - Canvas cleaner
   - Purpose: Clear existing D3 elements

## Expected CSV Format

The system expects CSV files with the following headers:
- ID
- Title
- ACF Title
- Status
- Difficulty
- Importance
- Target Keyword
- Slug
- Parent URL
- Summary
- Internal Links
- External Links
- Semantic Links
- Last Modified
- Edit URL
- View URL

## Error Points

Common failure points in the process:

1. **Step 14**: `process_single_csv_row()` function missing
2. **Step 11**: Session data expiring from transient storage
3. **Steps 10-16**: Generic "Unknown error occurred" from backend
4. **Missing error logging**: Insufficient debugging information

## Session Management

CSV import uses WordPress transients for session management:
- Session ID format: `slmm_csv_import_[timestamp]_[random]`
- Storage duration: 1 hour (`HOUR_IN_SECONDS`)
- Contains: csv_data, total_rows, processed_rows, errors, status

## Security

All AJAX calls implement:
- Nonce verification: `check_ajax_referer('slmm_interlinking_nonce')`
- Capability checks: `current_user_can('edit_posts')` or `current_user_can('edit_pages')`
- Input sanitization: `sanitize_text_field()` for all user inputs

## Visual Update Mechanism

After database updates, the D3 tree updates through:
1. Fresh data retrieval via `slmm_generate_silo_grid`
2. Direct rendering via `renderD3Tree()` (no redundant AJAX)
3. Visual verification loop to ensure UI matches database values