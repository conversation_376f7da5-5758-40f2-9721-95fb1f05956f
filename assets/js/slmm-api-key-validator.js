/**
 * SLMM API Key Validator
 * Provides client-side validation for API keys with real-time feedback
 */
(function($) {
    'use strict';

    $(document).ready(function() {
        // OpenAI API Key Validation
        $('#openai_api_key').on('blur change', function() {
            var apiKey = $(this).val().trim();
            var $feedback = $(this).siblings('.api-key-feedback');
            
            // Create feedback element if it doesn't exist
            if (!$feedback.length) {
                $feedback = $('<div class="api-key-feedback"></div>');
                $(this).after($feedback);
            }
            
            if (apiKey) {
                if (apiKey.startsWith('sk-')) {
                    $feedback.html('<span style="color: green;">✓ Valid OpenAI API key format</span>');
                    
                    // Optional: Test the API key with a simple request
                    testOpenAIKey(apiKey, $feedback);
                } else {
                    $feedback.html('<span style="color: red;">✗ Invalid format. OpenAI API keys should start with "sk-"</span>');
                }
            } else {
                $feedback.empty();
            }
        });
        
        // OpenRouter API Key Validation
        $('#openrouter_api_key').on('blur change', function() {
            var apiKey = $(this).val().trim();
            var $feedback = $(this).siblings('.api-key-feedback');
            
            // Create feedback element if it doesn't exist
            if (!$feedback.length) {
                $feedback = $('<div class="api-key-feedback"></div>');
                $(this).after($feedback);
            }
            
            if (apiKey) {
                if (apiKey.startsWith('sk-or-')) {
                    $feedback.html('<span style="color: green;">✓ Valid OpenRouter API key format</span>');
                    
                    // Optional: Test the API key with a simple request
                    testOpenRouterKey(apiKey, $feedback);
                } else {
                    $feedback.html('<span style="color: red;">✗ Invalid format. OpenRouter API keys should start with "sk-or-"</span>');
                }
            } else {
                $feedback.empty();
            }
        });
        
        /**
         * Test OpenAI API key with a simple request
         */
        function testOpenAIKey(apiKey, $feedback) {
            $feedback.html('<span style="color: #666;">⟳ Validating API key...</span>');
            
            $.ajax({
                url: 'https://api.openai.com/v1/models',
                type: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + apiKey
                },
                success: function(response) {
                    $feedback.html('<span style="color: green;">✓ API key validated successfully</span>');
                },
                error: function(xhr) {
                    if (xhr.status === 401) {
                        $feedback.html('<span style="color: red;">✗ Invalid API key. Please check your key.</span>');
                    } else if (xhr.status === 429) {
                        $feedback.html('<span style="color: orange;">⚠ Rate limited. Key may be valid but has reached limits.</span>');
                    } else {
                        $feedback.html('<span style="color: orange;">⚠ Could not validate key (network error)</span>');
                    }
                }
            });
        }
        
        /**
         * Test OpenRouter API key with a simple request
         */
        function testOpenRouterKey(apiKey, $feedback) {
            $feedback.html('<span style="color: #666;">⟳ Validating API key...</span>');
            
            $.ajax({
                url: 'https://openrouter.ai/api/v1/models',
                type: 'GET',
                headers: {
                    'Authorization': 'Bearer ' + apiKey,
                    'HTTP-Referer': window.location.origin,
                    'X-Title': 'SLMM SEO Bundle'
                },
                success: function(response) {
                    $feedback.html('<span style="color: green;">✓ API key validated successfully</span>');
                },
                error: function(xhr) {
                    if (xhr.status === 401) {
                        $feedback.html('<span style="color: red;">✗ Invalid API key. Please check your key.</span>');
                    } else if (xhr.status === 429) {
                        $feedback.html('<span style="color: orange;">⚠ Rate limited. Key may be valid but has reached limits.</span>');
                    } else {
                        $feedback.html('<span style="color: orange;">⚠ Could not validate key (network error)</span>');
                    }
                }
            });
        }
        
        // Add visual feedback for form submission
        $('form[action="options.php"]').on('submit', function(e) {
            var hasInvalidKey = false;
            
            // Check OpenAI key
            var openaiKey = $('#openai_api_key').val().trim();
            if (openaiKey && !openaiKey.startsWith('sk-')) {
                hasInvalidKey = true;
                $('#openai_api_key').css('border-color', 'red');
            }
            
            // Check OpenRouter key
            var openrouterKey = $('#openrouter_api_key').val().trim();
            if (openrouterKey && !openrouterKey.startsWith('sk-or-')) {
                hasInvalidKey = true;
                $('#openrouter_api_key').css('border-color', 'red');
            }
            
            if (hasInvalidKey) {
                if (!confirm('You have invalid API key formats. Do you want to save anyway?')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    });
})(jQuery);