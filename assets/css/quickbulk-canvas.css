/**
 * SLMM QuickBulk Canvas Integration Styles
 * Dark theme styling for the canvas-integrated bulk page creation system
 * 
 * @version 1.0.0
 * <AUTHOR> SEO Bundle
 */

/* ==========================================================================
   QuickBulk Widget Styles
   ========================================================================== */

.slmm-quickbulk-widget {
    pointer-events: auto;
    position: absolute;
    z-index: 10;
}

/* SVG-specific QuickBulk Widget Styles */
.slmm-quickbulk-widget {
    opacity: 0;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.slmm-quickbulk-circle {
    fill: url(#quickbulk-gradient);
    stroke: #ffffff;
    stroke-width: 1.5;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 1px 4px rgba(0,0,0,0.2));
}

.slmm-quickbulk-text {
    fill: #ffffff;
    font-family: monospace;
    font-size: 10px;
    font-weight: bold;
    text-anchor: middle;
    dominant-baseline: central;
    pointer-events: none;
    user-select: none;
}

/* Show SVG widgets on parent node hover */
.slmm-tree-node:hover .slmm-quickbulk-widget,
.tree-node:hover .slmm-quickbulk-widget,
.node:hover .slmm-quickbulk-widget {
    opacity: 1 !important;
}

/* SVG widget hover effects */
.slmm-quickbulk-widget:hover .slmm-quickbulk-circle {
    transform: scale(1.2);
    filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.4));
}

.slmm-quickbulk-widget:hover .slmm-quickbulk-text {
    fill: #f0f9ff;
}

/* Active state for SVG widgets */
.slmm-quickbulk-widget.active .slmm-quickbulk-circle {
    fill: url(#quickbulk-active-gradient);
    animation: slmmPulse 2s infinite;
    transform: scale(1.05);
}

/* SVG Gradient Definitions (to be added via JavaScript) */
svg defs linearGradient#quickbulk-gradient stop:nth-child(1) {
    stop-color: #8b5cf6;
    stop-opacity: 1;
}

svg defs linearGradient#quickbulk-gradient stop:nth-child(2) {
    stop-color: #7c3aed;
    stop-opacity: 1;
}

svg defs linearGradient#quickbulk-active-gradient stop:nth-child(1) {
    stop-color: #10b981;
    stop-opacity: 1;
}

svg defs linearGradient#quickbulk-active-gradient stop:nth-child(2) {
    stop-color: #059669;
    stop-opacity: 1;
}

.slmm-quickbulk-trigger {
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    border: 2px solid white;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0; /* Hidden by default */
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    line-height: 1;
    padding: 0;
    transform: scale(0.9);
    font-family: inherit !important;
}

/* Show on parent card hover */
.slmm-tree-node:hover .slmm-quickbulk-trigger,
.tree-node:hover .slmm-quickbulk-trigger,
.node:hover .slmm-quickbulk-trigger {
    opacity: 1;
    transform: scale(1.05);
}

/* Enhanced hover state */
.slmm-quickbulk-trigger:hover {
    opacity: 1 !important;
    transform: scale(1.15);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

/* Active state */
.slmm-quickbulk-trigger.active {
    opacity: 1 !important;
    background: linear-gradient(135deg, #10b981, #059669);
    transform: scale(1.05) !important;
    animation: slmmPulse 2s infinite;
}

/* Performance-optimized hover states */
.slmm-quickbulk-trigger.hovered {
    opacity: 1 !important;
    transform: scale(1.15) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4) !important;
}

.slmm-quickbulk-trigger.card-hovered {
    opacity: 1 !important;
    transform: scale(1.05) !important;
}

@keyframes slmmPulse {
    0%, 100% { transform: scale(1.05); }
    50% { transform: scale(1.15); }
}

/* ==========================================================================
   Popup Base Styles
   ========================================================================== */

.slmm-quickbulk-popup {
    position: fixed;
    z-index: 100001;
    background: #1a1a1a;
    border-radius: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    color: #f3f4f6;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    opacity: 0;
    transform: scale(0.8) translateY(10px);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 16px;
    width: 800px;
    min-height: 400px;
    border: 1px solid #374151;
}

/* Performance-optimized popup positioning */
.slmm-quickbulk-popup.positioned {
    /* Positioning styles handled by JS */
}

.slmm-quickbulk-popup * {
    box-sizing: border-box;
}

/* ==========================================================================
   Popup Header
   ========================================================================== */

.slmm-quickbulk-popup .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    border-bottom: 1px solid #374151;
    margin-bottom: 16px;
}

.slmm-quickbulk-popup .parent-context {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.slmm-quickbulk-popup .context-icon {
    font-size: 16px;
}

.slmm-quickbulk-popup .context-text {
    color: #e5e7eb;
}

.slmm-quickbulk-popup .context-text strong {
    color: #8b5cf6;
}

.slmm-quickbulk-popup .close-btn {
    background: none;
    border: none;
    color: #9ca3af;
    font-size: 20px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slmm-quickbulk-popup .close-btn:hover {
    color: #f3f4f6;
    background: #374151;
    transform: scale(1.1);
}

/* ==========================================================================
   Input Section
   ========================================================================== */

.slmm-quickbulk-popup .input-section {
    margin-bottom: 16px;
}

/* Dual Input Container for Side-by-Side Textareas */
.slmm-quickbulk-popup .dual-input-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.slmm-quickbulk-popup .input-column.titles-column,
.slmm-quickbulk-popup .input-column.slugs-column {
    /* Grid items - no display property needed */
}

/* Ensure both sections have consistent heights */
.slmm-quickbulk-popup .dual-input-container .input-header {
    min-height: 28px;
    margin-bottom: 8px;
}

.slmm-quickbulk-popup .dual-input-container .quickbulk-input {
    margin-bottom: 0;
    height: 120px;
    resize: vertical;
}

.slmm-quickbulk-popup .input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.slmm-quickbulk-popup .input-header label {
    font-size: 12px;
    font-weight: 500;
    color: #e5e7eb;
}

.slmm-quickbulk-popup .input-actions {
    display: flex;
    gap: 4px;
}

.slmm-quickbulk-popup .quick-action {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    padding: 4px 8px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 24px;
    height: 24px;
}

.slmm-quickbulk-popup .quick-action:hover {
    background: #4b5563;
    border-color: #6b7280;
    transform: translateY(-1px);
}

.slmm-quickbulk-popup .quick-action:active {
    transform: translateY(0);
}

.slmm-quickbulk-popup .quickbulk-input {
    width: 100%;
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    border-radius: 6px;
    padding: 12px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.4;
    resize: vertical;
    margin-bottom: 12px;
    min-height: 120px;
    transition: all 0.2s ease;
}

.slmm-quickbulk-popup .quickbulk-input:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 1px #8b5cf6, 0 2px 8px rgba(139, 92, 246, 0.1);
    background: #2d3748;
}

.slmm-quickbulk-popup .quickbulk-input::placeholder {
    color: #6b7280;
    font-style: italic;
}

/* ==========================================================================
   Clipboard Suggestion
   ========================================================================== */

.slmm-quickbulk-popup .clipboard-suggestion {
    background: linear-gradient(135deg, #064e3b, #047857);
    border: 1px solid #10b981;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 12px;
    animation: slmmSlideIn 0.3s ease-out;
}

@keyframes slmmSlideIn {
    from { 
        opacity: 0; 
        transform: translateY(-10px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

.slmm-quickbulk-popup .suggestion-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 500;
    color: #d1fae5;
}

.slmm-quickbulk-popup .use-clipboard-btn {
    background: #10b981;
    border: none;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.slmm-quickbulk-popup .use-clipboard-btn:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
}

.slmm-quickbulk-popup .suggestion-preview {
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 12px;
    color: #a7f3d0;
    white-space: pre-line;
    max-height: 60px;
    overflow: hidden;
    padding: 8px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    position: relative;
}

.slmm-quickbulk-popup .suggestion-preview::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(transparent, rgba(6, 78, 59, 0.8));
    pointer-events: none;
}

/* ==========================================================================
   Preview Section
   ========================================================================== */

.slmm-quickbulk-popup .preview-section {
    margin: 16px 0;
}

.slmm-quickbulk-popup .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
    font-weight: 500;
}

.slmm-quickbulk-popup .preview-title {
    color: #e5e7eb;
}

.slmm-quickbulk-popup .page-counter {
    color: #9ca3af;
    background: #374151;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
}

.slmm-quickbulk-popup .page-counter strong {
    color: #8b5cf6;
    font-weight: 600;
}

.slmm-quickbulk-popup .preview-list {
    max-height: 120px;
    overflow-y: auto;
    background: #111827;
    border-radius: 4px;
    padding: 8px;
    border: 1px solid #374151;
}

/* Custom scrollbar */
.slmm-quickbulk-popup .preview-list::-webkit-scrollbar {
    width: 4px;
}

.slmm-quickbulk-popup .preview-list::-webkit-scrollbar-track {
    background: #1f2937;
}

.slmm-quickbulk-popup .preview-list::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 2px;
}

.slmm-quickbulk-popup .preview-list::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
}

.slmm-quickbulk-popup .preview-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 0;
    font-size: 12px;
    border-bottom: 1px solid #374151;
    transition: all 0.2s ease;
}

.slmm-quickbulk-popup .preview-item:last-child {
    border-bottom: none;
}

.slmm-quickbulk-popup .preview-item:hover {
    background: #1f2937;
    border-radius: 4px;
    padding-left: 4px;
    padding-right: 4px;
}

.slmm-quickbulk-popup .preview-number {
    color: #6b7280;
    min-width: 20px;
    font-weight: 500;
}

.slmm-quickbulk-popup .preview-title {
    flex: 1;
    color: #f3f4f6;
    font-weight: 400;
}

.slmm-quickbulk-popup .preview-slug {
    color: #9ca3af;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 11px;
    opacity: 0.8;
}

.slmm-quickbulk-popup .preview-more {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 8px;
    font-size: 11px;
    background: #1f2937;
    border-radius: 4px;
    margin-top: 4px;
}

.slmm-quickbulk-popup .empty-state {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 20px;
    font-size: 13px;
}

/* ==========================================================================
   AI Suggestions Section
   ========================================================================== */

.slmm-quickbulk-popup .ai-suggestions {
    margin: 16px 0;
    background: linear-gradient(135deg, #1e1b4b, #312e81);
    border-radius: 6px;
    padding: 12px;
    border: 1px solid #4c1d95;
    animation: slmmSlideIn 0.4s ease-out;
}

.slmm-quickbulk-popup .suggestions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 12px;
    font-weight: 500;
    color: #c4b5fd;
}

.slmm-quickbulk-popup .collapse-suggestions {
    background: none;
    border: none;
    color: #a78bfa;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.slmm-quickbulk-popup .collapse-suggestions:hover {
    background: rgba(139, 92, 246, 0.2);
    color: #c4b5fd;
}

.slmm-quickbulk-popup .suggestions-grid {
    display: grid;
    gap: 8px;
}

.slmm-quickbulk-popup .suggestions-loading {
    text-align: center;
    color: #a78bfa;
    padding: 16px;
    font-style: italic;
    font-size: 12px;
}

.slmm-quickbulk-popup .suggestion-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.2);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    transition: all 0.2s ease;
    cursor: pointer;
}

.slmm-quickbulk-popup .suggestion-item:hover {
    background: rgba(139, 92, 246, 0.2);
    border-color: rgba(139, 92, 246, 0.4);
    transform: translateY(-1px);
}

.slmm-quickbulk-popup .suggestion-text {
    flex: 1;
    color: #e0e7ff;
    margin-right: 8px;
}

.slmm-quickbulk-popup .add-suggestion {
    background: #8b5cf6;
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.slmm-quickbulk-popup .add-suggestion:hover {
    background: #7c3aed;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
}

/* ==========================================================================
   Footer Section
   ========================================================================== */

.slmm-quickbulk-popup .popup-footer {
    padding-top: 16px;
    border-top: 1px solid #374151;
    margin-top: 16px;
}

.slmm-quickbulk-popup .creation-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    font-size: 12px;
}

.slmm-quickbulk-popup .option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    color: #e5e7eb;
    transition: color 0.2s ease;
}

.slmm-quickbulk-popup .option:hover {
    color: #f3f4f6;
}

.slmm-quickbulk-popup .option input[type="checkbox"] {
    width: 14px;
    height: 14px;
    accent-color: #8b5cf6;
    cursor: pointer;
}

.slmm-quickbulk-popup .status-select {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f3f4f6;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.slmm-quickbulk-popup .status-select:focus {
    outline: none;
    border-color: #8b5cf6;
    box-shadow: 0 0 0 1px #8b5cf6;
}

.slmm-quickbulk-popup .action-buttons {
    display: flex;
    gap: 8px;
}

.slmm-quickbulk-popup .create-btn {
    flex: 1;
    background: linear-gradient(135deg, #10b981, #059669);
    border: none;
    color: white;
    padding: 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    position: relative;
    overflow: hidden;
}

.slmm-quickbulk-popup .create-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.slmm-quickbulk-popup .create-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.slmm-quickbulk-popup .create-btn:hover:not(:disabled):before {
    left: 100%;
}

.slmm-quickbulk-popup .create-btn:active:not(:disabled) {
    transform: translateY(-1px);
}

.slmm-quickbulk-popup .create-btn:disabled {
    background: #374151;
    color: #6b7280;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* ==========================================================================
   Progress Overlay
   ========================================================================== */

.slmm-quickbulk-popup .progress-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(26, 26, 26, 0.98);
    backdrop-filter: blur(4px);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.slmm-quickbulk-popup .progress-content {
    text-align: center;
    width: 100%;
    padding: 20px;
}

.slmm-quickbulk-popup .progress-header {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: #f3f4f6;
}

.slmm-quickbulk-popup .progress-bar {
    width: 100%;
    height: 8px;
    background: #374151;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
    position: relative;
}

.slmm-quickbulk-popup .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #34d399);
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.slmm-quickbulk-popup .progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: slmmProgressShimmer 1.5s infinite;
}

@keyframes slmmProgressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.slmm-quickbulk-popup .progress-details {
    font-size: 12px;
    color: #9ca3af;
    margin-bottom: 8px;
    min-height: 16px;
}

.slmm-quickbulk-popup .progress-stats {
    font-size: 14px;
    font-weight: 500;
    color: #e5e7eb;
}

/* ==========================================================================
   Success and Error States
   ========================================================================== */

.slmm-quickbulk-popup .success-content,
.slmm-quickbulk-popup .error-content {
    text-align: center;
    padding: 20px;
    animation: slmmSlideIn 0.3s ease-out;
}

.slmm-quickbulk-popup .success-header {
    font-size: 18px;
    color: #10b981;
    margin-bottom: 12px;
    font-weight: 600;
}

.slmm-quickbulk-popup .success-message {
    color: #d1fae5;
    margin-bottom: 16px;
    font-size: 14px;
}

.slmm-quickbulk-popup .error-header {
    font-size: 18px;
    color: #ef4444;
    margin-bottom: 12px;
    font-weight: 600;
}

.slmm-quickbulk-popup .error-message {
    color: #fecaca;
    margin-bottom: 16px;
    font-size: 14px;
    background: rgba(239, 68, 68, 0.1);
    padding: 12px;
    border-radius: 6px;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.slmm-quickbulk-popup .success-actions,
.slmm-quickbulk-popup .error-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    margin-top: 16px;
}

.slmm-quickbulk-popup .success-btn,
.slmm-quickbulk-popup .error-btn {
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid;
    transition: all 0.2s ease;
}

.slmm-quickbulk-popup .success-btn {
    background: #10b981;
    border-color: #10b981;
    color: white;
}

.slmm-quickbulk-popup .success-btn:hover {
    background: #059669;
    transform: translateY(-1px);
}

.slmm-quickbulk-popup .error-btn {
    background: #ef4444;
    border-color: #ef4444;
    color: white;
}

.slmm-quickbulk-popup .error-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.slmm-quickbulk-popup .success-btn.secondary,
.slmm-quickbulk-popup .error-btn.secondary {
    background: transparent;
    color: #9ca3af;
    border-color: #4b5563;
}

.slmm-quickbulk-popup .success-btn.secondary:hover,
.slmm-quickbulk-popup .error-btn.secondary:hover {
    background: #374151;
    color: #f3f4f6;
}

/* ==========================================================================
   Utility Classes and Animations
   ========================================================================== */

.slmm-keyboard-hint {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1a1a1a;
    color: #f3f4f6;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 100002;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    border: 1px solid #374151;
    animation: slmmFadeInOut 2s ease-in-out forwards;
}

.slmm-keyboard-hint .hint-content {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.paste-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #374151;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    color: #f3f4f6;
    z-index: 100;
    animation: slmmFadeIn 0.2s ease-in;
    pointer-events: none;
}

/* ==========================================================================
   Keyframe Animations
   ========================================================================== */

@keyframes slmmFadeInOut {
    0% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.9); 
    }
    50% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1); 
    }
    100% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.9); 
    }
}

@keyframes slmmFadeIn {
    from { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.9); 
    }
    to { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1); 
    }
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .slmm-quickbulk-popup {
        width: calc(100vw - 32px);
        max-width: 320px;
        left: 16px !important;
        right: 16px !important;
        transform: translateX(0) translateY(10px) scale(0.8);
    }
    
    .slmm-quickbulk-popup .quickbulk-input {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 100px;
    }
    
    .slmm-quickbulk-popup .creation-options {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
}

@media (max-height: 600px) {
    .slmm-quickbulk-popup {
        max-height: calc(100vh - 40px);
        overflow-y: auto;
    }
    
    .slmm-quickbulk-popup .preview-list {
        max-height: 80px;
    }
    
    .slmm-quickbulk-popup .quickbulk-input {
        min-height: 80px;
    }
}

/* ==========================================================================
   High Contrast Mode Support
   ========================================================================== */

@media (prefers-contrast: high) {
    .slmm-quickbulk-popup {
        border: 2px solid #ffffff;
    }
    
    .slmm-quickbulk-trigger {
        border-width: 3px;
    }
    
    .slmm-quickbulk-popup .quickbulk-input:focus {
        border-width: 2px;
    }
}

/* ==========================================================================
   Reduced Motion Support
   ========================================================================== */

@media (prefers-reduced-motion: reduce) {
    .slmm-quickbulk-popup,
    .slmm-quickbulk-trigger,
    .slmm-quickbulk-popup *,
    .slmm-keyboard-hint {
        transition: none !important;
        animation: none !important;
    }
    
    .slmm-quickbulk-popup .progress-fill::after {
        animation: none !important;
    }
}

/* ==========================================================================
   Print Styles
   ========================================================================== */

@media print {
    .slmm-quickbulk-widget,
    .slmm-quickbulk-popup,
    .slmm-keyboard-hint {
        display: none !important;
    }
}