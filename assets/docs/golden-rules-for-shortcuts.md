# Golden Rules for Keyboard Shortcuts & Menus

## 🔑 Critical Understanding

### The Two-System Architecture

The SLMM SEO plugin uses **TWO SEPARATE SYSTEMS** for GPT prompt execution:

1. **Button System** (`assets/js/slmm-prompt-execution.js` & `assets/js/slmm-gpt-prompt.js`)
2. **Keyboard Shortcut System** (`snippets/chat_gpt_title_and_description_generator_v2_0.php`)

**⚠️ GOLDEN RULE #1: NEVER assume these two systems work the same way!**

---

## 🚫 What NOT To Do

### ❌ DON'T: Change keyboard shortcuts when changing button IDs
- Keyboard shortcuts use `executePromptDirectly()` which bypasses DOM entirely
- They work with `slmmGptPromptData.prompts` directly
- Button ID changes don't affect shortcuts IF you don't touch the shortcut code

### ❌ DON'T: Make shortcuts depend on dropdown IDs
- Dropdowns can have multiple instances with unique IDs
- Shortcuts should use data sources, not DOM parsing
- Original working approach: `slmmGptPromptData.prompts[promptIndex]`

### ❌ DON'T: Conditionally localize `slmmGptPromptData` 
- **CRITICAL**: Never wrap `wp_localize_script()` in `if (!empty($prompts))` 
- Shortcuts need data available on EVERY page load, even if empty
- Symptoms: "Shortcuts work if menu dropped down but not on fresh page"
- Root cause: Data not available when TinyMCE initializes shortcuts
- Fix: Always localize, use empty array if no prompts exist

### ❌ DON'T: Unify the two systems without understanding both
- Buttons need DOM interaction (multiple instances, ACF fields)
- Shortcuts need TinyMCE integration (direct editor access)
- Each serves different use cases

---

## ✅ Golden Rules

### 🥇 RULE #1: Shortcuts Are Data-Driven, Buttons Are DOM-Driven
```javascript
// SHORTCUT: Uses data directly
var promptData = slmmGptPromptData.prompts[promptIndex];
editor.selection.setContent(response.data);

// BUTTON: Uses DOM detection  
var $dropdown = $('#slmm-gpt-prompt-dropdown-' + instanceId);
var $textarea = findTargetEditor(button);
```

### 🥈 RULE #2: Keep Working Code Untouched
- If shortcuts work perfectly, **DON'T touch the shortcut code**
- Only fix what's actually broken
- Test both systems independently

### 🥉 RULE #3: Shortcuts Use TinyMCE, Buttons Use Flexible Detection
```javascript
// SHORTCUT: Always TinyMCE editor
function executePromptDirectly(promptIndex, editor) {
    if (editor) {
        selectedText = editor.selection.getContent({format: 'text'});
        editor.selection.setContent(response.data);
    }
}

// BUTTON: Smart detection for multiple editors
function getSelectedText(button) {
    // Try TinyMCE first, then find associated textarea
}
```

---

## 🔧 Technical Architecture

### Keyboard Shortcut Flow
```
1. TinyMCE Setup → setupTinyMCEKeyboardShortcuts(editor)
2. Key Press → editor.addShortcut('meta+ctrl+1', ...)  
3. Execute → executePromptDirectly(promptIndex, editor)
4. Data Source → slmmGptPromptData.prompts[promptIndex]
5. Insert → editor.selection.setContent(response.data)
```

### Button Click Flow
```
1. Click Event → $('.slmm-execute-gpt-prompt').on('click', ...)
2. Find Dropdown → $('#slmm-gpt-prompt-dropdown-' + instanceId)
3. Find Textarea → findTargetEditor(button)
4. Execute → AJAX with prompt_index and selected_text
5. Insert → Smart insertion based on editor type
```

---

## 📋 Implementation Checklist

### Before Changing Button System:
- [ ] Document current working state
- [ ] Understand why changes are needed
- [ ] Test shortcuts independently first
- [ ] Plan unique ID strategy

### Before Changing Shortcut System:
- [ ] **STOP!** Ask: "Are shortcuts actually broken?"
- [ ] If yes, what specifically is broken?
- [ ] Can we fix without changing working code?
- [ ] Test in isolated environment first

### After Any Changes:
- [ ] **CRITICAL**: Check console for "slmmGptPromptData is undefined" on fresh page load
- [ ] Test shortcuts: Ctrl+Cmd+1-9 (Mac) / Ctrl+Alt+1-9 (Windows)
- [ ] Test buttons: All instances, ACF fields, regular content  
- [ ] Test notifications: No popup alerts
- [ ] Test multiple editor instances
- [ ] Verify data localization is ALWAYS active (not conditional)

---

## 🏗️ Code Structure Reference

### Working Keyboard Shortcut Components:

```javascript
// 1. SETUP: Always check data availability first (CRITICAL!)
function setupTinyMCEKeyboardShortcuts(editor) {
    if (typeof slmmGptPromptData === 'undefined') {
        console.error('SLMM Shortcuts: slmmGptPromptData is undefined - shortcuts will not work');
        return;
    }
    
    if (!slmmGptPromptData.prompts) {
        console.log('SLMM Shortcuts: No prompts available - shortcuts will not be created');
        return;
    }
    
    // Read prompts from data (NOT DOM)
    for (var index in slmmGptPromptData.prompts) {
        availablePrompts.push({
            index: index,
            title: slmmGptPromptData.prompts[index].title
        });
    }
}

// 2. BINDING: Use TinyMCE's addShortcut
editor.addShortcut('meta+ctrl+' + (num + 1), 'GPT Prompt: ' + promptData.title, function() {
    executePromptDirectly(promptData.index, editor);
});

// 3. EXECUTION: Always check data availability (CRITICAL!)
function executePromptDirectly(promptIndex, editor) {
    if (typeof slmmGptPromptData === 'undefined') {
        alert('GPT Prompt data not available. Please refresh the page and try again.');
        return;
    }
    
    var selectedText = editor.selection.getContent({format: 'text'});
    var promptData = slmmGptPromptData.prompts[promptIndex];
    // ... AJAX call ...
    editor.selection.setContent(response.data);
}
```

### Working Button Components:

```javascript
// 1. UNIQUE IDs: Avoid conflicts
static $instance_counter = 0;
$instance_counter++;
$unique_id = $instance_counter;

// 2. SMART DETECTION: Find correct elements
const instanceId = $button.data('instance');
let $dropdown = $('#slmm-gpt-prompt-dropdown-' + instanceId);
const $textarea = findTargetEditor(button);

// 3. FLEXIBLE INSERTION: Handle different editor types
function insertContent(button, content) {
    if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor) {
        tinyMCE.activeEditor.selection.setContent(content);
    } else {
        // Smart textarea detection and insertion
    }
}
```

---

## 🐛 Common Pitfalls & Solutions

### Problem: "Shortcuts stopped working after button changes"
**Cause:** Changed dropdown IDs, shortcuts still looking for old IDs
**Solution:** Use `slmmGptPromptData.prompts` directly, not DOM parsing

### Problem: "Buttons don't work with ACF fields"  
**Cause:** Using fixed IDs, multiple instances conflict
**Solution:** Unique IDs + smart textarea detection

### Problem: "Multiple notifications showing"
**Cause:** Both systems have notification functions
**Solution:** Ensure notification ID is same across systems

### Problem: "Shortcuts work but buttons don't"
**Cause:** Different execution paths, different data expectations
**Solution:** Check AJAX parameters match backend expectations

### Problem: "Shortcuts work if dropdown is open but not on fresh page load"
**Cause:** `slmmGptPromptData` conditionally localized (`if (!empty($prompts))`)
**Symptoms:** 
- Console error: "slmmGptPromptData is undefined"
- Shortcuts work after interacting with dropdown first
- Fresh page loads fail shortcut execution
**Solution:** Always localize data in PHP:
```php
// WRONG (conditional)
if (!empty($prompts)) {
    wp_localize_script('script', 'slmmGptPromptData', array('prompts' => $prompts));
}

// RIGHT (always available)
wp_localize_script('script', 'slmmGptPromptData', array('prompts' => $prompts));
```

---

## 📚 Data Flow Documentation

### Shared Data Structure: `slmmGptPromptData`
```javascript
slmmGptPromptData = {
    prompts: {
        "0": { title: "Prompt 1", prompt: "...", model: "..." },
        "1": { title: "Prompt 2", prompt: "...", model: "..." }
    },
    ajax_url: "admin-ajax.php",
    nonce: "abc123..."
}
```

### Backend AJAX Handler Expectations:
```php
$prompt_index = intval($_POST['prompt_index']);  // String index from dropdown
$selected_text = sanitize_textarea_field($_POST['selected_text']);
$prompts = get_option('slmm_gpt_prompts', array());
$prompt = $prompts[$prompt_index];
```

---

## 🎯 Testing Protocol

### Keyboard Shortcuts Test:
1. Select text in TinyMCE editor
2. Press Ctrl+Cmd+1 (Mac) or Ctrl+Alt+1 (Windows)
3. Should execute first prompt without popup
4. Check notification appears and fades
5. Verify content replaced in editor

### Button Test (Regular Content):
1. Select text in main WordPress editor
2. Choose prompt from dropdown
3. Click "Execute GPT Prompt" button
4. Should work same as shortcut

### Button Test (ACF Fields):
1. Navigate to post with ACF textarea fields
2. Each field should have its own dropdown/button
3. Test each instance independently
4. Verify content goes to correct field

### Multi-Instance Test:
1. Page with multiple GPT prompt interfaces
2. All should have unique IDs (check browser console)
3. No "duplicate ID" warnings
4. Each works independently

---

## 💡 Future Development Guidelines

### When Adding New Features:
1. **Identify target system**: Shortcuts vs Buttons vs Both
2. **Follow existing patterns**: Don't reinvent the wheel
3. **Test isolation**: Verify feature doesn't break existing functionality
4. **Document decisions**: Update this file with new patterns

### When Debugging Issues:
1. **Isolate the problem**: Shortcuts only? Buttons only? Both?
2. **Check browser console**: Look for JavaScript errors, duplicate IDs
3. **Verify data flow**: Is `slmmGptPromptData` available?
4. **Test step-by-step**: Don't assume systems work the same

### When Refactoring:
1. **Document current state**: What works, what doesn't, why
2. **Plan in phases**: Don't change everything at once
3. **Keep working systems**: If it ain't broke, don't fix it
4. **Update documentation**: Keep this file current

---

## 📞 Emergency Recovery

### If Shortcuts Break:
1. Revert to last working commit
2. Check `snippets/chat_gpt_title_and_description_generator_v2_0.php`
3. Ensure `executePromptDirectly()` function is intact
4. Verify `slmmGptPromptData` is being localized

### If Buttons Break:
1. Check for duplicate IDs in browser console
2. Verify event delegation: `$(document).on('click', '.slmm-execute-gpt-prompt', ...)`
3. Ensure AJAX parameters match backend expectations
4. Test `findTargetEditor()` function

### If Both Break:
1. **STOP** - Don't make more changes
2. Revert to last known working state
3. Read this document again
4. Identify what was actually broken vs. what was working
5. Fix only the broken parts

### Quick Debugging Commands:
Open browser console and run these commands to diagnose issues:

```javascript
// Check if data is available
console.log('Data available:', typeof slmmGptPromptData !== 'undefined');
console.log('Prompts:', slmmGptPromptData?.prompts);

// Check shortcut setup
console.log('TinyMCE available:', typeof tinyMCE !== 'undefined');
console.log('Active editor:', tinyMCE?.activeEditor);

// Test shortcut execution manually
if (typeof executePromptDirectly === 'function' && tinyMCE?.activeEditor) {
    executePromptDirectly('0', tinyMCE.activeEditor);
}
```

---

## 🎖️ Success Metrics

### ✅ Working State Indicators:
- No duplicate ID warnings in console
- Shortcuts respond instantly (no dropdown interaction)
- Buttons find correct textareas automatically
- Notifications fade automatically (no OK clicks)
- Multiple ACF instances work independently
- Both TinyMCE and regular textareas supported

### 🚨 Warning Signs:
- JavaScript errors about undefined elements
- Buttons that work sometimes but not others
- Shortcuts that stopped working after "unrelated" changes
- Multiple notification windows
- Content appearing in wrong textareas

---

**Remember: The best code is code that doesn't need to be changed. If something works perfectly, leave it alone!** 