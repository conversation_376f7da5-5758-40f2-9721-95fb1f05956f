# Pattern: Data Localization for JavaScript

## Context
The plugin requires JavaScript data to be available for both button and keyboard shortcut systems.

## Pattern
Always localize `slmmGptPromptData` regardless of whether prompts exist:

```php
// CORRECT - Always localize
wp_localize_script('script', 'slmmGptPromptData', array(
    'prompts' => $prompts ?: array(),
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
));
```

## Anti-Pattern
```php
// WRONG - Conditional localization breaks shortcuts
if (!empty($prompts)) {
    wp_localize_script('script', 'slmmGptPromptData', array('prompts' => $prompts));
}
```

## Why This Matters
- Keyboard shortcuts initialize during TinyMCE setup
- If data isn't available on fresh page loads, shortcuts fail
- Symptoms: "Shortcuts work after dropdown interaction but not on fresh page"
- Root cause: `slmmGptPromptData` is undefined during initialization

## Implementation
- Always provide the data structure
- Use empty arrays for missing data
- Include all required properties (prompts, ajax_url, nonce)
- Maintain consistency across all localization points