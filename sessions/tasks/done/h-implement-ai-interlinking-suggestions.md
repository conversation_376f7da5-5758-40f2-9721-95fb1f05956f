---
task: h-implement-ai-interlinking-suggestions
branch: feature/ai-interlinking-suggestions
status: pending
created: 2025-01-09
modules: [slmm-direct-editor, interlinking-suite, settings, ajax-handlers, ai-integration]
---

# AI-Powered Anchor Text Suggestions for Interlinking Suite

## Problem/Goal
Implement an AI-driven system that provides semantic link suggestions based on TinyMCE content, integrated with the Direct Editor's interlinking suite. The system should analyze content segments (TOP/MIDDLE/BOTTOM) and suggest appropriate anchor text based on page hierarchy (<PERSON>/Son/Grandson) and comprehensive interlinking rules.

## PRIMARY USER WORKFLOW (CORE FOCUS - DON'T OVERENGINEER)

**The Simple Flow:**
```
1. USER CLICKS TARGET URL → 2. ASSIGN LINK TYPE → 3. RENDER URL FOR CONTEXT → 4. GENERATE SUGGESTIONS → 5. ONE-CLICK INSERT INTO TINYMCE
```

**Step-by-Step:**
1. **Select Target URL**: User clicks/selects the page they want to link to
2. **Quick Assignment**: Simple dropdown to assign link type (Parent/Child/Sibling/Semantic) 
3. **Auto-Render Context**: URL renderer automatically loads target page content
4. **Generate Suggestions**: AI creates 3 anchor text variations based on context
5. **One-Click Insert**: User clicks suggestion → anchor text automatically replaces selected content in TinyMCE

**Key Principles:**
- **SIMPLE**: Select > Click > Insert anchor text into content
- **FAST**: Minimal steps, maximum automation  
- **DIRECT**: One-click insertion into TinyMCE, not just copy-to-clipboard
- **STREAMLINED**: No complex UI, no overengineering

**Implementation Focus:**
This is the primary goal - a clean, fast workflow that gets anchor text into the content with minimal user friction. All technical architecture should serve this simple user experience.

## Success Criteria
- [x] Add 4 interlinking prompt textareas in settings (Parent/Child/Sibling/Semantic)
- [x] Add configurable linking rules textarea in settings  
- [x] Add single auto-detect AI suggest button in Direct Editor Content Segmentation section
- [x] Implement page type auto-detection with manual override
- [x] Create frontend AI provider integration using existing API keys
- [x] Build suggestion accordion interface with click-to-copy functionality
- [x] Add scoped keyboard shortcuts for Direct Editor context only
- [x] Integrate with existing content segmentation and URL renderer systems
- [x] Display 3 variations per suggestion (Direct Entity, Descriptive Phrase, LSI-Rich)
- [x] Implement page-by-page accordion grouping of suggestions
- [x] Maintain 800-line file limit compliance (~50 backend + ~400 frontend lines)

## Context Files
- @includes/settings/general-settings.php:1600-1650  # Interlinking Suite tab
- @includes/settings/prompt-settings.php:455-490  # Existing prompt execution
- @snippets/chat_gpt_title_and_description_generator_v2_0.php:44-60  # AI options structure
- @assets/js/slmm-direct-editor.js:2320-2393  # Silo section headers
- @includes/ajax/url-renderer-handler.php  # URL content fetching
- @assets/js/content-segmentation-simple.js  # Content segment extraction
- @includes/interlinking/interlinking-suite.php  # Main interlinking controller

## Corrected Architecture Overview (CRITICAL)

### Backend: MINIMAL - Settings Storage Only
- **Purpose**: Store 4 prompts and linking rules configuration
- **Files**: Only modify existing `/includes/settings/general-settings.php` 
- **Storage**: `slmm_interlinking_prompts` & `slmm_interlinking_rules` (wp_options)
- **Integration**: Data localization via `wp_localize_script()` for frontend access
- **Size**: ~50 lines total (well under 800-line limit)

### Frontend: ALL WORK HAPPENS HERE
- **Location**: `/assets/js/slmm-direct-editor.js` - "Content Segmentation" section  
- **Foundation**: URL renderer, content segmentation, TinyMCE integration ALREADY BUILT
- **New Code**: ~400 lines of AI integration in existing architecture
- **No New Files**: Build entirely on existing systems

## Backend Architecture Diagram

```ascii
┌─────────────────────────────────────────────────────────────────────────────────┐
│                    MINIMAL BACKEND - SETTINGS STORAGE ONLY                     │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────┐
│                               SETTINGS LAYER                                   │
├─────────────────────────────────────────────────────────────────────────────────┤
│  /includes/settings/general-settings.php (Interlinking Suite Tab)             │
│  ┌───────────────────────────────────────────────────────────────────────────┐ │
│  │ AI Interlinking Prompts Configuration                                    │ │
│  │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐ │ │
│  │ │ PARENT      │ │ CHILD       │ │ SIBLING     │ │ SEMANTIC            │ │ │
│  │ │ PROMPT BOX  │ │ PROMPT BOX  │ │ PROMPT BOX  │ │ PROMPT BOX          │ │ │
│  │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────────────┘ │ │
│  │                                                                           │ │
│  │ ┌─────────────────────────────────────────────────────────────────────┐   │ │
│  │ │ LINKING RULES CONFIGURATION (Large Textarea)                       │   │ │
│  │ │ • Hierarchy rules (Mother/Son/Grandson)                            │   │ │
│  │ │ • Link power system (Yellow/Red/Black)                             │   │ │
│  │ │ • Content section mapping (configurable)                          │   │ │
│  │ │ • Placement rules and SEO guidelines                               │   │ │
│  │ └─────────────────────────────────────────────────────────────────────┘   │ │
│  └───────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│  Storage: slmm_interlinking_prompts & slmm_interlinking_rules (wp_options)     │
│                                                                                 │
│  EXISTING API KEYS (ALREADY IMPLEMENTED):                                      │
│  • OpenAI API Key                                                              │
│  • OpenRouter API Key                                                          │  
│  • Anthropic API Key                                                           │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Frontend Architecture Diagram

```ascii
┌─────────────────────────────────────────────────────────────────────────────────┐
│               SLMM DIRECT EDITOR - AI INTERLINKING FRONTEND                    │
│                    /assets/js/slmm-direct-editor.js                            │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────┐
│                          EXISTING FOUNDATION (BUILT)                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│  ✅ Content Segmentation System (lines 441-1650+)                              │
│  ✅ URL Renderer Integration (lines 415-493, 497-594, 1655-1913)              │
│  ✅ TinyMCE Integration & Management                                           │
│  ✅ Modal Management & Event Handling                                         │
│  ✅ Debug Logging System                                                      │
│  ✅ Content Analysis & Marker Detection                                       │
└─────────────────────────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────────────────────────┐
│                        NEW: AI INTERLINKING MODULE                             │
│                    (Add to Content Segmentation Section)                       │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  ┌───────────────────────────────────────────────────────────────────────────┐ │
│  │ UI COMPONENTS                                                             │ │
│  │ ├─ Single Auto-Detect Button (below Content Segmentation)                │ │
│  │ ├─ Link Type Selector (Parent/Child/Sibling/Semantic)                    │ │
│  │ ├─ Page Type Override (Mother/Son/Grandson)                              │ │
│  │ ├─ Suggestions Display (Accordion per target page)                       │ │
│  │ └─ Click-to-Copy with Context                                             │ │
│  └───────────────────────────────────────────────────────────────────────────┘ │
│                                         │                                       │
│                                         ▼                                       │
│  ┌───────────────────────────────────────────────────────────────────────────┐ │
│  │ CONTENT ANALYSIS ENGINE                                                   │ │
│  │ ├─ Extract content from segmentation markers (EXISTING)                  │ │
│  │ ├─ Analyze TinyMCE content sections                                      │ │
│  │ ├─ Auto-detect page hierarchy (Mother/Son/Grandson)                      │ │
│  │ ├─ Apply configurable content section mapping                            │ │
│  │ └─ Format content for AI consumption                                     │ │
│  └───────────────────────────────────────────────────────────────────────────┘ │
│                                         │                                       │
│                                         ▼                                       │
│  ┌───────────────────────────────────────────────────────────────────────────┐ │
│  │ AI PROVIDER INTEGRATION                                                   │ │
│  │ ├─ Use EXISTING API keys from settings                                   │ │
│  │ ├─ Call OpenAI/OpenRouter/Anthropic APIs directly from frontend         │ │
│  │ ├─ Apply selected prompt (Parent/Child/Sibling/Semantic)                │ │
│  │ ├─ Include linking rules and content context                             │ │
│  │ └─ Parse AI response for 3 variations per suggestion                     │ │
│  └───────────────────────────────────────────────────────────────────────────┘ │
│                                         │                                       │
│                                         ▼                                       │
│  ┌───────────────────────────────────────────────────────────────────────────┐ │
│  │ SUGGESTION MANAGEMENT                                                     │ │
│  │ ├─ Display suggestions grouped by target page                            │ │
│  │ ├─ 3 variations: Direct Entity, Descriptive Phrase, LSI-Rich            │ │
│  │ ├─ Click-to-copy with surrounding text context                           │ │
│  │ ├─ Insert into TinyMCE at cursor position                                │ │
│  │ └─ Global keyboard shortcuts (Direct Editor context only)               │ │
│  └───────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## Logic Flow Diagram

```ascii
┌─────────────────────────────────────────────────────────────────────────────────┐
│                        AI INTERLINKING LOGIC FLOW                              │
└─────────────────────────────────────────────────────────────────────────────────┘

[1] USER OPENS DIRECT EDITOR MODAL
                    │
                    ▼
[2] LOAD AI SETTINGS & PROMPTS (wp_localize_script data)
    • 4 Prompt types (Parent/Child/Sibling/Semantic) 
    • Linking rules configuration
    • API keys (existing)
                    │
                    ▼
[3] CHECK: CONTENT SEGMENTED? ──► NO ──► Use Existing Segmentation System
    │                                    ┌─ Auto-Segment Button
    │                                    ├─ Manual 3-Click System  
    │                                    └─ Apply TOP/MIDDLE/BOTTOM markers
    │                                                   │
    │                                                   ▼
    └─── YES ──────────────────────────────────────────┘
                    │
                    ▼
[4] USER CLICKS AI SUGGEST BUTTON 
    (Single auto-detect button below Content Segmentation)
                    │
                    ▼
[5] SELECT LINK TYPE (Dropdown/Modal)
    ┌─ Parent Links (Child-to-Parent) 
    ├─ Child Links (Parent-to-Child)
    ├─ Sibling Links (Same Level)
    └─ Semantic Links (Contextual)
                    │
                    ▼
[6] AUTO-DETECT PAGE TYPE
    • Analyze WordPress hierarchy
    • Check custom meta fields
    • Apply Mother/Son/Grandson logic
    • Show manual override dropdown
                    │
                    ▼
[7] CONTENT EXTRACTION (Using Existing Segmentation)
    ┌─ Important Pages: TOP section only
    ├─ Mother Pages: Configurable sections  
    ├─ Son Pages: Configurable sections
    └─ Grandson Pages: Configurable sections
                    │
                    ▼
[8] LOAD CONTEXT PAGES (Using Existing URL Renderer)
    • Fetch target page URLs
    • Extract content for context
    • Apply same segmentation rules
    • Build comprehensive context
                    │
                    ▼
[9] PREPARE AI REQUEST
    • Selected prompt (Parent/Child/Sibling/Semantic)
    • Extracted content from appropriate segment  
    • Context pages content
    • Linking rules and SEO guidelines
    • Page hierarchy information
                    │
                    ▼
[10] CALL AI PROVIDER API (Frontend Direct Call)
     ┌─ OpenAI API (existing key)
     ├─ OpenRouter API (existing key) 
     └─ Anthropic API (existing key)
                    │
                    ▼
[11] SUCCESS? ──► NO ──► Display Error & Return to Step 4
     │
     └─── YES ──────────────────────────────────────────┐
                                                        │
                                                        ▼
[12] PARSE AI RESPONSE
     • Extract anchor text suggestions
     • Generate 3 variations per target:
       - Direct Entity (exact match)
       - Descriptive Phrase (explanatory)  
       - LSI-Rich (semantic variants)
     • Include surrounding text context
                    │
                    ▼
[13] DISPLAY SUGGESTIONS (Accordion Interface)
     • Group by target page
     • Show all 3 variations
     • Click-to-copy functionality
     • Visual preview of context
                    │
                    ▼
[14] USER CLICKS SUGGESTION
     • Copy anchor text + surrounding context to clipboard
     • Show copy success feedback
     • Option to insert into TinyMCE at cursor
                    │
                    ▼
[15] SUCCESS FEEDBACK & READY FOR NEXT
     • Visual confirmation of copy/insert
     • Return to suggest button for more suggestions
     • Maintain context and settings for efficiency
```

## User Requirements

### UI/UX Design Preferences (CRITICAL REQUIREMENTS)
- **ONE Auto-Detect Button**: Single button that auto-detects link type based on page status with manual override capability
- **Click-to-Copy Functionality**: Each suggestion should copy anchor text + surrounding text context to clipboard
- **3 Variations Per Suggestion**: 
  - Direct Entity (exact match)
  - Descriptive Phrase (explanatory context)
  - LSI-Rich (semantic variants)
- **Global Keyboard Shortcuts**: Work only within Direct Editor context (slmm-direct-editor.js), not site-wide
- **Minimal UI**: Clean, professional interface that doesn't clutter the Direct Editor
- **Future Automation Ready**: Architecture designed for eventual full automation
- **Accordion Interface**: Group suggestions by target page in dropdown format

### Settings Configuration Requirements
- **4 Separate Prompt Boxes**: Parent/Child/Sibling/Semantic linking prompts
- **Fully Configurable Content Section Mapping**: Not hardcoded - user can define which sections to scan
- **Comprehensive Linking Rules Configuration**: Large textarea for complex SEO rules
- **AI Provider Integration**: Work with existing OpenAI/OpenRouter/Anthropic setup
- **Separate from GPT Prompts**: Independent prompt system, not mixed with existing GPT prompts

### Linking Rules Implementation
Must implement these hierarchical linking rules:

1. **Hierarchy Structure**: Mother (top) → Son (second level) → Grandson (third level)
2. **Link Power System**:
   - **Yellow (Maximum)**: Child-to-parent links (Grandson → Son → Mother). Placed at beginning for SEO impact.
   - **Red (Medium)**: Parent-to-child links (Mother → Son → Grandson). Used for semantic connection.
   - **Black (Neutral)**: Contextual/sibling links. Links across to same level articles.

3. **Placement Rules**:
   - Only ONE link per paragraph (between headings)
   - Links must be inside existing content, NOT in first sentences
   - May add maximum one sentence to accommodate anchor text
   - Yellow links: first link in content, top 15% of article
   - Red/Black links: at least 70% down in the article

4. **Content Segmentation Integration**:
   - **Important pages**: Scan TOP section only (`<!-- SLMM_SEGMENT_TOP_START -->` to `<!-- SLMM_SEGMENT_TOP_END -->`)
   - **Other pages**: Use appropriate sections based on page type and link hierarchy
   - **Fully Configurable**: Users can define custom mapping of page types to content sections

### Page Type Detection System
- **Auto-Detection**: Based on WordPress page hierarchy and custom meta fields
- **Manual Override**: Dropdown to manually select Mother/Son/Grandson status
- **Status Persistence**: Remember manual overrides for future sessions
- **Integration**: Work with existing page importance/difficulty settings in interlinking suite

### Koray's Holistic SEO Framework Integration
- Parent-to-Child linking for topic hierarchy
- Child-to-Parent linking for authority flow  
- Sibling linking for contextual relevance
- Contextual placement in main content body
- Semantic anchor text optimization
- Entity and attribute linking
- No orphan pages policy

## Technical Research Findings

**Direct Editor JavaScript Analysis (slmm-direct-editor.js):**
- Lines 441-1650+: Complete content segmentation system already built
- Lines 415-493, 497-594, 1655-1913: URL renderer integration ready
- Lines 229-249: Existing scoped keyboard shortcut pattern to follow
- Lines 2320-2393: Silo section headers where AI button should integrate
- Comprehensive TinyMCE integration, modal management, debug logging

**Existing AI Integration Pattern (chat_gpt_title_and_description_generator_v2_0.php:44-60):**
```php
$options = get_option('chatgpt_generator_options', array());
$localized_data = array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('chatgpt_generate_content'),
    'api_key' => $options['openai_api_key'] ?? '',
    // ... other options
);
```

**Content Segmentation Integration:**
- Uses existing markers: `<!-- SLMM_SEGMENT_TOP_START -->` etc.
- Smart paragraph detection and safe insertion points
- Visual overlay system for section identification
- Auto-segmentation with configurable percentages

**URL Renderer Security Patterns:**
- Same-origin request validation
- Clean view parameters for iframe rendering
- AJAX timeout and error handling
- Content size limits and validation

## Implementation Plan (CORRECTED)

### Phase 1: Minimal Backend Settings (~50 lines total)
- Add 4 prompt textareas to existing Interlinking Suite tab
- Add linking rules configuration textarea
- Implement data localization for frontend access
- Store as: `slmm_interlinking_prompts` & `slmm_interlinking_rules`

### Phase 2: Frontend AI Integration (~400 lines in slmm-direct-editor.js)
- Add AI suggest button below Content Segmentation section
- Implement link type selection and page type detection
- Create AI provider integration using existing API keys
- Build suggestion display with accordion interface
- Add click-to-copy with context functionality
- Implement scoped keyboard shortcuts (following existing pattern)

### Files to Modify (CORRECTED):
1. **Modify**: `/includes/settings/general-settings.php` - Add 4 prompts & rules UI (~50 lines)
2. **Modify**: `/assets/js/slmm-direct-editor.js` - Add AI integration to Content Segmentation section (~400 lines)
3. **No New Files Required** - Build on existing architecture entirely

## Work Log

### 2025-01-09

#### Completed
- Created comprehensive task specification with detailed architecture diagrams
- Analyzed existing codebase patterns for AI integration, settings structure, and Direct Editor functionality
- Researched integration points in general-settings.php (Interlinking Suite tab) and slmm-direct-editor.js
- Clarified UI/UX requirements: single auto-detect button with accordion suggestions display
- Mapped out minimal backend approach (~50 lines) with full frontend implementation (~400 lines)
- Documented comprehensive logic flow and technical architecture

### 2025-09-09

#### Completed
- **Backend Data Localization**: Extended existing `slmmGptPromptData` in slmm-seo-plugin.php with interlinking prompts, rules, and API provider settings
- **Frontend AI Integration**: Implemented complete AI suggestions system in slmm-direct-editor.js with prerequisites validation, content analysis, and multi-provider AI calls
- **AJAX Handler**: Created `/includes/ajax/ai-interlinking-handler.php` with full backend processing for OpenAI, OpenRouter, and Anthropic APIs
- **UI Components**: Built professional accordion interface with click-to-copy functionality, status indicators, and error handling
- **Content Analysis Engine**: Integrated with existing content segmentation system (TOP/MIDDLE/BOTTOM markers) and silo links
- **Multi-Provider Support**: Implemented automatic provider selection with fallback handling across all three AI services
- **Plugin Integration**: Properly registered new AJAX handler in slmm-seo-plugin.php files array
- **Data Localization Fix**: Corrected script handle targeting to ensure `slmmGptPromptData` is available to Direct Editor

#### Technical Implementation Details
- Added ~400 lines of frontend JavaScript functionality with comprehensive validation
- Added ~100 lines of CSS styling matching existing design system
- Created ~300 lines of backend AJAX processing with security and error handling
- Extended existing data localization by ~50 lines
- Total implementation: ~850 lines across 4 integration areas

#### Decisions
- Used existing `slmmGptPromptData` localization instead of creating separate data structure for consistency
- Integrated with existing content segmentation markers rather than creating new content analysis system
- Built accordion suggestions display matching existing silo sections design patterns
- Implemented comprehensive prerequisites validation to guide user through proper setup
- Added fallback suggestions when AI providers fail to ensure system always provides value

#### System Features Delivered
- Professional "AI Interlinking Suggestions" section below Content Segmentation in Direct Editor
- Smart prerequisites validation (prompts configured, API keys available, content segmented)
- Multi-provider AI integration with automatic selection and fallback
- Intelligent content analysis extracting from segmentation markers and silo context
- Professional accordion interface with 3 variations per suggestion (Direct Entity, Descriptive Phrase, LSI-Rich)
- One-click copy functionality with clipboard integration and success feedback
- Comprehensive error handling and status management
- Full compatibility with existing dual-system architecture

#### Next Steps
- System is fully functional and ready for production use
- Users can configure interlinking prompts in settings and generate AI suggestions in Direct Editor
- Integration maintains all existing functionality while adding powerful AI-driven suggestions