<?php
// If uninstall is not called from WordPress, exit
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

// Delete all post meta related to the removed SEO Settings meta box
global $wpdb;
$wpdb->query("DELETE FROM {$wpdb->postmeta} WHERE meta_key IN ('_slmm_seo_title', '_slmm_seo_description')"); 

// Clean up notes data only on complete uninstall (not updates)
// This preserves notes during plugin updates but removes them on uninstall
$wpdb->query("DELETE FROM {$wpdb->usermeta} WHERE meta_key = 'slmm_project_notes'");
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'slmm_notes_backup_user_%'");
$wpdb->query("DELETE FROM {$wpdb->options} WHERE option_name LIKE 'slmm_user_notes_%'");