/**
 * SLMM Link Visualization Overlay System
 * Integrates with existing D3.js tree to show internal and external link connections
 * 
 * @version 1.0.0
 * <AUTHOR> SEO Bundle
 */

(function($, d3) {
    'use strict';
    
    // Ensure dependencies are available
    if (typeof $ === 'undefined' || typeof d3 === 'undefined') {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.error('Link Overlay', 'Missing dependencies (jQuery or D3.js)');
        }
        return;
    }
    
    /**
     * SLMM Link Overlay Manager Class
     * Handles rendering and management of link visualizations on the D3.js tree
     */
    class SLMM_LinkOverlay {
        
        constructor() {
            this.isActive = false;
            this.svg = null;
            this.treeGroup = null;
            this.linkGroup = null;
            this.linkData = {
                internal: [],
                external: []
            };
            this.nodePositions = new Map(); // Cache for performance
            this.visibleNodes = new Set(); // Viewport culling
            this.linkGenerator = null;
            
            // Configuration
            this.config = {
                internal: {
                    stroke: '#3b82f6', // Use direct color value for better color picker integration
                    strokeWidth: 1.5,
                    opacity: 0.6,
                    strokeDasharray: 'none'
                },
                external: {
                    stroke: '#f59e0b', // Use direct color value for better color picker integration
                    strokeWidth: 1.2,
                    opacity: 0.4,
                    strokeDasharray: '4,2'
                },
                animation: {
                    duration: 300,
                    easing: 'd3.easeQuadInOut'
                },
                performance: {
                    maxLinksShown: 500, // Limit for performance
                    viewportBuffer: 100 // Pixels buffer for culling
                }
            };

            // Initialize debug helpers
            this.debug = {
                log: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.log(category, message, data);
                    }
                },
                success: function(category, message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.success(category, message, data);
                    }
                }
            };
            
            this.init();
        }
        
        /**
         * Initialize the link overlay system
         */
        init() {
            this.findD3Elements();
            this.setupLinkGenerator();
            this.bindEventListeners();
            
            this.debug.log('Link Overlay', 'Initialized');
        }
        
        /**
         * Find and store references to existing D3.js tree elements
         */
        findD3Elements() {
            this.svg = d3.select('#slmm-tree-svg');
            
            if (this.svg.empty()) {
                this.debug.log('Link Overlay', 'Tree SVG not found, retrying...');
                setTimeout(() => this.findD3Elements(), 1000);
                return false;
            }
            
            this.treeGroup = this.svg.select('.slmm-tree-group');
            
            if (this.treeGroup.empty()) {
                this.debug.log('Link Overlay', 'Tree group not found');
                return false;
            }
            
            // Create link overlay group (inserted before tree nodes for proper z-order)
            this.linkGroup = this.treeGroup.select('.slmm-link-overlay-group');
            if (this.linkGroup.empty()) {
                this.linkGroup = this.treeGroup.insert('g', ':first-child')
                    .attr('class', 'slmm-link-overlay-group')
                    .style('opacity', 0);
            }
            
            this.debug.success('Link Overlay', 'D3.js elements found and initialized');
            return true;
        }
        
        /**
         * Setup D3.js link generator for smooth curves
         */
        setupLinkGenerator() {
            this.linkGenerator = d3.linkHorizontal()
                .x(d => d.x)
                .y(d => d.y);
        }
        
        /**
         * Bind event listeners for toggle button and tree interactions
         */
        bindEventListeners() {
            // Toggle switch handler (now checkbox instead of button)
            $('#slmm-toggle-links').on('change', (e) => {
                this.toggle();
            });
            
            // Color picker handlers
            $('#slmm-internal-color').on('change', (e) => {
                this.updateLinkColor('internal', e.target.value);
            });
            
            $('#slmm-external-color').on('change', (e) => {
                this.updateLinkColor('external', e.target.value);
            });
            
            // Legend toggle
            $('#slmm-show-legend').on('change', (e) => {
                this.toggleLegend(e.target.checked);
            });
            
            // Thickness slider handler
            $('#slmm-thickness-slider').on('input', (e) => {
                this.updateLinkThickness(parseFloat(e.target.value));
            });
            
            // Listen for tree updates to refresh link positions
            $(document).on('slmmTreeUpdated', () => {
                if (this.isActive) {
                    this.updateNodePositions();
                    this.renderLinks();
                }
            });
            
            // Listen for tree zoom/pan to update viewport culling
            if (this.treeGroup.node()) {
                const observer = new MutationObserver(() => {
                    if (this.isActive) {
                        this.debounceViewportUpdate();
                    }
                });
                observer.observe(this.treeGroup.node(), {
                    attributes: true,
                    attributeFilter: ['transform']
                });
            }
        }
        
        /**
         * Toggle link visualization on/off
         */
        async toggle() {
            const toggleInput = $('#slmm-toggle-links');
            const toggleSwitch = $('.slmm-toggle-switch');
            const controlsPanel = $('#slmm-link-controls-panel');
            const thicknessContainer = $('.slmm-thickness-control-container');
            
            if (this.isActive) {
                // Turn off
                this.hideLinks();
                toggleInput.prop('checked', false);
                this.isActive = false;
                
                // Hide controls panel and thickness control when links are inactive
                controlsPanel.removeClass('show');
                thicknessContainer.hide();
            } else {
                // Turn on
                toggleSwitch.addClass('loading');
                
                try {
                    await this.loadLinkData();
                    this.showLinks();
                    toggleInput.prop('checked', true);
                    this.isActive = true;
                    
                    // Show controls panel and thickness control when links are active
                    controlsPanel.addClass('show');
                    thicknessContainer.show();
                } catch (error) {
                    this.debug.log('Link Overlay', 'Failed to load link data', error);
                    this.showError('Failed to load link data. Please try again.');
                    
                    // Reset toggle state on error
                    toggleInput.prop('checked', false);
                } finally {
                    toggleSwitch.removeClass('loading');
                }
            }
        }
        
        /**
         * Load link data from server via existing AJAX endpoint
         */
        async loadLinkData() {
            const activeTab = $('.slmm-tab-button.active').data('tab');
            
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'slmm_generate_silo_grid',
                        nonce: slmmInterlinkingData.nonce,
                        post_type_filter: activeTab,
                        include_links: 'true' // Request link data
                    },
                    success: (response) => {
                        if (response.success && response.data) {
                            this.linkData.internal = response.data.internal_links || [];
                            this.linkData.external = response.data.external_links || [];
                            
                            this.debug.success('Link Overlay', 
                                `Loaded ${this.linkData.internal.length} internal links, ${this.linkData.external.length} external links`
                            );
                            
                            resolve(response.data);
                        } else {
                            reject(new Error(response.data || 'Unknown error'));
                        }
                    },
                    error: (xhr, status, error) => {
                        reject(new Error(`AJAX error: ${error}`));
                    }
                });
            });
        }
        
        /**
         * Show links with animated transition
         */
        showLinks() {
            if (!this.findD3Elements()) {
                this.debug.log('Link Overlay', 'D3.js elements not ready');
                return;
            }
            
            this.updateNodePositions();
            this.renderLinks();
            
            // Animate in
            this.linkGroup
                .transition()
                .duration(this.config.animation.duration)
                .style('opacity', 1);
                
            // Create legend if show legend is checked
            if ($('#slmm-show-legend').is(':checked')) {
                this.toggleLegend(true);
            }
                
            this.updateStatusMessage();
        }
        
        /**
         * Hide links with animated transition
         */
        hideLinks() {
            if (this.linkGroup) {
                this.linkGroup
                    .transition()
                    .duration(this.config.animation.duration)
                    .style('opacity', 0)
                    .on('end', () => {
                        this.linkGroup.selectAll('*').remove();
                    });
            }
            
            this.updateStatusMessage();
        }
        
        /**
         * Update cached node positions for performance
         */
        updateNodePositions() {
            this.nodePositions.clear();
            this.visibleNodes.clear();
            
            this.debug.log('Link Overlay', 'Updating node positions...');
            
            // Cache all visible node positions
            let nodeCount = 0;
            d3.selectAll('.slmm-tree-node').each((d) => {
                if (d && d.data && d.data.id) {
                    const nodeId = String(d.data.id); // Ensure consistent string format
                    this.nodePositions.set(nodeId, {
                        x: d.x || 0,
                        y: d.y || 0,
                        visible: true
                    });
                    this.visibleNodes.add(nodeId);
                    nodeCount++;
                    
                    if (nodeCount <= 5) { // Log first 5 for debugging
                        this.debug.log('Link Overlay', `Node ${nodeId} at position (${d.x}, ${d.y})`);
                    }
                }
            });
            
            this.debug.success('Link Overlay', `Cached positions for ${nodeCount} nodes`);
        }
        
        /**
         * Render link overlays with performance optimizations
         */
        renderLinks() {
            if (!this.linkGroup) {
                this.debug.log('Link Overlay', 'No link group available for rendering');
                return;
            }
            
            this.debug.log('Link Overlay', 'Starting renderLinks()');
            
            // Clear existing links
            this.linkGroup.selectAll('.slmm-link-overlay').remove();
            
            // Prepare link data for rendering
            const renderableLinks = this.prepareRenderableLinks();
            
            if (renderableLinks.length === 0) {
                this.debug.log('Link Overlay', 'No renderable links found');
                return;
            }
            
            // Limit links for performance
            const linksToRender = renderableLinks.slice(0, this.config.performance.maxLinksShown);
            
            if (linksToRender.length < renderableLinks.length) {
                this.debug.log('Link Overlay', `Limiting to ${linksToRender.length} links for performance`);
            }
            
            // Separate internal and external links
            const internalLinks = linksToRender.filter(l => l.type === 'internal');
            const externalLinks = linksToRender.filter(l => l.type === 'external');
            
            this.debug.log('Link Overlay', `Rendering ${internalLinks.length} internal links and ${externalLinks.length} external links`);
            
            // Render internal links
            if (internalLinks.length > 0) {
                this.renderInternalLinks(internalLinks);
            }
            
            // Render external links (as arcs from nodes to edge indicators)
            if (externalLinks.length > 0) {
                this.renderExternalLinks(externalLinks);
            }
            
            this.debug.success('Link Overlay', 'Finished rendering links');
        }
        
        /**
         * Prepare links for rendering with position data
         */
        prepareRenderableLinks() {
            const renderableLinks = [];
            
            this.debug.log('Link Overlay', 'Preparing links for rendering...');
            this.debug.log('Link Overlay', 'Available node positions', Array.from(this.nodePositions.keys()));
            this.debug.log('Link Overlay', 'Internal links data', this.linkData.internal);
            this.debug.log('Link Overlay', 'External links data', this.linkData.external);
            
            // Process internal links
            this.linkData.internal.forEach((link, index) => {
                // Convert IDs to strings to ensure consistent matching
                const fromId = String(link.from || link.source_id || link.post_from);
                const toId = String(link.to || link.target_id || link.post_to);
                
                const sourcePos = this.nodePositions.get(fromId);
                const targetPos = this.nodePositions.get(toId);
                
                if (sourcePos && targetPos) {
                    renderableLinks.push({
                        ...link,
                        source: sourcePos,
                        target: targetPos,
                        linkType: 'internal',
                        type: 'internal'
                    });
                    this.debug.log('Link Overlay', `Added internal link ${index}: ${fromId} -> ${toId}`);
                } else {
                    this.debug.log('Link Overlay', `Missing positions for internal link ${index}: ${fromId} -> ${toId}`, 
                                 { sourcePos, targetPos });
                }
            });
            
            // Process external links
            this.linkData.external.forEach((link, index) => {
                const fromId = String(link.from || link.source_id || link.post_from);
                const sourcePos = this.nodePositions.get(fromId);
                
                if (sourcePos) {
                    renderableLinks.push({
                        ...link,
                        source: sourcePos,
                        linkType: 'external',
                        type: 'external'
                    });
                    this.debug.log('Link Overlay', `Added external link ${index}: ${fromId}`);
                } else {
                    this.debug.log('Link Overlay', `Missing position for external link ${index}: ${fromId}`, 
                                 { sourcePos });
                }
            });
            
            this.debug.success('Link Overlay', `Total renderable links: ${renderableLinks.length}`);
            return renderableLinks;
        }
        
        /**
         * Render internal links as curved paths
         */
        renderInternalLinks(links) {
            this.debug.log('Link Overlay', `renderInternalLinks called with ${links.length} links`);
            
            const internalGroup = this.linkGroup.selectAll('.slmm-internal-links')
                .data([null])
                .join('g')
                .attr('class', 'slmm-internal-links');
                
            const paths = internalGroup.selectAll('.slmm-internal-link')
                .data(links)
                .join('path')
                .attr('class', 'slmm-internal-link slmm-link-overlay')
                .attr('d', d => {
                    const pathData = this.linkGenerator(d);
                    this.debug.log('Link Overlay', 'Generated path for internal link', { pathData, data: d });
                    return pathData;
                })
                .style('fill', 'none')
                .style('stroke', this.config.internal.stroke)
                .style('stroke-width', this.config.internal.strokeWidth)
                .style('opacity', this.config.internal.opacity)
                .style('pointer-events', 'visibleStroke')
                .style('cursor', 'pointer');
                
            this.debug.success('Link Overlay', `Created ${paths.size()} internal link paths`);
                
            // Add hover effects and tooltips
            paths
                .on('mouseover', (event, d) => {
                    d3.select(event.currentTarget)
                        .style('stroke-width', this.config.internal.strokeWidth + 1)
                        .style('opacity', 0.8);
                        
                    this.showLinkTooltip(event, d);
                })
                .on('mouseout', (event, d) => {
                    d3.select(event.currentTarget)
                        .style('stroke-width', this.config.internal.strokeWidth)
                        .style('opacity', this.config.internal.opacity);
                        
                    this.hideLinkTooltip();
                });
        }
        
        /**
         * Render external links as dashed arcs
         */
        renderExternalLinks(links) {
            this.debug.log('Link Overlay', `renderExternalLinks called with ${links.length} links`);
            
            const externalGroup = this.linkGroup.selectAll('.slmm-external-links')
                .data([null])
                .join('g')
                .attr('class', 'slmm-external-links');
                
            // Create arc paths for external links
            const paths = externalGroup.selectAll('.slmm-external-link')
                .data(links)
                .join('path')
                .attr('class', 'slmm-external-link slmm-link-overlay')
                .attr('d', d => {
                    const pathData = this.generateExternalLinkPath(d);
                    this.debug.log('Link Overlay', 'Generated path for external link', { pathData, data: d });
                    return pathData;
                })
                .style('fill', 'none')
                .style('stroke', this.config.external.stroke)
                .style('stroke-width', this.config.external.strokeWidth)
                .style('stroke-dasharray', this.config.external.strokeDasharray)
                .style('opacity', this.config.external.opacity)
                .style('pointer-events', 'visibleStroke')
                .style('cursor', 'pointer');
                
            // Create mindmap-style endpoint nodes for external links
            const nodes = externalGroup.selectAll('.slmm-external-node')
                .data(links)
                .join('circle')
                .attr('class', 'slmm-external-node slmm-link-overlay')
                .attr('cx', d => {
                    const angle = d.visual_angle ? (d.visual_angle * Math.PI / 180) : 0;
                    const distance = d.visual_distance || 80;
                    return d.source.x + Math.cos(angle) * distance;
                })
                .attr('cy', d => {
                    const angle = d.visual_angle ? (d.visual_angle * Math.PI / 180) : 0;
                    const distance = d.visual_distance || 80;
                    return d.source.y + Math.sin(angle) * distance;
                })
                .attr('r', 4) // Small node radius
                .style('fill', this.config.external.stroke)
                .style('stroke', 'white')
                .style('stroke-width', 1)
                .style('opacity', this.config.external.opacity + 0.2)
                .style('cursor', 'pointer');
                
            this.debug.success('Link Overlay', `Created ${paths.size()} external link paths and ${nodes.size()} endpoint nodes`);
                
            // Add hover effects and tooltips for paths
            paths
                .on('mouseover', (event, d) => {
                    d3.select(event.currentTarget)
                        .style('stroke-width', this.config.external.strokeWidth + 1)
                        .style('opacity', 0.7);
                        
                    this.showLinkTooltip(event, d);
                })
                .on('mouseout', (event, d) => {
                    d3.select(event.currentTarget)
                        .style('stroke-width', this.config.external.strokeWidth)
                        .style('opacity', this.config.external.opacity);
                        
                    this.hideLinkTooltip();
                });
                
            // Add hover effects and tooltips for endpoint nodes
            nodes
                .on('mouseover', (event, d) => {
                    d3.select(event.currentTarget)
                        .attr('r', 6) // Expand node on hover
                        .style('opacity', 1)
                        .style('stroke-width', 2);
                        
                    this.showLinkTooltip(event, d);
                })
                .on('mouseout', (event, d) => {
                    d3.select(event.currentTarget)
                        .attr('r', 4) // Return to normal size
                        .style('opacity', this.config.external.opacity + 0.2)
                        .style('stroke-width', 1);
                        
                    this.hideLinkTooltip();
                });
        }
        
        /**
         * Generate path for external links (arc to indicate direction)
         */
        generateExternalLinkPath(d) {
            const source = d.source;
            
            // Use backend-provided positioning data for consistent external link visualization
            const angle = d.visual_angle ? (d.visual_angle * Math.PI / 180) : 0; // Convert degrees to radians
            const distance = d.visual_distance || 80; // Use backend distance or default
            
            // Calculate target position based on domain-specific angle
            const targetX = source.x + Math.cos(angle) * distance;
            const targetY = source.y + Math.sin(angle) * distance;
            
            // Create a curved arc path for visual appeal
            const controlDistance = distance * 0.6; // Control point distance
            const controlX = source.x + Math.cos(angle) * controlDistance;
            const controlY = source.y + Math.sin(angle) * controlDistance - 15; // Slight curve upward
            
            return `M${source.x},${source.y} Q${controlX},${controlY} ${targetX},${targetY}`;
        }
        
        /**
         * Show link tooltip with details
         */
        showLinkTooltip(event, d) {
            let tooltipText = '';
            
            if (d.linkType === 'internal') {
                tooltipText = `Internal Link: ${d.anchor || 'No text'}`;
            } else {
                tooltipText = `External Link: ${d.anchor || 'No text'}<br>
                              Domain: ${d.domain || 'Unknown'}<br>
                              URL: ${d.url}`;
            }
            
            // Use existing tooltip system if available, or create simple one
            if (window.showCustomTooltip) {
                window.showCustomTooltip(event, tooltipText);
            } else {
                this.showSimpleTooltip(event, tooltipText);
            }
        }
        
        /**
         * Hide link tooltip
         */
        hideLinkTooltip() {
            if (window.hideCustomTooltip) {
                window.hideCustomTooltip();
            } else {
                this.hideSimpleTooltip();
            }
        }
        
        /**
         * Simple tooltip implementation
         */
        showSimpleTooltip(event, text) {
            const tooltip = d3.select('body').selectAll('.slmm-link-tooltip')
                .data([null])
                .join('div')
                .attr('class', 'slmm-link-tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0,0,0,0.8)')
                .style('color', 'white')
                .style('padding', '8px 12px')
                .style('border-radius', '4px')
                .style('font-size', '12px')
                .style('pointer-events', 'none')
                .style('z-index', '10000');
                
            tooltip
                .html(text)
                .style('left', (event.pageX + 10) + 'px')
                .style('top', (event.pageY - 10) + 'px')
                .style('opacity', 1);
        }
        
        /**
         * Hide simple tooltip
         */
        hideSimpleTooltip() {
            d3.select('.slmm-link-tooltip').remove();
        }
        
        /**
         * Debounced viewport update for performance
         */
        debounceViewportUpdate() {
            clearTimeout(this.viewportUpdateTimer);
            this.viewportUpdateTimer = setTimeout(() => {
                this.updateViewportCulling();
            }, 100);
        }
        
        /**
         * Update viewport culling to hide off-screen links
         */
        updateViewportCulling() {
            // Simple implementation - can be enhanced with actual viewport calculations
            this.renderLinks();
        }
        
        /**
         * Update status message with link counts
         */
        updateStatusMessage() {
            const totalInternal = this.linkData.internal.length;
            const totalExternal = this.linkData.external.length;
            
            if (this.isActive && (totalInternal > 0 || totalExternal > 0)) {
                const message = `Links: ${totalInternal} internal, ${totalExternal} external`;
                if (window.updateStatusMessage) {
                    window.updateStatusMessage(message);
                }
            }
        }
        
        /**
         * Show error message
         */
        showError(message) {
            if (window.updateStatusMessage) {
                window.updateStatusMessage('Error: ' + message);
            } else {
                this.debug.log('Link Overlay', message);
            }
        }
        
        /**
         * Toggle color controls panel
         */
        toggleColorControls() {
            const panel = $('#slmm-link-color-panel');
            const button = $('#slmm-link-colors');
            
            if (panel.hasClass('visible')) {
                panel.removeClass('visible');
                button.removeClass('button-primary').addClass('button-secondary');
            } else {
                panel.addClass('visible');
                button.removeClass('button-secondary').addClass('button-primary');
            }
        }
        
        /**
         * Update link colors dynamically
         */
        updateLinkColor(type, color) {
            if (type === 'internal') {
                this.config.internal.stroke = color;
                document.documentElement.style.setProperty('--slmm-internal-link-color', color);
                
                // Update existing internal links
                this.linkGroup.selectAll('.slmm-internal-link')
                    .style('stroke', color);
            } else if (type === 'external') {
                this.config.external.stroke = color;
                document.documentElement.style.setProperty('--slmm-external-link-color', color);
                
                // Update existing external links (paths and nodes)
                this.linkGroup.selectAll('.slmm-external-link')
                    .style('stroke', color);
                this.linkGroup.selectAll('.slmm-external-node')
                    .style('fill', color);
            }
            
            // Update legend if visible
            this.updateLegendColors();
        }
        
        /**
         * Update link thickness dynamically
         * @param {number} multiplier - Thickness multiplier (0.5 to 3.0)
         */
        updateLinkThickness(multiplier) {
            // Calculate new stroke widths maintaining proportional relationship
            const baseInternalWidth = 1.5; // Default internal width
            const baseExternalWidth = 1.2;  // Default external width
            
            const newInternalWidth = baseInternalWidth * multiplier;
            const newExternalWidth = baseExternalWidth * multiplier;
            
            // Update configuration
            this.config.internal.strokeWidth = newInternalWidth;
            this.config.external.strokeWidth = newExternalWidth;
            
            // Update existing internal links
            this.linkGroup.selectAll('.slmm-internal-link')
                .style('stroke-width', newInternalWidth);
                
            // Update existing external links (paths and nodes)
            this.linkGroup.selectAll('.slmm-external-link')
                .style('stroke-width', newExternalWidth);
                
            // Update thickness value display
            $('.slmm-thickness-value').text(newInternalWidth.toFixed(1));
            
            this.debug.success('Link Overlay', `Updated thickness to ${multiplier}x (Internal: ${newInternalWidth.toFixed(1)}px, External: ${newExternalWidth.toFixed(1)}px)`);
        }
        
        /**
         * Toggle legend visibility
         */
        toggleLegend(show) {
            const legend = $('.slmm-link-legend');
            
            if (show && this.isActive) {
                if (legend.length === 0) {
                    this.createLegend();
                } else {
                    legend.addClass('visible');
                }
            } else {
                legend.removeClass('visible');
            }
        }
        
        /**
         * Create legend element
         */
        createLegend() {
            const legendHtml = `
                <div class="slmm-link-legend visible">
                    <div class="slmm-legend-item">
                        <span class="slmm-legend-line internal"></span>
                        <span>Internal Links</span>
                    </div>
                    <div class="slmm-legend-item">
                        <span class="slmm-legend-line external"></span>
                        <span>External Links</span>
                    </div>
                </div>
            `;
            
            $('.slmm-canvas-container').append(legendHtml);
            this.updateLegendColors();
        }
        
        /**
         * Update legend colors to match current settings
         */
        updateLegendColors() {
            $('.slmm-legend-line.internal').css('background', this.config.internal.stroke);
            $('.slmm-legend-line.external').css('background', this.config.external.stroke);
        }
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        // Wait for D3.js tree to be ready
        const initInterval = setInterval(() => {
            if (d3.select('#slmm-tree-svg').node() && d3.select('.slmm-tree-group').node()) {
                window.slmmLinkOverlay = new SLMM_LinkOverlay();
                clearInterval(initInterval);
            }
        }, 500);
        
        // Stop trying after 30 seconds
        setTimeout(() => {
            clearInterval(initInterval);
        }, 30000);
    });
    
})(jQuery, d3);