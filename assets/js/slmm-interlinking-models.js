/**
 * SLMM Interlinking Models Handler
 * Handles model loading for interlinking prompts and page summarization
 */
(function($) {
    'use strict';

    // Cache for models to avoid redundant API calls
    let modelsCache = {
        openai: null,
        openrouter: null
    };

    /**
     * Refresh models for a specific prompt type
     */
    function refreshInterlinkingModels(type, provider) {
        const $button = $(`.slmm-refresh-interlinking-models[data-type="${type}"]`);
        const $modelSelect = $(`.slmm-interlinking-prompt[data-type="${type}"] .slmm-gpt-prompt-model`);
        
        // Show loading state
        $button.prop('disabled', true).addClass('loading');
        $modelSelect.prop('disabled', true);
        
        // Make AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'slmm_refresh_models',
                provider: provider,
                field: 'interlinking_' + type,
                nonce: slmmInterlinkingData.nonce || ''
            },
            success: function(response) {
                if (response.success && response.data.models) {
                    // Cache the models
                    modelsCache[provider] = response.data.models;
                    
                    // Update the dropdown
                    updateModelDropdown(type, response.data.models);
                    
                    // Show success message
                    showNotification('Models refreshed successfully', 'success');
                } else {
                    console.error('Failed to refresh models:', response);
                    showNotification('Failed to refresh models. Please check your API key.', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                showNotification('Network error while refreshing models', 'error');
            },
            complete: function() {
                // Remove loading state
                $button.prop('disabled', false).removeClass('loading');
                $modelSelect.prop('disabled', false);
            }
        });
    }

    /**
     * Update model dropdown with new models
     */
    function updateModelDropdown(type, models) {
        const $modelSelect = $(`.slmm-interlinking-prompt[data-type="${type}"] .slmm-gpt-prompt-model`);
        const currentValue = $modelSelect.val();
        
        // Clear existing options
        $modelSelect.empty();
        
        // Add new options
        $.each(models, function(modelId, modelName) {
            $modelSelect.append(
                $('<option>').val(modelId).text(modelName)
            );
        });
        
        // Try to restore previous selection
        if (currentValue && $modelSelect.find(`option[value="${currentValue}"]`).length) {
            $modelSelect.val(currentValue);
        } else {
            // Select first option if previous selection not available
            $modelSelect.prop('selectedIndex', 0);
        }
    }

    /**
     * Handle provider change
     */
    function handleProviderChange(type, provider) {
        console.log('Provider changed to:', provider, 'for type:', type);
        
        // Check if we have cached models for this provider
        if (modelsCache[provider]) {
            updateModelDropdown(type, modelsCache[provider]);
        } else {
            // Fetch models for the new provider
            refreshInterlinkingModels(type, provider);
        }
    }

    /**
     * Show notification message
     */
    function showNotification(message, type) {
        // Create notification element
        const $notification = $('<div>')
            .addClass('slmm-notification')
            .addClass('slmm-notification-' + type)
            .text(message)
            .css({
                position: 'fixed',
                top: '32px',
                right: '20px',
                zIndex: 9999,
                padding: '12px 20px',
                borderRadius: '4px',
                fontSize: '14px',
                fontWeight: '500',
                maxWidth: '400px',
                backgroundColor: type === 'success' ? '#46a049' : '#dc3545',
                color: '#fff',
                boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
            });
        
        // Add to body
        $('body').append($notification);
        
        // Auto-hide after 3 seconds
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 3000);
    }

    /**
     * Load default prompts and reset all fields
     */
    function loadDefaultPrompts() {
        const $button = $('#slmm-load-default-prompts');

        // Show loading state
        $button.prop('disabled', true);
        $button.find('.dashicons').addClass('loading');
        $button.find('span:last').text('Loading...');

        // Make AJAX request
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'slmm_load_default_prompts',
                nonce: slmmInterlinkingData.loadDefaultsNonce || ''
            },
            success: function(response) {
                if (response.success && response.data) {
                    // Reset interlinking prompts
                    if (response.data.interlinking_prompts) {
                        $.each(response.data.interlinking_prompts, function(type, promptData) {
                            const $prompt = $(`.slmm-interlinking-prompt[data-type="${type}"]`);
                            if ($prompt.length) {
                                // Update provider
                                $prompt.find('.slmm-interlinking-provider').val(promptData.provider);

                                // Update model (trigger provider change to load models)
                                handleProviderChange(type, promptData.provider);
                                setTimeout(() => {
                                    $prompt.find('.slmm-gpt-prompt-model').val(promptData.model);
                                }, 500);

                                // Update prompt text
                                $prompt.find('.slmm-gpt-prompt-content').val(promptData.prompt);

                                // Update temperature and max_tokens
                                $prompt.find('.slmm-gpt-prompt-temperature').val(promptData.temperature);
                                $prompt.find('.slmm-gpt-prompt-max-tokens').val(promptData.max_tokens);
                            }
                        });
                    }

                    // Reset page summarization
                    if (response.data.page_summarization) {
                        const $pageSum = $('.slmm-interlinking-prompt[data-type="summarization"]');
                        if ($pageSum.length) {
                            const sumData = response.data.page_summarization;
                            $pageSum.find('.slmm-interlinking-provider').val(sumData.provider);
                            $pageSum.find('.slmm-gpt-prompt-model').val(sumData.model);
                            $pageSum.find('.slmm-gpt-prompt-content').val(sumData.prompt);
                            $pageSum.find('.slmm-gpt-prompt-temperature').val(sumData.temperature);
                            $pageSum.find('.slmm-gpt-prompt-max-tokens').val(sumData.max_tokens);
                        }
                    }

                    // Reset linking rules
                    if (response.data.linking_rules) {
                        $('textarea[name="slmm_interlinking_rules[prompt]"]').val(response.data.linking_rules);
                    }

                    // Show success message
                    showNotification(response.data.message || 'Default values loaded successfully!', 'success');
                } else {
                    console.error('Failed to load defaults:', response);
                    showNotification('Failed to load default values. Please try again.', 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                showNotification('Network error while loading defaults', 'error');
            },
            complete: function() {
                // Remove loading state
                $button.prop('disabled', false);
                $button.find('.dashicons').removeClass('loading');
                $button.find('span:last').text('Load Default');
            }
        });
    }

    /**
     * Initialize on document ready
     */
    $(document).ready(function() {
        // Handle provider changes for interlinking prompts
        $(document).on('change', '.slmm-interlinking-provider', function() {
            const type = $(this).data('type');
            const provider = $(this).val();
            handleProviderChange(type, provider);
        });

        // Handle refresh button clicks
        $(document).on('click', '.slmm-refresh-interlinking-models', function(e) {
            e.preventDefault();
            const type = $(this).data('type');
            const $providerSelect = $(`.slmm-interlinking-provider[data-type="${type}"]`);
            const provider = $providerSelect.val() || 'openai';
            refreshInterlinkingModels(type, provider);
        });

        // Handle Load Default button clicks
        $(document).on('click', '#slmm-load-default-prompts', function(e) {
            e.preventDefault();

            // Confirm action with user
            if (confirm('This will reset all AI interlinking prompts and page summarization to their default values. Are you sure you want to continue?')) {
                loadDefaultPrompts();
            }
        });
        
        // Add spinner animation CSS
        if (!$('#slmm-interlinking-spinner-css').length) {
            $('<style>')
                .attr('id', 'slmm-interlinking-spinner-css')
                .text(`
                    @keyframes slmm-spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                    .slmm-refresh-interlinking-models.loading,
                    #slmm-load-default-prompts .dashicons.loading {
                        animation: slmm-spin 1s linear infinite;
                    }
                    .slmm-interlinking-prompt .slmm-model-selector {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    }
                    #slmm-load-default-prompts {
                        background: #7a39e8;
                        border: 1px solid #6a31d4;
                        color: #fff;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: 500;
                        transition: all 0.2s ease;
                        box-shadow: 0 2px 4px rgba(122, 57, 232, 0.2);
                    }
                    #slmm-load-default-prompts:hover:not(:disabled) {
                        background: #6a31d4;
                        border-color: #5a29b8;
                        box-shadow: 0 3px 6px rgba(122, 57, 232, 0.3);
                    }
                    #slmm-load-default-prompts:disabled {
                        opacity: 0.6;
                        cursor: not-allowed;
                    }
                    .slmm-load-defaults-section {
                        text-align: center;
                        padding: 20px;
                        background: #2c3e50;
                        border: 1px solid #34495e;
                        border-radius: 4px;
                        margin: 1.5rem 0;
                    }
                    .slmm-load-defaults-section .description {
                        color: #bdc3c7;
                        font-style: italic;
                        margin-top: 8px !important;
                    }
                `)
                .appendTo('head');
        }
    });

})(jQuery);