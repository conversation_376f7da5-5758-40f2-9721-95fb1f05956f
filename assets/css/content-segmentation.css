/**
 * SLMM Content Segmentation - Visual Styles
 * 
 * CSS styles for content segmentation visual overlays, markers,
 * and TinyMCE integration elements.
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage CSS
 * @version 1.0.0
 * @since 4.10.0
 */

/* ========================================
   TINYMCE EDITOR STYLES
   ======================================== */

/* Segmentation button in TinyMCE toolbar */
.mce-slmm_segmentation_button .mce-ico {
    font-family: 'dashicons' !important;
    font-size: 16px !important;
    line-height: 1 !important;
}

.mce-slmm_segmentation_button .mce-ico:before {
    content: "\f535" !important; /* Dashicons grid view */
}

.mce-slmm_segmentation_button.mce-active,
.mce-slmm_segmentation_button:hover {
    background-color: #0073aa !important;
    color: #fff !important;
}

/* ========================================
   VISUAL MARKERS IN EDITOR
   ======================================== */

/* Base marker styles */
.slmm-segment-marker {
    display: block !important;
    width: 100% !important;
    height: 3px !important;
    margin: 15px 0 !important;
    position: relative !important;
    border: none !important;
    background: transparent !important;
    cursor: pointer !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* Top section marker */
.slmm-segment-marker-top {
    background: linear-gradient(
        90deg, 
        transparent 0%, 
        rgba(245, 158, 11, 0.3) 20%, 
        rgba(245, 158, 11, 0.8) 50%, 
        rgba(245, 158, 11, 0.3) 80%, 
        transparent 100%
    ) !important;
    box-shadow: 0 0 8px rgba(245, 158, 11, 0.4) !important;
}

/* Bottom section marker */
.slmm-segment-marker-bottom {
    background: linear-gradient(
        90deg, 
        transparent 0%, 
        rgba(239, 68, 68, 0.3) 20%, 
        rgba(239, 68, 68, 0.8) 50%, 
        rgba(239, 68, 68, 0.3) 80%, 
        transparent 100%
    ) !important;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.4) !important;
}

/* Marker labels */
.slmm-segment-marker::before {
    content: attr(data-label) !important;
    position: absolute !important;
    top: -12px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(0, 0, 0, 0.85) !important;
    color: white !important;
    padding: 3px 10px !important;
    border-radius: 4px !important;
    font-size: 10px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    white-space: nowrap !important;
    pointer-events: none !important;
    z-index: 10 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif !important;
}

/* Marker hover effects */
.slmm-segment-marker:hover {
    transform: scaleY(1.5) !important;
    transition: transform 0.2s ease !important;
}

.slmm-segment-marker:hover::before {
    background: rgba(0, 0, 0, 1) !important;
    transform: translateX(-50%) scale(1.1) !important;
}

/* ========================================
   CONTENT SECTION OVERLAYS
   ======================================== */

/* Base section overlay styles */
.slmm-content-section {
    position: relative !important;
    min-height: 20px !important;
    padding: 5px !important;
    margin: 2px 0 !important;
    border-radius: 4px !important;
    transition: background-color 0.3s ease !important;
}

/* TOP section - Yellow overlay */
.slmm-content-section-top,
.slmm-has-top-marker p:first-child,
.slmm-has-top-marker h1:first-child,
.slmm-has-top-marker h2:first-child,
.slmm-has-top-marker h3:first-child,
.slmm-has-top-marker div:first-child {
    background: rgba(245, 158, 11, 0.1) !important;
    border-left: 3px solid rgba(245, 158, 11, 0.6) !important;
    position: relative !important;
}

/* MIDDLE section - Red overlay */
.slmm-content-section-middle {
    background: rgba(239, 68, 68, 0.1) !important;
    border-left: 3px solid rgba(239, 68, 68, 0.6) !important;
    position: relative !important;
}

/* BOTTOM section - Neutral (no overlay) */
.slmm-content-section-bottom {
    background: transparent !important;
    border-left: 3px solid rgba(107, 114, 128, 0.4) !important;
    position: relative !important;
}

/* Section name indicators */
.slmm-content-section-top::before,
.slmm-content-section-middle::before,
.slmm-content-section-bottom::before {
    content: attr(data-section-name) !important;
    position: absolute !important;
    top: 2px !important;
    right: 2px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 2px 6px !important;
    border-radius: 3px !important;
    font-size: 9px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    z-index: 20 !important;
    pointer-events: none !important;
    font-family: monospace !important;
    letter-spacing: 0.5px !important;
}

/* Section-specific indicator colors */
.slmm-content-section-top::before {
    background: rgba(245, 158, 11, 0.95) !important;
    color: white !important;
}

.slmm-content-section-middle::before {
    background: rgba(239, 68, 68, 0.95) !important;
    color: white !important;
}

.slmm-content-section-bottom::before {
    background: rgba(107, 114, 128, 0.95) !important;
    color: white !important;
}

/* ========================================
   EDITOR BODY CLASSES FOR SEGMENTATION
   ======================================== */

/* Editor body classes to indicate marker presence */
.mce-content-body.slmm-has-top-marker,
.mce-content-body.slmm-has-bottom-marker,
.mce-content-body.slmm-has-both-markers {
    position: relative !important;
}

/* Visual indicator when markers are present */
.mce-content-body.slmm-has-both-markers::before {
    content: "CONTENT SEGMENTATION ACTIVE" !important;
    position: fixed !important;
    top: 10px !important;
    right: 10px !important;
    background: rgba(16, 185, 129, 0.9) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 10px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    z-index: 1000 !important;
    pointer-events: none !important;
    font-family: monospace !important;
    letter-spacing: 0.5px !important;
}

/* ========================================
   POSITIONED OVERLAY SYSTEM
   ======================================== */

/* Base overlay element styling */
.slmm-section-overlay {
    position: absolute !important;
    pointer-events: none !important;
    z-index: 1 !important;
    border-radius: 2px !important;
    transition: opacity 0.3s ease !important;
}

/* TOP section overlay - Yellow */
.slmm-section-overlay.slmm-section-top {
    background-color: rgba(255, 255, 0, 0.1) !important;
    border: 1px solid rgba(245, 158, 11, 0.2) !important;
}

/* MIDDLE section overlay - Red */
.slmm-section-overlay.slmm-section-middle {
    background-color: rgba(255, 0, 0, 0.1) !important;
    border: 1px solid rgba(239, 68, 68, 0.2) !important;
}

/* Section labels */
.slmm-section-label {
    position: absolute !important;
    top: 2px !important;
    right: 2px !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 1px 4px !important;
    font-size: 9px !important;
    font-weight: bold !important;
    border-radius: 2px !important;
    font-family: monospace !important;
    pointer-events: none !important;
    z-index: 2 !important;
}

/* Hover effects for overlays */
.slmm-section-overlay:hover {
    opacity: 0.8 !important;
}

/* Hide overlays during text selection */
.mce-content-body.mce-edit-focus .slmm-section-overlay {
    opacity: 0.3 !important;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

/* Small screens adjustments */
@media (max-width: 768px) {
    .slmm-segment-marker {
        margin: 10px 0 !important;
        height: 2px !important;
    }
    
    .slmm-segment-marker::before {
        font-size: 9px !important;
        padding: 2px 6px !important;
        top: -10px !important;
    }
    
    .slmm-content-section-top::before,
    .slmm-content-section-middle::before,
    .slmm-content-section-bottom::before {
        font-size: 8px !important;
        padding: 1px 4px !important;
    }
}

/* ========================================
   ACCESSIBILITY ENHANCEMENTS
   ======================================== */

/* High contrast mode support */
@media (prefers-contrast: high) {
    .slmm-segment-marker-top {
        background: linear-gradient(90deg, transparent, #000000, transparent) !important;
        box-shadow: 0 0 4px #000000 !important;
    }
    
    .slmm-segment-marker-bottom {
        background: linear-gradient(90deg, transparent, #000000, transparent) !important;
        box-shadow: 0 0 4px #000000 !important;
    }
    
    .slmm-content-section-top {
        background: rgba(0, 0, 0, 0.1) !important;
        border-left: 4px solid #000000 !important;
    }
    
    .slmm-content-section-middle {
        background: rgba(0, 0, 0, 0.05) !important;
        border-left: 4px solid #666666 !important;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .slmm-segment-marker {
        transition: none !important;
    }
    
    .slmm-segment-marker:hover {
        transform: none !important;
    }
    
    .slmm-content-section {
        transition: none !important;
    }
}

/* ========================================
   PRINT STYLES
   ======================================== */

/* Hide markers and overlays in print */
@media print {
    .slmm-segment-marker,
    .slmm-segment-marker-top,
    .slmm-segment-marker-bottom {
        display: none !important;
    }
    
    .slmm-content-section-top,
    .slmm-content-section-middle,
    .slmm-content-section-bottom {
        background: transparent !important;
        border: none !important;
    }
    
    .slmm-content-section-top::before,
    .slmm-content-section-middle::before,
    .slmm-content-section-bottom::before {
        display: none !important;
    }
}

/* ========================================
   ANIMATION KEYFRAMES
   ======================================== */

/* Subtle pulse animation for newly inserted markers */
@keyframes slmm-marker-inserted {
    0% {
        opacity: 0;
        transform: scaleY(0.1);
    }
    50% {
        opacity: 1;
        transform: scaleY(1.2);
    }
    100% {
        opacity: 1;
        transform: scaleY(1);
    }
}

/* Apply insertion animation */
.slmm-segment-marker.slmm-just-inserted {
    animation: slmm-marker-inserted 0.4s ease-out !important;
}

/* Fade out animation for removed markers */
@keyframes slmm-marker-removed {
    0% {
        opacity: 1;
        transform: scaleY(1);
    }
    100% {
        opacity: 0;
        transform: scaleY(0.1);
    }
}

.slmm-segment-marker.slmm-being-removed {
    animation: slmm-marker-removed 0.3s ease-in forwards !important;
}

/* ========================================
   DIRECT EDITOR MODAL INTEGRATION STYLES
   ======================================== */

/* Content Segmentation Section in Direct Editor Modal */
.slmm-content-segmentation {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #444;
}

.slmm-segmentation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 8px;
}

.slmm-segmentation-label {
    font-weight: 600;
    color: #f0f0f1;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.slmm-segmentation-info {
    color: #999;
    cursor: help;
    font-size: 14px;
}

.slmm-segmentation-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slmm-segmentation-buttons {
    display: flex;
    gap: 6px;
}

.slmm-segment-btn {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    min-height: 28px;
}

.slmm-segment-btn:hover {
    background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.slmm-segment-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.2);
}

.slmm-segment-btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.slmm-segment-btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}

.slmm-segment-icon {
    font-size: 12px;
}

.slmm-segmentation-status {
    background: #1f2937;
    border-radius: 4px;
    padding: 6px 10px;
    font-size: 11px;
    border-left: 3px solid #6b7280;
    display: none;
}

.slmm-segmentation-status.success {
    border-left-color: #10b981;
    color: #34d399;
}

.slmm-segmentation-status.error {
    border-left-color: #ef4444;
    color: #f87171;
}

.slmm-segmentation-status.loading {
    border-left-color: #f59e0b;
    color: #fbbf24;
}

.slmm-segmentation-status-text {
    font-weight: 500;
}

/* Responsive adjustments for small screens */
@media (max-width: 768px) {
    .slmm-segmentation-buttons {
        flex-direction: column;
    }
    
    .slmm-segment-btn {
        width: 100%;
        justify-content: center;
    }
}

/* ========================================
   DIRECT EDITOR MODAL INTEGRATION
   ======================================== */

/* Content Segmentation section in Direct Editor sidebar */
.slmm-content-segmentation {
    margin: 15px 0;
    padding: 12px;
    background: #1a1a1a;
    border-radius: 6px;
    border: 1px solid #333;
}

.slmm-segmentation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
}

.slmm-segmentation-label {
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    color: #ecf0f1;
    letter-spacing: 0.5px;
}

.slmm-segmentation-info {
    font-size: 12px;
    color: #bdc3c7;
    cursor: help;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.slmm-segmentation-info:hover {
    opacity: 1;
}

.slmm-segmentation-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.slmm-segmentation-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.slmm-segment-btn {
    flex: 1;
    min-width: 0;
    padding: 8px 12px;
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    border: 1px solid #7c3aed;
    color: white;
    font-size: 11px;
    font-weight: 500;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    text-decoration: none;
    box-shadow: 0 2px 4px rgba(124, 58, 237, 0.2);
}

.slmm-segment-btn:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    border-color: #6d28d9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(124, 58, 237, 0.3);
}

.slmm-segment-btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(124, 58, 237, 0.2);
}

.slmm-segment-btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    border-color: #4b5563;
    box-shadow: 0 2px 4px rgba(75, 85, 99, 0.2);
}

.slmm-segment-btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
    border-color: #374151;
    box-shadow: 0 4px 8px rgba(75, 85, 99, 0.3);
}

.slmm-segment-icon {
    font-size: 12px;
}

.slmm-segmentation-status {
    display: none;
    padding: 6px 10px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
}

.slmm-segmentation-status.success {
    background: rgba(46, 204, 113, 0.2);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.slmm-segmentation-status.error {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.slmm-segmentation-status.loading {
    background: rgba(52, 152, 219, 0.2);
    color: #3498db;
    border: 1px solid rgba(52, 152, 219, 0.3);
}

/* Responsive adjustments for segmentation controls */
@media (max-width: 768px) {
    .slmm-segmentation-buttons {
        flex-direction: column;
    }
    
    .slmm-segment-btn {
        flex: none;
    }
}

/* ========================================
   TOGGLE BUTTON STYLES
   ======================================== */

/* Overlay toggle button styling */
.slmm-overlay-toggle {
    background: none !important;
    border: none !important;
    color: #999 !important;
    cursor: pointer !important;
    padding: 4px !important;
    margin-left: 8px !important;
    border-radius: 3px !important;
    transition: all 0.2s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.slmm-overlay-toggle:hover {
    color: #f0f0f1 !important;
    background-color: rgba(255,255,255,0.1) !important;
}

.slmm-overlay-toggle.active {
    color: #10b981 !important;
}

/* Ensure overlays stay on top */
.mce-content-body .slmm-section-overlay {
    position: absolute !important;
    pointer-events: none !important;
    z-index: 10 !important;
}

/* ========================================
   DEBUG STYLES (only in debug mode)
   ======================================== */

/* Debug information overlay */
.slmm-debug-enabled .slmm-segment-marker::after {
    content: "Marker: " attr(data-marker-type) " | Pos: " attr(data-position) !important;
    position: absolute !important;
    bottom: -18px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: rgba(255, 0, 255, 0.8) !important;
    color: white !important;
    padding: 2px 6px !important;
    border-radius: 2px !important;
    font-size: 8px !important;
    font-family: monospace !important;
    white-space: nowrap !important;
    pointer-events: none !important;
}

/* Debug section boundaries */
.slmm-debug-enabled .slmm-content-section-top,
.slmm-debug-enabled .slmm-content-section-middle,
.slmm-debug-enabled .slmm-content-section-bottom {
    outline: 2px dashed rgba(255, 0, 255, 0.5) !important;
    outline-offset: 2px !important;
}