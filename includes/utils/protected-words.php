<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_Protected_Words {
    private static $instance = null;
    private $protected_words_cache = null;

    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function __construct() {
        // Initialize hooks
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('update_option_chatgpt_generator_options', array($this, 'update_protected_words_js'), 10, 2);
        add_action('wp_ajax_add_protected_words', array($this, 'add_protected_words'));
        add_action('add_meta_boxes', array($this, 'add_protected_words_meta_box'));
        
        // Add meta box to post types and admin pages
        if (is_admin()) {
            add_action('load-post.php', array($this, 'add_screen_options'));
            add_action('load-post-new.php', array($this, 'add_screen_options'));
            add_action('load-settings_page_slmm-seo-settings', array($this, 'add_screen_options'));
            add_filter('hidden_meta_boxes', array($this, 'show_meta_box_by_default'), 10, 3);
            add_filter('set-screen-option', array($this, 'save_screen_options'), 10, 3);
        }
    }

    public function enqueue_scripts() {
        wp_enqueue_script(
            'slmm-protected-words',
            plugins_url('/js/protected-words.js', dirname(dirname(__FILE__))),
            array('jquery'),
            '1.0',
            true
        );

        // Pass protected words to JavaScript
        wp_localize_script('slmm-protected-words', 'slmmProtectedWords', array(
            'words' => $this->get_protected_words_array(),
            'ajaxurl' => admin_url('admin-ajax.php')
        ));
    }

    public function update_protected_words_js($old_value, $new_value) {
        // Clear the cache when options are updated
        $this->protected_words_cache = null;
        
        // Update localStorage via JavaScript
        add_action('admin_footer', array($this, 'update_local_storage'));
    }

    public function update_local_storage() {
        ?>
        <script type="text/javascript">
            localStorage.setItem('slmmProtectedWords', '<?php echo json_encode($this->get_protected_words_array()); ?>');
        </script>
        <?php
    }

    public function get_protected_words_array() {
        if ($this->protected_words_cache === null) {
            $options = get_option('chatgpt_generator_options', array());
            $protected_words = isset($options['protected_words_list']) ? $options['protected_words_list'] : '';
            $this->protected_words_cache = array_filter(array_map('trim', explode("\n", $protected_words)));
        }
        return $this->protected_words_cache;
    }

    public function is_protected_word($word) {
        $protected_words = $this->get_protected_words_array();
        $word = trim($word);
        
        foreach ($protected_words as $protected_word) {
            if (strcasecmp($word, $protected_word) === 0) {
                return $protected_word; // Return the correct capitalization
            }
        }
        
        return false;
    }

    public function apply_sentence_case_with_protection($text) {
        if (empty($text)) return $text;

        // First, lowercase everything
        $text = strtolower($text);
        
        // Capitalize first letter of sentences
        $text = ucfirst(trim($text));
        $text = preg_replace('/([\.\?!]\s+)([a-z])/', '$1' . strtoupper('$2'), $text);
        
        // Split text into words while preserving delimiters
        $words = preg_split('/(\s+|(?=[,.!?])|(?<=[,.!?]))/', $text, -1, PREG_SPLIT_DELIM_CAPTURE);
        
        foreach ($words as $i => $word) {
            $trimmed_word = trim($word);
            if (empty($trimmed_word)) continue;
            
            $protected_version = $this->is_protected_word($trimmed_word);
            if ($protected_version !== false) {
                // Replace the word while preserving surrounding whitespace
                $words[$i] = str_replace($trimmed_word, $protected_version, $word);
            }
        }
        
        return implode('', $words);
    }

    public function add_protected_words() {
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $new_words = isset($_POST['words']) ? sanitize_textarea_field($_POST['words']) : '';
        if (empty($new_words)) {
            wp_send_json_error('No words provided');
            return;
        }

        // Get current options
        $options = get_option('chatgpt_generator_options', array());
        $current_words = isset($options['protected_words_list']) ? $options['protected_words_list'] : '';

        // Add new words
        $words_array = array_filter(array_map('trim', explode("\n", $current_words)));
        $new_words_array = array_filter(array_map('trim', explode("\n", $new_words)));
        $combined_words = array_unique(array_merge($words_array, $new_words_array));
        
        // Update options
        $options['protected_words_list'] = implode("\n", $combined_words);
        update_option('chatgpt_generator_options', $options);

        // Clear cache
        $this->protected_words_cache = null;

        wp_send_json_success(array(
            'message' => 'Protected words added successfully',
            'words' => $this->get_protected_words_array()
        ));
    }

    public function save_screen_options($status, $option, $value) {
        if ($option === 'slmm_protected_words_visible') {
            return $value;
        }
        return $status;
    }

    public function add_screen_options() {
        $screen = get_current_screen();
        add_screen_option('slmm_protected_words_visible', array(
            'label' => __('Protected Words Box', 'slmm-seo-bundle'),
            'default' => 1,
            'option' => 'slmm_protected_words_visible'
        ));
    }

    public function show_meta_box_by_default($hidden, $screen, $context) {
        // Remove our meta box from the hidden list if it's there
        return array_diff($hidden, array('slmm-protected-words-box'));
    }

    public function add_protected_words_meta_box() {
        // Get all public post types
        $post_types = get_post_types(array('public' => true));
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'slmm-protected-words-box',
                'Add Protected Words',
                array($this, 'render_protected_words_box'),
                $post_type,
                'side',
                'default'
            );
        }
    }

    public function render_protected_words_box() {
        ?>
        <div id="slmm-add-protected-words">
            <p><small>Add words to protect (one per line)</small></p>
            <textarea id="slmm-new-protected-words" rows="4" style="width: 100%;"></textarea>
            <p>
                <button type="button" id="slmm-protect-words-btn" class="button button-primary">
                    Protect Words
                </button>
                <span class="spinner" style="float: none; margin-top: 4px;"></span>
            </p>
        </div>
        <?php
    }
}

// Initialize the singleton
SLMM_Protected_Words::get_instance();

// Global functions for backward compatibility
function is_protected_word($word) {
    return SLMM_Protected_Words::get_instance()->is_protected_word($word);
}

function apply_sentence_case_with_protection($text) {
    return SLMM_Protected_Words::get_instance()->apply_sentence_case_with_protection($text);
}
