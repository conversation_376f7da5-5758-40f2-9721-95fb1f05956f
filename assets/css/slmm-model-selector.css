/* SLMM Model Selector Styles */
.slmm-model-container {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin: 10px 0;
    background: #f9f9f9;
}

.slmm-provider-selection {
    margin-bottom: 15px;
}

.slmm-provider-selection label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.slmm-model-search {
    margin-bottom: 15px;
}

.slmm-model-search input {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.slmm-model-search input:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.slmm-model-list {
    min-height: 150px;
    max-height: 300px;
    width: 100%;
    max-width: 500px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    overflow-y: auto;
}

.slmm-model-list option {
    padding: 8px 12px;
    border-bottom: 1px solid #f0f0f0;
}

.slmm-model-list option:hover {
    background-color: #f0f0f0;
}

.slmm-model-list option:selected {
    background-color: #0073aa;
    color: white;
}

.slmm-model-controls {
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.slmm-refresh-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.slmm-refresh-btn.loading {
    opacity: 0.7;
    pointer-events: none;
}

.slmm-model-count {
    color: #666;
    font-style: italic;
    font-size: 13px;
    height: 34px!important;
    align-self: center;
    align-items: center;
    border-radius: 3px;
    font-size: 12px;
    color: #fff;
}

.slmm-model-description {
    margin-top: 10px;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

.slmm-model-description a {
    color: #0073aa;
    text-decoration: none;
}

.slmm-model-description a:hover {
    text-decoration: underline;
}

/* Provider indicator */
.slmm-provider-indicator {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    margin-left: 8px;
}

.slmm-provider-indicator.openai {
    background-color: #10b981;
    color: white;
}

.slmm-provider-indicator.openrouter {
    background-color: #8b5cf6;
    color: white;
}

/* Loading states */
.slmm-loading {
    opacity: 0.6;
    pointer-events: none;
}

.slmm-spin {
    animation: slmm-spin 1s linear infinite;
}

@keyframes slmm-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Search highlighting */
.slmm-model-list option.slmm-highlighted {
    background-color: #fff3cd !important;
    color: #856404 !important;
}

/* No results message */
.slmm-no-results {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Responsive design */
@media (max-width: 782px) {
    .slmm-model-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .slmm-model-search input,
    .slmm-model-list {
        max-width: 100%;
    }
} 