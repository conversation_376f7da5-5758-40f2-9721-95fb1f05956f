/**
 * Lorem Ipsum Detector JavaScript
 * Handles AJAX scanning functionality for the Lorem Ipsum Detector
 */

jQuery(document).ready(function($) {
    'use strict';

    // Debug helper - ALWAYS include this pattern for standalone functions
    var debug = {
        log: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log(category, message, data);
            }
        },
        success: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.success(category, message, data);
            }
        },
        warn: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.warn(category, message, data);
            }
        },
        error: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error(category, message, data);
            }
        },
        info: function(category, message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.info(category, message, data);
            }
        }
    };

    // Initialize Lorem Ipsum Detector
    initializeLoremDetector();

    function initializeLoremDetector() {
        // Bind scan button click
        $(document).on('click', '#slmm-start-lorem-scan', handleScanClick);
        
        // Handle tab activation
        $(document).on('slmm:tabChanged', function(e, tabId) {
            if (tabId === 'lorem-detector') {
                // Tab was activated, could auto-scan here if needed
                debug.log('Lorem Detector', 'Tab activated');
            }
        });
        
        // Initialize keyboard shortcuts for this tab
        initializeKeyboardShortcuts();
    }

    function handleScanClick(e) {
        e.preventDefault();
        
        const $button = $(this);
        const $resultsContainer = $('#slmm-lorem-results');
        
        // Disable button and show loading state
        $button.prop('disabled', true);
        $button.find('.slmm-scan-text').hide();
        $button.find('.slmm-scan-loading').show();
        
        // Clear previous results
        $resultsContainer.empty();
        
        // Show initial loading message
        $resultsContainer.html('<div class="slmm-lorem-loading"><div class="slmm-spinner"></div><p>Scanning your content for Lorem Ipsum text...</p></div>');
        
        // Perform AJAX scan
        performScan($button, $resultsContainer);
    }


    function performScan($button, $resultsContainer) {
        $.ajax({
            url: slmmLoremDetector.ajaxurl,
            type: 'POST',
            data: {
                action: 'start_lorem_ipsum_scan',
                nonce: slmmLoremDetector.nonce
            },
            timeout: 60000, // 60 second timeout
            success: function(response) {
                debug.log('Lorem Detector', 'AJAX response received', response.success ? 'Success' : 'Failed');
                
                if (response.success && response.data && response.data.html) {
                    // Fade out loading, then fade in results
                    $resultsContainer.fadeOut(300, function() {
                        $(this).html(response.data.html).fadeIn(300);
                        
                        // Initialize result interactions
                        initializeResultsTable();
                        
                        // Show completion notification
                        const resultCount = $('.slmm-lorem-table tbody tr').length;
                        if (resultCount > 0) {
                            showNotification(`Scan complete! Found ${resultCount} items with Lorem Ipsum text.`, 'warning');
                        } else {
                            showNotification('Scan complete! No Lorem Ipsum text found.', 'success');
                        }
                    });
                } else if (!response.success) {
                    // Handle WordPress AJAX error response
                    const errorMessage = response.data || 'Unknown server error';
                    debug.error('Lorem Detector', 'Server error', errorMessage);
                    showError($resultsContainer, errorMessage);
                } else {
                    // Handle unexpected response format
                    debug.error('Lorem Detector', 'Invalid response format');
                    showError($resultsContainer, 'Invalid response format from server');
                }
            },
            error: function(xhr, status, error) {
                debug.error('Lorem Detector', 'AJAX error', {
                    status: status,
                    error: error,
                    xhrStatus: xhr.status,
                    hasResponseText: !!xhr.responseText
                });
                
                let errorMessage = 'Network error occurred';
                
                if (status === 'timeout') {
                    errorMessage = 'Scan timed out. Please try again.';
                } else if (xhr.responseJSON && xhr.responseJSON.data) {
                    errorMessage = xhr.responseJSON.data;
                } else if (xhr.responseText) {
                    errorMessage = 'Server error: ' + xhr.responseText.substring(0, 100);
                }
                
                showError($resultsContainer, errorMessage);
            },
            complete: function() {
                // Reset button state
                $button.prop('disabled', false);
                $button.find('.slmm-scan-text').show();
                $button.find('.slmm-scan-loading').hide();
            }
        });
    }

    function initializeResultsTable() {
        // Add sorting functionality to table headers
        $('.slmm-lorem-table th').on('click', function() {
            const $table = $(this).closest('table');
            const $tbody = $table.find('tbody');
            const $rows = $tbody.find('tr');
            const columnIndex = $(this).index();
            const isAscending = !$(this).hasClass('sort-asc');
            
            // Remove sort classes from all headers
            $('.slmm-lorem-table th').removeClass('sort-asc sort-desc');
            
            // Add appropriate sort class
            $(this).addClass(isAscending ? 'sort-asc' : 'sort-desc');
            
            // Sort rows
            const sortedRows = $rows.sort(function(a, b) {
                const cellA = $(a).find('td').eq(columnIndex).text().trim();
                const cellB = $(b).find('td').eq(columnIndex).text().trim();
                
                let comparison = 0;
                if (cellA > cellB) {
                    comparison = 1;
                } else if (cellA < cellB) {
                    comparison = -1;
                }
                
                return isAscending ? comparison : -comparison;
            });
            
            // Reorder table
            $tbody.empty().append(sortedRows);
        });
        
        // Add hover effects and animations
        $('.slmm-lorem-table tbody tr').hover(
            function() {
                $(this).addClass('hover');
            },
            function() {
                $(this).removeClass('hover');
            }
        );
        
        // Handle action link clicks
        $('.slmm-action-link').on('click', function(e) {
            // Links will open in new tabs as specified in HTML
            const action = $(this).hasClass('slmm-edit-link') ? 'edit' : 'view';
            const postTitle = $(this).closest('tr').find('.slmm-lorem-title').text();
            
            debug.log('Lorem Detector', 'Opening ' + action + ' for: ' + postTitle);
        });
    }

    function initializeKeyboardShortcuts() {
        $(document).on('keydown', function(e) {
            // Only handle shortcuts when lorem detector tab is active
            if (!$('#lorem-detector').hasClass('active')) {
                return;
            }
            
            // Ctrl/Cmd + Enter to start scan
            if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                e.preventDefault();
                $('#slmm-start-lorem-scan').trigger('click');
                return false;
            }
        });
    }

    function showError($container, message) {
        const errorHtml = `
            <div class="slmm-lorem-error">
                <div class="slmm-lorem-icon error">❌</div>
                <h3>Scan Error</h3>
                <p>${message}</p>
                <button type="button" class="button button-secondary" onclick="location.reload()">Reload Page</button>
            </div>
        `;
        
        $container.fadeOut(300, function() {
            $(this).html(errorHtml).fadeIn(300);
        });
        
        showNotification(`Scan failed: ${message}`, 'error');
    }

    function showNotification(message, type = 'success') {
        // Remove existing notifications
        $('.slmm-lorem-notification').remove();
        
        const $notification = $(`
            <div class="slmm-lorem-notification slmm-lorem-notification-${type}">
                <span class="slmm-notification-icon"></span>
                <span class="slmm-notification-text">${message}</span>
                <button type="button" class="slmm-notification-close">&times;</button>
            </div>
        `);
        
        // Add to page
        $('body').append($notification);
        
        // Position at top of viewport
        $notification.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 999999
        });
        
        // Show with animation
        $notification.slideDown(300);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $notification.slideUp(300, function() {
                $(this).remove();
            });
        }, 5000);
        
        // Handle close button
        $notification.find('.slmm-notification-close').on('click', function() {
            $notification.slideUp(300, function() {
                $(this).remove();
            });
        });
    }

    // Utility function to format numbers
    function formatNumber(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    }

    // Initialization complete
    debug.success('Lorem Detector', 'Lorem Ipsum Detector initialized successfully');
});