---
task: h-fix-ai-prompt-selection-bug
branch: fix/ai-prompt-selection-bug
status: completed
created: 2025-01-15
modules: [ai-integration, slmm-direct-editor]
---

# Fix AI Prompt Selection Bug in Direct Editor

## Problem/Goal
Critical bug in the AI suggestions feature: when user clicks on a SIBLING page in the interlinking suite, then clicks "AI Suggest Links", the system is NOT using the correct "Sibling Links" prompt from settings. Instead, it appears to be selecting prompts based on the "Assign to Segment" buttons (TOP/MIDDLE/BOTTOM) rather than the active link section (parent/child/sibling/semantic).

## Success Criteria
- [x] AI suggestions correctly use "Sibling Links" prompt when sibling section is active
- [x] AI suggestions correctly use "Parent Links" prompt when parent section is active
- [x] AI suggestions correctly use "Child Links" prompt when child section is active
- [x] AI suggestions correctly use "Semantic Links" prompt when semantic section is active
- [x] Prompt selection is based on active interlinking section, NOT segment assignment buttons
- [x] Tested across all four link types (parent/child/sibling/semantic)
- [x] No regression in existing functionality
- [x] AI Interlinking section remains visible at all times (doesn't disappear on button clicks)

## Context Files
- @assets/js/slmm-direct-editor.js - Main AI prompt selection logic
- @includes/ai-integration/ - AI provider integration files
- @includes/settings/ - GPT prompt settings configuration

## User Notes
User reported that clicking on sibling pages and then AI suggestions uses wrong prompt. The prompt selection logic seems to be incorrectly tied to content segmentation (TOP/MIDDLE/BOTTOM) instead of the hierarchical link type (parent/child/sibling/semantic).

## Work Log
- [2025-01-15] Task created, investigation needed in slmm-direct-editor.js prompt selection logic
- [2025-01-15] **FIXED**: Root cause identified - `determineActiveLinkType()` was using section expansion state instead of tracking user's last clicked link
- [2025-01-15] **IMPLEMENTATION**:
  - Added `lastClickedLinkSection` property to track user's last clicked section
  - Added `trackLinkSectionClick()` helper function to detect section from clicked element
  - Modified all silo link click handlers to call tracking function
  - Updated `determineActiveLinkType()` to prioritize tracked section over expansion state
- [2025-01-15] **UI FIXES COMPLETED**:
  - Fixed text visibility issues in AI suggestions with maximum specificity CSS selectors
  - Fixed button accessibility issues with sticky positioning and proper margins
  - Modified `showDashboardLoading()` to preserve AI Interlinking section visibility during loading states
- [2025-01-15] **TASK COMPLETED**: All success criteria met, AI prompt selection working correctly across all link types with improved UI/UX