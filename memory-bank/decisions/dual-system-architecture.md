# Decision: Dual System Architecture for GPT Prompts

## Date: 2025-01-09

## Context
The SLMM SEO plugin implements two separate systems for GPT prompt execution that must be maintained independently.

## Decision
Maintain the dual system architecture with:

1. **Button System** (`assets/js/slmm-prompt-execution.js`)
   - DOM-driven approach
   - Handles multiple instances with unique IDs
   - Supports ACF fields and various textarea types
   - Uses class-based selectors for flexibility

2. **Keyboard Shortcut System** (`snippets/chat_gpt_title_and_description_generator_v2_0.php`)
   - Data-driven approach using `slmmGptPromptData.prompts`
   - Direct TinyMCE editor integration
   - Uses `executePromptDirectly()` function

## Rationale
- Each system serves different use cases and contexts
- Attempts to unify them have historically caused issues
- Both systems work reliably when left independent
- Existing documentation supports this approach

## Consequences
- Developers must understand both systems
- Changes to one system don't affect the other
- Data localization must support both systems
- Testing requires validating both independently

## Status
Active - This decision is critical and documented in `assets/docs/golden-rules-for-shortcuts.md`