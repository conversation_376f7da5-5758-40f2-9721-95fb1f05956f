# Better Search Replace Plugin - Complete Technical Documentation

## Overview

Better Search Replace is a WordPress plugin that provides advanced database search and replace functionality with support for serialized data, table selection, and dry run capabilities. The plugin is developed by WP Engine and offers a safer alternative to basic search/replace operations.

## Plugin Architecture

### Core Files Structure

```
better-search-replace/
├── better-search-replace.php     # Main plugin file and entry point
├── includes/                     # Core functionality classes
│   ├── class-bsr-main.php       # Main plugin orchestrator
│   ├── class-bsr-admin.php      # Admin interface and menu handling
│   ├── class-bsr-ajax.php       # AJAX request processing
│   ├── class-bsr-db.php         # Database operations and search/replace logic
│   ├── class-bsr-loader.php     # Hook management system
│   ├── class-bsr-i18n.php       # Internationalization
│   ├── class-bsr-utils.php      # Utility functions
│   └── class-bsr-compatibility.php # Compatibility features
├── templates/                    # UI template files
├── assets/                       # CSS, JS, and image assets
└── languages/                    # Translation files
```

## How the Plugin Works

### 1. Plugin Initialization

**Entry Point (`better-search-replace.php`):**
- Defines plugin constants (BSR_PATH, BSR_URL, BSR_VERSION, BSR_NAME)
- Checks user capabilities via `bsr_enabled_for_user()` function
- Requires `manage_options` capability by default (filterable via `bsr_capability` filter)
- Initializes the main plugin class on `after_setup_theme` hook

**Main Class (`BSR_Main`):**
- Loads all dependencies and class files
- Sets up internationalization
- Registers admin hooks and AJAX handlers
- Uses a loader pattern to manage WordPress hooks

### 2. User Interface System

**Admin Interface (`BSR_Admin`):**
- Creates submenu page under Tools → Better Search Replace
- Implements tabbed interface with three sections:
  - **Search/Replace**: Main functionality
  - **Settings**: Configuration options
  - **Help**: Documentation and support

**Template System:**
- Uses separate PHP template files for each tab
- `BSR_Templates_Helper` class manages template loading
- Templates include form validation and user guidance

### 3. Search and Replace Engine

**Core Algorithm (`BSR_DB::srdb()`):**
The heart of the plugin is based on interconnectit's search/replace script with WordPress-specific modifications:

#### Data Processing Flow:
1. **Table Analysis**: Gets table structure and identifies primary keys
2. **Batch Processing**: Processes data in configurable page sizes (default: 20,000 rows)
3. **Column Iteration**: Examines each column in each row
4. **Serialization Handling**: Detects and properly handles serialized PHP data
5. **Search/Replace Execution**: Performs recursive search/replace on all data types

#### Serialization Support:
```php
public function recursive_unserialize_replace($from, $to, $data, $serialised = false, $case_insensitive = false)
```
- Handles strings, arrays, objects, and nested serialized data
- Preserves data integrity during serialization/unserialization
- Supports case-insensitive searches
- Manages object properties and protected/private data safely

How does the plugin handle serialized data during search/replace operations?

The plugin handles serialized data during search/replace operations through the `recursive_unserialize_replace()` method in the `BSR_DB` class. Here's how it works:

````php path=includes/class-bsr-db.php mode=EXCERPT
public function recursive_unserialize_replace($from = '', $to = '', $data = '', $serialised = false, $case_insensitive = false) {
    try {
        // Exit early if $data is a string but has no search matches.
        if (is_string($data)) {
            $has_match = $case_insensitive ? false !== stripos($data, $from) : false !== strpos($data, $from);
            if (!$has_match) {
                return $data;
            }
        }

        if (is_string($data) && !is_serialized_string($data) && ($unserialized = $this->unserialize($data)) !== false) {
            $data = $this->recursive_unserialize_replace($from, $to, $unserialized, true, $case_insensitive);
        }
        elseif (is_array($data)) {
            $_tmp = array();
            foreach ($data as $key => $value) {
                $_tmp[$key] = $this->recursive_unserialize_replace($from, $to, $value, false, $case_insensitive);
            }
            $data = $_tmp;
            unset($_tmp);
        }
        elseif ('object' == gettype($data)) {
            // Handle objects
        }
        elseif (is_serialized_string($data)) {
            $unserialized = $this->unserialize($data);
            if ($unserialized !== false) {
                $data = $this->recursive_unserialize_replace($from, $to, $unserialized, true, $case_insensitive);
            }
        }
        else {
            if (is_string($data)) {
                $data = $this->str_replace($from, $to, $data, $case_insensitive);
            }
        }

        if ($serialised) {
            return serialize($data);
        }
    } catch(Exception $error) {
        // Silent exception handling
    }

    return $data;
}
````

The process works as follows:

1. It first checks if the data is a string with no matches to avoid unnecessary processing.
2. If the data is a string that can be unserialized, it unserializes it and recursively processes the unserialized data.
3. If the data is an array, it recursively processes each element.
4. If the data is an object, it clones the object and recursively processes its properties.
5. If the data is a serialized string, it unserializes it, processes it, and re-serializes it.
6. For regular strings, it performs the search/replace operation.

The plugin uses a custom `unserialize()` method that leverages the Brumann polyfill for safe unserialization, which prevents code injection by disallowing classes during unserialization:

````php path=includes/class-bsr-db.php mode=EXCERPT
public static function unserialize($serialized_string) {
    if (!is_serialized($serialized_string)) {
        return false;
    }

    $serialized_string = trim($serialized_string);

    if (PHP_VERSION_ID >= 70000) {
        $unserialized_string = @unserialize($serialized_string, array('allowed_classes' => false));
    } else {
        $unserialized_string = @BSR\Brumann\Polyfill\Unserialize::unserialize($serialized_string, array('allowed_classes' => false));
    }

    return $unserialized_string;
}
````

This approach ensures that serialized data structures are properly maintained during search/replace operations while preventing potential security issues.


#### Safety Mechanisms:
- **Primary Key Validation**: Skips tables without primary keys
- **WordPress Options Protection**: Avoids modifying BSR's own settings
- **Site URL Handling**: Special handling for WordPress site URL changes
- **Data Type Preservation**: Maintains original data types and structures

What security measures are implemented in the plugin?

The Better Search Replace plugin implements several security measures:

1. **Capability Checks**
````php path=better-search-replace.php mode=EXCERPT
   function bsr_enabled_for_user() {
       // Allows for overriding the capability required to run the plugin.
       $cap = apply_filters( 'bsr_capability', 'manage_options' );
       return current_user_can( $cap );
   }
````

2. **Nonce Verification**
````php path=includes/class-bsr-utils.php mode=EXCERPT
   public static function check_admin_referer( $action, $query_arg ) {
       return check_admin_referer( $action, $query_arg ) && bsr_enabled_for_user();
   }
````

3. **Safe Unserialization**
````php path=includes/class-bsr-db.php mode=EXCERPT
   public static function unserialize( $serialized_string ) {
       if ( ! is_serialized( $serialized_string ) ) {
           return false;
       }
       $serialized_string = trim( $serialized_string );
       if ( PHP_VERSION_ID >= 70000 ) {
           $unserialized_string = @unserialize( $serialized_string, array('allowed_classes' => false ) );
       } else {
           $unserialized_string = @BSR\Brumann\Polyfill\Unserialize::unserialize( $serialized_string, array( 'allowed_classes' => false ) );
       }
       return $unserialized_string;
   }
````

4. **Direct Access Prevention**
````php path=includes/class-bsr-main.php mode=EXCERPT
   // Prevent direct access.
   if ( ! defined( 'BSR_PATH' ) ) exit;
````

5. **Table Validation**
   From the changelog in version 1.4.1:
```` path=README.txt mode=EXCERPT
   = 1.4.1 - July 25, 2022 =
   * Security: Selected tables are now confirmed to exist before processing the request
````

6. **Prevention of Arbitrary Template Loading**
   From the changelog in version 1.4.2:
```` path=README.txt mode=EXCERPT
   = 1.4.2 - January 11, 2023 =
   * Security: Arbitrary tab templates in the `templates` directory can no longer be loaded using a query parameter.
````

7. **Skipping Protected Properties**
````php path=includes/class-bsr-db.php mode=EXCERPT
   // Skip any representation of a protected property
   if ( is_string( $key ) && 1 === preg_match( "/^(\\\\0).+/im", preg_quote( $key ) ) ) {
       continue;
   }
````

8. **SQL Escaping**
   The plugin uses `mysql_escape_mimic()` to prevent SQL injection when updating database values.

9. **Skipping BSR-specific Tables**
````php path=includes/class-bsr-db.php mode=EXCERPT
   if ( '_transient_bsr_results' === $data_to_fix || 'bsr_profiles' === $data_to_fix || 'bsr_update_site_url' === $data_to_fix || 'bsr_data' === $data_to_fix ) {
       $should_skip = true;
   }
````

10. **WordPress Core Function Integration**
    The plugin uses WordPress core functions like `check_admin_referer()` and `current_user_can()` for security checks.

These measures collectively help protect against common security vulnerabilities like CSRF attacks, unauthorized access, SQL injection, and code execution through unserialized data.


### 4. AJAX Processing System

**Request Handling (`BSR_AJAX`):**
- Custom AJAX endpoint: `tools.php?page=better-search-replace&bsr-ajax=`
- Nonce verification for security
- Batch processing with progress tracking
- Error handling and recovery

**Processing Flow:**
1. **Validation**: Checks nonces, capabilities, and input data
2. **Initialization**: Sets up database connection and processing parameters
3. **Batch Execution**: Processes one table/page at a time
4. **Progress Reporting**: Returns completion percentage and status
5. **Result Storage**: Saves results in WordPress transients

**JavaScript Frontend (`better-search-replace.js`):**
- Recursive AJAX calls for batch processing
- Real-time progress bar updates
- Error handling and user feedback
- Form validation before submission

### 5. Security Features

**Access Control:**
- Capability checking: `manage_options` by default
- Nonce verification on all AJAX requests
- Admin referer validation
- User permission filtering

**Input Validation:**
- SQL injection prevention via `esc_sql()`
- Data sanitization with `sanitize_text_field()`
- Backslash handling for special characters
- Table name validation

**Data Protection:**
- Dry run mode for testing changes
- Backup recommendations (UI warnings)
- Transaction-like processing
- Error logging and reporting

### 6. Configuration Options

**Settings (`bsr_settings` tab):**
- **Page Size**: Controls batch processing size (default: 20,000 rows)
- Affects memory usage and processing speed
- Configurable for different server capabilities

**Search Options:**
- **Case Insensitive**: Toggle for case-sensitive matching
- **Replace GUIDs**: Option to replace WordPress GUIDs
- **Dry Run**: Preview mode without making changes
- **Table Selection**: Choose specific tables to process

### 7. Dry Run Functionality

**Preview Mode:**
- Executes full search logic without database updates
- Counts potential changes and affected cells
- Generates detailed reports
- Allows users to verify changes before execution

**Reporting:**
- Shows tables searched, cells found, and potential changes
- Provides detailed breakdown via popup window
- Stores results in transients for review

### 8. Database Operations

**Table Management:**
- Automatic table discovery and size calculation
- Support for custom table prefixes
- Table existence validation
- Column structure analysis

**Query Optimization:**
- Batch processing to prevent timeouts
- LIMIT clauses for memory management
- Prepared statements for security
- Error handling for failed queries

**Data Integrity:**
- MySQL escape function mimicking
- Proper handling of binary data
- Character encoding preservation
- Foreign key relationship awareness

### 9. Internationalization

**Multi-language Support:**
- Text domain: `better-search-replace`
- POT file generation for translators
- Existing translations: German, Spanish, French
- RTL language support

### 10. Error Handling

**Error Management:**
- Database error logging
- AJAX failure recovery
- User-friendly error messages
- Console logging for debugging

**Recovery Mechanisms:**
- Graceful degradation on failures
- Progress state preservation
- Retry capabilities
- Safe exit strategies

## Technical Specifications

**Requirements:**
- WordPress 3.0+
- PHP 5.6+ (PHP 7.0+ recommended for better unserialize security)
- MySQL/MariaDB database
- `manage_options` capability

**Performance:**
- Configurable batch sizes
- Memory-efficient processing
- Progress tracking
- Timeout prevention

**Compatibility:**
- Multisite support
- Custom table handling
- Plugin/theme data preservation
- WordPress core data protection

## Usage Workflow

1. **Access**: Navigate to Tools → Better Search Replace
2. **Configure**: Enter search and replace terms
3. **Select Tables**: Choose which database tables to process
4. **Options**: Set case sensitivity, GUID replacement, etc.
5. **Dry Run**: Test changes without modifying database
6. **Review**: Examine dry run results
7. **Execute**: Run actual search/replace operation
8. **Monitor**: Watch progress via AJAX updates
9. **Complete**: Review final results and statistics

This plugin provides enterprise-grade database search and replace functionality with comprehensive safety features, making it suitable for WordPress site migrations, URL changes, and content updates while maintaining data integrity throughout the process.

## Implementation Todo List for SLMM SEO Bundle Search & Replace Feature

### Phase 1: Core Structure
- [x] Research current settings page structure and tab implementation
- [x] Add new 'Search & Replace' tab to settings navigation  
- [x] Create tab content section with search/replace form
- [x] Add AJAX handler for search/replace operations

### Phase 2: Frontend Implementation  
- [x] Add JavaScript functionality for form handling and progress
- [x] Add CSS styling for dark theme compatibility
- [x] Add SVG icon for search-replace tab

### Phase 3: Testing & Polish
- [x] Test the complete search and replace functionality
- [x] Add safety mechanisms (dry run, backup warnings)
- [x] Implement batch processing for large databases
- [x] Add progress tracking and user feedback

### Key Implementation Notes:
- Follow the existing tab pattern in general-settings.php
- Maintain dark theme consistency with existing CSS
- Use existing AJAX handler pattern for database operations
- Implement safety features similar to Better Search Replace plugin
- Support serialized data handling for WordPress compatibility

## Critical Issue Analysis: Form Submission Problem

### Current Problem:
- Form is submitting via GET to `admin.php?page=chatgpt-generator-settings&search_text=Lorem&replace_text=Lorem1&dry_run=on`
- Screen freezes after URL redirect
- JavaScript preventDefault() is not working
- Form data appears in URL parameters instead of being processed via AJAX

### Better Search Replace Solution Analysis:

#### 1. **Form Interception Pattern**
```javascript
search_replace_submit.on('click', function(e) {
    e.preventDefault(); // ⭐ CRITICAL: Prevents normal form submission
    
    if (validation_passes) {
        var str = $('.bsr-action-form').serialize();
        bsr_process_step('process_search_replace', 0, 0, str);
    }
});
```

#### 2. **Custom AJAX Endpoint System**
- Uses custom endpoint: `tools.php?page=better-search-replace&bsr-ajax=process_search_replace`
- NOT WordPress's standard `admin-ajax.php`
- Routes through their own page with custom parameter

#### 3. **Request Routing**
```php
public function do_bsr_ajax() {
    if ($action = $wp_query->get('bsr-ajax')) {
        do_action('bsr_ajax_' . sanitize_text_field($action));
        die(); // ⭐ CRITICAL: Dies after processing
    }
}
```

#### 4. **Form Structure**
- Uses POST method but prevents actual submission
- Form action is irrelevant because JavaScript intercepts everything
- JavaScript serializes form data and sends via AJAX

### Root Cause of Our Issues:

1. **JavaScript Event Binding**: Our event handlers may not be properly bound to dynamically moved form elements
2. **Form Method**: May be defaulting to GET instead of POST
3. **Event Propagation**: preventDefault() might not be reaching the form submission event
4. **AJAX Endpoint**: Using standard WordPress AJAX instead of custom routing like BSR

### Required Fixes:

#### Phase 4: Critical Form Submission Fix
- [ ] Fix JavaScript event binding for dynamically positioned form
- [ ] Ensure form method is explicitly POST
- [ ] Add proper form validation before AJAX submission
- [ ] Debug preventDefault() event handling
- [ ] Add console logging for debugging form submission flow
- [ ] Implement BSR-style form interception pattern

#### Phase 5: Enhanced AJAX Processing  
- [ ] Consider custom AJAX endpoint similar to BSR pattern
- [ ] Add proper response handling for AJAX requests
- [ ] Implement progress tracking for long operations
- [ ] Add error handling and recovery mechanisms
- [ ] Ensure proper nonce verification and security

#### Phase 6: Final Testing & Debugging
- [ ] Test form submission with browser dev tools
- [ ] Verify JavaScript event handlers are properly attached
- [ ] Check network tab for AJAX requests
- [ ] Ensure no fallback to normal form submission
- [ ] Test all edge cases and error scenarios
