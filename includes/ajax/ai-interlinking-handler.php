<?php
/**
 * AI Interlinking Suggestions Handler
 * 
 * Handles AJAX requests for AI-powered interlinking suggestions
 * using OpenAI, OpenRouter, or Anthropic APIs based on content analysis.
 * 
 * @package SLMM_SEO_Bundle
 * @version 4.10.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SLMM_AI_Interlinking_Handler {

    /**
     * Singleton instance
     * @var SLMM_AI_Interlinking_Handler|null
     */
    private static $instance = null;

    /**
     * Hook registration tracking
     * @var bool
     */
    private static $hooks_registered = false;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the handler (private constructor)
     */
    private function __construct() {
        // CRITICAL FIX: Only register hooks once
        if (!self::$hooks_registered) {
            add_action('wp_ajax_slmm_ai_interlinking_suggest', array($this, 'handle_ai_interlinking_suggest'));
            self::$hooks_registered = true;
            error_log('[SLMM AI Interlinking Handler] AJAX hooks registered once via singleton');
        }
    }

    /**
     * Handle AI interlinking suggestions request
     */
    public function handle_ai_interlinking_suggest() {
        try {
            // Verify nonce
            if (!wp_verify_nonce($_POST['nonce'], 'slmm_ai_interlinking_suggest')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            // Check capabilities
            if (!current_user_can('edit_posts')) {
                wp_send_json_error(array('message' => 'Insufficient permissions'));
                return;
            }

            // Sanitize and validate input data
            $content_segments = $this->sanitize_content_segments($_POST['content_segments'] ?? array());
            $context_pages = $this->sanitize_context_pages($_POST['context_pages'] ?? array());
            $prompt_type = sanitize_text_field($_POST['prompt_type'] ?? 'parent');
            $prompt_content = sanitize_textarea_field($_POST['prompt_content'] ?? '');
            $linking_rules = sanitize_textarea_field($_POST['linking_rules'] ?? '');
            $current_post_id = absint($_POST['current_post_id'] ?? 0);

            // Validate required data
            if (empty($content_segments) || empty($prompt_content)) {
                wp_send_json_error(array('message' => 'Missing required content or prompt data'));
                return;
            }

            // Get AI provider settings
            $ai_settings = $this->get_ai_provider_settings();
            if (!$ai_settings['has_api_key']) {
                wp_send_json_error(array('message' => 'AI provider API key not configured'));
                return;
            }

            // Build AI prompt
            $ai_prompt = $this->build_ai_prompt(
                $content_segments,
                $context_pages,
                $prompt_content,
                $linking_rules,
                $prompt_type
            );

            // Make AI request
            $ai_response = $this->make_ai_request($ai_prompt, $ai_settings);
            if ($ai_response['success'] === false) {
                wp_send_json_error(array('message' => $ai_response['error']));
                return;
            }

            // Parse AI response into suggestions
            $suggestions = $this->parse_ai_response($ai_response['content'], $context_pages);

            // Return suggestions
            wp_send_json_success(array(
                'suggestions' => $suggestions,
                'prompt_type' => $prompt_type,
                'context_pages_count' => count($context_pages),
                'debug' => array(
                    'content_segments_count' => count($content_segments),
                    'ai_provider' => $ai_settings['provider'],
                    'ai_model' => $ai_settings['model']
                )
            ));

        } catch (Exception $e) {
            error_log('SLMM AI Interlinking Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Internal error occurred'));
        }
    }

    /**
     * Sanitize content segments data
     */
    private function sanitize_content_segments($segments) {
        if (!is_array($segments)) {
            return array();
        }

        $sanitized = array();
        foreach ($segments as $key => $content) {
            $clean_key = sanitize_key($key);
            $clean_content = wp_strip_all_tags($content);
            $clean_content = sanitize_textarea_field($clean_content);
            
            if ($clean_content && strlen($clean_content) > 10) {
                $sanitized[$clean_key] = $clean_content;
            }
        }

        return $sanitized;
    }

    /**
     * Sanitize context pages data
     */
    private function sanitize_context_pages($pages) {
        if (!is_array($pages)) {
            return array();
        }

        $sanitized = array();
        foreach ($pages as $page) {
            if (!is_array($page)) continue;

            $clean_page = array(
                'title' => sanitize_text_field($page['title'] ?? ''),
                'url' => esc_url_raw($page['url'] ?? ''),
                'section' => sanitize_text_field($page['section'] ?? '')
            );

            if ($clean_page['title'] && $clean_page['url']) {
                $sanitized[] = $clean_page;
            }
        }

        return $sanitized;
    }

    /**
     * Get AI provider settings
     */
    private function get_ai_provider_settings() {
        $chatgpt_options = get_option('chatgpt_generator_options', array());
        $openrouter_key = $chatgpt_options['openrouter_api_key'] ?? '';
        $anthropic_key = get_option('slmm_anthropic_api_key', '');
        
        $openai_key = $chatgpt_options['openai_api_key'] ?? '';
        $default_provider = $chatgpt_options['ai_provider'] ?? 'openai';
        $default_model = $chatgpt_options['model'] ?? 'gpt-4';

        // Determine best available provider
        $provider = 'openai';
        $api_key = $openai_key;
        $model = $default_model;

        if ($default_provider === 'openrouter' && $openrouter_key) {
            $provider = 'openrouter';
            $api_key = $openrouter_key;
            $model = $chatgpt_options['openrouter_model'] ?? 'openai/gpt-4';
        } elseif ($default_provider === 'anthropic' && $anthropic_key) {
            $provider = 'anthropic';
            $api_key = $anthropic_key;
            $model = $chatgpt_options['anthropic_model'] ?? 'claude-3-haiku-20240307';
        }

        // Fallback to any available provider
        if (!$api_key) {
            if ($openrouter_key) {
                $provider = 'openrouter';
                $api_key = $openrouter_key;
                $model = 'openai/gpt-4';
            } elseif ($anthropic_key) {
                $provider = 'anthropic';
                $api_key = $anthropic_key;
                $model = 'claude-3-haiku-20240307';
            }
        }

        return array(
            'provider' => $provider,
            'api_key' => $api_key,
            'model' => $model,
            'has_api_key' => !empty($api_key)
        );
    }

    /**
     * Build AI prompt for interlinking suggestions
     */
    private function build_ai_prompt($content_segments, $context_pages, $prompt_template, $linking_rules, $prompt_type) {
        // Extract the most relevant content segment
        $primary_content = '';
        if (!empty($content_segments['top'])) {
            $primary_content = $content_segments['top'];
        } elseif (!empty($content_segments['middle'])) {
            $primary_content = $content_segments['middle'];
        } elseif (!empty($content_segments['bottom'])) {
            $primary_content = $content_segments['bottom'];
        } else {
            $primary_content = substr($content_segments['full_content'] ?? '', 0, 1000);
        }

        // Build context pages list
        $context_text = '';
        if (!empty($context_pages)) {
            $context_text = "\n\nAvailable pages for linking:\n";
            foreach ($context_pages as $page) {
                $context_text .= "- {$page['title']} ({$page['section']} link)\n";
            }
        }

        // Build comprehensive prompt
        $prompt = $prompt_template . "\n\n";
        $prompt .= "CONTENT TO ANALYZE:\n" . $primary_content . "\n";
        
        if ($context_text) {
            $prompt .= $context_text;
        }
        
        if ($linking_rules) {
            $prompt .= "\n\nLINKING RULES:\n" . $linking_rules . "\n";
        }
        
        $prompt .= "\n\nPlease provide 3 anchor text variations for each suggested link:";
        $prompt .= "\n1. Direct Entity (exact match to target page)";
        $prompt .= "\n2. Descriptive Phrase (explanatory context)";
        $prompt .= "\n3. LSI-Rich (semantic variants)";
        $prompt .= "\n\nFormat your response as JSON with this structure:";
        $prompt .= "\n```json";
        $prompt .= "\n[";
        $prompt .= "\n  {";
        $prompt .= "\n    \"page_title\": \"Target Page Title\",";
        $prompt .= "\n    \"variations\": [";
        $prompt .= "\n      {\"type\": \"Direct Entity\", \"text\": \"exact anchor text\"},";
        $prompt .= "\n      {\"type\": \"Descriptive Phrase\", \"text\": \"descriptive anchor text\"},";
        $prompt .= "\n      {\"type\": \"LSI-Rich\", \"text\": \"semantic anchor text\"}";
        $prompt .= "\n    ]";
        $prompt .= "\n  }";
        $prompt .= "\n]";
        $prompt .= "\n```";

        return $prompt;
    }

    /**
     * Make AI request to the configured provider
     */
    private function make_ai_request($prompt, $settings) {
        switch ($settings['provider']) {
            case 'openai':
                return $this->make_openai_request($prompt, $settings);
            case 'openrouter':
                return $this->make_openrouter_request($prompt, $settings);
            case 'anthropic':
                return $this->make_anthropic_request($prompt, $settings);
            default:
                return array('success' => false, 'error' => 'Unknown AI provider');
        }
    }

    /**
     * Make OpenAI API request
     */
    private function make_openai_request($prompt, $settings) {
        $url = 'https://api.openai.com/v1/chat/completions';
        
        $data = array(
            'model' => $settings['model'],
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => 1000,
            'temperature' => 0.7
        );

        $response = wp_remote_post($url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $settings['api_key'],
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        return $this->process_openai_response($response);
    }

    /**
     * Make OpenRouter API request
     */
    private function make_openrouter_request($prompt, $settings) {
        $url = 'https://openrouter.ai/api/v1/chat/completions';
        
        $data = array(
            'model' => $settings['model'],
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => 1000,
            'temperature' => 0.7
        );

        $response = wp_remote_post($url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $settings['api_key'],
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url()
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        return $this->process_openai_response($response); // Same format as OpenAI
    }

    /**
     * Make Anthropic API request
     */
    private function make_anthropic_request($prompt, $settings) {
        $url = 'https://api.anthropic.com/v1/messages';
        
        $data = array(
            'model' => $settings['model'],
            'max_tokens' => 1000,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            )
        );

        $response = wp_remote_post($url, array(
            'headers' => array(
                'x-api-key' => $settings['api_key'],
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01'
            ),
            'body' => json_encode($data),
            'timeout' => 30
        ));

        return $this->process_anthropic_response($response);
    }

    /**
     * Process OpenAI/OpenRouter API response
     */
    private function process_openai_response($response) {
        if (is_wp_error($response)) {
            return array('success' => false, 'error' => $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['choices'][0]['message']['content'])) {
            return array('success' => false, 'error' => 'Invalid API response');
        }

        return array('success' => true, 'content' => $data['choices'][0]['message']['content']);
    }

    /**
     * Process Anthropic API response
     */
    private function process_anthropic_response($response) {
        if (is_wp_error($response)) {
            return array('success' => false, 'error' => $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['content'][0]['text'])) {
            return array('success' => false, 'error' => 'Invalid API response');
        }

        return array('success' => true, 'content' => $data['content'][0]['text']);
    }

    /**
     * Parse AI response into structured suggestions
     */
    private function parse_ai_response($ai_content, $context_pages) {
        // Try to extract JSON from the response
        $json_match = null;
        if (preg_match('/```json\s*(.*?)\s*```/s', $ai_content, $json_match)) {
            $json_content = $json_match[1];
        } else {
            // Look for JSON-like content without code blocks
            $json_content = $ai_content;
        }

        $suggestions = json_decode($json_content, true);
        
        if (!$suggestions || !is_array($suggestions)) {
            // Fallback: create basic suggestions from context pages
            $suggestions = array();
            foreach (array_slice($context_pages, 0, 3) as $page) {
                $suggestions[] = array(
                    'page_title' => $page['title'],
                    'variations' => array(
                        array('type' => 'Direct Entity', 'text' => $page['title']),
                        array('type' => 'Descriptive Phrase', 'text' => 'Learn more about ' . $page['title']),
                        array('type' => 'LSI-Rich', 'text' => 'Comprehensive guide to ' . $page['title'])
                    )
                );
            }
        }

        // Validate and sanitize suggestions
        $clean_suggestions = array();
        foreach ($suggestions as $suggestion) {
            if (!is_array($suggestion) || empty($suggestion['page_title'])) {
                continue;
            }

            $clean_suggestion = array(
                'page_title' => sanitize_text_field($suggestion['page_title']),
                'variations' => array()
            );

            if (isset($suggestion['variations']) && is_array($suggestion['variations'])) {
                foreach ($suggestion['variations'] as $variation) {
                    if (is_array($variation) && !empty($variation['text'])) {
                        $clean_suggestion['variations'][] = array(
                            'type' => sanitize_text_field($variation['type'] ?? 'Suggestion'),
                            'text' => sanitize_text_field($variation['text'])
                        );
                    } elseif (is_string($variation) && !empty($variation)) {
                        $clean_suggestion['variations'][] = array(
                            'type' => 'Suggestion',
                            'text' => sanitize_text_field($variation)
                        );
                    }
                }
            }

            if (!empty($clean_suggestion['variations'])) {
                $clean_suggestions[] = $clean_suggestion;
            }
        }

        return $clean_suggestions;
    }
}

// Initialize the handler (SINGLETON)
SLMM_AI_Interlinking_Handler::get_instance();