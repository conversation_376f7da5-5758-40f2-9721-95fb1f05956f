/**
 * SEO Overview Meta Box CSS
 */

/* Container styling - more compact */
.slmm-seo-overview-content {
    min-height: 80px;
    padding: 0;
    margin: -6px -12px -12px -12px;
    position: relative;
}

/* Table styling - more compact */
.seo-overview-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.seo-overview-table tr {
    border-bottom: 1px solid #f0f0f0;
}

.seo-overview-table tr:last-child {
    border-bottom: none;
}

/* Reduce font size for headers by 25% */
.seo-overview-table th {
    text-align: left;
    padding: 5px 8px;
    font-weight: 600;
    color: #1d2327;
    font-size: 10px; /* 25% smaller than 13px */
    background-color: #f8f9fa;
}

/* More compact cell padding */
.seo-overview-table td {
    text-align: right;
    padding: 5px 8px;
    vertical-align: middle;
    height: 30px; /* Reduced row height */
}

/* Reduce indicator size by 25% */
.status-indicator {
    display: inline-block;
    font-size: 24px; /* Base size */
    line-height: 0;
    vertical-align: middle;
    margin-right: 2px;
}

/* Make indicators 100% larger than original size */
.status-indicator.large-indicator {
    font-size: 48px; /* Double the original size of 24px */
    line-height: 0;
    display: inline-block;
    vertical-align: middle;
}

/* Status colors */
.status-green {
    color: #00a32a;
}

.status-red {
    color: #d63638;
}

.status-orange {
    color: #f56e28; /* Brighter orange for visibility */
}

/* Reduce count text size by 25% */
.status-count {
    font-weight: 600;
    vertical-align: middle;
    font-size: 10px; /* 25% smaller than 13px */
}

/* Reduce large count size by 25% */
.status-count.large-count {
    font-size: 26px; /* 25% smaller than 26px */
    font-weight: 600;
}

/* Last update text styling */
.last-update {
    display: none;
}

/* Loading state */
.slmm-seo-overview-content .loading {
    text-align: center;
    padding: 20px;
    color: #666;
}

/* Header style updated without auto-update text */
.seo-overview-header {
    display: none;
}

/* Publish status text styling */
.status-indicator.large-indicator:not(.status-green):not(.status-red):not(.status-orange) {
    font-size: 26px;
    line-height: 1;
    font-weight: 600;
}

/* Value highlighting - for word count */
.value-highlight {
    font-size: 20px;
    font-weight: 600;
    display: inline-block;
    vertical-align: middle;
    color: #333; 
}

/* Status count styling - for duplicates and hidden divs */
.status-count.large-count {
    font-size: 20px !important;
    font-weight: 600 !important;
    display: inline-block;
    vertical-align: middle;
    color: #333;
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .seo-overview-table th,
    .seo-overview-table td {
        padding: 5px 6px;
    }
}

/* Refresh button styling */
#refresh-seo-overview {
    display: none;
}

/* Spinning animation for refresh button */
@keyframes spin {
    /* empty keyframes to null out any animation */
    0%, 100% { transform: none; }
}

#refresh-seo-overview.spinning .dashicons {
    animation: none;
}

/* Section styling - more compact */
.seo-overview-section {
    margin-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 5px;
    overflow: hidden; /* Prevent margin collapse */
}

.seo-overview-section:last-child {
    margin-bottom: 0;
    border-bottom: none;
    padding-bottom: 0;
}

/* Section title - smaller */
.section-title {
    margin: 0 0 3px 0;
    padding: 3px 8px;
    background-color: #f8f9fa;
    font-size: 18px; /* 25% smaller than 13px */
    font-weight: 800;
    color: #1d2327;
    border-bottom: 1px solid #f0f0f0;
}

/* Loading spinner styling - more compact */
.seo-overview-loading .loading-spinner {
    text-align: center;
    padding: 20px 0;
}

.seo-overview-loading .spinner {
    float: none;
    margin: 0 auto;
    visibility: visible;
}

/* Publish status styling - larger for Published status */
.publish-status {
    font-size: 20px;
    line-height: 1;
    font-weight: 600;
}

.publish-status.large-text {
    font-size: 24px; /* Normal size */
}

/* Make Published status 25% larger */
.publish-status.status-green {
    font-size: 30px; /* 25% larger than 24px */
}

/* Ensure publish status text is properly styled */
.publish-status.status-orange {
    color: #f56e28;
    font-weight: 600;
}

/* Table heading styling - larger font */
.heading-larger {
    font-size: 12px !important; /* Larger than the default 10px */
    font-weight: 700 !important;
}

/* Status count size - 25% smaller than previous */
.status-count.large-count {
    font-size: 15px !important; /* 25% smaller with !important */
    font-weight: 600 !important;
}

/* Publish status text - 25% smaller than previous */
.publish-status.large-text {
    font-size: 15px !important; /* 25% smaller with !important */
    font-weight: 600 !important;
}

/* Status circle alignment */
.status-green.large-indicator, .status-red.large-indicator, .status-orange.large-indicator {
    position: relative;
    top: -2px;
}

/* Larger count text as requested */
.status-count.large-count {
    font-size: 26px; /* 100% larger */
    font-weight: 600;
}

/* Make Published status 25% larger */
.publish-status.published-larger {
    font-size: 30px !important; /* 25% larger than the 24px base */
    font-weight: 700 !important;
}

/* Consistent number styling across all metrics */
.value-highlight, .status-count.large-count {
    font-size: 20px;
    font-weight: 600;
    display: inline-block;
    vertical-align: middle;
    color: #333;
}

/* Ensure top margin is removed for the first section */
.seo-overview-section:first-child {
    margin-top: 0;
}

/* SILO STRUCTURE Circles Styling - Matches Direct Editor Design */
.silo-importance-circles,
.silo-difficulty-circles {
    display: inline-flex;
    align-items: center;
    gap: 3px;
    vertical-align: middle;
}

.silo-circle {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s ease;
    border: 2px solid transparent;
}

/* Importance Circles - Yellow theme matching Direct Editor */
.importance-circle {
    background: #f3f4f6;
    color: #6b7280;
    border-color: #6b7280;
}

.importance-circle.active {
    background: #eab308;
    color: white;
    border-color: #ca8a04;
}

.importance-circle.inactive {
    opacity: 0.3;
}

/* Individual importance level colors (matching interlinking suite) */
.importance-circle.importance-1.active {
    background: #eab308;
    border-color: #ca8a04;
}

.importance-circle.importance-2.active {
    background: #ef4444;
    border-color: #dc2626;
}

.importance-circle.importance-3.active {
    background: #3b82f6;
    border-color: #2563eb;
}

.importance-circle.importance-4.active {
    background: #6b7280;
    border-color: #4b5563;
}

.importance-circle.importance-5.active {
    background: #1f2937;
    border-color: #111827;
}

/* Difficulty Circles - Color-coded matching Direct Editor */
.difficulty-circle {
    background: #f3f4f6;
    color: #6b7280;
    border-color: #6b7280;
}

.difficulty-circle.active {
    color: white;
    border-width: 2px;
}

.difficulty-circle.inactive {
    opacity: 0.3;
}

/* Difficulty level colors (matching interlinking suite) */
.difficulty-circle.difficulty-easy.active {
    background: #10b981;
    border-color: #059669;
}

.difficulty-circle.difficulty-medium.active {
    background: #f59e0b;
    border-color: #d97706;
}

.difficulty-circle.difficulty-hard.active {
    background: #f97316;
    border-color: #ea580c;
}

.difficulty-circle.difficulty-very-hard.active {
    background: #ef4444;
    border-color: #dc2626;
}

/* Keyword Display Styling */
.silo-keyword-display {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid transparent;
    min-width: 100px;
    text-align: center;
}

.silo-keyword-display.has-keyword {
    background: #f3f4f6;
    color: #1f2937;
    border-color: #9ca3af;
}

.silo-keyword-display.no-keyword {
    background: #fef3c7;
    color: #92400e;
    border-color: #fcd34d;
    font-style: italic;
}

.silo-keyword-display:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Clickable circles styling */
.clickable-importance, .clickable-difficulty {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable-importance:hover, .clickable-difficulty:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Navigation Links Styling */
.nav-link {
    display: inline-block;
    background: #f3f4f6;
    color: #1f2937;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #d1d5db;
    font-size: 11px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.nav-link:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
    transform: translateY(-1px);
}

.nav-separator {
    color: #6b7280;
    margin: 0 4px;
    font-weight: 400;
}

/* Copy feedback states */
.nav-link.copy-success {
    background: #10b981 !important;
    color: white !important;
    border-color: #059669 !important;
}

.nav-link.copy-error {
    background: #ef4444 !important;
    color: white !important;
    border-color: #dc2626 !important;
}

/* Semantic indicator state - green background when link found in content */
.nav-link.link-found-in-content {
    background: #10b981 !important; /* Green background */
    color: white !important;
    border-color: #059669 !important;
    font-weight: 600;
    transition: all 0.2s ease;
}

.nav-link.link-found-in-content:hover {
    background: #059669 !important; /* Darker green on hover */
    color: white !important;
    transform: translateY(-1px);
}

/* No data and loading states */
.no-data {
    color: #9ca3af;
    font-style: italic;
    font-size: 11px;
}

.loading-data {
    color: #6b7280;
    font-size: 11px;
}

/* Priority Accordion Styling */
.priority-accordion {
    margin: 0;
    padding: 0;
}

.priority-accordion-item {
    margin-bottom: 4px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    overflow: hidden;
}

.priority-accordion-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 10px;
    background: #f8f9fa;
    border-bottom: 1px solid #e5e7eb;
    cursor: pointer;
    font-size: 11px;
    font-weight: 600;
    transition: all 0.2s ease;
    user-select: none;
}

.priority-accordion-header:hover {
    background: #e5e7eb;
}

.priority-accordion-header.expanded {
    background: #dbeafe;
    border-bottom-color: #93c5fd;
}

/* Priority level colors matching the D3 canvas */
.priority-accordion-header.priority-1 {
    border-left: 4px solid #eab308; /* Yellow */
}

.priority-accordion-header.priority-2 {
    border-left: 4px solid #ef4444; /* Red */
}

.priority-accordion-header.priority-3 {
    border-left: 4px solid #3b82f6; /* Blue */
}

.accordion-icon {
    font-size: 10px;
    transition: transform 0.2s ease;
    margin-right: 6px;
}

.priority-accordion-header.expanded .accordion-icon {
    transform: rotate(180deg);
}

.accordion-title {
    flex: 1;
    color: #1f2937;
}

.accordion-count {
    color: #6b7280;
    font-size: 10px;
    font-weight: 500;
}

.priority-accordion-content {
    display: none;
    padding: 8px 10px;
    background: #ffffff;
    border-top: 1px solid #e5e7eb;
    max-height: 200px;
    overflow-y: auto;
}

/* Priority pages list styling */
.priority-pages-list {
    margin: 0;
    padding: 0;
}

.priority-page-item {
    margin-bottom: 8px;
    padding: 6px 8px;
    background: #f9fafb;
    border-radius: 3px;
    border: 1px solid #e5e7eb;
}

.priority-page-item:last-child {
    margin-bottom: 0;
}

.page-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.page-title .nav-link {
    max-width: 140px; /* Shorter than navigation links to fit status */
    font-size: 11px;
    font-weight: 600;
}

.page-status {
    font-size: 9px;
    font-weight: 500;
    padding: 1px 4px;
    border-radius: 2px;
    margin-left: 6px;
}

.page-status.status-published {
    background: #dcfce7;
    color: #166534;
}

.page-status.status-draft {
    background: #fef3c7;
    color: #92400e;
}

.page-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 9px;
    color: #6b7280;
}

.page-keyword, .page-difficulty {
    font-size: 9px;
}

.page-edit-link {
    font-size: 9px;
    color: #3b82f6;
    text-decoration: none;
    font-weight: 500;
}

.page-edit-link:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* Responsive adjustments for priority accordion */
@media screen and (max-width: 782px) {
    .priority-accordion-header {
        padding: 6px 8px;
    }
    
    .priority-accordion-content {
        padding: 6px 8px;
    }
    
    .page-title .nav-link {
        max-width: 100px;
    }
} 