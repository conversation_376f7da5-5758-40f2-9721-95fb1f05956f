<?php
// File: includes/interlinking/class-interlinking-multiselect.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * SLMM Interlinking Multi-Selection Handler
 * 
 * Extends the existing single-selection D3.js tree system with multi-selection capabilities
 * including Shift+drag, Cmd+click, batch operations, and enhanced keyboard shortcuts
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Interlinking
 */
class SLMM_Interlinking_Multiselect {
    
    private static $instance = null;
    
    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Initialize multi-selection functionality
     */
    private function __construct() {
        $this->setup_hooks();
        error_log('[SLMM Multiselect] Multi-selection handler initialized');
    }
    
    /**
     * Setup WordPress hooks for multi-selection system
     */
    private function setup_hooks() {
        // Add AJAX endpoint for batch delete operations
        add_action('wp_ajax_slmm_batch_delete_pages', array($this, 'ajax_batch_delete_pages'));
        
        // Add AJAX endpoint for batch move operations
        add_action('wp_ajax_slmm_batch_move_pages', array($this, 'ajax_batch_move_pages'));
        
        // Add multi-selection CSS and JavaScript to interlinking page
        add_action('admin_head', array($this, 'add_multiselect_assets'));
        
        error_log('[SLMM Multiselect] Hooks registered successfully');
    }
    
    /**
     * Add multi-selection CSS and JavaScript assets
     */
    public function add_multiselect_assets() {
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'slmm-interlinking-suite') === false) {
            return;
        }
        
        $this->add_multiselect_styles();
        $this->add_multiselect_scripts();
    }
    
    /**
     * Add multi-selection CSS styles
     */
    private function add_multiselect_styles() {
        ?>
        <style>
        /* Multi-Selection Enhanced Styles */
        .slmm-interlinking-theme {
            /* Multi-selection specific colors - BRIGHT YELLOW for visibility */
            --slmm-multiselect-primary: #FFD700;
            --slmm-multiselect-border: #FFD700;
            --slmm-multiselect-bg: rgba(255, 215, 0, 0.15);
            --slmm-selection-counter: #FFD700;
        }

        /* Multi-selected node styling - distinct from single selection */
        .slmm-tree-node.multi-selected .slmm-node-rect {
            stroke: var(--slmm-multiselect-border) !important;
            stroke-width: 2 !important;
            fill: var(--slmm-multiselect-bg) !important;
            filter: brightness(1.1);
        }

        /* Selection preview during rectangle drag */
        .slmm-tree-node.selection-preview .slmm-node-rect {
            stroke: var(--slmm-multiselect-primary) !important;
            stroke-width: 1 !important;
            stroke-dasharray: 2,2 !important;
            fill: var(--slmm-multiselect-bg) !important;
            opacity: 0.7;
            animation: selection-pulse 1s ease-in-out infinite alternate;
        }

        @keyframes selection-pulse {
            from { opacity: 0.7; }
            to { opacity: 1; }
        }

        /* Selection rectangle during drag operations */
        .slmm-selection-rectangle {
            fill: var(--slmm-multiselect-bg);
            stroke: var(--slmm-multiselect-primary);
            stroke-width: 1;
            stroke-dasharray: 3,3;
            pointer-events: none;
            opacity: 0.7;
        }

        /* Bulk action toolbar */
        .slmm-bulk-toolbar {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--slmm-dark-surface);
            border: 1px solid var(--slmm-dark-border);
            border-radius: 8px;
            padding: 12px 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            z-index: 1000;
            display: none;
            align-items: center;
            gap: 12px;
            color: var(--slmm-text-primary);
        }

        .slmm-bulk-toolbar.visible {
            display: flex;
        }

        .slmm-selection-count {
            color: var(--slmm-selection-counter);
            font-weight: 600;
        }

        .slmm-bulk-action-btn {
            background: var(--slmm-primary) !important;
            border: none !important;
            color: white !important;
            padding: 6px 12px !important;
            border-radius: 4px !important;
            font-size: 12px !important;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .slmm-bulk-action-btn:hover {
            background: var(--slmm-primary-hover) !important;
            transform: translateY(-1px);
        }

        .slmm-bulk-action-btn.danger {
            background: #ef4444 !important;
        }

        .slmm-bulk-action-btn.danger:hover {
            background: #dc2626 !important;
        }

        /* Move Selected button styling - YELLOW as requested */
        .slmm-bulk-action-btn.move {
            background: #EAB308 !important; /* Yellow */
            color: #1a1a1a !important;
        }

        .slmm-bulk-action-btn.move:hover {
            background: #D97706 !important; /* Darker yellow on hover */
            transform: translateY(-1px);
            color: #1a1a1a !important;
        }

        /* Bulk Change button styling - BLUE/TEAL */
        .slmm-bulk-action-btn.change {
            background: #3B82F6 !important; /* Blue */
            color: white !important;
        }

        .slmm-bulk-action-btn.change:hover {
            background: #2563EB !important; /* Darker blue on hover */
            transform: translateY(-1px);
            color: white !important;
        }

        /* Bulk Change Modal Styles */
        .slmm-modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .slmm-modal-backdrop.visible {
            opacity: 1;
            visibility: visible;
        }

        .slmm-bulk-change-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.9);
            background: var(--slmm-dark-surface);
            border: 1px solid var(--slmm-dark-border);
            border-radius: 8px;
            width: 500px;
            max-width: 90vw;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }

        .slmm-bulk-change-modal.visible {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }

        .slmm-modal-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid var(--slmm-dark-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .slmm-modal-header h3 {
            margin: 0;
            color: var(--slmm-text-primary);
            font-size: 18px;
            font-weight: 600;
        }

        .slmm-modal-close {
            background: none;
            border: none;
            color: var(--slmm-text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .slmm-modal-close:hover {
            background: var(--slmm-dark-surface-hover);
            color: var(--slmm-text-primary);
        }

        .slmm-modal-content {
            padding: 0;
        }

        .slmm-change-tabs {
            display: flex;
            border-bottom: 1px solid var(--slmm-dark-border);
            background: var(--slmm-dark-bg);
        }

        .slmm-change-tab {
            flex: 1;
            padding: 12px 16px;
            background: none;
            border: none;
            color: var(--slmm-text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
            border-bottom: 2px solid transparent;
        }

        .slmm-change-tab:hover {
            background: var(--slmm-dark-surface-hover);
            color: var(--slmm-text-primary);
        }

        .slmm-change-tab.active {
            color: var(--slmm-primary);
            background: var(--slmm-dark-surface);
            border-bottom-color: var(--slmm-primary);
        }

        .slmm-change-panels {
            padding: 24px;
        }

        .slmm-change-panel {
            display: none;
        }

        .slmm-change-panel.active {
            display: block;
        }

        .slmm-change-panel h4 {
            margin: 0 0 16px 0;
            color: var(--slmm-text-primary);
            font-size: 16px;
            font-weight: 600;
        }

        .slmm-status-options,
        .slmm-difficulty-options,
        .slmm-importance-options,
        .slmm-summarization-options {
            display: grid;
            gap: 12px;
        }

        .slmm-status-options label,
        .slmm-difficulty-options label,
        .slmm-importance-options label,
        .slmm-summarization-options label {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: var(--slmm-dark-bg);
            border: 1px solid var(--slmm-dark-border);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--slmm-text-primary);
        }

        .slmm-status-options label:hover,
        .slmm-difficulty-options label:hover,
        .slmm-importance-options label:hover,
        .slmm-summarization-options label:hover {
            background: var(--slmm-dark-surface-hover);
            border-color: var(--slmm-primary);
        }

        .slmm-status-options input:checked + .status-dot,
        .slmm-difficulty-options input:checked + .difficulty-dot,
        .slmm-importance-options input:checked + .importance-dot,
        .slmm-summarization-options input:checked + .summary-dot,
        .slmm-interlinking-options input:checked + .interlinking-dot {
            border: 2px solid var(--slmm-primary);
            box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
        }

        .status-dot, .difficulty-dot, .importance-dot, .summary-dot, .interlinking-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .status-dot.publish { background-color: #10B981; }
        .status-dot.draft { background-color: #EF4444; }
        .status-dot.private { background-color: #F59E0B; }
        .status-dot.pending { background-color: #8B5CF6; }

        .difficulty-dot.easy { background-color: #10B981; }
        .difficulty-dot.medium { background-color: #F59E0B; }
        .difficulty-dot.hard { background-color: #F97316; }
        .difficulty-dot.very-hard { background-color: #EF4444; }

        .importance-dot.level-1 { background-color: #EAB308; }
        .importance-dot.level-2 { background-color: #EF4444; }
        .importance-dot.level-3 { background-color: #3B82F6; }
        .importance-dot.level-4 { background-color: #6B7280; }
        .importance-dot.level-5 { background-color: #1F2937; }

        .summary-dot.generate { background-color: #10B981; }
        .summary-dot.delete { background-color: #EF4444; }
        .summary-dot.regenerate { background-color: #3B82F6; }

        .interlinking-dot.insert { background-color: #7C3AED; }
        .interlinking-dot.remove { background-color: #EF4444; }
        .interlinking-dot.replace { background-color: #F59E0B; }

        .slmm-modal-footer {
            padding: 16px 24px 20px;
            border-top: 1px solid var(--slmm-dark-border);
            display: flex;
            justify-content: flex-end;
            gap: 12px;
        }

        .slmm-btn-primary,
        .slmm-btn-secondary {
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .slmm-btn-primary {
            background: var(--slmm-primary);
            color: white;
        }

        .slmm-btn-primary:hover {
            background: var(--slmm-primary-hover);
        }

        .slmm-btn-secondary {
            background: var(--slmm-dark-border);
            color: var(--slmm-text-primary);
            border-color: var(--slmm-dark-border);
        }

        .slmm-btn-secondary:hover {
            background: var(--slmm-dark-surface-hover);
        }

        /* Target selection mode styles */
        .slmm-target-selection-active {
            cursor: grab !important;
        }

        .slmm-target-selection-active * {
            cursor: grab !important;
        }

        .slmm-tree-node.valid-move-target .slmm-node-rect {
            stroke: #10B981 !important; /* Green for valid targets */
            stroke-width: 2 !important;
            filter: brightness(1.1);
            animation: valid-target-pulse 2s ease-in-out infinite;
        }

        .slmm-tree-node.invalid-move-target .slmm-node-rect {
            stroke: #EF4444 !important; /* Red for invalid targets */
            stroke-width: 2 !important;
            opacity: 0.5;
        }

        @keyframes valid-target-pulse {
            0%, 100% { 
                stroke: #10B981; 
                stroke-width: 2; 
            }
            50% { 
                stroke: #059669; 
                stroke-width: 3; 
            }
        }

        /* Move confirmation dialog - Enhanced DARK UI styling */
        .slmm-move-confirm-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1E1E23; /* Lighter than dark-surface for better contrast */
            border: 2px solid var(--slmm-dark-border);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            z-index: 10001;
            min-width: 400px;
            max-width: 500px;
            text-align: center;
            color: var(--slmm-text-primary);
            display: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            backdrop-filter: blur(10px);
        }

        .slmm-move-confirm-dialog.visible {
            display: block;
            animation: modal-appear 0.2s ease-out;
        }

        .slmm-move-modal-icon {
            font-size: 48px;
            margin-bottom: 16px;
            color: #EAB308; /* Yellow icon */
        }

        .slmm-move-confirm-dialog h2 {
            margin: 0 0 16px 0;
            font-size: 24px;
            font-weight: 600;
            color: #ffffff; /* Pure white - no CSS variables */
            line-height: 1.2;
        }

        .slmm-move-modal-message {
            font-size: 16px;
            margin: 0 0 8px 0;
            color: #ffffff; /* Pure white - no CSS variables */
            line-height: 1.4;
        }

        .slmm-move-modal-target {
            font-weight: 600;
            color: #EAB308;
        }

        .slmm-move-modal-subtitle {
            font-size: 14px;
            color: #d1d5db; /* Light gray - no CSS variables */
            margin: 0 0 32px 0;
            font-style: italic;
        }

        .slmm-move-modal-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .slmm-modal-btn-move {
            background: #EAB308;
            color: #1a1a1a;
            font-weight: 600;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .slmm-modal-btn-move:hover {
            background: #D97706;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(234, 179, 8, 0.4);
        }

        /* Move modal close button - Enhanced Dark UI styling */
        .slmm-move-confirm-dialog .slmm-modal-close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            color: #d1d5db; /* Light gray - no CSS variables */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .slmm-move-confirm-dialog .slmm-modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: #ffffff; /* Pure white - no CSS variables */
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        /* Move modal cancel button - Enhanced Dark UI styling with red accent */
        .slmm-move-confirm-dialog .slmm-modal-btn-cancel {
            background: rgba(239, 68, 68, 0.1); /* Subtle red background */
            border: 1px solid rgba(239, 68, 68, 0.3); /* Red border */
            color: #FCA5A5; /* Light red text */
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .slmm-move-confirm-dialog .slmm-modal-btn-cancel:hover {
            background: rgba(239, 68, 68, 0.15); /* Slightly more red on hover */
            border-color: rgba(239, 68, 68, 0.5); /* Brighter red border on hover */
            color: #FED7D7; /* Lighter red text on hover */
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.3); /* Red shadow */
        }

        /* Enhanced selection counter in status bar */
        .slmm-status-left .slmm-multiselect-info {
            color: var(--slmm-selection-counter);
            font-weight: 500;
            margin-left: 12px;
        }

        /* Progress modal for batch operations */
        .slmm-batch-progress-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--slmm-dark-surface);
            border: 1px solid var(--slmm-dark-border);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            z-index: 10000;
            min-width: 300px;
            text-align: center;
            color: var(--slmm-text-primary);
            display: none;
        }

        .slmm-batch-progress-modal.visible {
            display: block;
        }

        .slmm-batch-progress-bar {
            width: 100%;
            height: 8px;
            background: var(--slmm-dark-border);
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .slmm-batch-progress-fill {
            height: 100%;
            background: var(--slmm-primary);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        /* Confirmation dialog styling */
        .slmm-batch-confirm-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--slmm-dark-surface);
            border: 1px solid var(--slmm-dark-border);
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
            z-index: 10000;
            min-width: 400px;
            color: var(--slmm-text-primary);
            display: none;
        }

        .slmm-batch-confirm-dialog.visible {
            display: block;
        }

        .slmm-batch-confirm-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        /* Modal backdrop */
        .slmm-modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            display: none;
        }

        .slmm-modal-backdrop.visible {
            display: block;
        }

        /* Enhanced Modal Backdrop for Move Dialog */
        .slmm-modal-backdrop-black {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.85);
            z-index: 10000;
            display: none;
            backdrop-filter: blur(4px);
            animation: backdrop-fade-in 0.2s ease-out;
        }

        .slmm-modal-backdrop-black.visible {
            display: block;
        }

        @keyframes backdrop-fade-in {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(4px);
            }
        }

        /* New Clean Delete Confirmation Dialog - Enhanced DARK UI styling */
        .slmm-batch-confirm-dialog-new {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #1E1E23; /* Lighter than dark-surface for better contrast */
            border: 2px solid var(--slmm-dark-border);
            border-radius: 16px;
            padding: 32px;
            box-shadow: 
                0 20px 60px rgba(0, 0, 0, 0.8),
                0 0 0 1px rgba(255, 255, 255, 0.05),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            z-index: 10001;
            min-width: 400px;
            max-width: 500px;
            text-align: center;
            color: var(--slmm-text-primary);
            display: none;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            backdrop-filter: blur(10px);
        }

        .slmm-batch-confirm-dialog-new.visible {
            display: block;
            animation: modal-appear 0.2s ease-out;
        }

        @keyframes modal-appear {
            from {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }

        .slmm-modal-close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.05);
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            color: #d1d5db; /* Light gray - no CSS variables */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .slmm-modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: #ffffff; /* Pure white - no CSS variables */
            transform: scale(1.1);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .slmm-modal-icon {
            font-size: 48px;
            margin-bottom: 16px;
            filter: sepia(1) saturate(5) hue-rotate(0deg) brightness(1.2);
        }

        .slmm-batch-confirm-dialog-new h2 {
            margin: 0 0 16px 0;
            font-size: 24px;
            font-weight: 600;
            color: #ffffff; /* Pure white - no CSS variables */
            line-height: 1.2;
        }

        .slmm-modal-warning {
            font-size: 16px;
            margin: 0 0 8px 0;
            color: #ffffff; /* Pure white - no CSS variables */
            line-height: 1.4;
        }

        .slmm-modal-warning strong {
            color: #EF4444; /* Bright red for dark theme */
            font-weight: 600;
        }

        .slmm-modal-subtitle {
            font-size: 14px;
            color: #d1d5db; /* Light gray - no CSS variables */
            margin: 0 0 32px 0;
            font-style: italic;
        }

        .slmm-modal-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .slmm-modal-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 120px;
        }

        .slmm-modal-btn-cancel {
            background: rgba(107, 114, 128, 0.1); /* Subtle gray background */
            border: 1px solid rgba(107, 114, 128, 0.3); /* Gray border */
            color: #d1d5db; /* Light gray text */
        }

        .slmm-modal-btn-cancel:hover {
            background: rgba(107, 114, 128, 0.15); /* Slightly more gray on hover */
            border-color: rgba(107, 114, 128, 0.5); /* Brighter gray border on hover */
            color: #f3f4f6; /* Lighter gray text on hover */
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3); /* Gray shadow */
        }

        .slmm-modal-btn-delete {
            background: #dc2626;
            color: white;
            font-weight: 600;
        }

        .slmm-modal-btn-delete:hover {
            background: #b91c1c;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
        }

        .slmm-modal-btn:active {
            transform: translateY(0);
        }
        </style>
        <?php
    }
    
    /**
     * Add multi-selection JavaScript functionality (now integrated into D3.js system)
     */
    private function add_multiselect_scripts() {
        ?>
        <script>
        jQuery(document).ready(function($) {
            'use strict';
            
            // Multi-selection is now fully integrated into the D3.js tree system
            // See interlinking-suite.php for implementation details
            if (window.SLMM && window.SLMM.debug) {
                SLMM.debug.success('Multiselect', 'Enhanced multi-selection system active');
                SLMM.debug.info('Multiselect', 'Features: Ctrl+Click, Shift+drag rectangle, X key batch delete, Ctrl+A select all, Escape clear');
            }
        });
        </script>
        <?php
    }
    
    /**
     * Handle batch page deletion via AJAX
     */
    public function ajax_batch_delete_pages() {
        // Verify nonce for security
        if (!check_ajax_referer('slmm_interlinking_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check if user has permission to delete posts/pages
        if (!current_user_can('delete_posts') && !current_user_can('delete_pages')) {
            wp_send_json_error('Insufficient permissions to delete content');
            return;
        }
        
        // Get and validate page IDs
        $page_ids = isset($_POST['page_ids']) ? $_POST['page_ids'] : array();
        if (empty($page_ids) || !is_array($page_ids)) {
            wp_send_json_error('Invalid page IDs provided');
            return;
        }
        
        // Sanitize page IDs
        $page_ids = array_map('intval', $page_ids);
        $page_ids = array_filter($page_ids, function($id) { return $id > 0; });
        
        if (empty($page_ids)) {
            wp_send_json_error('No valid page IDs provided');
            return;
        }

        // Limit batch size for performance - increased for large cleanup operations
        if (count($page_ids) > 200) {
            wp_send_json_error('Batch size too large - maximum 200 items per batch');
            return;
        }

        // Extend execution time for large batches
        if (count($page_ids) > 50) {
            set_time_limit(300); // 5 minutes for large operations
            ini_set('memory_limit', '512M'); // Increase memory limit
        }

        $results = array();
        $deleted_count = 0;
        $failed_count = 0;

        error_log('[SLMM Multiselect] Starting batch delete operation for ' . count($page_ids) . ' pages');
        
        foreach ($page_ids as $page_id) {
            try {
                // Get the post to verify it exists and check specific permissions
                $post = get_post($page_id);
                if (!$post) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'Page not found'
                    );
                    $failed_count++;
                    continue;
                }
                
                // Check specific delete permission for this post type
                $post_type_obj = get_post_type_object($post->post_type);
                if (!$post_type_obj || !current_user_can($post_type_obj->cap->delete_post, $page_id)) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'Insufficient permissions to delete this ' . $post->post_type
                    );
                    $failed_count++;
                    continue;
                }
                
                // Attempt to delete the post permanently
                $deleted = wp_delete_post($page_id, true); // true = force delete (skip trash)
                
                if ($deleted) {
                    // Log successful deletion
                    error_log('[SLMM Multiselect] Page deleted - ID: ' . $page_id . ', Title: ' . $post->post_title . ', User: ' . get_current_user_id());
                    
                    $results[] = array(
                        'id' => $page_id,
                        'success' => true,
                        'title' => $post->post_title,
                        'post_type' => $post->post_type
                    );
                    $deleted_count++;
                } else {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'WordPress deletion failed'
                    );
                    $failed_count++;
                }
                
            } catch (Exception $e) {
                error_log('[SLMM Multiselect] Delete error for page ' . $page_id . ': ' . $e->getMessage());
                $results[] = array(
                    'id' => $page_id,
                    'success' => false,
                    'error' => 'Error: ' . $e->getMessage()
                );
                $failed_count++;
            }
        }
        
        // Log batch operation summary
        error_log('[SLMM Multiselect] Batch delete completed - Deleted: ' . $deleted_count . ', Failed: ' . $failed_count . ', User: ' . get_current_user_id());
        
        // Return comprehensive results
        wp_send_json_success(array(
            'message' => sprintf('Batch delete completed: %d deleted, %d failed', $deleted_count, $failed_count),
            'deleted_count' => $deleted_count,
            'failed_count' => $failed_count,
            'total_count' => count($page_ids),
            'results' => $results
        ));
    }
    
    /**
     * Handle batch page move operations via AJAX
     */
    public function ajax_batch_move_pages() {
        // Verify nonce for security
        if (!check_ajax_referer('slmm_interlinking_nonce', 'nonce', false)) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        // Check if user has permission to edit posts/pages
        if (!current_user_can('edit_posts') && !current_user_can('edit_pages')) {
            wp_send_json_error('Insufficient permissions to move content');
            return;
        }
        
        // Get and validate page IDs
        $page_ids = isset($_POST['page_ids']) ? $_POST['page_ids'] : array();
        if (empty($page_ids) || !is_array($page_ids)) {
            wp_send_json_error('Invalid page IDs provided');
            return;
        }
        
        // Get and validate target parent ID
        $target_parent_id = isset($_POST['target_parent_id']) ? intval($_POST['target_parent_id']) : 0;
        if ($target_parent_id < 0) {
            wp_send_json_error('Invalid target parent ID');
            return;
        }
        
        // Sanitize page IDs
        $page_ids = array_map('intval', $page_ids);
        $page_ids = array_filter($page_ids, function($id) { return $id > 0; });
        
        if (empty($page_ids)) {
            wp_send_json_error('No valid page IDs provided');
            return;
        }
        
        // Limit batch size for performance - increased for large move operations
        if (count($page_ids) > 200) {
            wp_send_json_error('Batch size too large - maximum 200 items per batch');
            return;
        }

        // Extend execution time for large batches
        if (count($page_ids) > 50) {
            set_time_limit(300); // 5 minutes for large operations
            ini_set('memory_limit', '512M'); // Increase memory limit
        }
        
        // Validate target parent exists and user can edit it (if not root level)
        if ($target_parent_id > 0) {
            $target_post = get_post($target_parent_id);
            if (!$target_post) {
                wp_send_json_error('Target parent page not found');
                return;
            }
            
            // Check permission to edit target parent
            $target_post_type_obj = get_post_type_object($target_post->post_type);
            if (!$target_post_type_obj || !current_user_can($target_post_type_obj->cap->edit_post, $target_parent_id)) {
                wp_send_json_error('Insufficient permissions to use this page as parent');
                return;
            }
        }
        
        $results = array();
        $moved_count = 0;
        $failed_count = 0;
        
        error_log('[SLMM Multiselect] Starting batch move operation for ' . count($page_ids) . ' pages to parent ' . $target_parent_id);
        
        foreach ($page_ids as $page_id) {
            try {
                // Get the post to verify it exists and check permissions
                $post = get_post($page_id);
                if (!$post) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'Page not found'
                    );
                    $failed_count++;
                    continue;
                }
                
                // Check specific edit permission for this post
                $post_type_obj = get_post_type_object($post->post_type);
                if (!$post_type_obj || !current_user_can($post_type_obj->cap->edit_post, $page_id)) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'Insufficient permissions to move this ' . $post->post_type
                    );
                    $failed_count++;
                    continue;
                }
                
                // Prevent circular dependency - page cannot become child of its own descendant
                if ($target_parent_id > 0 && $this->would_create_circular_dependency($page_id, $target_parent_id)) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'Would create circular dependency'
                    );
                    $failed_count++;
                    continue;
                }
                
                // Attempt to update the post parent
                $update_result = wp_update_post(array(
                    'ID' => $page_id,
                    'post_parent' => $target_parent_id
                ), true);
                
                if (is_wp_error($update_result)) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'WordPress update failed: ' . $update_result->get_error_message()
                    );
                    $failed_count++;
                } else if ($update_result === 0) {
                    $results[] = array(
                        'id' => $page_id,
                        'success' => false,
                        'error' => 'WordPress update failed'
                    );
                    $failed_count++;
                } else {
                    // Success - log the move
                    error_log('[SLMM Multiselect] Page moved - ID: ' . $page_id . ', Title: ' . $post->post_title . ', New Parent: ' . $target_parent_id . ', User: ' . get_current_user_id());
                    
                    $results[] = array(
                        'id' => $page_id,
                        'success' => true,
                        'title' => $post->post_title,
                        'post_type' => $post->post_type,
                        'old_parent' => $post->post_parent,
                        'new_parent' => $target_parent_id
                    );
                    $moved_count++;
                }
                
            } catch (Exception $e) {
                error_log('[SLMM Multiselect] Move error for page ' . $page_id . ': ' . $e->getMessage());
                $results[] = array(
                    'id' => $page_id,
                    'success' => false,
                    'error' => 'Error: ' . $e->getMessage()
                );
                $failed_count++;
            }
        }
        
        // Log batch operation summary
        error_log('[SLMM Multiselect] Batch move completed - Moved: ' . $moved_count . ', Failed: ' . $failed_count . ', Target Parent: ' . $target_parent_id . ', User: ' . get_current_user_id());
        
        // Get target parent name for response
        $target_name = 'Root Level';
        if ($target_parent_id > 0) {
            $target_post = get_post($target_parent_id);
            if ($target_post) {
                $target_name = $target_post->post_title;
            }
        }
        
        // Return comprehensive results
        wp_send_json_success(array(
            'message' => sprintf('Batch move completed: %d moved to "%s", %d failed', $moved_count, $target_name, $failed_count),
            'moved_count' => $moved_count,
            'failed_count' => $failed_count,
            'total_count' => count($page_ids),
            'target_parent_id' => $target_parent_id,
            'target_parent_name' => $target_name,
            'results' => $results
        ));
    }
    
    /**
     * Check if moving a page to a target parent would create a circular dependency
     */
    private function would_create_circular_dependency($page_id, $target_parent_id) {
        // A page cannot be moved under itself
        if ($page_id === $target_parent_id) {
            return true;
        }
        
        // A page cannot be moved under any of its descendants
        $descendants = get_post_ancestors($target_parent_id);
        return in_array($page_id, $descendants);
    }
}

// Initialize the multi-selection system
SLMM_Interlinking_Multiselect::get_instance();