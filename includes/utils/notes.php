<?php
/**
 * SLMM Notes Feature
 * 
 * Adds a notes feature to the WordPress admin bar with a dropdown popup
 * that allows users to take notes about the project with basic formatting.
 * 
 * @package SLMM_SEO_Bundle
 * @since 4.8.1
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_Notes {
    
    private static $instance = null;
    
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct() {
        add_action('init', array($this, 'init'));
    }
    
    public function init() {
        // Check if notes feature is enabled (default to enabled for new installations)
        $options = get_option('chatgpt_generator_options', array());
        $enable_notes = isset($options['enable_notes']) ? $options['enable_notes'] : true;
        if (!$enable_notes) {
            return;
        }
        
        // Load in admin area OR Bricks Builder context
        $is_bricks_context = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
        if (!is_admin() && !$is_bricks_context) {
            return;
        }
        
        add_action('admin_bar_menu', array($this, 'add_notes_to_admin_bar'), 999);
        
        // Enqueue assets for both admin and Bricks context
        if (is_admin()) {
            add_action('admin_enqueue_scripts', array($this, 'enqueue_notes_assets'));
            add_action('admin_footer', array($this, 'render_notes_popup'));
        } elseif ($is_bricks_context) {
            add_action('wp_enqueue_scripts', array($this, 'enqueue_notes_assets'));
            add_action('wp_footer', array($this, 'render_notes_popup'));
        }
        
        add_action('wp_ajax_slmm_save_note', array($this, 'handle_save_note'));
        add_action('wp_ajax_slmm_get_note', array($this, 'handle_get_note'));
    }
    
    public function add_notes_to_admin_bar($wp_admin_bar) {
        // Check if user has notes to determine icon color
        $user_id = get_current_user_id();
        $has_notes = $this->user_has_notes($user_id);
        $icon_class = $has_notes ? 'slmm-notes-admin-bar-item slmm-notes-has-content' : 'slmm-notes-admin-bar-item';
        
        $wp_admin_bar->add_node(array(
            'id'    => 'slmm-notes',
            'title' => '<span class="ab-icon dashicons dashicons-edit-page"></span><span class="ab-label">Notes</span>',
            'href'  => '#',
            'meta'  => array(
                'class' => $icon_class,
                'title' => $has_notes ? 'Notes available - Click to open' : 'Click to open project notes'
            )
        ));
    }
    
    /**
     * Check if user has any notes content
     */
    private function user_has_notes($user_id) {
        // Check primary storage
        $note_content = get_user_meta($user_id, 'slmm_project_notes', true);
        
        if (!empty(trim($note_content))) {
            return true;
        }
        
        // Check backup sources if primary is empty
        $version_independent_key = 'slmm_user_notes_' . $user_id;
        $backup_content = get_option($version_independent_key, '');
        
        if (!empty(trim($backup_content))) {
            return true;
        }
        
        // Check structured backup
        $backup_key = 'slmm_notes_backup_user_' . $user_id;
        $backup_data = get_option($backup_key, array());
        if (is_array($backup_data) && isset($backup_data['content']) && !empty(trim($backup_data['content']))) {
            return true;
        }
        
        // Check global backup
        $global_backup_key = 'slmm_global_notes_backup';
        $global_backups = get_option($global_backup_key, array());
        if (isset($global_backups[$user_id]) && !empty(trim($global_backups[$user_id]['content']))) {
            return true;
        }
        
        return false;
    }
    
    public function enqueue_notes_assets() {
        wp_enqueue_script(
            'slmm-notes-script',
            plugin_dir_url(__FILE__) . '../../assets/js/slmm-notes.js',
            array('jquery'),
            SLMM_SEO_VERSION,
            false  // Load in header instead of footer for faster initialization
        );
        
        wp_enqueue_style(
            'slmm-notes-style',
            plugin_dir_url(__FILE__) . '../../assets/css/slmm-notes.css',
            array(),
            SLMM_SEO_VERSION
        );
        
        wp_localize_script('slmm-notes-script', 'slmmNotes', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_notes_nonce'),
            'strings' => array(
                'saving' => __('Saving...', 'slmm-seo-bundle'),
                'saved' => __('✓ Saved', 'slmm-seo-bundle'),
                'error' => __('Error saving note', 'slmm-seo-bundle')
            )
        ));
    }
    
    public function render_notes_popup() {
        ?>
        <div id="slmm-notes-popup" class="slmm-notes-popup" style="display: none;">
            <div class="slmm-notes-header">
                <h3 class="slmm-notes-title">
                    <span class="dashicons dashicons-edit-page"></span>
                    Project Notes
                </h3>
                <div class="slmm-notes-controls">
                    <span class="slmm-notes-status"></span>
                    <button type="button" class="slmm-notes-close" title="Close notes (ESC)">
                        <span class="dashicons dashicons-no-alt"></span>
                    </button>
                </div>
            </div>
            <div class="slmm-notes-content">
                <div class="slmm-notes-toolbar">
                    <button type="button" class="slmm-notes-format-btn" data-command="bold">
                        <strong>B</strong>
                    </button>
                    <button type="button" class="slmm-notes-format-btn" data-command="italic">
                        <em>I</em>
                    </button>
                    <button type="button" class="slmm-notes-format-btn" data-command="underline">
                        <u>U</u>
                    </button>
                    <button type="button" class="slmm-notes-format-btn" data-command="strikeThrough">
                        <s>S</s>
                    </button>
                    <div class="slmm-notes-divider"></div>
                    <button type="button" class="slmm-notes-format-btn" data-command="insertUnorderedList">
                        • 
                    </button>
                    <button type="button" class="slmm-notes-format-btn" data-command="insertOrderedList">
                        1.
                    </button>
                    <div class="slmm-notes-divider"></div>
                    <button type="button" class="slmm-notes-format-btn" data-command="createLink">
                        🔗
                    </button>
                    <button type="button" class="slmm-notes-format-btn" data-command="unlink">
                        🚫
                    </button>
                    <div class="slmm-notes-shortcuts-info">
                        <div class="slmm-shortcuts-line">Shortcuts: <strong>Bold</strong> Ctrl/Cmd+B • <strong>Italic</strong> Ctrl/Cmd+I • <strong>Underline</strong> Ctrl/Cmd+U</div>
                        <div class="slmm-shortcuts-line"><strong>🔗</strong> Ctrl/Cmd+K • <strong>S</strong> Click only • Lists Click only</div>
                    </div>
                </div>
                <div class="slmm-notes-editor-container">
                    <div id="slmm-notes-editor" 
                         class="slmm-notes-editor" 
                         contenteditable="true" 
                         placeholder="Start typing your project notes here...">
                    </div>
                </div>
            </div>
            <div class="slmm-notes-footer">
                <!-- Footer removed - no resize functionality -->
            </div>
        </div>
        <div id="slmm-notes-overlay" class="slmm-notes-overlay" style="display: none;"></div>
        <?php
    }
    
    public function handle_save_note() {
        check_ajax_referer('slmm_notes_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $note_content = wp_kses_post($_POST['note_content']);
        $client_timestamp = isset($_POST['client_timestamp']) ? sanitize_text_field($_POST['client_timestamp']) : '';
        $user_id = get_current_user_id();
        
        // Get current server-side note with timestamp for conflict detection
        $current_note_meta = get_user_meta($user_id, 'slmm_project_notes_meta', true);
        $current_content = get_user_meta($user_id, 'slmm_project_notes', true);
        
        // Initialize conflict resolution variables
        $conflict_detected = false;
        $final_content = $note_content;
        
        // Check for conflicts if we have both timestamps
        if (!empty($client_timestamp) && !empty($current_note_meta['last_modified'])) {
            $server_timestamp = strtotime($current_note_meta['last_modified']);
            $client_timestamp_parsed = strtotime($client_timestamp);
            
            // If server has newer content and it's different from what client is saving
            if ($server_timestamp > $client_timestamp_parsed && $current_content !== $note_content) {
                $conflict_detected = true;
                
                // Append client changes to server content with conflict markers
                $conflict_separator = "\n\n" . str_repeat('=', 50) . "\n";
                $conflict_header = "CONFLICT RESOLVED - " . current_time('mysql') . "\n";
                $conflict_header .= "Multiple tabs detected. Both versions merged below:\n";
                $conflict_separator .= $conflict_header . str_repeat('=', 50) . "\n\n";
                
                $final_content = $current_content . $conflict_separator . $note_content;
                
                error_log("SLMM Notes: Conflict detected and resolved for user $user_id");
            }
        }
        
        $current_timestamp = current_time('mysql');
        
        // Save note content with metadata
        $note_meta = array(
            'last_modified' => $current_timestamp,
            'conflict_resolved' => $conflict_detected,
            'save_count' => isset($current_note_meta['save_count']) ? ($current_note_meta['save_count'] + 1) : 1
        );
        
        // Primary storage
        $primary_result = update_user_meta($user_id, 'slmm_project_notes', $final_content);
        $meta_result = update_user_meta($user_id, 'slmm_project_notes_meta', $note_meta);
        
        // Create multiple backup layers for persistence
        $backup_key = 'slmm_notes_backup_user_' . $user_id;
        $backup_result = update_option($backup_key, array(
            'content' => $final_content,
            'timestamp' => $current_timestamp,
            'user_id' => $user_id,
            'meta' => $note_meta
        ));
        
        // Version-independent backup
        $version_independent_key = 'slmm_user_notes_' . $user_id;
        update_option($version_independent_key, $final_content);
        
        // Global backup (in case user meta fails)
        $global_backup_key = 'slmm_global_notes_backup';
        $global_backups = get_option($global_backup_key, array());
        $global_backups[$user_id] = array(
            'content' => $final_content,
            'timestamp' => $current_timestamp
        );
        // Keep only last 10 users to prevent bloat
        $global_backups = array_slice($global_backups, -10, 10, true);
        update_option($global_backup_key, $global_backups);
        
        if ($primary_result !== false && $meta_result !== false) {
            wp_send_json_success(array(
                'message' => $conflict_detected ? 'Conflict resolved - both versions merged' : 'Note saved successfully',
                'timestamp' => $current_timestamp,
                'conflict_resolved' => $conflict_detected,
                'save_count' => $note_meta['save_count'],
                'backup_saved' => $backup_result !== false,
                'content' => $final_content // Send back the final content in case of conflicts
            ));
        } else {
            wp_send_json_error('Failed to save note');
        }
    }
    
    public function handle_get_note() {
        check_ajax_referer('slmm_notes_nonce', 'nonce');
        
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }
        
        $user_id = get_current_user_id();
        
        // Try to get note content and metadata from primary storage
        $note_content = get_user_meta($user_id, 'slmm_project_notes', true);
        $note_meta = get_user_meta($user_id, 'slmm_project_notes_meta', true);
        
        // If primary storage is empty, try backup sources with restoration
        if (empty($note_content)) {
            // Try version-independent backup first
            $version_independent_key = 'slmm_user_notes_' . $user_id;
            $note_content = get_option($version_independent_key, '');
            
            // If still empty, try structured backup
            if (empty($note_content)) {
                $backup_key = 'slmm_notes_backup_user_' . $user_id;
                $backup_data = get_option($backup_key, array());
                if (is_array($backup_data) && isset($backup_data['content'])) {
                    $note_content = $backup_data['content'];
                    $note_meta = isset($backup_data['meta']) ? $backup_data['meta'] : array();
                }
            }
            
            // If still empty, try global backup
            if (empty($note_content)) {
                $global_backup_key = 'slmm_global_notes_backup';
                $global_backups = get_option($global_backup_key, array());
                if (isset($global_backups[$user_id])) {
                    $note_content = $global_backups[$user_id]['content'];
                    $note_meta = array(
                        'last_modified' => $global_backups[$user_id]['timestamp'],
                        'restored_from' => 'global_backup'
                    );
                }
            }
            
            // Restore to primary storage if we found any backup data
            if (!empty($note_content)) {
                update_user_meta($user_id, 'slmm_project_notes', $note_content);
                if (!empty($note_meta)) {
                    update_user_meta($user_id, 'slmm_project_notes_meta', $note_meta);
                }
                error_log("SLMM Notes: Restored notes from backup for user $user_id");
            }
        }
        
        // Ensure we have a timestamp
        $timestamp = '';
        if (is_array($note_meta) && isset($note_meta['last_modified'])) {
            $timestamp = $note_meta['last_modified'];
        } else {
            $timestamp = current_time('mysql');
            // Update meta with current timestamp if missing
            $note_meta = array(
                'last_modified' => $timestamp,
                'save_count' => 0
            );
            update_user_meta($user_id, 'slmm_project_notes_meta', $note_meta);
        }
        
        wp_send_json_success(array(
            'content' => $note_content ? $note_content : '',
            'timestamp' => $timestamp,
            'meta' => $note_meta
        ));
    }
}

// Initialize the Notes feature
SLMM_Notes::get_instance(); 