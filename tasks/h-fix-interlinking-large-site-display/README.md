# Fix Interlinking Suite Memory Issues for Large Sites

## Success Criteria
- [x] Fix PHP syntax errors in interlinking-suite.php
- [x] Resolve major memory leaks in content-segmentation-simple.js
- [x] Implement server-side memory optimizations
- [x] Identify root cause of 1.4GB browser memory usage
- [ ] Add batch size setting in interlinking-suite-content section
- [ ] Implement progressive loading with AJAX endpoints
- [ ] Add client-side lazy loading for D3 tree rendering
- [ ] Test memory usage improvements on large sites

## Context Manifest

### Problem Statement
Interlinking suite consuming 1.4GB of browser memory on large sites with hundreds/thousands of pages, causing browser crashes and performance issues.

### Root Cause Analysis
**Server Side**: `analyze_wordpress_hierarchy()` loads ALL pages at once (line 14420 in interlinking-suite.php)
**Client Side**: D3.js attempts to render thousands of nodes simultaneously, overwhelming browser memory

### Technical Architecture
- **Dual Memory Issue**: Both server PHP memory exhaustion and client-side DOM rendering overload
- **Data Flow**: Server loads all pages → processes links/hierarchy → sends massive JSON → client renders everything
- **Current Bottleneck**: No pagination, lazy loading, or progressive disclosure

### Files Modified
- `interlinking-suite.php` - Fixed syntax errors, server-side optimization target
- `assets/js/content-segmentation-simple.js` - Comprehensive memory leak fixes

## Work Log

### 2025-01-27

#### Completed
- **Fixed critical PHP syntax error** in interlinking-suite.php (line 17447 - missing error_log opening parenthesis)
- **Eliminated major memory leaks** in content-segmentation-simple.js:
  - Added timeout tracking and cleanup system (`timeouts[]` array)
  - Fixed recursive `waitForTinyMCE()` calls with protection flag
  - Reduced batch size from 1000 to 100 in ACF integration
  - Added `destroy()` method with comprehensive cleanup
  - Implemented memory monitoring with 500MB warnings
  - Added automatic cleanup on page unload
  - Replaced multiple setTimeout accumulation with single tracked timeouts
  - Added namespaced event handlers for proper cleanup

#### Memory Leak Fixes Implemented
1. **Timeout Management**: All setTimeout calls now tracked in `timeouts[]` array for cleanup
2. **Recursion Protection**: `waitingForTinyMCE` flag prevents recursive calls
3. **Instance Destruction**: `destroy()` method clears timers, events, and references
4. **Event Namespacing**: All events use `.slmm-segmentation` namespace for targeted removal
5. **Memory Monitoring**: Browser memory usage tracked with console warnings
6. **Automatic Cleanup**: Page unload triggers complete cleanup

#### Root Cause Identified
- **Server**: `analyze_wordpress_hierarchy()` function loads ALL pages simultaneously into PHP memory
- **Client**: D3.js receives massive JSON payload and attempts to render thousands of DOM nodes
- **Solution Required**: Progressive loading with server-side pagination + client-side lazy rendering

#### Decisions
- Focus on server-side optimization as primary bottleneck
- Maintain all existing functionality while adding progressive loading
- Implement setting-based batch control (default: 150 pages)
- Use hierarchical loading: top-level pages → children on expand → "Load More" buttons

## Next Steps

### Phase 1: Settings Infrastructure
1. Add batch size setting in interlinking-suite-content section at `wp-admin/admin.php?page=chatgpt-generator-settings#interlinking-suite`
2. Default batch size: 150 pages
3. Setting controls both initial load and "Load More" increments

### Phase 2: Server-Side Progressive Loading
1. Modify `analyze_wordpress_hierarchy()` to support pagination
2. Create new AJAX endpoint for loading child pages on demand
3. Implement "Load More" functionality for large page sets
4. Ensure backward compatibility with existing functionality

### Phase 3: Client-Side Optimization
1. Update D3.js rendering to handle progressive data loading
2. Implement lazy expansion for tree nodes
3. Add loading indicators and "Load More" buttons
4. Maintain all existing interlinking features

### Phase 4: Testing and Validation
1. Test memory usage on sites with 1000+ pages
2. Verify all existing functionality preserved
3. Performance benchmarking before/after
4. User acceptance testing

## Technical Implementation Notes

### Memory Leak Resolution Summary
**Before**: Multiple setTimeout accumulation, recursive waitForTinyMCE calls, no cleanup
**After**: Tracked timeouts, recursion protection, comprehensive cleanup system

### Progressive Loading Strategy
- **Initial Load**: Top-level pages only (respects batch size setting)
- **On Expand**: Load children of specific parent page
- **Load More**: Additional pages when batch limit exceeded
- **Data Persistence**: Maintain loaded data to avoid re-fetching

### Backward Compatibility Requirements
- All existing interlinking features must continue working
- No changes to public API or user interface (except new setting)
- Progressive enhancement approach - degrades gracefully

### Performance Targets
- Browser memory usage < 200MB for sites with 1000+ pages
- Initial page load time < 3 seconds
- Subsequent data loading < 1 second per batch
- No functionality regression
