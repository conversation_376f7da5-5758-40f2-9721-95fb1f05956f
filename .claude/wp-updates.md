---
description: 
globs: 
alwaysApply: true
---
# GOLDEN RULE
Typing "updateWP" will trigger the below update sequence

## Update Sequence

Before doing anything make sure the plugin has a valid header.

Please analyze the recent code changes and update CHANGELOG.md with a new version entry. Look for changes in:
1. /src directory for feature and improvement changes
2. Recent commits or modified files for bug fixes

## IMPORTANT
 - Format the changes according to the existing changelog structure and increment the version number appropriately based on semantic versioning (major.minor.patch). Use today's date for the release.

- Create a Gihub summary statement that tells you the important changes made - like a summary - that can be used in the Github commit description.

- ALWAYS update the plugin.php file to latest version to match

- write the changes at the TOP of the changelog file.

- Create a plugin Summary Statement in this format
<Update Format>
(v4.8.0) Major Release: Comprehensive Dark Theme Implementation and UI Enhancement 

This major release introduces a complete dark theme transformation across all WordPress admin pages, featuring modern UI design with enhanced user experience and critical architectural improvements. The update includes comprehensive dark styling for all plugin interfaces, advanced visibility controls, and resolution of critical CSS conflicts affecting frontend styling.

Key Features:
✅ Complete dark theme for SLMM Settings, GPT Prompts, Structure Analyzer, and Content Freshness pages
✅ Scoped CSS variables system preventing global conflicts and frontend styling issues
✅ Consistent purple color scheme (#7C3AED) across all admin interfaces
✅ Advanced plugin visibility control and authorization system with emergency access
✅ Authorized admin username configuration with dynamic user management
✅ Plugin access restriction to specific admin users only
✅ Alternating row colors in content freshness table for improved readability
✅ Enhanced responsive design and mobile optimization

Major Improvements:
🔧 Emergency URL generation for plugin access recovery when locked out
🔧 Enhanced security features with user-based access control system
🔧 Removed redundant "Save Settings" button from Export/Import tab for cleaner UX
🔧 Fixed critical CSS variable leak affecting frontend elements
🔧 Enhanced tab navigation with improved visual indicators and animations
🔧 Better organization of feature groups and settings sections
🔧 Improved form validation and user feedback systems
🔧 Enhanced Bricks Builder integration with better debugging capabilities

</Update Format>