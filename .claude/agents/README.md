# Debug Migration Agent

A specialized agent for safely migrating console.log statements to the SLMM centralized debug logging system.

## 🚨 Critical Safety Features

- **ALWAYS creates new git branch** - Never modifies main branch directly
- **Creates automatic backups** - Original files are preserved  
- **NO bulk operations** - Handles migrations one logical group at a time
- **Context-aware migrations** - Prevents `this` context loss in callbacks
- **Syntax validation** - Checks for JavaScript errors before completion
- **NEVER auto-commits** - Requires explicit human approval
- **Emergency rollback** - Automatic recovery on failure

## 🎯 What It Migrates

### Before (console.log)
```javascript
console.log('Direct editor initialized');
console.log('Found URLs:', urlArray);
console.warn('No editor found');
console.error('Validation failed:', error);
```

### After (Debug System)
```javascript
this.debug.log('Direct Editor', 'Initialized');
this.debug.log('Link Tracking', 'Found URLs', urlArray);  
this.debug.warn('Direct Editor', 'No editor found');
this.debug.error('Validation', 'Validation failed', error);
```

### Context Safety (CRITICAL)
```javascript
// BEFORE (Broken):
urls.forEach(function(url) {
    console.log('Processing:', url);  // ❌ BREAKS in callback
});

// AFTER (Safe):
var self = this;  // ✅ Added automatically
urls.forEach(function(url) {
    self.debug.log('Processing', 'URL: ' + url);  // ✅ Safe
});
```

## 📋 Usage Instructions

### Basic Migration
```bash
# Navigate to plugin directory
cd /path/to/wp-content/plugins/slmm_seo_bundle

# Migrate a file
./.claude/agents/migrate-debug assets/js/slmm-direct-editor.js
```

### Preview Changes (Dry Run)
```bash
# See what would be changed without modifying anything
./.claude/agents/migrate-debug assets/js/file.js --dry-run
```

### Override Category Detection
```bash
# Force a specific debug category
./.claude/agents/migrate-debug assets/js/slmm-link-popup.js --category="Link Popup"
```

### Extra Safety for Critical Files
```bash
# Emergency mode with extra validation
./.claude/agents/migrate-debug assets/js/critical-file.js --emergency
```

## 🔧 What The Agent Does

### Phase 1: Safety Setup
1. ✅ Creates new git branch (`debug-migration/filename-timestamp`)
2. ✅ Creates backup file (`filename.js.backup-timestamp`)
3. ✅ Validates file exists and is readable

### Phase 2: Analysis  
1. 🔍 Finds all console.log/warn/error/info statements
2. 🔍 Detects callback contexts that need `var self = this;`
3. 🔍 Checks if debug helper object exists
4. 🔍 Suggests appropriate debug category

### Phase 3: Migration Planning
1. 📋 Plans debug helper addition (if needed)
2. 📋 Plans self reference additions for callbacks
3. 📋 Maps each console statement to appropriate debug method
4. 📋 Validates migration safety

### Phase 4: Safe Migration
1. ➕ Adds debug helper object (if needed)
2. ➕ Adds `var self = this;` in callback contexts
3. 🔄 Replaces console statements with debug calls
4. 💾 Writes updated file

### Phase 5: Validation
1. ✅ JavaScript syntax check (`node -c`)
2. ✅ ESLint validation (if available)
3. ✅ File size sanity check
4. ✅ Git status verification

### Phase 6: Report (NO AUTO-COMMIT)
1. 📋 Shows summary of all changes
2. 📄 Displays git diff preview
3. ⚠️ **WAITS for human approval**
4. 🧪 Provides testing recommendations

## 🛡️ Safety Protocols

### Automatic Context Loss Prevention
The agent automatically detects and fixes these dangerous patterns:

```javascript
// ❌ DANGEROUS - Will break application
$(document).on('click', '.button', function(e) {
    this.debug.log('Click', 'Button clicked'); // this = DOM element!
});

// ✅ SAFE - Agent adds this automatically  
var self = this; // Added by agent
$(document).on('click', '.button', function(e) {
    self.debug.log('Click', 'Button clicked'); // Works correctly
});
```

### Debug Helper Integration
For objects that don't have debug access, the agent adds this pattern:

```javascript
debug: {
    log: function(category, message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.log(category, message, data);
        }
    },
    success: function(category, message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.success(category, message, data);
        }
    },
    warn: function(category, message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.warn(category, message, data);
        }
    },
    error: function(category, message, data) {
        if (window.SLMM && window.SLMM.debug) {
            window.SLMM.debug.error(category, message, data);
        }
    }
}
```

## 📊 Category Intelligence

### Auto-Detection Rules
- `*editor*` → 'Direct Editor'
- `*ajax*` → 'AJAX'  
- `*link*` → 'Link Tracking'
- `*popup*` → 'Popup Management'
- `*resize*` → 'Resize'
- `*dashboard*` → 'Dashboard'
- `*acf*` → 'ACF Integration'

### Available Categories
```
'Direct Editor', 'Dashboard', 'ACF Title', 'Regular Title',
'Link Tracking', 'Important Pages', 'Silo Nav', 'Copy Links',
'Delete Semantic', 'Importance', 'Copy', 'Popup Management',
'Resize', 'ACF Direct Editor', 'Link Popup', 'ACF Integration',
'Initialization', 'Tree Loading', 'Mouseover', 'Completion',
'Surgical Update', 'Link Overlay', 'Notes', 'Visual Highlighting',
'Link Connections', 'Fallback Matching', 'Debug Analysis'
```

## 🧪 After Migration Testing

The agent provides these testing recommendations:

1. **Test with debug ENABLED**: Add `?slmm_debug=true` to URL
2. **Test with debug DISABLED**: Default behavior - no console output  
3. **Check browser console**: Verify no JavaScript errors
4. **Verify functionality**: Ensure all original features still work
5. **Test callback contexts**: Especially click handlers, AJAX calls, loops

## 📁 File Structure

```
.claude/agents/
├── debug-migration.md      # Agent documentation and patterns
├── debug-migration.js      # Main migration engine  
├── migrate-debug          # Simple command wrapper
└── README.md              # This file
```

## 🚨 Emergency Recovery

If something goes wrong:

```bash
# View what changed
git diff

# See all branches
git branch -a

# Rollback to original state
git checkout main
git branch -D debug-migration/filename-timestamp

# Restore from backup (if needed)
cp filename.js.backup-timestamp filename.js
```

## ⚡ Performance Considerations

The agent handles these performance traps automatically:

```javascript
// ❌ SLOW - Always runs expensive operation
this.debug.log('Analysis', 'Data', this.expensiveCalculation());

// ✅ FAST - Agent guards expensive operations when possible  
if (SLMM.debug.isEnabled('Analysis')) {
    var data = this.expensiveCalculation();
    this.debug.log('Analysis', 'Data', data);
}
```

## 🏁 Commit Workflow

The agent **NEVER** commits automatically. After successful migration:

```bash
# Review changes
git diff

# If changes look good, commit them
git add .
git commit -m "Migrate console logging to debug system"

# Merge back to main (if desired)
git checkout main
git merge debug-migration/filename-timestamp
git branch -D debug-migration/filename-timestamp

# If changes have issues, abandon them
git checkout main  
git branch -D debug-migration/filename-timestamp
```

## 🔍 Debugging The Agent

If the agent has issues:

```bash
# Check Node.js version
node --version

# Test basic functionality
./.claude/agents/migrate-debug --help

# Run in dry-run mode first
./.claude/agents/migrate-debug assets/js/file.js --dry-run

# Check git status
git status

# View git branches
git branch -a
```

## 📞 Support

If you encounter issues:

1. Check the emergency recovery section above
2. Verify all safety files exist (backup files, git branches)
3. Test with `--dry-run` first to preview changes
4. Use `--emergency` mode for extra safety checks
5. Check browser console for JavaScript errors after migration

Remember: **The agent prioritizes safety over speed. It's designed to never break your application.**