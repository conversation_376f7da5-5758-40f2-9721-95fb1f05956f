// File: assets/js/slmm-prompt-settings.js
// Replace the entire content of this file with the following:

(function($) {
    'use strict';

    var SLMM_PromptSettings = {
        promptIndex: 0,

        init: function() {
            this.cacheDom();
            this.bindEvents();
            this.updatePromptIndexes();
        },

        cacheDom: function() {
            this.$container = $('#slmm-gpt-prompts-container');
            this.$addButton = $('#slmm-add-prompt');
            this.$form = $('form[action="options.php"]');
        },

        bindEvents: function() {
            this.$addButton.on('click', this.addNewPrompt.bind(this));
            this.$container.on('click', '.slmm-remove-prompt', this.removePrompt.bind(this));
            this.$container.on('click', '.slmm-move-prompt', this.movePrompt.bind(this));
            this.$form.on('submit', this.onFormSubmit.bind(this));

            this.$container.sortable({
                handle: '.slmm-gpt-prompt-handle',
                update: this.updatePromptIndexes.bind(this)
            });
        },

        addNewPrompt: function(e) {
            e.preventDefault();
            var newPrompt = this.createPromptHtml(this.promptIndex);
            this.$container.append(newPrompt);
            this.promptIndex++;
            this.updatePromptIndexes();
        },

        createPromptHtml: function(index) {
            return `
                <div class="slmm-gpt-prompt" data-index="${index}">
                    <div class="slmm-gpt-prompt-header">
                        <span class="slmm-gpt-prompt-handle dashicons dashicons-menu"></span>
                        <div class="slmm-gpt-prompt-arrows">
                            <span class="dashicons dashicons-arrow-up-alt2 slmm-move-prompt" data-direction="up"></span>
                            <span class="dashicons dashicons-arrow-down-alt2 slmm-move-prompt" data-direction="down"></span>
                        </div>
                        <input type="text" name="slmm_gpt_prompts[${index}][title]" placeholder="GPT Menu Title" required class="slmm-gpt-prompt-title">
                        <select name="slmm_gpt_prompts[${index}][model]" class="slmm-gpt-prompt-model">
                            <option value="gpt-3.5-turbo">GPT-3.5</option>
                            <option value="gpt-4">GPT-4</option>
                            <option value="gpt-4-turbo-preview">GPT-4 Turbo</option>
                        </select>
                        <button type="button" class="button button-secondary slmm-remove-prompt">Remove Prompt</button>
                    </div>
                    <textarea name="slmm_gpt_prompts[${index}][prompt]" placeholder="Enter your GPT prompt here. Use {INSERT} as a placeholder for the selected text." required class="slmm-gpt-prompt-content"></textarea>
                    <div class="slmm-gpt-prompt-footer">
                        <label>Temperature: <input type="number" step="0.1" min="0" max="1" name="slmm_gpt_prompts[${index}][temperature]" value="0.7" class="slmm-gpt-prompt-temperature"></label>
                        <label>Max Tokens: <input type="number" step="1" min="1" max="4096" name="slmm_gpt_prompts[${index}][max_tokens]" value="1000" class="slmm-gpt-prompt-max-tokens"></label>
                    </div>
                </div>
            `;
        },

        removePrompt: function(e) {
            $(e.target).closest('.slmm-gpt-prompt').remove();
            this.updatePromptIndexes();
        },

        movePrompt: function(e) {
            var $prompt = $(e.target).closest('.slmm-gpt-prompt');
            var direction = $(e.target).data('direction');

            if (direction === 'up' && $prompt.prev('.slmm-gpt-prompt').length) {
                $prompt.insertBefore($prompt.prev());
            } else if (direction === 'down' && $prompt.next('.slmm-gpt-prompt').length) {
                $prompt.insertAfter($prompt.next());
            }

            this.updatePromptIndexes();
        },

        updatePromptIndexes: function() {
            this.$container.find('.slmm-gpt-prompt').each(function(index) {
                $(this).attr('data-index', index);
                $(this).find('input, textarea, select').each(function() {
                    var name = $(this).attr('name');
                    if (name) {
                        $(this).attr('name', name.replace(/\[\d+\]/, '[' + index + ']'));
                    }
                });
            });
            this.promptIndex = this.$container.find('.slmm-gpt-prompt').length;
        },

        onFormSubmit: function(e) {
            // You can add form validation here if needed
            // e.preventDefault();
            // Perform validation
            // If valid, e.target.submit();
        }
    };

    $(document).ready(function() {
        SLMM_PromptSettings.init();
    });

})(jQuery);