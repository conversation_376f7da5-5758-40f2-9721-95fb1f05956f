# Interlinking Suite: Hierarchical Grid Layout Documentation

## Overview
The Hierarchical Grid Layout is a feature in the SLMM Interlinking Suite that allows users to constrain the width of the tree visualization while preserving the hierarchical relationships between pages. This solves the problem of extremely wide layouts when dealing with large sites (200+ posts).

## Problem Statement
When visualizing sites with hundreds of pages, the default D3 tree layout creates an extremely wide horizontal spread that extends beyond screen boundaries, making navigation difficult. Users needed a way to limit the width while still understanding the parent-child relationships between pages.

## Solution: Tiered Grid System
The hierarchical grid layout groups nodes by their depth level in the WordPress page hierarchy and arranges each level in its own grid, stacked vertically. This maintains visual hierarchy while constraining horizontal width.

## Technical Implementation

### Location
- **File**: `includes/interlinking/interlinking-suite.php`
- **Function**: `applyGridLayout(nodes)` (lines ~5807-5868)
- **Trigger**: Column width input field (ID: `slmm-column-width`)

### How It Works

#### 1. User Control
```html
<input type="number" id="slmm-column-width" class="slmm-column-width-input"
       min="1" max="1000" placeholder="Auto"
       title="Set columns per row (empty=auto layout, number=grid layout)">
```
- Empty/Auto: Uses default D3 tree layout
- Number (1-1000): Activates grid layout with specified columns

#### 2. Grid Layout Activation
```javascript
$('#slmm-column-width').on('input change', function() {
    const inputValue = $(this).val().trim();

    if (inputValue === '') {
        useGridLayout = false;  // Tree layout
    } else {
        columnWidth = parseInt(inputValue);
        useGridLayout = true;   // Grid layout
    }

    if (treeData && d3ComponentsReady) {
        updateTree(treeData);
    }
});
```

#### 3. Depth Preservation Algorithm
The key insight is using WordPress hierarchy depth (`node.data.depth`) instead of D3's calculated depth (`node.depth`):

```javascript
function applyGridLayout(nodes) {
    // Separate virtual root from content nodes
    const virtualRoot = nodes.find(d => d.data.post_type === 'site');
    const contentNodes = nodes.filter(d => d.data.post_type !== 'site');

    // Group by WordPress hierarchy depth (NOT D3's depth)
    const nodesByDepth = {};
    let maxDepth = 0;

    contentNodes.forEach(node => {
        // Critical: Use WordPress depth, not D3 depth
        const depth = node.data.depth !== undefined ? node.data.depth : 0;
        maxDepth = Math.max(maxDepth, depth);

        if (!nodesByDepth[depth]) {
            nodesByDepth[depth] = [];
        }
        nodesByDepth[depth].push(node);
    });

    // Apply grid to each depth level
    let cumulativeY = startY;

    for (let depth = 0; depth <= maxDepth; depth++) {
        const levelNodes = nodesByDepth[depth] || [];
        if (levelNodes.length === 0) continue;

        const rowsInLevel = Math.ceil(levelNodes.length / columnWidth);

        // Position nodes in this depth level's grid
        levelNodes.forEach((node, index) => {
            const col = index % columnWidth;
            const row = Math.floor(index / columnWidth);

            const totalGridWidth = (columnWidth - 1) * gridSpacing;
            const startX = -totalGridWidth / 2;

            node.x = startX + (col * gridSpacing);
            node.y = cumulativeY + (row * rowHeight);
        });

        // Move to next level's Y position
        cumulativeY += (rowsInLevel * rowHeight) + levelSpacing;
    }
}
```

### Key Technical Details

#### Depth Value Sources
1. **WordPress Depth** (`node.data.depth`):
   - Set from PHP via `$hierarchy['depths'][$page_id]`
   - Calculated in `calculate_page_depths()` method
   - Represents true page hierarchy in WordPress
   - Preserved across post type filtering

2. **D3 Depth** (`node.depth`):
   - Calculated by `d3.hierarchy()` based on JavaScript tree structure
   - Can be incorrect when filtering by post type
   - All filtered posts may appear as depth 0 if they have no parent-child relationships

#### Layout Parameters
```javascript
const gridSpacing = nodeWidth + 50;    // Horizontal space between nodes (~210px)
const rowHeight = nodeHeight + 80;     // Vertical space between rows (~180px)
const levelSpacing = 200;              // Extra space between hierarchy levels
const startY = 100;                    // Start position below virtual root
```

#### Visual Result
```
[Virtual Root - Site Name]
        |
================== Level 0 (Depth 0) ==================
[Page] [Page] [Page] [Page] [Page] [Page] [Page] [Page]
[Page] [Page] [Page] [Page] [Page] [Page] [Page] [Page]

================== Level 1 (Depth 1) ==================
[Child] [Child] [Child] [Child] [Child] [Child] [Child]
[Child] [Child] [Child] [Child] [Child] [Child] [Child]

================== Level 2 (Depth 2) ==================
[Grand] [Grand] [Grand] [Grand] [Grand] [Grand] [Grand]
```

### Why This Approach Works

1. **Preserves True Hierarchy**: By using WordPress depth values, the original page hierarchy is maintained even when filtering by custom post types.

2. **Handles Filtered Views**: When viewing only "Glossaries" or other CPTs, pages retain their depth values from the full site structure.

3. **Maintains Connections**: D3's link system still draws parent-child connections between levels, making relationships clear.

4. **Scalable**: Works with any number of depth levels and any column width setting.

## Usage Guidelines

### When to Use Grid Layout
- Sites with 50+ pages at any single depth level
- When horizontal scrolling becomes excessive
- For overview/audit purposes where seeing all pages is more important than tree aesthetics

### Recommended Column Settings
- **10-15 columns**: Narrow screens or very large sites (500+ pages)
- **20-25 columns**: Standard desktop screens with 100-300 pages
- **30-40 columns**: Wide screens or when preserving more tree-like appearance
- **Auto (empty)**: Small sites or when tree structure clarity is paramount

### Performance Considerations
- Grid layout is actually faster than tree layout for large datasets
- No recursive traversal needed - simple array iteration
- Linear time complexity: O(n) where n = number of nodes

## Integration with Other Features

### Works With
- **Status Filters**: Filtered nodes maintain their depth in grid view
- **Importance/Difficulty Filters**: Filtering doesn't affect depth calculation
- **Search**: Found nodes appear at their correct depth level
- **Expand/Collapse**: Grid re-layouts when nodes are hidden/shown
- **ACF Title Mode**: Alternative titles work in grid layout

### Zoom and Pan
The grid layout respects D3's zoom behavior:
- Mouse wheel: Zoom in/out
- Click and drag: Pan around canvas
- Grid maintains relative positioning during transforms

## Troubleshooting

### All Nodes Appear at Same Level
**Cause**: Post type has no hierarchical relationships
**Solution**: This is correct behavior - non-hierarchical CPTs will all be depth 0

### Grid Too Wide/Narrow
**Cause**: Column width not optimal for screen size
**Solution**: Adjust column width value or use Auto mode

### Parent-Child Lines Crossing
**Cause**: Natural result of grid layout forcing positions
**Solution**: Reduce column width or use tree mode for clearer relationships

## Future Enhancements

### Potential Improvements
1. **Smart Column Width**: Auto-calculate optimal columns based on viewport
2. **Depth Labels**: Add visual labels for each hierarchy level
3. **Compact Mode**: Tighter spacing for very large sites
4. **Hybrid Layout**: Tree for upper levels, grid for deep levels
5. **Grouping**: Keep parent-child groups together in grid

### Configuration Options
Could add settings for:
- `levelSpacing`: Customize space between hierarchy levels
- `gridSpacing`: Adjust horizontal density
- `rowHeight`: Control vertical density
- `maxGridWidth`: Set maximum total width before wrapping

## Code References

### Key Functions
- `applyGridLayout()`: Main grid layout algorithm
- `updateTree()`: Calls grid layout when enabled
- `calculate_page_depths()`: PHP method that sets WordPress depths
- `d3.hierarchy()`: Creates tree structure but may calculate wrong depths

### Key Variables
- `useGridLayout`: Boolean flag for grid vs tree mode
- `columnWidth`: Number of columns per row
- `nodesByDepth`: Object grouping nodes by depth level
- `node.data.depth`: WordPress hierarchy depth (correct)
- `node.depth`: D3 calculated depth (potentially incorrect)

## Summary
The hierarchical grid layout successfully solves the wide display problem while preserving the critical parent-child relationships that make the interlinking suite valuable. By respecting WordPress's understanding of page hierarchy rather than D3's calculated depths, the feature works correctly even with filtered post type views, providing users with a powerful way to visualize and navigate large site structures.