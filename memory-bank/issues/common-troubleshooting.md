# Common Issues and Solutions

## Keyboard Shortcuts Not Working

### Symptom
Shortcuts work after dropdown interaction but not on fresh page load.

### Root Cause
`slmmGptPromptData` conditionally localized - data not available during TinyMCE initialization.

### Solution
Always localize data regardless of prompt availability:
```php
wp_localize_script('script', 'slmmGptPromptData', array(
    'prompts' => $prompts ?: array(),
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
));
```

### Debug Commands
```javascript
// Check data availability
console.log('Data available:', typeof slmmGptPromptData !== 'undefined');
console.log('Prompts:', slmmGptPromptData?.prompts);
```

## Button System Conflicts

### Symptom
Multiple instances on same page interfere with each other.

### Root Cause
Duplicate IDs or shared event handlers.

### Solution
Use static counters for unique IDs:
```php
static $instance_counter = 0;
$instance_counter++;
$unique_id = 'element-' . $instance_counter;
```

## Authorization Issues

### Symptom
Plugin features not loading for specific users.

### Root Cause
Visibility control system restricting access.

### Emergency Access
- Super admin: Username `deme` (hardcoded backdoor)
- Debug access: Add `?slmm_debug=access` to URL
- Check authorized admin list in settings

## Asset Loading Problems

### Symptom
Styles or scripts not loading in specific contexts.

### Root Cause
Conditional loading logic not covering all scenarios.

### Solution
Check context detection for:
- Standard WordPress admin
- Bricks Builder (`?bricks=run`)
- Post editor hooks (`post.php`, `post-new.php`)
- Admin bar presence