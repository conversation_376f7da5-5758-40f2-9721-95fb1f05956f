(function($) {
    'use strict';

    var KeywordChecker = {
        init: function() {
            this.bindEvents();
        },

        bindEvents: function() {
            $('#check-keywords').on('click', this.checkKeywords);
        },

        checkKeywords: function(e) {
            e.preventDefault();
            console.log('Checking keywords...');
        }
    };

    $(document).ready(function() {
        KeywordChecker.init();
    });

})(jQuery);