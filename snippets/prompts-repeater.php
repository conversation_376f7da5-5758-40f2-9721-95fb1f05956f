<?php
// File: snippets/prompts-repeater.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

function slmm_enqueue_prompt_scripts($hook) {
    if ('post.php' != $hook && 'post-new.php' != $hook) {
        return;
    }

    wp_enqueue_script('jquery');
    wp_enqueue_script('slmm-prompt-execution', plugin_dir_url(__FILE__) . '../assets/js/slmm-prompt-execution.js', array('jquery'), '1.0', true);

    $localized_data = array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('slmm_execute_gpt_prompt'),
        'prompts' => get_option('slmm_gpt_prompts', array())
    );

    wp_localize_script('slmm-prompt-execution', 'slmmGptData', $localized_data);
}
add_action('admin_enqueue_scripts', 'slmm_enqueue_prompt_scripts');
