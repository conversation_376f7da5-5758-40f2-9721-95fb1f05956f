<?php
/**
 * SLMM Direct Post Editor - Main Management Class
 * 
 * Provides WP Sheet Editor-style inline editing functionality within 
 * the Interlinking Suite nodes with bulletproof data integrity.
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Direct_Editing
 * @version 1.0.0
 * @since 4.10.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_Direct_Editor
 * 
 * Main coordinator for direct post editing functionality.
 * Handles asset loading, WordPress integration, and safety systems.
 */
class SLMM_Direct_Editor {
    
    /**
     * Singleton instance
     * @var SLMM_Direct_Editor|null
     */
    private static $instance = null;
    
    /**
     * AJAX handler instance
     * @var SLMM_Editor_AJAX_Handler|null
     */
    private $ajax_handler = null;
    
    /**
     * Assets enqueued flag
     * @var bool
     */
    private $assets_enqueued = false;
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_Direct_Editor
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor - enforces singleton pattern
     */
    private function __construct() {
        $this->load_dependencies();
        $this->setup_hooks();
        
        error_log('[SLMM Direct Editor] Main class initialized');
    }
    
    /**
     * Load required dependencies
     */
    private function load_dependencies() {
        require_once SLMM_SEO_PLUGIN_DIR . 'includes/features/direct-editing/class-slmm-editor-ajax-handler.php';
    }
    
    /**
     * Setup WordPress hooks for integration
     */
    private function setup_hooks() {
        // Initialize AJAX handler
        $this->ajax_handler = SLMM_Editor_AJAX_Handler::get_instance();
        
        // Hook into Interlinking Suite asset loading
        add_action('admin_enqueue_scripts', array($this, 'maybe_enqueue_assets'), 15);
        
        // Add editor assets to Interlinking Suite pages
        add_filter('slmm_interlinking_localized_data', array($this, 'add_editor_data'));
        
        // Safety: Ensure wp_enqueue_editor is called early enough
        add_action('admin_init', array($this, 'prepare_editor_assets'));
        
        // Context-aware initialization for different WordPress contexts
        add_action('init', array($this, 'init_context_aware'));
        
        error_log('[SLMM Direct Editor] WordPress hooks configured');
    }
    
    /**
     * Context-aware initialization for different WordPress environments
     */
    public function init_context_aware() {
        // Check for special contexts that may affect editor behavior
        $is_bricks = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
        $is_gutenberg_active = function_exists('the_gutenberg_project');
        
        if ($is_bricks) {
            // CRITICAL: Bricks context - validate extra carefully
            add_filter('slmm_direct_editor_validation', array($this, 'extra_validation_for_bricks'));
            add_action('wp_enqueue_scripts', array($this, 'enqueue_context_safe_assets'));
            
            // CRITICAL: Modify editor behavior for Bricks context
            add_filter('slmm_direct_editor_config', array($this, 'modify_config_for_bricks_context'));
        } else {
            // Normal admin context
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        }
        
        error_log('[SLMM Direct Editor] Context-aware initialization complete');
    }
    
    /**
     * Extra validation for Bricks Builder context
     * 
     * @param array $validation_result
     * @return array
     */
    public function extra_validation_for_bricks($validation_result) {
        // In Bricks context, be extra cautious - only allow confirmed Classic Editor content
        if (!$validation_result['can_edit'] || $validation_result['editor_type'] !== 'classic') {
            return array(
                'can_edit' => false,
                'reason' => 'Only confirmed Classic Editor content can be edited in Bricks context',
                'editor_type' => $validation_result['editor_type'],
                'lock_reason' => 'Direct editing in Bricks context is restricted to Classic Editor content only for safety.',
                'can_unlock' => false
            );
        }
        
        return $validation_result;
    }
    
    /**
     * Modify editor configuration for special contexts
     * 
     * @param array $config
     * @return array
     */
    public function modify_config_for_bricks_context($config) {
        // Adjust editor configuration for Bricks context
        $config['tinymce_config']['toolbar1'] = 'bold,italic,link,unlink'; // Simplified toolbar for safety
        $config['modal_z_index'] = 999999; // Higher z-index for overlay contexts
        $config['safe_mode'] = true; // Enable additional safety checks
        $config['validation_required'] = true; // Force validation before opening
        
        return $config;
    }
    
    /**
     * Prepare editor assets early in admin_init
     * Following PRD requirement for safe asset loading
     */
    public function prepare_editor_assets() {
        if (!is_admin()) {
            return;
        }
        
        // Only prepare on Interlinking Suite pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'slmm-interlinking-suite') === false) {
            return;
        }
        
        // CRITICAL: Safe asset loading - only enqueue if not already loaded
        if (!wp_script_is('editor', 'registered') && !wp_script_is('editor', 'enqueued')) {
            wp_enqueue_editor();
        }
        
        // CRITICAL: Check for existing TinyMCE configurations to prevent conflicts
        if (!wp_script_is('wp-tinymce', 'enqueued')) {
            wp_enqueue_script('wp-tinymce');
        }
        
        error_log('[SLMM Direct Editor] Editor assets prepared safely');
    }
    
    /**
     * Maybe enqueue assets on Interlinking Suite pages
     * 
     * @param string $hook Current page hook
     */
    public function maybe_enqueue_assets($hook) {
        // Only load on interlinking suite page
        if (strpos($hook, 'slmm-interlinking-suite') === false) {
            return;
        }
        
        if ($this->assets_enqueued) {
            return; // Prevent double-enqueuing
        }
        
        $this->enqueue_direct_editor_assets();
        $this->assets_enqueued = true;
        
        error_log('[SLMM Direct Editor] Assets enqueued for hook: ' . $hook);
    }
    
    /**
     * Enqueue Direct Editor assets with conflict detection
     */
    private function enqueue_direct_editor_assets() {
        // CRITICAL: Namespace our editor assets to prevent conflicts
        wp_enqueue_script(
            'slmm-direct-editor',
            SLMM_SEO_PLUGIN_URL . 'assets/js/slmm-direct-editor.js',
            array('jquery', 'editor', 'wp-tinymce'),
            SLMM_SEO_VERSION,
            true
        );
        
        // Enqueue Direct Editor styles
        wp_enqueue_style(
            'slmm-direct-editor',
            SLMM_SEO_PLUGIN_URL . 'assets/css/slmm-direct-editor.css',
            array(),
            SLMM_SEO_VERSION
        );
        
        // Get debug logging setting from plugin options
        $options = get_option('chatgpt_generator_options', array());
        $debug_logging_enabled = isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : false;
        $debug_categories = isset($options['debug_logging_categories']) ? $options['debug_logging_categories'] : array();
        
        // Get AI interlinking data for Direct Editor modal
        $interlinking_prompts = get_option('slmm_interlinking_prompts', array());
        $interlinking_rules = get_option('slmm_interlinking_rules', '');
        
        // Get API keys from the correct location (chatgpt_generator_options)
        $openrouter_api_key = $options['openrouter_api_key'] ?? '';
        $anthropic_api_key = get_option('slmm_anthropic_api_key', '');
        
        // Localize script data for Direct Editor
        $editor_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_direct_edit_nonce'),
            'interlinking_nonce' => wp_create_nonce('slmm_ai_interlinking_suggest'),
            'interlinking_prompts' => $interlinking_prompts,
            'interlinking_rules' => $interlinking_rules,
            'ai_providers' => array(
                'openai_api_key' => $options['openai_api_key'] ?? '',
                'openrouter_api_key' => $openrouter_api_key,
                'anthropic_api_key' => $anthropic_api_key,
                'default_provider' => $options['ai_provider'] ?? 'openai',
                'default_model' => $options['model'] ?? 'gpt-4'
            ),
            'debug_logging_enabled' => $debug_logging_enabled,
            'debug_categories' => $debug_categories,
            'debug_mode' => defined('WP_DEBUG') && WP_DEBUG,
            'strings' => array(
                'loading' => __('Loading editor...', 'slmm-seo-bundle'),
                'saving' => __('Saving...', 'slmm-seo-bundle'),
                'saved' => __('✓ Saved', 'slmm-seo-bundle'),
                'error' => __('Save Error', 'slmm-seo-bundle'),
                'typing' => __('Typing...', 'slmm-seo-bundle'),
                'content_locked' => __('Content Not Editable', 'slmm-seo-bundle'),
                'validation_failed' => __('Direct editing is only available for Classic Editor content.', 'slmm-seo-bundle')
            ),
            'config' => apply_filters('slmm_direct_editor_config', array(
                'modal_z_index' => 100001, // Higher than Notes modal (100000)
                'session_timeout' => 300000, // 5 minutes session timeout
                'max_content_size' => 100000, // 100KB content limit
                'tinymce_config' => array(
                    'toolbar1' => 'bold,italic,underline,strikethrough,|,link,unlink,|,bullist,numlist,|,undo,redo',
                    'toolbar2' => 'formatselect,|,alignleft,aligncenter,alignright,|,indent,outdent,|,wp_adv',
                    'menubar' => false,
                    'height' => 400
                ),
                'save_on_close' => true, // Save only when closing - resource efficient
                'safe_mode' => false,
                'validation_required' => true,
                'auto_segmentation' => array(
                    'top_percent' => isset($options['auto_segment_top_percent']) ? (int)$options['auto_segment_top_percent'] : 15,
                    'bottom_percent' => isset($options['auto_segment_bottom_percent']) ? (int)$options['auto_segment_bottom_percent'] : 75
                )
            ))
        );
        
        wp_localize_script('slmm-direct-editor', 'slmmDirectEditorData', $editor_data);
        
        error_log('[SLMM Direct Editor] Direct Editor assets enqueued successfully');
    }
    
    /**
     * Add Direct Editor data to Interlinking Suite localized data
     * 
     * @param array $data Existing localized data
     * @return array Enhanced data with Direct Editor support
     */
    public function add_editor_data($data) {
        $data['direct_editor'] = array(
            'enabled' => true,
            'nonce' => wp_create_nonce('slmm_direct_edit_nonce'),
            'endpoints' => array(
                'validate' => 'slmm_validate_post_content',
                'load' => 'slmm_load_post_editor',
                'save' => 'slmm_auto_save_post'
            )
        );
        
        return $data;
    }
    
    /**
     * Enqueue assets for admin context
     */
    public function enqueue_admin_assets() {
        $this->maybe_enqueue_assets(get_current_screen()->id);
    }
    
    /**
     * Enqueue context-safe assets for special environments (Bricks, etc.)
     */
    public function enqueue_context_safe_assets() {
        // In special contexts, load with extra safety checks
        if (!wp_script_is('slmm-direct-editor', 'enqueued')) {
            $this->enqueue_direct_editor_assets();
        }
    }
    
    /**
     * Check if Direct Editor is available for current context
     * 
     * @return bool
     */
    public function is_available() {
        // Check authorization
        if (!slmm_seo_check_visibility_authorization()) {
            return false;
        }
        
        // Check required capabilities
        if (!current_user_can('edit_posts') && !current_user_can('edit_pages')) {
            return false;
        }
        
        // Check if we're in a supported context
        if (!is_admin() && !isset($_GET['bricks'])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Get editor configuration for JavaScript
     * 
     * @return array
     */
    public function get_editor_config() {
        $config = array(
            'modal_z_index' => 100001, // Higher than Notes modal
            'memory_cleanup_interval' => 30000, // 30 seconds
            'session_timeout' => 300000, // 5 minutes session timeout
            'max_content_size' => 100000, // 100KB content limit
            'tinymce_config' => array(
                'menubar' => false,
                'height' => 400,
                'toolbar1' => 'bold,italic,underline,strikethrough,|,link,unlink,|,bullist,numlist,|,undo,redo',
                'toolbar2' => 'formatselect,|,alignleft,aligncenter,alignright,|,indent,outdent,|,wp_adv',
                'plugins' => 'lists,link,textcolor,paste,tabfocus,wordpress,wpautoresize,wpeditimage,wpgallery,wplink,wptextpattern,wpview',
                'body_class' => 'slmm-direct-editor-content'
            ),
            'save_on_close' => true // Save only when closing - resource efficient
        );
        
        return apply_filters('slmm_direct_editor_config', $config);
    }
    
    /**
     * Handle Direct Editor activation
     */
    public function activate() {
        // Create any necessary database tables or options
        $this->create_editor_options();
        
        // Ensure required directories exist
        $this->ensure_directories();
        
        error_log('[SLMM Direct Editor] Activation complete');
    }
    
    /**
     * Create necessary options for Direct Editor
     */
    private function create_editor_options() {
        add_option('slmm_direct_editor_version', '1.0.0');
        add_option('slmm_direct_editor_settings', array(
            'save_on_close_enabled' => true, // Resource-efficient save-on-close
            'session_timeout' => 300, // 5 minutes
            'validation_strict' => true,
            'memory_cleanup' => true,
            'max_content_size' => 100000 // 100KB limit
        ));
    }
    
    /**
     * Ensure required directories exist
     */
    private function ensure_directories() {
        $upload_dir = wp_upload_dir();
        $editor_dir = $upload_dir['basedir'] . '/slmm-direct-editor';
        
        if (!file_exists($editor_dir)) {
            wp_mkdir_p($editor_dir);
        }
    }
    
    /**
     * Handle Direct Editor deactivation
     */
    public function deactivate() {
        // Clean up any temporary data
        delete_transient('slmm_direct_editor_cache');
        
        error_log('[SLMM Direct Editor] Deactivation complete');
    }
    
    /**
     * Get Direct Editor statistics
     * 
     * @return array
     */
    public function get_stats() {
        return array(
            'version' => get_option('slmm_direct_editor_version', '1.0.0'),
            'active_sessions' => 0, // Could be enhanced to track active editing sessions
            'total_edits' => get_option('slmm_direct_editor_total_edits', 0),
            'last_used' => get_option('slmm_direct_editor_last_used', null)
        );
    }
    
    /**
     * Update usage statistics
     */
    public function update_usage_stats() {
        $total_edits = get_option('slmm_direct_editor_total_edits', 0);
        update_option('slmm_direct_editor_total_edits', $total_edits + 1);
        update_option('slmm_direct_editor_last_used', current_time('mysql'));
    }
}

// Initialize Direct Editor (singleton pattern)
SLMM_Direct_Editor::get_instance();