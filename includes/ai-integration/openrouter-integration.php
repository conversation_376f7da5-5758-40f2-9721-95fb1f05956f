<?php
// File: includes/ai-integration/openrouter-integration.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_OpenRouter_Integration {
    private $api_key;
    private $base_url;

    public function __construct() {
        $options = get_option('chatgpt_generator_options', array());
        $this->api_key = isset($options['openrouter_api_key']) ? $options['openrouter_api_key'] : '';
        $this->base_url = 'https://openrouter.ai/api/v1';
    }

    /**
     * Check if API key is configured
     */
    public function is_configured() {
        return !empty($this->api_key);
    }

    /**
     * Get API key from settings
     */
    public function get_api_key() {
        return $this->api_key;
    }

    /**
     * Fetch available models from OpenRouter API
     */
    public function get_models() {
        if (empty($this->api_key)) {
            return $this->get_fallback_models();
        }

        // Check if we have cached models that are still valid (cache for 1 hour)
        $cached_models = get_transient('slmm_openrouter_models');
        if ($cached_models !== false) {
            return $cached_models;
        }

        $url = $this->base_url . '/models';
        $headers = array(
            'Authorization' => 'Bearer ' . $this->api_key,
            'Content-Type' => 'application/json'
        );

        $response = wp_remote_get($url, array(
            'headers' => $headers,
            'timeout' => 15
        ));

        if (is_wp_error($response)) {
            return $this->get_fallback_models();
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['data']) || !is_array($data['data'])) {
            return $this->get_fallback_models();
        }

        $models = array();
        foreach ($data['data'] as $model) {
            if (isset($model['id'])) {
                $model_name = isset($model['name']) ? $model['name'] : $this->format_model_name($model['id']);
                $models[$model['id']] = $model_name;
            }
        }

        // Sort models by name
        asort($models);

        // Cache the results for 1 hour
        set_transient('slmm_openrouter_models', $models, HOUR_IN_SECONDS);

        return $models;
    }

    /**
     * Get fallback models when API call fails
     */
    private function get_fallback_models() {
        return array(
            'google/gemini-2.0-flash-exp:free' => 'Gemini 2.0 Flash Exp (Free)',
            'google/gemini-1.5-flash' => 'Gemini 1.5 Flash',
            'google/gemini-1.5-pro' => 'Gemini 1.5 Pro',
            'meta-llama/llama-3.1-8b-instruct:free' => 'Llama 3.1 8B (Free)',
            'meta-llama/llama-3.1-70b-instruct:free' => 'Llama 3.1 70B (Free)',
            'anthropic/claude-3-haiku-20240307:free' => 'Claude 3 Haiku (Free)',
            'anthropic/claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
            'anthropic/claude-sonnet-4' => 'Claude Sonnet 4',
            'anthropic/claude-3-opus-20240229' => 'Claude 3 Opus',
            'mistralai/mistral-7b-instruct:free' => 'Mistral 7B (Free)',
            'microsoft/phi-3-mini-128k-instruct' => 'Phi 3 Mini 128K',
            'microsoft/phi-4-reasoning-plus' => 'Phi 4 Reasoning Plus',
            'openai/gpt-3.5-turbo' => 'GPT-3.5 Turbo',
            'openai/gpt-4-turbo' => 'GPT-4 Turbo',
            'openai/gpt-4' => 'GPT-4'
        );
    }

    /**
     * Format model name for display
     */
    private function format_model_name($model_id) {
        // Custom formatting for known model patterns
        $formatted_names = array(
            'google/gemini-2.0-flash-exp:free' => 'Gemini 2.0 Flash Exp (Free)',
            'google/gemini-1.5-flash' => 'Gemini 1.5 Flash',
            'google/gemini-1.5-pro' => 'Gemini 1.5 Pro',
            'meta-llama/llama-3.1-8b-instruct:free' => 'Llama 3.1 8B (Free)',
            'meta-llama/llama-3.1-70b-instruct:free' => 'Llama 3.1 70B (Free)',
            'anthropic/claude-3-haiku-20240307:free' => 'Claude 3 Haiku (Free)',
            'anthropic/claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
            'anthropic/claude-sonnet-4' => 'Claude Sonnet 4',
            'anthropic/claude-3-opus-20240229' => 'Claude 3 Opus',
            'mistralai/mistral-7b-instruct:free' => 'Mistral 7B (Free)',
            'microsoft/phi-3-mini-128k-instruct' => 'Phi 3 Mini 128K',
            'microsoft/phi-4-reasoning-plus' => 'Phi 4 Reasoning Plus'
        );

        if (isset($formatted_names[$model_id])) {
            return $formatted_names[$model_id];
        }

        // Extract model name from provider/model format
        $parts = explode('/', $model_id);
        if (count($parts) >= 2) {
            $model_part = end($parts);
            
            // Remove :free suffix
            $model_part = str_replace(':free', ' (Free)', $model_part);
            
            // Convert kebab-case to Title Case
            $formatted = ucwords(str_replace('-', ' ', $model_part));
            
            // Add provider prefix for clarity
            $provider = ucfirst($parts[0]);
            return $provider . ' ' . $formatted;
        }

        // Fallback: just clean up the model ID
        return ucwords(str_replace('-', ' ', $model_id));
    }

    /**
     * Filter models based on search term
     */
    public function filter_models($models, $search_term = '') {
        if (empty($search_term)) {
            return $models;
        }

        // Split search term into individual words and clean them
        $search_terms = preg_split('/\s+/', strtolower($search_term));
        $search_terms = array_filter($search_terms, 'strlen');

        if (empty($search_terms)) {
            return $models;
        }

        $filtered = array();
        foreach ($models as $model_id => $model_name) {
            $search_string = strtolower($model_id . ' ' . $model_name);
            
            // Check if ALL search terms are found in either id or name
            $matches_all = true;
            foreach ($search_terms as $term) {
                if (strpos($search_string, $term) === false) {
                    $matches_all = false;
                    break;
                }
            }
            
            if ($matches_all) {
                $filtered[$model_id] = $model_name;
            }
        }

        return $filtered;
    }

    /**
     * Generate content using OpenRouter API
     */
    public function generate_content($prompt, $model = 'google/gemini-2.0-flash-exp:free', $max_tokens = 1000, $temperature = 0.7, $top_p = 0.9) {
        if (empty($this->api_key)) {
            return new WP_Error('no_api_key', 'OpenRouter API key is not configured');
        }

        $url = $this->base_url . '/chat/completions';
        $headers = array(
            'Authorization' => 'Bearer ' . $this->api_key,
            'Content-Type' => 'application/json',
            'HTTP-Referer' => get_site_url(),
            'X-Title' => get_bloginfo('name')
        );

        $body = wp_json_encode(array(
            'model' => $model,
            'messages' => array(
                array('role' => 'user', 'content' => $prompt)
            ),
            'max_tokens' => intval($max_tokens),
            'temperature' => floatval($temperature),
            'top_p' => floatval($top_p)
        ));

        $response = wp_remote_post($url, array(
            'headers' => $headers,
            'body' => $body,
            'timeout' => 60
        ));

        if (is_wp_error($response)) {
            return $response;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (isset($data['error'])) {
            return new WP_Error('openrouter_error', $data['error']['message'] ?? 'Unknown OpenRouter error');
        }

        return isset($data['choices'][0]['message']['content']) ? trim($data['choices'][0]['message']['content']) : '';
    }

    /**
     * Get generation credits/balance
     */
    public function get_credits() {
        if (empty($this->api_key)) {
            return null;
        }

        $url = $this->base_url . '/auth/key';
        $headers = array(
            'Authorization' => 'Bearer ' . $this->api_key,
            'Content-Type' => 'application/json'
        );

        $response = wp_remote_get($url, array(
            'headers' => $headers,
            'timeout' => 10
        ));

        if (is_wp_error($response)) {
            return null;
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        return $data['data'] ?? null;
    }

    /**
     * Clear cached models
     */
    public function clear_model_cache() {
        delete_transient('slmm_openrouter_models');
    }
} 