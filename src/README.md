# WordPress Search & Replace Tool

This JavaScript-powered search and replace tool for WordPress TinyMCE editor provides advanced find and replace functionality with support for multi-word phrases, whole sentences, and complex patterns.

## Features

### Multi-Word Phrase Support (v6.3.0+)
- Search for complete phrases like "ALL PRODUCTS" as single blocks
- No quotes needed - just type the phrase naturally
- Handles spaces, punctuation, and special characters correctly
- Works with whole word matching for phrases
- Enhanced matching algorithms with improved relevance scoring

### Advanced Search Options
- **Match case**: Case-sensitive search
- **Whole words**: Find complete words/phrases only
- **Multiple terms**: Search for multiple comma-separated terms
- **Swap mode**: Replace multiple terms with corresponding replacements

### Interactive Interface
- Keyboard shortcut: `Shift+Cmd+F` (Mac) or `Shift+Ctrl+F` (Windows)
- Real-time match counter showing current position
- Navigate through matches with visual highlighting
- Replace individual matches or all at once

## Installation

1. Include the `searchReplace.js` file in your plugin directory
2. Enqueue the script in your WordPress admin:
   ```php
   wp_enqueue_script('slmm-search-replace', plugins_url('/src/searchReplace.js', dirname(__FILE__)), array('jquery'), '6.3.0', true);
   ```
3. The tool automatically integrates with TinyMCE when loaded

## Usage

### Basic Search & Replace
1. Press `Shift+Cmd+F` (Mac) or `Shift+Ctrl+F` (Windows) to open the dialog
2. Type your search term (e.g., "ALL PRODUCTS")
3. Enter replacement text
4. Click "Find" to locate matches
5. Use "Replace" for individual matches or "Replace All" for bulk replacement

### Multi-Word Phrases
- Type phrases naturally: `ALL PRODUCTS`
- No quotes needed - the tool treats it as one block
- Works with punctuation: `Hello, world!`
- Supports whole sentences with proper word boundaries

### Advanced Features
- **Multiple Terms**: Enter comma-separated terms for batch operations
- **Swap Mode**: Replace multiple terms with corresponding replacements
- **Favorites**: Save commonly used patterns for quick access
- **Enhanced UI**: Professional "Show" buttons for detailed view expansion (v6.3.0+)
- **Performance Optimized**: Console logging cleanup for production use (v6.3.0+)

## Technical Details

### Regex Pattern Handling (v6.3.0 Enhanced)
- Escapes special regex characters for safe pattern matching
- Handles multi-word phrases with proper word boundary logic
- For phrases with spaces: uses flexible whitespace matching (`\s+`)
- Word boundaries applied at phrase start/end only for multi-word terms
- Optimized performance with reduced console overhead
- Enhanced pattern matching for complex search queries

### Search Algorithm
1. Processes search terms (single or comma-separated)
2. Escapes special characters for regex safety
3. Applies word boundaries intelligently for phrases
4. Marks matches with highlighting spans
5. Provides navigation and replacement functionality

## Recent Updates

### Version 6.3.0 (2025-09-23)
- **Performance Optimizations**: Comprehensive console logging cleanup for production environments
- **Enhanced UI Components**: Improved "Show" button functionality with better state management
- **Visual Improvements**: Better contrast and readability in content segmentation displays
- **Reliability Fixes**: Resolved interaction issues with expandable content sections
- **Multi-Word Search Intelligence**: Enhanced phrase matching with improved relevance scoring

### Version 6.2.0 (2025-09-20)
- **Advanced Search & Replace System**: Page-by-page approval with individual checkboxes
- **Professional UI**: 40px button standardization and purple-themed styling
- **Enhanced Performance**: Removed rate limiting restrictions for unlimited usage
- **Improved Reliability**: Fixed "Show Details" button with proper event delegation 