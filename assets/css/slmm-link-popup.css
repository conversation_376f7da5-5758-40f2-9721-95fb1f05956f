/**
 * SLMM Link Popup Styles
 * 
 * Dark theme popup styling for internal and external link indicators
 * in the interlinking suite. Follows existing dark theme patterns.
 * 
 * @package SLMM_SEO_Bundle
 * @since 4.10.1
 */

/* Main popup container */
.slmm-link-popup {
    position: fixed;
    background: var(--slmm-dark-surface, #141419);
    border: 1px solid var(--slmm-dark-border, #1F1F24);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6);
    padding: 0;
    z-index: 100050;
    min-width: 320px;
    max-width: 480px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: var(--slmm-text-primary, #F1F5F9);
    display: none;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    pointer-events: none;
}

.slmm-link-popup.visible {
    display: block;
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
}

/* Popup header */
.slmm-link-popup-header {
    padding: 12px 16px;
    border-bottom: 1px solid var(--slmm-dark-border-subtle, #16161B);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--slmm-dark-bg, #0A0A0F);
    border-radius: 8px 8px 0 0;
}

.slmm-link-popup-title {
    font-size: 13px;
    font-weight: 600;
    color: var(--slmm-text-primary, #F1F5F9);
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0;
}

.slmm-link-popup-icon {
    width: 16px;
    height: 16px;
    border-radius: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.slmm-link-popup-icon.internal {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.slmm-link-popup-icon.external {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

/* Close button */
.slmm-link-popup-close {
    background: none;
    border: none;
    color: var(--slmm-text-muted, #64748B);
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.slmm-link-popup-close:hover {
    color: var(--slmm-text-primary, #F1F5F9);
    background: var(--slmm-dark-surface-hover, #1A1A1F);
}

/* Popup content */
.slmm-link-popup-content {
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;
}

.slmm-link-popup-content::-webkit-scrollbar {
    width: 6px;
}

.slmm-link-popup-content::-webkit-scrollbar-track {
    background: var(--slmm-dark-bg, #0A0A0F);
    border-radius: 3px;
}

.slmm-link-popup-content::-webkit-scrollbar-thumb {
    background: var(--slmm-dark-border, #1F1F24);
    border-radius: 3px;
}

.slmm-link-popup-content::-webkit-scrollbar-thumb:hover {
    background: var(--slmm-text-muted, #64748B);
}

/* Link summary */
.slmm-link-summary {
    margin-bottom: 16px;
    padding: 12px;
    background: var(--slmm-dark-bg, #0A0A0F);
    border-radius: 6px;
    border: 1px solid var(--slmm-dark-border-subtle, #16161B);
}

.slmm-link-count {
    font-size: 12px;
    color: var(--slmm-text-secondary, #94A3B8);
    margin-bottom: 4px;
}

.slmm-link-count-number {
    font-weight: 600;
    color: var(--slmm-text-primary, #F1F5F9);
}

/* Link list */
.slmm-link-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.slmm-link-item {
    padding: 8px 0;
    border-bottom: 1px solid var(--slmm-dark-border-subtle, #16161B);
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.slmm-link-item:last-child {
    border-bottom: none;
}

.slmm-link-bullet {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: var(--slmm-text-muted, #64748B);
    margin-top: 6px;
    flex-shrink: 0;
}

.slmm-link-details {
    flex: 1;
    min-width: 0;
}

.slmm-link-anchor {
    font-size: 12px;
    color: var(--slmm-text-primary, #F1F5F9);
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 4px;
    word-break: break-word;
}

.slmm-link-url {
    font-size: 11px;
    color: var(--slmm-text-muted, #64748B);
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    line-height: 1.3;
    word-break: break-all;
}

.slmm-link-target {
    font-size: 11px;
    color: var(--slmm-text-secondary, #94A3B8);
    margin-top: 2px;
    font-style: italic;
}

/* Action buttons */
.slmm-link-popup-actions {
    padding: 12px 16px;
    border-top: 1px solid var(--slmm-dark-border-subtle, #16161B);
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    background: var(--slmm-dark-bg, #0A0A0F);
    border-radius: 0 0 8px 8px;
}

.slmm-link-popup-btn {
    padding: 6px 12px;
    background: var(--slmm-dark-border, #1F1F24);
    border: 1px solid var(--slmm-dark-border, #1F1F24);
    color: var(--slmm-text-primary, #F1F5F9);
    font-size: 11px;
    font-weight: 500;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    text-decoration: none;
}

.slmm-link-popup-btn:hover {
    background: var(--slmm-dark-surface-hover, #1A1A1F);
    color: var(--slmm-text-primary, #F1F5F9);
    text-decoration: none;
}

.slmm-link-popup-btn.primary {
    background: var(--slmm-primary, #7C3AED);
    border-color: var(--slmm-primary, #7C3AED);
    color: white;
}

.slmm-link-popup-btn.primary:hover {
    background: var(--slmm-primary-hover, #8B5CF6);
    color: white;
}

/* Loading state */
.slmm-link-popup.loading .slmm-link-popup-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 32px 16px;
    color: var(--slmm-text-secondary, #94A3B8);
    font-size: 12px;
}

.slmm-link-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--slmm-dark-border, #1F1F24);
    border-top: 2px solid var(--slmm-primary, #7C3AED);
    border-radius: 50%;
    animation: slmm-spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes slmm-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.slmm-link-popup-error {
    padding: 16px;
    text-align: center;
    color: var(--slmm-error, #EF4444);
    font-size: 12px;
}

/* Empty state */
.slmm-link-popup-empty {
    padding: 24px 16px;
    text-align: center;
    color: var(--slmm-text-muted, #64748B);
    font-size: 12px;
}

.slmm-link-popup-empty-icon {
    font-size: 24px;
    margin-bottom: 8px;
    opacity: 0.5;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .slmm-link-popup {
        min-width: 280px;
        max-width: calc(100vw - 40px);
        max-height: calc(100vh - 40px);
    }
    
    .slmm-link-popup-content {
        max-height: 250px;
    }
    
    .slmm-link-popup-actions {
        flex-wrap: wrap;
    }
    
    .slmm-link-popup-btn {
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }
}

/* Animation refinements */
.slmm-link-popup.fade-in {
    animation: slmm-popup-fade-in 0.2s ease-out;
}

.slmm-link-popup.fade-out {
    animation: slmm-popup-fade-out 0.2s ease-in;
}

@keyframes slmm-popup-fade-in {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slmm-popup-fade-out {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
}

/* Link Relationship Highlighting */
.slmm-tree-node.slmm-link-dimmed {
    opacity: 0.05 !important;
    transition: opacity 0.3s ease;
}

.slmm-tree-node.slmm-link-highlighted {
    opacity: 1 !important;
    transition: opacity 0.3s ease;
}

.slmm-tree-node.slmm-link-highlighted .slmm-node-rect {
    fill: var(--slmm-primary-light, rgba(124, 58, 237, 0.1)) !important;
    stroke: var(--slmm-primary, #7C3AED) !important;
    transition: all 0.3s ease;
}

/* Source node highlighting (the hovered node) */
.slmm-tree-node.slmm-link-source {
    opacity: 1 !important;
}

.slmm-tree-node.slmm-link-source .slmm-node-rect {
    fill: var(--slmm-primary-light, rgba(124, 58, 237, 0.15)) !important;
    stroke: var(--slmm-primary, #7C3AED) !important;
    stroke-width: 2px !important;
    transition: all 0.3s ease;
}

/* Temporary Link Connections Styling (SHIFT+hover) */
/* These styles ensure temporary links match the Link Overlay system exactly */

.slmm-temp-link-group {
    pointer-events: none; /* Allow tree interactions underneath */
}

.slmm-temp-internal-link {
    fill: none;
    stroke: var(--slmm-internal-link-color, #3b82f6);
    stroke-width: var(--slmm-link-stroke-width, 1.5px);
    opacity: var(--slmm-link-default-opacity, 0.6);
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: all 0.2s ease;
    animation: slmm-temp-link-fade-in 0.3s ease-out;
}

.slmm-temp-external-link {
    fill: none;
    stroke: var(--slmm-external-link-color, #f59e0b);
    stroke-width: var(--slmm-link-stroke-width, 1.2px);
    stroke-dasharray: 4,2;
    opacity: var(--slmm-link-default-opacity, 0.4);
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: all 0.2s ease;
    animation: slmm-temp-link-fade-in 0.3s ease-out;
}

.slmm-temp-external-node {
    fill: var(--slmm-external-link-color, #f59e0b);
    stroke: white;
    stroke-width: 1px;
    opacity: var(--slmm-link-default-opacity, 0.6);
    transition: all 0.2s ease;
    animation: slmm-temp-node-fade-in 0.3s ease-out;
}

/* Animations for temporary links */
@keyframes slmm-temp-link-fade-in {
    from {
        opacity: 0;
        stroke-width: 0;
    }
    to {
        opacity: var(--slmm-link-default-opacity, 0.6);
        stroke-width: var(--slmm-link-stroke-width, 1.5px);
    }
}

@keyframes slmm-temp-node-fade-in {
    from {
        opacity: 0;
        r: 0;
    }
    to {
        opacity: var(--slmm-link-default-opacity, 0.6);
        r: 4;
    }
}

/* Performance optimizations for temporary links */
.slmm-temp-link-group {
    will-change: transform;
}

.slmm-temp-internal-link,
.slmm-temp-external-link {
    vector-effect: non-scaling-stroke;
    shape-rendering: optimizeSpeed;
}