# JavaScript Assets CLAUDE.md

## Purpose
Frontend JavaScript components providing interactive content management, memory-safe operations, and seamless integration with WordPress Classic Editor and TinyMCE.

## Narrative Summary
The JavaScript asset collection provides sophisticated content management tools including content segmentation, direct editing capabilities, and advanced interlinking features. Key focus areas include memory leak prevention, progressive loading patterns, and robust cleanup mechanisms to ensure stable long-term usage in WordPress admin environments.

Recent improvements have focused on eliminating memory leaks through comprehensive timeout tracking, implementing proper destruction patterns, and establishing clear resource cleanup protocols for all interactive components.

## Key Components

### Content Segmentation System
- **File**: `content-segmentation-simple.js`
- **Purpose**: 3-click workflow for content segmentation with TinyMCE integration
- **Memory Management**: Comprehensive timeout tracking and cleanup patterns
- **Integration**: Seamless integration with Direct Editor and WordPress Classic Editor

### Memory Management Patterns

#### Timeout Tracking System
```javascript
// Lines 22-24: Memory leak protection
timeouts: [], // Track all timeouts for cleanup
isDestroyed: false, // Prevent operations after cleanup
waitingForTinyMCE: false, // Prevent recursive waitForTinyMCE calls
```

#### Resource Cleanup Implementation
```javascript
// Lines 835-862: Comprehensive cleanup
clearTimeouts: function() {
    this.timeouts.forEach(function(timeoutId) {
        clearTimeout(timeoutId);
    });
    this.timeouts = [];
},

destroy: function() {
    this.isDestroyed = true;
    this.clearTimeouts();
    // Remove event listeners
    $(document).off('.slmm-content-segmentation');
    // Clear overlay timers
    if (this.overlayUpdateTimer) {
        clearTimeout(this.overlayUpdateTimer);
        this.overlayUpdateTimer = null;
    }
    // Additional cleanup...
}
```

#### Progressive Loading Strategy
- **Debounced Operations**: TinyMCE editor state changes debounced to prevent excessive calls
- **Conditional Overlay Updates**: Overlays only updated when explicitly enabled by user
- **Recursive Call Prevention**: Flags prevent infinite loops in TinyMCE wait functions
- **Automatic Cleanup**: Page unload handlers ensure proper resource cleanup

### Content Segmentation Features

#### TinyMCE Integration
- **Editor Ready Detection**: Multiple event listeners for reliable editor initialization
- **Overlay Management**: User-controlled overlay visibility with state persistence
- **Content Change Tracking**: Debounced content change detection for overlay updates
- **Multi-Editor Support**: Handles multiple TinyMCE instances gracefully

#### Segmentation Workflow
1. **Start Segmentation**: User-initiated 3-click workflow
2. **Content Selection**: Interactive content selection with visual feedback
3. **Marker Insertion**: Automatic segment marker insertion with cleanup
4. **State Management**: Proper state tracking throughout workflow

### Security and Stability
- **XSS Prevention**: Proper content sanitization for all user inputs
- **Memory Leak Protection**: Comprehensive timeout and event listener cleanup
- **Error Handling**: Graceful degradation when TinyMCE unavailable
- **State Validation**: Proper state checks before operations

## Integration Points

### Consumes
- TinyMCE editor instances for content manipulation
- WordPress Classic Editor for seamless integration
- Direct Editor system for enhanced editing capabilities
- jQuery for DOM manipulation and event handling

### Provides
- Content segmentation interface for structured content creation
- Memory-safe JavaScript patterns for long-running admin sessions
- Progressive loading mechanisms for performance optimization
- Comprehensive cleanup patterns for resource management

## Memory Leak Prevention Protocols

### Mandatory Cleanup Requirements
1. **Timeout Tracking**: All `setTimeout` calls must be tracked in arrays
2. **Event Listener Cleanup**: All event listeners must be properly removed
3. **Timer Clearance**: All timers must be cleared during cleanup
4. **State Reset**: Component state must be reset to initial values
5. **Recursive Prevention**: Flags must prevent recursive function calls

### Implementation Standards
```javascript
// Proper timeout tracking pattern
var timeoutId = setTimeout(function() {
    // Operation logic
}, delay);
this.timeouts.push(timeoutId); // REQUIRED: Track for cleanup

// Proper event listener pattern
$(document).on('event.namespace', handler); // Use namespaces
$(document).off('.namespace'); // Clean up by namespace

// Proper cleanup on page unload
$(window).on('beforeunload', function() {
    ComponentName.destroy(); // REQUIRED: Cleanup on exit
});
```

## Performance Optimization

### Debouncing Strategy
- **Search Operations**: Debounced to prevent excessive API calls
- **Content Updates**: Debounced overlay updates during typing
- **Resize Operations**: Debounced window resize handlers
- **State Changes**: Debounced state persistence operations

### Progressive Enhancement
- **Feature Detection**: Graceful fallback when features unavailable
- **Lazy Initialization**: Components initialize only when needed
- **Conditional Loading**: Assets loaded based on context requirements
- **Memory Monitoring**: Debug mode provides memory usage insights

## Testing and Validation

### Memory Leak Testing
```javascript
// Debug mode memory monitoring
if (window.slmmDebugMode) {
    console.log('Active timeouts:', SLMMContentSegmentation.timeouts.length);
    console.log('Is destroyed:', SLMMContentSegmentation.isDestroyed);
}
```

### Browser Compatibility
- **Modern Browser Support**: ES5+ compatible implementation
- **jQuery Dependency**: Leverages WordPress jQuery for compatibility
- **TinyMCE Integration**: Compatible with WordPress TinyMCE versions
- **Mobile Responsive**: Touch-friendly interface elements

## Recent Improvements
- **Memory Leak Elimination**: Comprehensive timeout tracking and cleanup
- **Recursive Call Prevention**: Flags prevent infinite loops
- **Progressive Loading**: Improved performance for large content
- **Resource Management**: Proper cleanup on component destruction
- **Debug Capabilities**: Enhanced debugging for memory monitoring

## Related Documentation
- `includes/interlinking/CLAUDE.md` - Interlinking suite memory patterns
- `sessions/tasks/h-fix-memory-usage.md` - Memory optimization task
- `docs/performance-comparison.md` - Performance analysis documentation