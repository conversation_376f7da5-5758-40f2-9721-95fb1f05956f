<?php
/**
 * SLMM QuickBulk AJAX Handler
 * Handles AJAX requests for canvas-integrated bulk page creation
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Bulk_Creation
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_QuickBulk_AJAX_Handler
 * 
 * Manages all AJAX endpoints for the QuickBulk canvas integration system.
 * Provides secure, rate-limited endpoints for bulk page creation and AI suggestions.
 */
class SLMM_QuickBulk_AJAX_Handler {
    
    /**
     * Singleton instance
     * @var SLMM_QuickBulk_AJAX_Handler|null
     */
    private static $instance = null;
    
    /**
     * Rate limiting cache key prefix
     * @var string
     */
    private $rate_limit_prefix = 'slmm_quickbulk_rate_limit_';
    
    /**
     * Maximum requests per user per time window
     * @var int
     */
    private $max_requests_per_window = 10;
    
    /**
     * Rate limit time window in seconds
     * @var int
     */
    private $rate_limit_window = 300; // 5 minutes
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_QuickBulk_AJAX_Handler
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct() {
        $this->init_ajax_hooks();
    }
    
    /**
     * Initialize AJAX hooks
     */
    private function init_ajax_hooks() {
        // Main bulk creation endpoint
        add_action('wp_ajax_slmm_quickbulk_create_pages', array($this, 'handle_bulk_create_pages'));
        
        // AI suggestions endpoint
        add_action('wp_ajax_slmm_generate_contextual_suggestions', array($this, 'handle_generate_suggestions'));
        
        // Page validation endpoint
        add_action('wp_ajax_slmm_validate_page_titles', array($this, 'handle_validate_page_titles'));
        
        // Grid positioning endpoint for D3.js integration
        add_action('wp_ajax_slmm_calculate_grid_positions', array($this, 'handle_calculate_grid_positions'));
        
        // Statistics endpoint for admin dashboard
        add_action('wp_ajax_slmm_get_bulk_creation_stats', array($this, 'handle_get_bulk_stats'));
        
        // Health check endpoint
        add_action('wp_ajax_slmm_quickbulk_health_check', array($this, 'handle_health_check'));
    }
    
    /**
     * Handle bulk page creation AJAX request
     */
    public function handle_bulk_create_pages() {
        // CRITICAL DEBUG: Test if debug logging is working
        error_log("🚨 SLMM AJAX Handler: handle_bulk_create_pages() called - Debug logging is WORKING!");
        error_log("🚨 SLMM AJAX Handler: POST data keys: " . implode(', ', array_keys($_POST)));

        // Comprehensive security validation
        if (!$this->validate_ajax_request('slmm_quickbulk_create_pages')) {
            error_log("🚨 SLMM AJAX Handler: AJAX request validation FAILED!");
            return;
        } else {
            error_log("🚨 SLMM AJAX Handler: AJAX request validation PASSED!");
        }
        
        try {
            // Extract and sanitize request data
            $request_data = $this->extract_request_data();
            
            // Validate request data
            $validation_result = $this->validate_creation_request($request_data);
            if (!$validation_result['valid']) {
                wp_send_json_error(array(
                    'message' => $validation_result['message'],
                    'code' => 'VALIDATION_FAILED'
                ));
                return;
            }
            
            // Parse page titles into structured data
            $page_data = $this->parse_page_titles($request_data['page_titles'], $request_data['page_slugs']);
            if (empty($page_data)) {
                wp_send_json_error(array(
                    'message' => 'No valid page titles provided',
                    'code' => 'NO_VALID_TITLES'
                ));
                return;
            }
            
            // Prepare creation options
            $options = $this->prepare_creation_options($request_data);
            
            // Create pages using bulk creator
            $bulk_creator = SLMM_Bulk_Page_Creator::get_instance();
            $results = $bulk_creator->create_pages_batch($page_data, $options);
            
            // Format response
            $response = $this->format_creation_response($results);
            
            // Log successful operation
            $this->log_bulk_operation('success', $results, $request_data);
            
            wp_send_json_success($response);
            
        } catch (Exception $e) {
            // Log error
            $this->log_bulk_operation('error', null, $request_data ?? array(), $e);
            
            wp_send_json_error(array(
                'message' => 'An unexpected error occurred during page creation',
                'code' => 'INTERNAL_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Handle AI suggestions generation request
     */
    public function handle_generate_suggestions() {
        if (!$this->validate_ajax_request('slmm_generate_contextual_suggestions', false)) {
            return;
        }
        
        try {
            $parent_title = sanitize_text_field($_POST['parent_title'] ?? '');
            $keywords = sanitize_text_field($_POST['keywords'] ?? '');
            $count = min(10, max(3, intval($_POST['count'] ?? 5)));
            $style = sanitize_text_field($_POST['style'] ?? 'diverse');
            
            if (empty($parent_title)) {
                wp_send_json_error(array(
                    'message' => 'Parent title is required',
                    'code' => 'MISSING_PARENT_TITLE'
                ));
                return;
            }
            
            // Generate suggestions
            $suggestions = $this->generate_ai_suggestions($parent_title, $keywords, $count, $style);
            
            wp_send_json_success(array(
                'suggestions' => $suggestions,
                'parent_title' => $parent_title,
                'generated_at' => current_time('c')
            ));
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Failed to generate suggestions',
                'code' => 'SUGGESTION_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Handle page title validation request
     */
    public function handle_validate_page_titles() {
        if (!$this->validate_ajax_request('slmm_validate_page_titles', false)) {
            return;
        }
        
        try {
            $page_titles = sanitize_textarea_field($_POST['page_titles'] ?? '');
            
            if (empty($page_titles)) {
                wp_send_json_error(array(
                    'message' => 'No page titles provided',
                    'code' => 'EMPTY_INPUT'
                ));
                return;
            }
            
            // Use intelligent processing to convert slugs to titles (same logic as creation)
            $parsed_pages = $this->parse_page_titles($page_titles);
            
            // Format for validation response with converted titles
            $validation_results = array(
                'total_lines' => count(explode("\n", $page_titles)),
                'valid_titles' => $parsed_pages,
                'invalid_titles' => array(),
                'warnings' => array(),
                'statistics' => array(
                    'valid_count' => count($parsed_pages),
                    'invalid_count' => 0,
                    'success_rate' => 100
                )
            );
            
            wp_send_json_success($validation_results);
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Validation failed',
                'code' => 'VALIDATION_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Handle grid position calculation for D3.js integration
     */
    public function handle_calculate_grid_positions() {
        if (!$this->validate_ajax_request('slmm_calculate_grid_positions', false)) {
            return;
        }
        
        try {
            $new_nodes = json_decode(stripslashes($_POST['new_nodes'] ?? '[]'), true);
            $existing_layout = json_decode(stripslashes($_POST['existing_layout'] ?? '{}'), true);
            
            if (!is_array($new_nodes) || empty($new_nodes)) {
                wp_send_json_error(array(
                    'message' => 'No new nodes provided',
                    'code' => 'EMPTY_NODES'
                ));
                return;
            }
            
            // Calculate optimal positions using grid generator
            $positions = $this->calculate_optimal_positions($new_nodes, $existing_layout);
            
            wp_send_json_success(array(
                'positions' => $positions,
                'calculated_at' => current_time('c'),
                'node_count' => count($new_nodes)
            ));
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Failed to calculate positions',
                'code' => 'POSITION_CALCULATION_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Handle bulk creation statistics request
     */
    public function handle_get_bulk_stats() {
        if (!$this->validate_ajax_request('slmm_get_bulk_creation_stats', false)) {
            return;
        }
        
        try {
            // Check if user has admin capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array(
                    'message' => 'Insufficient permissions',
                    'code' => 'INSUFFICIENT_PERMISSIONS'
                ));
                return;
            }
            
            $bulk_creator = SLMM_Bulk_Page_Creator::get_instance();
            $stats = $bulk_creator->get_bulk_creation_stats();
            
            wp_send_json_success($stats);
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'message' => 'Failed to retrieve statistics',
                'code' => 'STATS_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Handle health check request
     */
    public function handle_health_check() {
        if (!$this->validate_ajax_request('slmm_quickbulk_health_check', false)) {
            return;
        }
        
        try {
            $health_data = array(
                'status' => 'healthy',
                'timestamp' => current_time('c'),
                'php_version' => PHP_VERSION,
                'wordpress_version' => get_bloginfo('version'),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
                'bulk_creator_available' => class_exists('SLMM_Bulk_Page_Creator'),
                'interlinking_suite_available' => class_exists('SLMM_Interlinking_Suite')
            );
            
            wp_send_json_success($health_data);
            
        } catch (Exception $e) {
            wp_send_json_error(array(
                'status' => 'unhealthy',
                'message' => 'Health check failed',
                'code' => 'HEALTH_CHECK_ERROR',
                'debug_info' => WP_DEBUG ? $e->getMessage() : null
            ));
        }
    }
    
    /**
     * Validate AJAX request with comprehensive security checks
     * 
     * @param string $action AJAX action name
     * @param bool $strict_rate_limiting Whether to apply strict rate limiting
     * @return bool True if valid
     */
    private function validate_ajax_request($action, $strict_rate_limiting = true) {
        // Check nonce
        $nonce_action = str_replace('slmm_', 'slmm_interlinking_', $action);
        if (!wp_verify_nonce($_POST['nonce'] ?? '', $nonce_action) && 
            !wp_verify_nonce($_POST['nonce'] ?? '', 'slmm_interlinking_nonce')) {
            wp_send_json_error(array(
                'message' => 'Security check failed',
                'code' => 'NONCE_VERIFICATION_FAILED'
            ));
            return false;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_pages')) {
            wp_send_json_error(array(
                'message' => 'Insufficient permissions',
                'code' => 'INSUFFICIENT_PERMISSIONS'
            ));
            return false;
        }
        
        // Check SLMM authorization if enabled
        if (function_exists('slmm_seo_check_visibility_authorization') && 
            !slmm_seo_check_visibility_authorization()) {
            wp_send_json_error(array(
                'message' => 'Access denied by visibility controls',
                'code' => 'VISIBILITY_DENIED'
            ));
            return false;
        }
        
        // Apply rate limiting for creation requests
        if ($strict_rate_limiting && !$this->check_rate_limit()) {
            wp_send_json_error(array(
                'message' => 'Rate limit exceeded. Please wait before creating more pages.',
                'code' => 'RATE_LIMIT_EXCEEDED'
            ));
            return false;
        }
        
        return true;
    }
    
    /**
     * Check rate limiting for current user
     * 
     * @return bool True if within limits
     */
    private function check_rate_limit() {
        $user_id = get_current_user_id();
        $cache_key = $this->rate_limit_prefix . $user_id;
        
        $requests = get_transient($cache_key);
        if ($requests === false) {
            $requests = array();
        }
        
        $current_time = time();
        $window_start = $current_time - $this->rate_limit_window;
        
        // Remove old requests outside the time window
        $requests = array_filter($requests, function($timestamp) use ($window_start) {
            return $timestamp > $window_start;
        });
        
        // Check if limit exceeded
        if (count($requests) >= $this->max_requests_per_window) {
            return false;
        }
        
        // Add current request
        $requests[] = $current_time;
        
        // Store updated requests
        set_transient($cache_key, $requests, $this->rate_limit_window);
        
        return true;
    }
    
    /**
     * Extract and sanitize request data
     * 
     * @return array Sanitized request data
     */
    private function extract_request_data() {
        // CRITICAL FIX: Prioritize target_post_type over post_type to handle parent type vs target type
        $post_type = sanitize_key($_POST['target_post_type'] ?? $_POST['post_type'] ?? 'page');

        error_log('SLMM QuickBulk: Request data extraction:');
        error_log('SLMM QuickBulk: $_POST[target_post_type]: ' . ($_POST['target_post_type'] ?? 'not set'));
        error_log('SLMM QuickBulk: $_POST[post_type]: ' . ($_POST['post_type'] ?? 'not set'));
        error_log('SLMM QuickBulk: Final post_type chosen: ' . $post_type);
        error_log('SLMM QuickBulk: Categories received: ' . ($_POST['categories'] ?? 'not set'));

        // Extract and validate categories
        $categories = array();
        if (!empty($_POST['categories'])) {
            $categories_json = stripslashes($_POST['categories']);
            $categories_data = json_decode($categories_json, true);
            if (is_array($categories_data)) {
                $categories = array_map('absint', $categories_data);
                $categories = array_filter($categories); // Remove any zero values
            }
        }

        // Get parent ID and resolve correct parent based on context
        $parent_id = absint($_POST['parent_id'] ?? 0);
        $parent_title = sanitize_text_field($_POST['parent_title'] ?? '');
        $original_parent_id = $parent_id;

        error_log("SLMM QuickBulk: Original parent_id = $parent_id, post_type = $post_type");

        // Check if we need to resolve the parent based on post type context
        $resolved_parent = $this->resolve_correct_parent($post_type, $parent_id, $parent_title);
        if ($resolved_parent) {
            $parent_id = $resolved_parent['id'];
            $parent_title = $resolved_parent['title'];
            error_log("SLMM QuickBulk: Resolved parent_id = $parent_id (was $original_parent_id), title = '$parent_title'");
        }

        return array(
            'parent_id' => $parent_id,
            'parent_title' => $parent_title,
            'post_type' => $post_type, // Use prioritized post type
            'page_titles' => sanitize_textarea_field($_POST['page_titles'] ?? ''),
            'page_slugs' => sanitize_textarea_field($_POST['page_slugs'] ?? ''),
            'categories' => $categories, // Add categories array
            'auto_link' => !empty($_POST['auto_link']),
            'page_status' => sanitize_text_field($_POST['page_status'] ?? 'draft'),
            'content_template' => wp_kses_post($_POST['content_template'] ?? ''),
            'batch_id' => sanitize_key($_POST['batch_id'] ?? uniqid('quickbulk_'))
        );
    }

    /**
     * Resolve the correct parent based on post type context and current parent
     * This handles cases where the clicked parent should be overridden based on post type logic
     *
     * @param string $post_type The target post type
     * @param int $current_parent_id The currently selected parent ID
     * @param string $current_parent_title The currently selected parent title
     * @return array|null Array with 'id' and 'title' if parent should be changed, null otherwise
     */
    private function resolve_correct_parent($post_type, $current_parent_id, $current_parent_title) {
        // If no parent specified, use default resolution
        if ($current_parent_id === 0) {
            return $this->resolve_default_parent($post_type);
        }

        // Get post type object to check capabilities
        $post_type_obj = get_post_type_object($post_type);
        if (!$post_type_obj) {
            return null; // Invalid post type, keep current parent
        }

        // Check if current parent is a post and get its post type
        $current_parent = get_post($current_parent_id);
        if (!$current_parent) {
            // Parent doesn't exist, resolve default parent
            return $this->resolve_default_parent($post_type);
        }

        $parent_post_type = $current_parent->post_type;
        error_log("SLMM QuickBulk: Current parent post_type = $parent_post_type, target post_type = $post_type");

        // CASE 1: Creating posts - should go under blog page, not under individual CPT items
        if ($post_type === 'post' && $parent_post_type !== 'page') {
            error_log("SLMM QuickBulk: Creating posts but clicked on $parent_post_type, redirecting to blog parent");
            return $this->resolve_default_parent($post_type);
        }

        // CASE 2: Creating pages - can go under any page or at root level
        if ($post_type === 'page') {
            if ($parent_post_type === 'page') {
                return null; // Keep current page parent
            } else {
                // Clicked on non-page, place at root level
                return array('id' => 0, 'title' => '');
            }
        }

        // CASE 3: Creating CPT items
        if ($post_type !== 'page' && $post_type !== 'post') {
            // Check if the CPT is hierarchical
            if ($post_type_obj->hierarchical) {
                // HIERARCHICAL CPTs (like pages)
                if ($parent_post_type === $post_type) {
                    // Creating CPT under another CPT of same type - this is valid hierarchy
                    return null; // Keep current parent
                } else if ($parent_post_type === 'page') {
                    // Creating CPT under a page - this is valid
                    return null; // Keep current parent
                } else {
                    // Creating CPT under wrong type, find appropriate parent
                    error_log("SLMM QuickBulk: Hierarchical CPT '$post_type' under wrong parent type '$parent_post_type', finding archive parent");
                    return $this->resolve_default_parent($post_type);
                }
            } else {
                // NON-HIERARCHICAL CPTs (like posts)
                // These should always go under their archive page, not under individual items
                if ($parent_post_type === $post_type || $parent_post_type !== 'page') {
                    error_log("SLMM QuickBulk: Non-hierarchical CPT '$post_type' needs archive parent, not individual item");
                    return $this->resolve_default_parent($post_type);
                } else {
                    // Under a page - might be valid archive parent
                    return null; // Keep current parent
                }
            }
        }

        return null; // Keep current parent by default
    }

    /**
     * Resolve the default parent based on post type when no parent is specified
     * Handles hierarchical vs non-hierarchical post types differently
     *
     * @param string $post_type The target post type
     * @return array Array with 'id' and 'title' of the resolved parent
     */
    private function resolve_default_parent($post_type) {
        // Get post type object to check capabilities
        $post_type_obj = get_post_type_object($post_type);

        // For posts, use the blog page from Reading Settings
        if ($post_type === 'post') {
            $blog_page_id = get_option('page_for_posts');
            if ($blog_page_id) {
                $blog_page = get_post($blog_page_id);
                if ($blog_page && $blog_page->post_status !== 'trash') {
                    return array(
                        'id' => (int) $blog_page_id,
                        'title' => $blog_page->post_title
                    );
                }
            }

            // If no blog page set, try to find a page with slug 'blog'
            $blog_page = get_page_by_path('blog');
            if ($blog_page) {
                return array(
                    'id' => $blog_page->ID,
                    'title' => $blog_page->post_title
                );
            }
        }

        // For all hierarchical post types (pages and hierarchical CPTs), no default parent (they can be hierarchical at any level)
        $post_type_obj = get_post_type_object($post_type);
        if ($post_type_obj && $post_type_obj->hierarchical) {
            error_log("SLMM: '$post_type' is hierarchical - allowing root level placement");
            return array(
                'id' => 0,
                'title' => ''
            );
        }

        // For Custom Post Types - handle non-hierarchical CPTs
        if ($post_type_obj) {

            // NON-HIERARCHICAL CPTs (like posts) - find appropriate archive/parent page
            error_log("SLMM: CPT '$post_type' is non-hierarchical - finding archive parent");

            // Try to find a page that matches the CPT slug
            $cpt_page = get_page_by_path($post_type);
            if ($cpt_page) {
                return array(
                    'id' => $cpt_page->ID,
                    'title' => $cpt_page->post_title
                );
            }

            // For CPTs with archive, try common patterns like "glossary" for "glossary_item"
            if ($post_type_obj->has_archive) {
                $archive_slug = $post_type_obj->rewrite['slug'] ?? $post_type;
                $archive_page = get_page_by_path($archive_slug);
                if ($archive_page) {
                    return array(
                        'id' => $archive_page->ID,
                        'title' => $archive_page->post_title
                    );
                }
            }

            // Try alternate patterns based on post type name
            $possible_slugs = array(
                $post_type,
                str_replace('_', '-', $post_type),
                preg_replace('/_item$/', '', $post_type), // glossary_item -> glossary
                preg_replace('/_post$/', '', $post_type)  // service_post -> service
            );

            foreach ($possible_slugs as $slug) {
                $parent_page = get_page_by_path($slug);
                if ($parent_page) {
                    return array(
                        'id' => $parent_page->ID,
                        'title' => $parent_page->post_title
                    );
                }
            }
        }

        // Default fallback: no parent (root level)
        return array(
            'id' => 0,
            'title' => ''
        );
    }

    /**
     * Validate creation request data
     *
     * @param array $data Request data
     * @return array Validation result
     */
    private function validate_creation_request($data) {
        if (empty($data['page_titles'])) {
            return array('valid' => false, 'message' => 'No page titles provided');
        }
        
        if (strlen($data['page_titles']) > 10000) {
            return array('valid' => false, 'message' => 'Input too large. Maximum 10KB allowed.');
        }
        
        if (!in_array($data['page_status'], array('draft', 'publish', 'private'))) {
            return array('valid' => false, 'message' => 'Invalid page status');
        }
        
        // Count number of pages
        $lines = array_filter(explode("\n", $data['page_titles']), 'trim');
        if (count($lines) > 100) {
            return array('valid' => false, 'message' => 'Maximum 100 pages per batch');
        }
        
        if (count($lines) === 0) {
            return array('valid' => false, 'message' => 'No valid page titles found');
        }
        
        return array('valid' => true);
    }
    
    /**
     * Parse page titles into structured data with optional custom slugs
     * 
     * @param string $titles_text Raw titles text from textarea
     * @param string $slugs_text Optional custom slugs text from textarea
     * @return array Parsed page data with titles and slugs properly matched
     */
    private function parse_page_titles($titles_text, $slugs_text = '') {
        $title_lines = explode("\n", $titles_text);
        $slug_lines = !empty($slugs_text) ? explode("\n", $slugs_text) : array();
        
        $formatted_data = array();
        
        foreach ($title_lines as $index => $title_line) {
            $title = trim($title_line);
            
            // Skip empty titles
            if (empty($title)) {
                continue;
            }
            
            // Get corresponding slug if provided, otherwise auto-generate
            $custom_slug = isset($slug_lines[$index]) ? trim($slug_lines[$index]) : '';
            
            if (!empty($custom_slug)) {
                // CRITICAL: Preserve slashes for hierarchy detection
                // Only sanitize individual segments, not the full path
                $slug = $custom_slug; // Keep raw slug with slashes for now
            } else {
                // Auto-generate slug from title
                $slug = sanitize_title($title);
            }
            
            $formatted_data[] = array(
                'title' => $title,
                'slug' => $slug,
                'line_number' => $index + 1,
                // Additional metadata for debugging/logging
                'input_type_detected' => !empty($custom_slug) ? 'custom_slug' : 'auto_generated',
                'original_input' => $title,
                'custom_slug_provided' => !empty($custom_slug)
            );
        }
        
        // Analyze for hierarchical slug patterns and enhance data if found
        $formatted_data = $this->enhance_with_hierarchy_analysis($formatted_data);

        return $formatted_data;
    }

    /**
     * Enhance page data with hierarchy analysis if slash-separated slugs are detected
     *
     * @param array $page_data Array of page data from parse_page_titles
     * @return array Enhanced page data with hierarchy information
     */
    private function enhance_with_hierarchy_analysis($page_data) {
        // Check if any slugs contain hierarchy patterns (forward slashes)
        $has_hierarchical_slugs = false;
        foreach ($page_data as $page) {
            if (strpos($page['slug'], '/') !== false) {
                $has_hierarchical_slugs = true;
                break;
            }
        }

        // If no hierarchical slugs found, still sanitize but return as flat structure
        if (!$has_hierarchical_slugs) {
            // Sanitize all slugs even if not hierarchical
            foreach ($page_data as $index => $page) {
                $page_data[$index]['slug'] = sanitize_title($page['slug']);
            }
            return $page_data;
        }

        error_log("SLMM Hierarchy: Detected hierarchical slug patterns, analyzing structure");

        // Analyze slug hierarchy structure
        $hierarchy_analysis = $this->analyze_slug_hierarchy($page_data);

        // Enhance each page with hierarchy metadata
        foreach ($page_data as $index => $page) {
            $sanitized_slug = $this->sanitize_hierarchical_slug($page['slug']);
            $depth = substr_count($sanitized_slug, '/');
            $parent_slug = $this->extract_parent_slug($sanitized_slug);

            $page_data[$index]['hierarchy'] = array(
                'sanitized_slug' => $sanitized_slug,
                'depth' => $depth,
                'parent_slug' => $parent_slug,
                'level' => $depth, // 0 = root, 1 = child, 2 = grandchild, etc.
                'is_hierarchical' => true
            );

            // Also update the main slug to be properly sanitized (for non-hierarchical pages)
            $page_data[$index]['slug'] = $sanitized_slug;

            error_log("SLMM Hierarchy: Page '{$page['title']}' - depth: $depth, parent: '$parent_slug', sanitized: '$sanitized_slug'");
            error_log("SLMM Hierarchy: DEBUG - Raw slug was: '{$page['slug']}' → sanitized: '$sanitized_slug'");
        }

        // Sort by hierarchy depth for dependency-ordered creation
        usort($page_data, function($a, $b) {
            $depth_a = isset($a['hierarchy']) ? $a['hierarchy']['depth'] : 0;
            $depth_b = isset($b['hierarchy']) ? $b['hierarchy']['depth'] : 0;
            return $depth_a - $depth_b; // Ascending order: 0, 1, 2, ...
        });

        error_log("SLMM Hierarchy: Sorted " . count($page_data) . " pages by hierarchy depth for ordered creation");

        // DEBUG: Log the final hierarchy structure being passed to bulk creator
        foreach($page_data as $idx => $page) {
            error_log("SLMM Hierarchy: FINAL[$idx] '{$page['title']}' → slug:'{$page['slug']}', parent:'{$page['hierarchy']['parent_slug']}', level:{$page['hierarchy']['level']}");
        }

        return $page_data;
    }

    /**
     * Analyze slug hierarchy structure and build dependency mapping
     *
     * @param array $page_data Array of page data
     * @return array Hierarchy analysis with levels and dependencies
     */
    private function analyze_slug_hierarchy($page_data) {
        $levels = array();
        $dependencies = array();
        $max_depth = 0;

        foreach ($page_data as $page) {
            $sanitized_slug = $this->sanitize_hierarchical_slug($page['slug']);
            $depth = substr_count($sanitized_slug, '/');
            $max_depth = max($max_depth, $depth);

            // Group by depth level
            if (!isset($levels[$depth])) {
                $levels[$depth] = array();
            }
            $levels[$depth][] = $sanitized_slug;

            // Build dependency mapping (child => parent)
            if ($depth > 0) {
                $parent_slug = $this->extract_parent_slug($sanitized_slug);
                $dependencies[$sanitized_slug] = $parent_slug;
            }
        }

        return array(
            'levels' => $levels,
            'dependencies' => $dependencies,
            'max_depth' => $max_depth
        );
    }

    /**
     * Sanitize hierarchical slug by cleaning slashes and encoding
     *
     * @param string $slug Raw slug input
     * @return string Sanitized hierarchical slug
     */
    private function sanitize_hierarchical_slug($slug) {
        // Handle URL encoding
        $slug = urldecode($slug);

        // Strip leading and trailing slashes
        $slug = trim($slug, '/');

        // Split into segments and sanitize each
        $segments = explode('/', $slug);
        $clean_segments = array();

        foreach ($segments as $segment) {
            $clean_segment = trim($segment);
            if (!empty($clean_segment)) {
                // WordPress slug sanitization for each segment
                $clean_segments[] = sanitize_title($clean_segment);
            }
        }

        $result = implode('/', $clean_segments);
        error_log("SLMM Hierarchy: Sanitized '$slug' → '$result' (preserved " . substr_count($result, '/') . " slashes)");
        return $result;
    }

    /**
     * Extract parent slug from hierarchical slug
     *
     * @param string $slug Hierarchical slug (e.g., 'diamond/care/repair')
     * @return string Parent slug (e.g., 'diamond/care') or empty string if no parent
     */
    private function extract_parent_slug($slug) {
        $segments = explode('/', $slug);

        if (count($segments) <= 1) {
            return ''; // No parent (root level)
        }

        // Remove last segment to get parent path
        array_pop($segments);
        return implode('/', $segments);
    }

    /**
     * Prepare creation options from request data
     * 
     * @param array $data Request data
     * @return array Creation options
     */
    private function prepare_creation_options($data) {
        // CRITICAL DEBUG: Log what post_type we received
        error_log("SLMM AJAX Handler: prepare_creation_options received post_type: " . ($data['post_type'] ?? 'NOT SET'));
        error_log("SLMM AJAX Handler: Full request data: " . print_r($data, true));

        $options = array(
            'status' => $data['page_status'],
            'author_id' => get_current_user_id(),
            'post_type' => $data['post_type'], // Pass post type to creator
            'parent_id' => $data['parent_id'], // Use frontend parent_id directly
            'parent_title' => $data['parent_title'],
            'categories' => $data['categories'], // Pass selected categories
            'auto_link' => $data['auto_link'],
            'content_template' => $data['content_template'],
            'batch_id' => $data['batch_id'],
            'register_with_interlinking' => true
        );

        error_log("SLMM AJAX Handler: Final options prepared with post_type: " . ($options['post_type'] ?? 'NOT SET'));
        error_log("SLMM AJAX Handler: Full options: " . print_r($options, true));

        return $options;
    }

    
    /**
     * Format creation response for frontend
     * 
     * @param array $results Bulk creation results
     * @return array Formatted response
     */
    private function format_creation_response($results) {
        return array(
            'success_count' => $results['success_count'],
            'error_count' => $results['error_count'],
            'total_processed' => $results['total_processed'],
            'processing_time' => $results['processing_time'],
            'memory_used' => $results['memory_used'],
            'created' => $results['created'],
            'failed' => $results['failed'],
            'warnings' => $results['warnings'],
            'batch_completed_at' => current_time('c')
        );
    }
    
    /**
     * Generate AI-powered suggestions for page titles
     * 
     * @param string $parent_title Parent page title
     * @param string $keywords Additional keywords
     * @param int $count Number of suggestions
     * @param string $style Suggestion style
     * @return array Generated suggestions
     */
    private function generate_ai_suggestions($parent_title, $keywords, $count, $style) {
        // For now, use template-based suggestions until AI integration is available
        $suggestions = $this->generate_template_suggestions($parent_title, $keywords, $count, $style);
        
        // TODO: Integrate with actual AI service (OpenAI, Anthropic, etc.)
        // $suggestions = $this->generate_ai_powered_suggestions($parent_title, $keywords, $count, $style);
        
        return $suggestions;
    }
    
    /**
     * Generate template-based suggestions
     * 
     * @param string $parent_title Parent page title
     * @param string $keywords Additional keywords
     * @param int $count Number of suggestions
     * @param string $style Suggestion style
     * @return array Template suggestions
     */
    private function generate_template_suggestions($parent_title, $keywords, $count, $style) {
        $title_lower = strtolower($parent_title);
        $keyword_array = array_filter(array_map('trim', explode(',', $keywords)));
        
        // Category-based templates
        $templates = array(
            'guide' => array(
                '{TITLE} for Beginners',
                'Complete {TITLE} Guide',
                'Advanced {TITLE} Techniques',
                '{TITLE} Best Practices',
                'Common {TITLE} Mistakes to Avoid',
                '{TITLE} Tools and Resources',
                '{TITLE} Step-by-Step Tutorial',
                'How to Master {TITLE}',
                '{TITLE} Tips and Tricks',
                '{TITLE} Case Studies'
            ),
            'seo' => array(
                'Keyword Research for {TITLE}',
                '{TITLE} On-Page Optimization',
                'Technical SEO for {TITLE}',
                '{TITLE} Link Building Strategies',
                '{TITLE} Content Optimization',
                '{TITLE} Local SEO Guide',
                '{TITLE} Mobile Optimization',
                '{TITLE} Site Speed Optimization',
                '{TITLE} Schema Markup Guide',
                '{TITLE} Analytics and Tracking'
            ),
            'marketing' => array(
                '{TITLE} Marketing Strategy',
                '{TITLE} Content Marketing',
                '{TITLE} Social Media Marketing',
                '{TITLE} Email Marketing Campaigns',
                '{TITLE} PPC Advertising',
                '{TITLE} Conversion Optimization',
                '{TITLE} Brand Building',
                '{TITLE} Customer Acquisition',
                '{TITLE} Marketing Analytics',
                '{TITLE} Marketing Automation'
            ),
            'business' => array(
                '{TITLE} Business Plan',
                '{TITLE} Revenue Strategies',
                '{TITLE} Cost Optimization',
                '{TITLE} Team Management',
                '{TITLE} Process Improvement',
                '{TITLE} Customer Service',
                '{TITLE} Sales Strategies',
                '{TITLE} Market Analysis',
                '{TITLE} Competitive Analysis',
                '{TITLE} Growth Strategies'
            ),
            'tutorial' => array(
                'Getting Started with {TITLE}',
                '{TITLE} Step-by-Step Instructions',
                '{TITLE} Advanced Techniques',
                '{TITLE} Troubleshooting Guide',
                '{TITLE} Examples and Templates',
                '{TITLE} Video Tutorial Series',
                '{TITLE} Hands-on Workshop',
                '{TITLE} Quick Reference Guide',
                '{TITLE} Common Issues and Solutions',
                '{TITLE} Pro Tips and Secrets'
            )
        );
        
        // Determine category based on parent title
        $category = 'guide'; // Default
        foreach (array_keys($templates) as $cat) {
            if (strpos($title_lower, $cat) !== false) {
                $category = $cat;
                break;
            }
        }
        
        // Get templates for selected category
        $category_templates = $templates[$category];
        
        // If keywords provided, add keyword-specific suggestions
        if (!empty($keyword_array)) {
            foreach ($keyword_array as $keyword) {
                $category_templates[] = ucwords($keyword) . ' and {TITLE}';
                $category_templates[] = '{TITLE}: ' . ucwords($keyword) . ' Focus';
                $category_templates[] = 'How ' . ucwords($keyword) . ' Impacts {TITLE}';
            }
        }
        
        // Shuffle and select requested count
        shuffle($category_templates);
        $selected_templates = array_slice($category_templates, 0, $count);
        
        // Replace placeholders
        $suggestions = array();
        foreach ($selected_templates as $template) {
            $suggestion = str_replace('{TITLE}', $parent_title, $template);
            $suggestions[] = $suggestion;
        }
        
        return $suggestions;
    }
    
    /**
     * Validate page titles input and provide feedback
     * 
     * @param string $titles_text Input text
     * @return array Validation results
     */
    private function validate_page_titles_input($titles_text) {
        $lines = explode("\n", $titles_text);
        $results = array(
            'total_lines' => count($lines),
            'valid_titles' => array(),
            'invalid_titles' => array(),
            'warnings' => array(),
            'statistics' => array()
        );
        
        foreach ($lines as $line_number => $line) {
            $title = trim($line);
            
            if (empty($title)) {
                continue;
            }
            
            $validation = $this->validate_single_title($title, $line_number + 1);
            
            if ($validation['valid']) {
                $results['valid_titles'][] = $validation;
            } else {
                $results['invalid_titles'][] = $validation;
            }
        }
        
        // Generate statistics
        $results['statistics'] = array(
            'valid_count' => count($results['valid_titles']),
            'invalid_count' => count($results['invalid_titles']),
            'success_rate' => count($results['valid_titles']) > 0 ? 
                            (count($results['valid_titles']) / (count($results['valid_titles']) + count($results['invalid_titles']))) * 100 : 0
        );
        
        // Generate warnings
        if (count($results['valid_titles']) > 100) {
            $results['warnings'][] = 'Maximum 100 pages per batch. Extra titles will be ignored.';
        }
        
        if ($results['statistics']['success_rate'] < 80) {
            $results['warnings'][] = 'Low success rate. Check for formatting issues or duplicate titles.';
        }
        
        return $results;
    }
    
    /**
     * Validate a single page title
     * 
     * @param string $title Page title
     * @param int $line_number Line number
     * @return array Validation result
     */
    private function validate_single_title($title, $line_number) {
        $result = array(
            'title' => $title,
            'line_number' => $line_number,
            'valid' => true,
            'issues' => array(),
            'slug' => sanitize_title($title)
        );
        
        // Check title length
        if (strlen($title) < 3) {
            $result['valid'] = false;
            $result['issues'][] = 'Title too short (minimum 3 characters)';
        }
        
        if (strlen($title) > 255) {
            $result['valid'] = false;
            $result['issues'][] = 'Title too long (maximum 255 characters)';
        }
        
        // Check for invalid characters
        if (preg_match('/[<>"\']/', $title)) {
            $result['issues'][] = 'Contains invalid characters (HTML tags or quotes)';
        }
        
        // Check if page already exists (use post type from request)
        $post_type = sanitize_key($_POST['post_type'] ?? 'page');
        if (get_page_by_title($title, OBJECT, $post_type)) {
            $result['valid'] = false;
            $result['issues'][] = ucfirst($post_type) . ' with this title already exists';
        }
        
        // Check for common non-title patterns
        if (preg_match('/^[\d\.\-\*\#]+$/', trim($title))) {
            $result['valid'] = false;
            $result['issues'][] = 'Appears to be a list marker, not a title';
        }
        
        return $result;
    }
    
    /**
     * Calculate optimal positions for new nodes in D3.js tree
     * 
     * @param array $new_nodes New node data
     * @param array $existing_layout Existing layout data
     * @return array Position data
     */
    private function calculate_optimal_positions($new_nodes, $existing_layout) {
        // Simple grid-based positioning for now
        // TODO: Integrate with SLMM_Grid_Generator when available
        
        $positions = array();
        $base_x = 100;
        $base_y = 100;
        $spacing_x = 150;
        $spacing_y = 80;
        
        foreach ($new_nodes as $index => $node) {
            $positions[$node['id']] = array(
                'x' => $base_x + ($index % 5) * $spacing_x,
                'y' => $base_y + floor($index / 5) * $spacing_y,
                'suggested' => true,
                'collision_free' => true
            );
        }
        
        return $positions;
    }
    
    /**
     * Detect if input string is a slug or title format
     * 
     * @param string $input Input string to analyze
     * @return string 'slug' or 'title'
     */
    private function detect_input_type($input) {
        $trimmed = trim($input);
        
        // Contains dashes but no spaces = slug format
        if (strpos($trimmed, '-') !== false && strpos($trimmed, ' ') === false) {
            // Additional validation: check if it looks like a slug
            // Slugs typically contain only lowercase letters, numbers, and dashes
            if (preg_match('/^[a-z0-9-]+$/i', $trimmed)) {
                return 'slug';
            }
        }
        
        // Default to title format
        return 'title';
    }
    
    /**
     * SIMPLE: Convert slug to title (replace dashes with spaces, title case)
     * 
     * @param string $slug Slug to convert (e.g., 'page-name-2')
     * @return string Converted title (e.g., 'Page Name 2')
     */
    private function convert_slug_to_title($slug) {
        // Step 1: Replace dashes with spaces
        $title = str_replace('-', ' ', trim($slug));
        
        // Step 2: Convert to title case (capitalize each word)
        $title = ucwords(strtolower($title));
        
        return $title;
    }
    
    /**
     * Process mixed input with intelligent title/slug detection
     * 
     * @param array $lines Array of input lines
     * @return array Processed page data with both titles and slugs
     */
    private function process_mixed_input($lines) {
        $processed_data = array();
        
        foreach ($lines as $line_number => $line) {
            $input = trim($line);
            
            // Skip empty lines
            if (empty($input)) {
                continue;
            }
            
            // Skip lines that look like headers or separators
            if (preg_match('/^[#\-=\*]+/', $input) || strlen($input) < 3) {
                continue;
            }
            
            // Truncate overly long input
            if (strlen($input) > 255) {
                $input = substr($input, 0, 252) . '...';
            }
            
            // Remove common prefixes like bullet points
            $input = preg_replace('/^[\•\*\-\+]\s*/', '', $input);
            $input = trim($input);
            
            if (empty($input)) {
                continue;
            }
            
            // SIMPLE RULE: If it has dashes and no spaces, it's a slug -> convert to title
            if (strpos($input, '-') !== false && strpos($input, ' ') === false) {
                // Input is a slug - convert to title, keep original slug
                $title = $this->convert_slug_to_title($input);
                $slug = $input; // Keep original slug
                $input_type = 'slug';
            } else {
                // Input is a title - keep title, generate slug
                $title = $input;
                $slug = sanitize_title($input);
                $input_type = 'title';
            }
            
            $processed_data[] = array(
                'title' => $title,
                'slug' => $slug,
                'line_number' => $line_number + 1,
                'input_type' => $input_type,
                'original_input' => $input
            );
        }
        
        return $processed_data;
    }
    
    /**
     * Log bulk operation for monitoring and debugging
     * 
     * @param string $status Operation status
     * @param array|null $results Operation results
     * @param array $request_data Request data
     * @param Exception|null $exception Exception if occurred
     */
    private function log_bulk_operation($status, $results, $request_data, $exception = null) {
        $log_data = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'status' => $status,
            'request_summary' => array(
                'parent_id' => $request_data['parent_id'] ?? null,
                'titles_count' => !empty($request_data['page_titles']) ? 
                                count(array_filter(explode("\n", $request_data['page_titles']), 'trim')) : 0,
                'page_status' => $request_data['page_status'] ?? null,
                'auto_link' => $request_data['auto_link'] ?? null
            )
        );
        
        if ($results) {
            $log_data['results_summary'] = array(
                'success_count' => $results['success_count'],
                'error_count' => $results['error_count'],
                'processing_time' => $results['processing_time'],
                'memory_used' => $results['memory_used']
            );
        }
        
        if ($exception) {
            $log_data['exception'] = array(
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine()
            );
        }
        
        error_log('[SLMM QuickBulk AJAX] ' . wp_json_encode($log_data));
        
        // Store in database for admin review
        $logs = get_option('slmm_quickbulk_ajax_logs', array());
        if (count($logs) >= 100) {
            $logs = array_slice($logs, -99); // Keep last 99 logs
        }
        $logs[] = $log_data;
        update_option('slmm_quickbulk_ajax_logs', $logs, false);
    }
}