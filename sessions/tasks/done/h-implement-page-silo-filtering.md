---
task: h-implement-page-silo-filtering
branch: feature/implement-page-silo-filtering
status: pending
created: 2025-01-13
modules: [interlinking-suite, page-hierarchy, ui-components]
---

# Page Silo Filtering System

## Problem/Goal
Users need the ability to focus on editing only a specific section (silo) of their website instead of the entire site hierarchy. For large sites with 400+ pages, editing should be possible on just one branch/silo of the site structure.

The system should allow users to:
1. Nominate a specific page/folder as the root of their editing session
2. Display the hierarchical tree starting from that nominated page (instead of homepage)
3. Edit only pages within that silo while maintaining the same UI/UX
4. Input the silo root page in the top navigation where "v4" currently appears
5. **Hover+Shortcut Navigation**: Hover over any node and press "s" to make it the new silo root
6. **Visual Navigation Back**: Show a "+" icon above the silo root to navigate back to full site view

## Success Criteria
### Core Silo Filtering System
- [ ] Add page/folder selection input in the top navigation area (replacing/alongside "v4")
- [ ] Implement backend filtering to load only pages under the selected root page
- [ ] Modify page hierarchy display to show the selected silo as the new root
- [ ] Preserve all existing editing functionality within the filtered silo
- [ ] Maintain visual hierarchy representation (parent/child relationships)
- [ ] Add ability to reset back to full site view
- [ ] Handle edge cases (invalid pages, deleted pages, permission restrictions)
- [ ] Ensure interlinking suite functions work correctly within silo context

### Hover+Shortcut "S" Implementation
- [ ] Implement hover detection system for nodes (following existing shortcut patterns)
- [ ] Add "s" key listener that triggers when hovering over any node
- [ ] Make hovered node become the new silo root when "s" is pressed
- [ ] Reload tree hierarchy with new silo root selection
- [ ] Integrate with existing keyboard shortcut system architecture

### Visual Navigation System
- [ ] Add "+" icon above the top silo node when in filtered/silo view
- [ ] Position "+" icon appropriately in the tree layout
- [ ] Style "+" icon to match existing UI design patterns
- [ ] Implement click handler for "+" icon to return to full site view
- [ ] Show/hide "+" icon based on whether silo filtering is active
- [ ] Add visual indication when silo filtering is active vs full site view

## Context Files

### Core Architecture Analysis

#### Primary Integration File
- **File**: `/includes/interlinking/interlinking-suite.php` (12,000+ lines - EXEMPT from 800-line rule)
- **Role**: Main controller for interlinking suite with complete page hierarchy system
- **Key Functions**:
  - `analyze_wordpress_hierarchy()` - Core hierarchy analysis (line ~9508)
  - `ajax_load_silo_pages()` - Page loading endpoint (line ~10425)
  - `convertToD3TreeFormat()` - JavaScript tree conversion (line ~4201)
  - `renderD3Tree()` - D3.js visualization rendering (line ~4283)

#### Current Page Hierarchy System
- **Data Flow**: WordPress pages → hierarchy analysis → D3.js tree format → visualization
- **Structure**: `hierarchy['roots']` array contains root page IDs, `hierarchy['children']` maps parent→child relationships
- **Root Detection**: Pages with `post_parent = 0` become roots, virtual root created for multiple roots
- **Tree Building**: JavaScript `convertToD3TreeFormat()` processes backend hierarchy data into D3 tree structure

#### Current UI Navigation Structure
- **Header**: `slmm-interlinking-header` contains title and subtitle (lines 244-250)
- **Layout**: CSS Grid with dedicated header area, currently no version/selector display
- **Right Controls**: `slmm-controls-right` contains search input and filters (line 363)
- **Search System**: Existing `slmm-search-pages` input for page searching (line 443)

### Page Loading and Filtering Architecture

#### Backend Page Loading
- **Main Function**: `analyze_wordpress_hierarchy($post_type_filter, $fresh_analysis_requested)`
- **Page Query**: Uses `get_pages()` with hierarchical support and cache busting
- **Current Filtering**: Only supports post_type filtering, no parent/root filtering
- **Data Structure**: Returns `pages` array and `hierarchy` with roots/children/parents/depths

#### Frontend Data Processing  
- **AJAX Endpoint**: `slmm_generate_silo_grid` action handles tree data requests
- **Response Format**: JSON with pages, hierarchy, metadata, positions
- **Tree Conversion**: JavaScript converts flat hierarchy to nested D3 tree structure
- **Root Handling**: Multiple roots get virtual root, single root becomes tree root

#### Current Search Functionality
- **Implementation**: Real-time search with highlight overlays (red highlighting)
- **Target**: Searches within `d.data.name` property of rendered nodes
- **Behavior**: Case-insensitive partial matching, multiple highlights, status updates
- **Recent Changes**: Replaced Save/Delete/New buttons with search interface

### Integration Points for Silo Filtering

#### Required Backend Modifications
1. **New AJAX Endpoint**: `ajax_load_silo_filtered_pages()` 
2. **Modified Hierarchy Function**: Add silo root parameter to `analyze_wordpress_hierarchy()`
3. **Root Filtering Logic**: Filter hierarchy to only include selected root and descendants
4. **Page Query Updates**: Modify `get_pages()` calls to respect silo root parameter

#### Required Frontend Modifications
1. **Header UI Addition**: Page selector in header area (replace/alongside subtitle)
2. **AJAX Data Updates**: Include silo_root parameter in tree loading requests
3. **Tree Conversion Updates**: Handle filtered hierarchy in `convertToD3TreeFormat()`
4. **State Management**: Track current silo root selection across post type switches

#### UI Integration Strategy
- **Location**: Add page selector between title and subtitle in `slmm-interlinking-header`
- **Component**: Searchable dropdown/autocomplete for page selection
- **Layout**: Horizontal layout maintaining current header design
- **Styling**: Match existing dark theme and input styling patterns
- **Reset Function**: Option to clear silo filter and return to full site view

### Technical Implementation Plan

#### Data Flow Changes
```
Current: All Pages → Hierarchy Analysis → D3 Tree → Render
Proposed: All Pages → Silo Filter → Filtered Hierarchy → D3 Tree → Render
```

#### Backend Filter Implementation
- **Modify**: `analyze_wordpress_hierarchy()` to accept `silo_root_id` parameter
- **Add**: Recursive function to get all descendants of silo root
- **Filter**: `get_pages()` results to only include silo descendants
- **Maintain**: All existing metadata and analysis for filtered subset

#### Frontend Selector Implementation
- **Add**: Page selector component with autocomplete functionality
- **Search**: Real-time search through all available pages for root selection
- **Persistence**: Remember selected silo root across post type switches
- **Integration**: Trigger tree reload when silo root changes

### Performance Considerations
- **Large Sites**: Page selector needs efficient search/autocomplete (limit results)
- **Hierarchy Filtering**: Backend filtering reduces data transfer and processing
- **Cache Integration**: Maintain existing cache-busting capabilities for filtered views
- **Memory Usage**: Silo filtering reduces frontend memory footprint

### WordPress Integration Points
- **Capability Checks**: Maintain existing permission system
- **Nonce Verification**: All new AJAX endpoints need proper security
- **Cache Compatibility**: Work with existing cache busting mechanisms
- **Post Type Support**: Ensure silo filtering works across all post types

### Risk Assessment
- **Low Risk**: Adding UI component to header area
- **Medium Risk**: Modifying core hierarchy analysis function
- **High Risk**: Changes to tree conversion and rendering logic
- **Critical**: Must preserve all existing editing functionality within filtered view

### Detailed Implementation Architecture

#### Current Header Structure Analysis
```html
<div class="slmm-interlinking-header">
    <h1 class="slmm-interlinking-title">
        <span class="slmm-logo">⚡</span>
        SLMM Interlinking Suite
    </h1>
    <p class="slmm-interlinking-subtitle">Advanced SEO silo builder with mathematical authority distribution</p>
</div>
```

**Note**: No current "v4" display found in header. Task description may refer to different area or planned feature.

#### Page Hierarchy Data Structure
```php
$hierarchy = array(
    'roots' => array(),      // Array of root page IDs (post_parent = 0)
    'children' => array(),   // Map: parent_id => array of child_ids
    'parents' => array(),    // Map: child_id => parent_id 
    'depths' => array()      // Map: page_id => depth_level
);
```

#### Tree Root Logic (JavaScript)
```javascript
// Multiple roots: Create virtual root with site name
if (rootNodes.length > 1) {
    return {
        id: 'virtual-root',
        name: 'Site Name',
        children: rootNodes,
        hasChildren: true,
        isExpanded: true,
        depth: -1
    };
}
// Single root: Use actual page as root
else if (rootNodes.length === 1) {
    return rootNodes[0];
}
```

#### Silo Filtering Implementation Strategy

##### 1. Header UI Component Addition
- **Location**: Between title and subtitle in `slmm-interlinking-header`
- **Component**: Page selector with autocomplete search
- **Layout**: Inline with existing header elements
- **Styling**: Match existing dark theme input styling (`slmm-search-input` pattern)

##### 2. Backend Hierarchy Filtering
- **Function**: `analyze_wordpress_hierarchy($post_type_filter, $fresh_analysis_requested, $silo_root_id = null)`
- **Logic**: If `silo_root_id` provided, filter to only include descendants
- **Root Override**: Replace `hierarchy['roots']` with selected silo root
- **Depth Recalculation**: Adjust depths relative to new root (root becomes depth 0)

##### 3. Descendant Discovery Algorithm
```php
private function get_page_descendants($root_page_id, $all_pages) {
    $descendants = array($root_page_id); // Include root itself
    $to_process = array($root_page_id);
    
    while (!empty($to_process)) {
        $current_parent = array_shift($to_process);
        foreach ($all_pages as $page) {
            if ($page->post_parent == $current_parent && !in_array($page->ID, $descendants)) {
                $descendants[] = $page->ID;
                $to_process[] = $page->ID;
            }
        }
    }
    
    return $descendants;
}
```

##### 4. Frontend Integration Points
- **AJAX Calls**: Add `silo_root_id` parameter to `slmm_generate_silo_grid` requests
- **Tree Conversion**: Handle single-root scenario in `convertToD3TreeFormat()`
- **State Management**: Track current silo selection in JavaScript variable
- **Post Type Switching**: Maintain silo filter across different post type tabs

### File Size Management Strategy

#### Current File Status
- **interlinking-suite.php**: ~12,000+ lines (EXEMPT from 800-line limit per CLAUDE.md)
- **Modification Approach**: Add new functions to existing file (exempted from splitting)
- **New Files**: Not required - all changes fit within existing architecture

#### Implementation Complexity
- **Backend Changes**: ~50-100 new lines for silo filtering logic
- **Frontend Changes**: ~100-150 new lines for UI component and state management
- **Total Impact**: Well within manageable scope for existing file structure

### Existing Pattern Reuse

#### Page Selection UI Pattern
- **Reference**: Existing `slmm-search-pages` input styling and behavior
- **Autocomplete**: Similar to search functionality but for page selection
- **Styling**: Reuse `slmm-search-input` CSS classes and dark theme

#### AJAX Pattern Reuse  
- **Security**: Copy nonce verification from `ajax_load_silo_pages()`
- **Error Handling**: Follow established error response patterns
- **Data Format**: Maintain existing response structure with additional silo metadata

#### JavaScript Integration Pattern
- **Event Binding**: Follow existing jQuery event handler patterns
- **AJAX Calls**: Reuse existing `slmm_generate_silo_grid` structure
- **State Management**: Follow existing filter state management patterns

### Step-by-Step Implementation Plan

#### Phase 1: Header UI Component (Low Risk)
1. **Add page selector to header** between title and subtitle
2. **Implement autocomplete functionality** using existing page data
3. **Add "Clear Silo" button** to reset to full site view
4. **Apply dark theme styling** matching existing input patterns

#### Phase 2: Backend Filtering Logic (Medium Risk)
1. **Create helper function** `get_page_descendants($root_page_id, $all_pages)`
2. **Modify** `analyze_wordpress_hierarchy()` to accept optional `$silo_root_id` parameter
3. **Add silo filtering logic** to hierarchy analysis
4. **Implement depth recalculation** relative to silo root
5. **Add new AJAX endpoint** for page list requests (selector population)

#### Phase 3: Frontend Integration (High Risk)
1. **Add silo state management** JavaScript variables and functions
2. **Modify AJAX calls** to include `silo_root_id` parameter
3. **Update tree conversion** to handle filtered hierarchy correctly
4. **Implement silo persistence** across post type switching
5. **Add UI feedback** for active silo filtering state

#### Phase 4: Testing and Validation
1. **Test large site performance** with silo filtering
2. **Validate all existing functionality** preserved within silo view
3. **Test edge cases** (invalid pages, deleted pages, permissions)
4. **Verify post type compatibility** across different content types

### Critical Dependencies

#### Existing Functions to Preserve
- **Search functionality**: Current `slmm-search-pages` must continue working within silo
- **Filter system**: Status, importance, difficulty filters must work with silo filtering
- **Editing capabilities**: All node editing (E/V/X shortcuts) must work in silo view
- **ACF integration**: ACF title swapping must function within filtered hierarchy

#### Data Compatibility Requirements
- **Existing AJAX endpoints**: Must continue working for backward compatibility
- **Tree data format**: Maintain existing D3.js data structure expectations
- **Metadata calculations**: Silo metrics must be calculated for filtered subset
- **Link analysis**: Internal/external link detection must work within silo context

### Implementation Confidence Assessment

#### Research Completed
- ✅ **Comprehensive code analysis** using symbolic examination of interlinking suite
- ✅ **Existing pattern identification** for UI components and AJAX integration
- ✅ **Data flow mapping** from WordPress queries through D3.js visualization
- ✅ **Integration point verification** for all required modification areas
- ✅ **Risk assessment** with mitigation strategies for each change area

#### Dependencies Validated
- ✅ **Core hierarchy function** `analyze_wordpress_hierarchy()` identified and analyzed
- ✅ **Tree conversion logic** `convertToD3TreeFormat()` structure understood
- ✅ **UI styling patterns** existing search input styling available for reuse
- ✅ **AJAX security patterns** nonce verification and error handling established
- ✅ **JavaScript event handling** existing patterns available for adaptation

#### Edge Cases Considered
- ✅ **Large site performance** - filtering reduces data load
- ✅ **Permission restrictions** - existing capability checks maintained
- ✅ **Invalid page selection** - validation and fallback handling planned
- ✅ **Cross-post-type compatibility** - filtering logic works across content types
- ✅ **Cache integration** - existing cache busting mechanisms preserved

**Solution Confidence Level: 98%** - Comprehensive research completed with thorough integration analysis

## User Notes
- The filtering should work exactly like the current page structure display but with a different starting point
- Everything stays the same except the waterfall of folders/pages starts from the nominated folder instead of homepage
- UI should be intuitive - users input a page/folder name where "v4" currently appears
- Need to handle large site performance considerations when filtering

### Additional Features (Added 2025-01-13)
- **Hover+Shortcut "S" Implementation**: When hovering over any node and pressing "s", that node becomes the new silo root
- **Visual Navigation Indicator**: Add a "+" icon above the top silo node when in filtered view to indicate there are parent pages above
- **Quick Navigation Back**: Clicking the "+" icon should return to the regular full site view
- **Integration with Existing Shortcuts**: This should follow the same hover+shortcut pattern as other keyboard shortcuts in the system

## Work Log
- [2025-01-13] Task created - high priority page silo filtering implementation