<?php
/**
 * Broken Links Detector - Server-side component
 *
 * Provides AJAX functionality for checking if links return valid responses
 * or result in 404/errors
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Class to handle broken link detection functionality
 */
class SLMM_Broken_Links {
    /**
     * Instance of the class
     */
    private static $instance = null;
    
    /**
     * Cache of checked URLs to prevent duplicate requests
     */
    private $url_cache = array();

    /**
     * Get the singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action('wp_ajax_check_link_status', array($this, 'ajax_check_link_status'));
    }

    /**
     * Check if a URL is valid and returns a successful response
     * 
     * @param string $url The URL to check
     * @return bool True if valid, false if broken
     */
    public function is_valid_url($url) {
        // Don't check empty URLs
        if (empty($url)) {
            return false;
        }
        
        // Check cache first
        if (isset($this->url_cache[$url])) {
            return $this->url_cache[$url];
        }

        // Parse URL to make sure it's properly formatted
        $parsed_url = parse_url($url);
        if (false === $parsed_url || empty($parsed_url['scheme']) || empty($parsed_url['host'])) {
            $this->url_cache[$url] = false;
            return false;
        }

        // Skip checking certain URL types
        if (preg_match('/^(javascript|mailto|tel):/', $url)) {
            $this->url_cache[$url] = true;
            return true; // These are not broken, they're just not web links
        }

        // Handle anchor links
        if (strpos($url, '#') === 0) {
            $this->url_cache[$url] = true;
            return true; // Anchor links are considered valid in this context
        }

        // Check for "/protected word" pattern which indicates invalid URLs
        if (preg_match('/^\/protected word/', $url)) {
            $this->url_cache[$url] = false;
            return false;
        }
        
        // Check for whitespace in the URL which is invalid
        if (preg_match('/\s/', $url)) {
            $this->url_cache[$url] = false;
            return false;
        }

        // Use WP_Http to check the URL
        $args = array(
            'timeout' => 5,
            'sslverify' => false,
            'user-agent' => 'Mozilla/5.0 (WordPress; SLMM SEO; Link Checker)',
            'redirection' => 5, // Follow up to 5 redirects
        );
        
        // First try a HEAD request (faster, less bandwidth)
        $response = wp_remote_head($url, $args);

        // If the HEAD request fails or returns 405 Method Not Allowed, try a GET request instead
        if (is_wp_error($response) || 
            wp_remote_retrieve_response_code($response) === 405 || 
            wp_remote_retrieve_response_code($response) === 403) {
            
            // Use GET with minimized data transfer
            $args['headers'] = array('Range' => 'bytes=0-1024'); // Only get the first 1KB
            $args['timeout'] = 10; // Longer timeout for GET requests
            $response = wp_remote_get($url, $args);
        }

        // Check for WP_Error
        if (is_wp_error($response)) {
            $this->url_cache[$url] = false;
            return false;
        }

        // Get response code
        $response_code = wp_remote_retrieve_response_code($response);
        
        // 2xx (success) or 3xx (redirection) status codes are considered valid
        $result = $response_code >= 200 && $response_code < 400;
        
        // Cache the result
        $this->url_cache[$url] = $result;
        
        return $result;
    }

    /**
     * AJAX handler for checking link status
     */
    public function ajax_check_link_status() {
        // Check nonce for security
        check_ajax_referer('slmm_link_check_nonce', 'security');
        
        // Get the URL from the request
        $url = isset($_POST['url']) ? esc_url_raw($_POST['url']) : '';
        
        if (empty($url)) {
            wp_send_json_error('No URL provided');
            return;
        }
        
        // Check if the URL is valid
        $is_valid = $this->is_valid_url($url);
        
        // Send the result
        if ($is_valid) {
            wp_send_json_success(true);
        } else {
            wp_send_json_error('Link is broken');
        }
    }
}

// Initialize the class
SLMM_Broken_Links::get_instance(); 