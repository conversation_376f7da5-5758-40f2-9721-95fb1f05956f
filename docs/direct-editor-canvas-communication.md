# Direct Editor ↔ Canvas Communication Architecture

## Overview

This document outlines the comprehensive communication system between the Direct Editor modal and D3.js canvas nodes in the SLMM SEO Bundle plugin. The system implements a dual-architecture approach with both AJAX-based persistence and direct DOM manipulation for immediate visual feedback.

## Core Architecture Patterns

### 1. Dual-System Communication Architecture

The plugin implements **two distinct but coordinated systems**:

1. **Button System**: DOM-driven interaction via injected buttons
2. **Keyboard Shortcut System**: Data-driven interaction via global functions
3. **Canvas Integration**: D3.js node-based direct editing

### 2. Communication Flow Types

#### Type A: Canvas → Direct Editor (Opening Modal)
```
Canvas Node Click → Global Bridge Function → Direct Editor Modal Opens → Load Post Data
```

#### Type B: Direct Editor → Canvas (Immediate Updates)
```
Modal Field Change → Temporary Storage → Modal Close → Direct DOM Update → Background Save
```

#### Type C: AJAX Persistence (Background)
```
Direct DOM Update → Fire-and-Forget AJAX → Database Save (no UI blocking)
```

## Technical Implementation Details

### Canvas to Direct Editor Bridge

#### Global Function Bridge
**Location**: `assets/js/slmm-direct-editor.js:4142-4149`

```javascript
// CRITICAL: Make Direct Editor globally accessible for canvas node integration
window.openDirectEditor = function(postId) {
    SLMMDirectEditor.openDirectEditor(postId);
};

// Also assign the full object for fallback access
window.SLMMDirectEditor = SLMMDirectEditor;
```

#### Canvas Node Click Handlers
**Location**: `includes/interlinking/interlinking-suite.php:~4990-5020`

```javascript
// Direct Editor button click handler in D3.js tree
nodeUpdateMerge.selectAll('.slmm-node-directedit-button, .slmm-node-directedit-symbol')
    .on('click', function(event, d) {
        event.stopPropagation();
        if (d.data.id && d.data.post_type !== 'site') {
            updateStatusMessage('Opening Direct Editor for: ' + d.data.name);
            
            // Trigger Direct Editor functionality
            if (typeof SLMMDirectEditor !== 'undefined') {
                SLMMDirectEditor.openDirectEditor(d.data.id);
            }
        }
    });
```

#### Button Injection System
**Location**: `assets/js/slmm-direct-editor.js:294-332`

```javascript
// Injects "DE" buttons into existing node structures
injectDirectEditButtons: function() {
    $('.node-buttons').each(function() {
        var $nodeButtons = $(this);
        var $node = $nodeButtons.closest('.node');
        var postId = $node.data('post-id') || $node.attr('data-post-id');
        
        if (postId && !$nodeButtons.find('.node-button-direct-edit').length) {
            var $directEditBtn = $('<button>')
                .addClass('node-button-direct-edit')
                .attr('data-post-id', postId)
                .text('DE')
                .on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    self.openDirectEditor(postId);
                });
            
            $nodeButtons.append($directEditBtn);
        }
    });
}
```

### Direct Editor to Canvas Communication

#### Immediate Update System (Direct Editing)

**Core Pattern**: Store changes temporarily → Apply immediately to DOM → Save in background

**Storage System**: `assets/js/slmm-direct-editor.js:3399-3435`

```javascript
// Pattern used for ALL properties: title, keyword, difficulty, importance
updateTitle: function(newTitle) {
    if (!this.temporaryNodeUpdates) {
        this.temporaryNodeUpdates = {};
    }
    this.temporaryNodeUpdates.post_title = newTitle;
    this.temporaryNodeUpdates.name = newTitle; // D3 nodes use 'name' field for title display
    console.log('[SLMM Dashboard] Title stored temporarily:', newTitle);
}

updateKeyword: function(newKeyword) {
    if (!this.temporaryNodeUpdates) {
        this.temporaryNodeUpdates = {};
    }
    this.temporaryNodeUpdates.target_keyword = newKeyword;
}

updateDifficulty: function(newDifficulty) {
    if (!this.temporaryNodeUpdates) {
        this.temporaryNodeUpdates = {};
    }
    this.temporaryNodeUpdates.difficulty_level = newDifficulty;
}

updateImportance: function(newImportance) {
    if (!this.temporaryNodeUpdates) {
        this.temporaryNodeUpdates = {};
    }
    this.temporaryNodeUpdates.importance_rating = newImportance;
}
```

#### Dashboard Field Event Handlers

**Location**: `assets/js/slmm-direct-editor.js:setupDashboardInteractions` function

```javascript
setupDashboardInteractions: function() {
    var self = this;
    var $modal = this.currentModal;
    
    // Status dropdown change - immediate update
    $modal.find('#slmm-status-select').off('change').on('change', function() {
        var newStatus = $(this).val();
        self.updatePostStatus(newStatus); // Uses AJAX pattern
    });
    
    // Title field change - direct update pattern (300ms debounce)
    var titleTimeout;
    $modal.find('.slmm-regular-title-editable').off('input').on('input', function() {
        if (!$(this).hasClass('slmm-acf-swapped-title') && !$(this).closest('.slmm-acf-editor-container').length) {
            var newTitle = $(this).text() || $(this).val();
            clearTimeout(titleTimeout);
            titleTimeout = setTimeout(function() {
                self.updateTitle(newTitle); // Direct update pattern
            }, 300);
        }
    });
    
    // Keyword input change (1 second debounce)
    var keywordTimeout;
    $modal.find('#slmm-keyword-input').off('input').on('input', function() {
        var newKeyword = $(this).val();
        clearTimeout(keywordTimeout);
        keywordTimeout = setTimeout(function() {
            self.updateKeyword(newKeyword); // Direct update pattern
        }, 1000);
    });
    
    // Difficulty circles - immediate click response
    $modal.find('.slmm-difficulty-circle').off('click').on('click', function() {
        var newDifficulty = $(this).data('difficulty');
        self.setDifficultySelection(newDifficulty);
        self.updateDifficulty(newDifficulty); // Direct update pattern
    });
}
```

#### Modal Close Trigger System

**Location**: `assets/js/slmm-direct-editor.js:1767-1777`

```javascript
forceCloseModal: function() {
    var self = this;
    
    if (this.currentModal) {
        // Apply temporary node updates directly to D3 nodes before closing
        this.applyDirectNodeUpdates();
        
        // Immediate cleanup to prevent stuck states
        this.cleanupCurrentEditor();
    }
}
```

#### Dual-Update Application System

**Location**: `assets/js/slmm-direct-editor.js:3541-3562`

```javascript
applyDirectNodeUpdates: function() {
    if (!this.temporaryNodeUpdates || !this.currentPostId) {
        return; // No updates to apply
    }
    
    console.log('[SLMM Dashboard] Applying dual-update system:', this.temporaryNodeUpdates);
    
    // CRITICAL: Capture postId and updates before resetState() clears them
    var postId = this.currentPostId;
    var updates = this.temporaryNodeUpdates;
    
    // 1. IMMEDIATE VISUAL UPDATE - No delays for responsive UX
    if (typeof window.refreshNodeWithD3Rebind === 'function') {
        window.refreshNodeWithD3Rebind(postId, updates);
        console.log('[SLMM Dashboard] ✅ Immediate visual updates applied');
    } else {
        console.warn('[SLMM Dashboard] ⚠️ Surgical update function not available');
    }
    
    // 2. BACKGROUND DATABASE SAVES - Fire and forget for persistence  
    this.saveUpdatesInBackground(postId, updates);
}
```

#### Background Persistence System

**Location**: `assets/js/slmm-direct-editor.js:3568-3613`

```javascript
saveUpdatesInBackground: function(postId, updates) {
    console.log('[SLMM Dashboard] 💾 Starting background database saves');
    
    // Save difficulty if changed
    if (updates.difficulty_level) {
        this.backgroundSave('slmm_change_difficulty', {
            post_id: postId,
            new_difficulty: updates.difficulty_level
        }, 'Difficulty');
    }
    
    // Save importance if changed  
    if (updates.importance_rating) {
        this.backgroundSave('slmm_change_importance', {
            post_id: postId,
            new_importance: updates.importance_rating
        }, 'Importance');
    }
    
    // Save title if changed - use Direct Editor AJAX endpoint
    if (updates.post_title || updates.name) {
        var newTitle = updates.post_title || updates.name;
        console.log('[SLMM Dashboard] 📤 Background saving Title:', newTitle);
        
        // Use Direct Editor AJAX endpoint for title updates
        $.ajax({
            url: slmmDirectEditorData.ajax_url,
            type: 'POST',
            data: {
                action: 'slmm_update_post_title',
                post_id: postId,
                new_title: newTitle,
                nonce: slmmDirectEditorData.nonce
            },
            success: function(response) {
                if (response.success) {
                    console.log('[SLMM Dashboard] ✅ Title background save successful');
                } else {
                    console.warn('[SLMM Dashboard] ⚠️ Title background save failed:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('[SLMM Dashboard] ❌ Title background save error:', error);
            }
        });
    }
    
    // Save keyword if changed (including empty string)
    if (updates.hasOwnProperty('target_keyword')) {
        this.backgroundSave('slmm_change_keyword', {
            post_id: postId,
            new_keyword: updates.target_keyword
        }, 'Keyword');
    }
}
```

### Canvas Surgical Update System

#### D3.js Surgical Refresh Function

**Location**: `includes/interlinking/interlinking-suite.php:8810-9100+`

This is the **critical function** that provides immediate visual feedback:

```javascript
window.refreshNodeWithD3Rebind = function(postId, freshNodeData, savedContent) {
    console.log('🔧 SLMM: refreshNodeWithD3Rebind called for post:', postId);
    
    if (!treeGroup || !postId) {
        console.warn('🔧 SLMM: Invalid parameters for surgical node refresh');
        return false;
    }
    
    // Find the specific node in the D3 tree
    let targetNode = null;
    let targetElement = null;
    
    treeGroup.selectAll('.slmm-tree-node').each(function(d) {
        if (d && d.data && String(d.data.id) === String(postId)) {
            targetNode = d;
            targetElement = this;
            console.log('🎯 SLMM: Found target node for update:', d.data.name);
        }
    });
    
    if (!targetNode || !targetElement) {
        console.warn('🔧 SLMM: Target node not found for post ID:', postId);
        return false;
    }
    
    // CRITICAL: Mark this node as surgically updated to prevent backend override
    window.surgicallyUpdatedNodes.add(String(postId));
    
    // Update node data with fresh backend data
    if (freshNodeData) {
        // Update title/name data (CRITICAL - respects ACF vs regular titles!)
        if (freshNodeData.name !== undefined) {
            const titleElement = d3.select(targetElement).select('.slmm-node-title');
            const isACFTitle = !titleElement.empty() && titleElement.attr('data-acf-title') === 'true';
            
            if (!isACFTitle) {
                targetNode.data.name = freshNodeData.name;
                console.log('✅ SLMM: Updated title/name data to:', freshNodeData.name);
            } else {
                console.log('✅ SLMM: Skipping name update - preserving ACF title data');
            }
        }
        
        // Handle ACF-specific title data if present
        if (freshNodeData.display_title !== undefined || freshNodeData.acf_field_name !== undefined) {
            const titleElement = d3.select(targetElement).select('.slmm-node-title');
            const isACFTitle = !titleElement.empty() && titleElement.attr('data-acf-title') === 'true';
            
            if (isACFTitle && freshNodeData.display_title !== undefined) {
                targetNode.data.display_title = freshNodeData.display_title;
                targetNode.data.name = freshNodeData.display_title; // ACF titles use display_title for display
                console.log('✅ SLMM: Updated ACF display_title data to:', freshNodeData.display_title);
            }
        }
        
        // Update status data
        if (freshNodeData.post_status !== undefined) {
            targetNode.data.post_status = freshNodeData.post_status;
            targetNode.data.status_display = freshNodeData.status_display || freshNodeData.post_status;
        }
        
        // Update difficulty data
        if (freshNodeData.difficulty_level !== undefined) {
            targetNode.data.difficulty_level = freshNodeData.difficulty_level;
        }
        
        // Update importance data
        if (freshNodeData.importance_rating !== undefined) {
            targetNode.data.importance_rating = freshNodeData.importance_rating;
        }
        
        // Update keyword data
        if (freshNodeData.target_keyword !== undefined) {
            targetNode.data.target_keyword = freshNodeData.target_keyword;
        }
    }
    
    // SURGICAL UPDATE: Update all DOM elements immediately
    
    // Update title text (CRITICAL - respects ACF title mode!)
    d3.select(targetElement).selectAll('.slmm-node-title').each(function() {
        const titleText = d3.select(this);
        const isACFTitle = titleText.attr('data-acf-title') === 'true';
        
        if (isACFTitle) {
            // For ACF titles, only update if we have fresh ACF data
            if (targetNode.data.display_title !== undefined) {
                titleText.text(targetNode.data.display_title);
                console.log('✅ SLMM: Updated ACF title text to:', targetNode.data.display_title);
            } else {
                // Preserve existing ACF field value - don't overwrite with post title
                console.log('✅ SLMM: Preserving existing ACF title display (no fresh ACF data)');
            }
        } else {
            // For regular titles, update with fresh post title
            const title = targetNode.data.name || targetNode.data.post_title || '';
            titleText.text(title);
            console.log('✅ SLMM: Updated regular title text to:', title);
        }
    });
    
    // Update status badge
    d3.select(targetElement).selectAll('.slmm-status-toggle').each(function() {
        const statusBadge = d3.select(this);
        const statusColor = getStatusColor(targetNode.data.post_status);
        statusBadge.attr('fill', statusColor);
    });
    
    // Update difficulty circle
    d3.select(targetElement).selectAll('.slmm-difficulty-dropdown').each(function() {
        const difficultyCircle = d3.select(this);
        const difficulty = targetNode.data.difficulty_level || 'medium';
        const difficultyMap = {
            'easy': { symbol: 'E', color: '#10b981' },
            'medium': { symbol: 'M', color: '#f59e0b' },
            'hard': { symbol: 'H', color: '#ef4444' },
            'very-hard': { symbol: 'VH', color: '#7c2d12' }
        };
        const config = difficultyMap[difficulty] || difficultyMap['medium'];
        
        let newClass = 'slmm-difficulty-dropdown ' + difficulty;
        difficultyCircle.attr('class', newClass).attr('fill', config.color);
    });
    
    // Update importance circle
    d3.select(targetElement).selectAll('.slmm-importance-dropdown').each(function() {
        const importanceCircle = d3.select(this);
        const importance = targetNode.data.importance_rating || '3';
        const importanceColors = {
            '1': '#ef4444', '2': '#f59e0b', '3': '#10b981', '4': '#3b82f6', '5': '#8b5cf6'
        };
        const color = importanceColors[String(importance)] || importanceColors['3'];
        
        let newClass = 'slmm-importance-dropdown level-' + importance;
        importanceCircle.attr('class', newClass).attr('fill', color);
    });
    
    // Update keyword text
    d3.select(targetElement).selectAll('.slmm-node-keyword-text').each(function() {
        const keywordText = d3.select(this);
        const keyword = targetNode.data.target_keyword || '';
        keywordText.text(keyword);
        keywordText.style('fill', keyword ? 'var(--slmm-text-primary)' : 'var(--slmm-text-muted)');
    });
    
    return true;
}
```

## Communication Patterns by Property Type

### Pattern A: Direct Update (Immediate + Background Save)
**Used for**: Title, Keyword, Difficulty, Importance

```
Modal Field Change → debounced event handler → updateProperty() → 
temporaryNodeUpdates → modal close → refreshNodeWithD3Rebind() → 
immediate DOM update + backgroundSave()
```

**Properties**: Changes are visible immediately, database saved in background

### Pattern B: AJAX Update (Immediate Frontend + Backend Response)  
**Used for**: Status, Slug

```
Modal Field Change → makeNodeUpdateRequest() → AJAX call → 
backend processes → returns node_data → refreshNodeWithD3Rebind() → 
immediate DOM update with server data
```

**Properties**: Server response includes full node refresh data

### Pattern C: ACF Integration (Specialized Handling)
**Used for**: ACF Title Fields

```
ACF Title Edit → saveACFTitleChange() → syncCanvasNodeTitle() → 
immediate frontend sync → AJAX to slmm_update_acf_field → 
canvas refresh with ACF data
```

**Properties**: Preserves ACF field values, doesn't overwrite with post titles

## Key Architecture Principles

### 1. Immediate Visual Feedback
- **Never block the UI** waiting for AJAX responses
- **Update DOM immediately** when user makes changes
- **Use temporary storage** to bridge modal → canvas communication

### 2. Background Persistence
- **Fire-and-forget AJAX** for database saves
- **No error blocking** - log failures but don't interrupt UX
- **Batched saves** when modal closes (not per-field)

### 3. State Management
- **temporaryNodeUpdates** object stores all pending changes
- **surgicallyUpdatedNodes** Set prevents backend overrides
- **Proper cleanup** when modal closes or errors occur

### 4. Dual-System Compatibility
- **Button System** for UI interactions
- **Keyboard Shortcuts** for power users  
- **Both systems** use the same backend data flow

### 5. ACF Integration Safeguards
- **Detect ACF titles** via `data-acf-title="true"` attribute
- **Preserve ACF field values** during refreshes
- **Separate ACF vs regular title logic** throughout the system

## Error Handling & Recovery

### Frontend Error Patterns
```javascript
// Pattern for graceful degradation
if (typeof window.refreshNodeWithD3Rebind === 'function') {
    window.refreshNodeWithD3Rebind(postId, updates);
} else {
    // Fallback to basic canvas sync
    console.warn('Surgical update not available, using fallback');
    self.syncCanvasNodeTitle(postId, newTitle);
}
```

### Backend Error Handling
```php
// Pattern for comprehensive error handling
try {
    // Main operation
    $result = wp_update_post($data);
    
    if (is_wp_error($result)) {
        error_log('Operation failed: ' . $result->get_error_message());
        wp_send_json_error('Update failed: ' . $result->get_error_message());
        return;
    }
    
    wp_send_json_success($response_data);
    
} catch (Exception $e) {
    error_log('Exception: ' . $e->getMessage());
    wp_send_json_error('Error: ' . $e->getMessage());
}
```

## Performance Optimizations

### 1. Debouncing Input Events
- **Title**: 300ms debounce for responsive feedback
- **Keyword**: 1000ms debounce to prevent excessive requests
- **Clicks**: Immediate response for buttons/circles

### 2. Batch Operations
- **Modal close**: Apply all changes at once instead of per-field
- **Background saves**: Multiple AJAX calls but non-blocking

### 3. Surgical DOM Updates
- **Target specific elements** instead of full tree refresh
- **Preserve existing state** when possible
- **Mark updated nodes** to prevent unnecessary backend overrides

### 4. Efficient Data Structures
```javascript
// Optimized temporary storage
temporaryNodeUpdates = {
    post_title: "New Title",
    name: "New Title", // D3 display field  
    difficulty_level: "hard",
    importance_rating: "4",
    target_keyword: "seo keyword"
}
```

## Security Considerations

### 1. Nonce Verification
```php
// All AJAX endpoints use nonce verification
if (!check_ajax_referer('slmm_direct_edit_nonce', 'nonce', false)) {
    wp_send_json_error('Security check failed');
    return;
}
```

### 2. Capability Checks
```php
// Verify user can edit the specific post
if (!current_user_can('edit_post', $post_id)) {
    wp_send_json_error('Cannot edit this post');
    return;
}
```

### 3. Input Sanitization
```php
// Sanitize all input data
$new_title = sanitize_text_field($_POST['new_title']);
$post_id = intval($_POST['post_id']);
```

### 4. Data Validation
```php
// Validate data before processing
if (!$post_id || empty($new_title)) {
    wp_send_json_error('Missing required parameters');
    return;
}
```

## Debugging & Monitoring

### Console Log Patterns
```javascript
// Consistent logging format
console.log('[SLMM Dashboard] Action completed:', data);
console.warn('[SLMM Dashboard] ⚠️ Warning condition:', details);
console.error('[SLMM Dashboard] ❌ Error occurred:', error);
console.log('[SLMM Dashboard] ✅ Success:', result);
```

### PHP Error Logging
```php
// Comprehensive audit trail
error_log('[SLMM Direct Editor] Post title changed - ID: ' . $post_id . ', Old: ' . $old_title . ', New: ' . $new_title . ', User: ' . get_current_user_id());
```

### State Tracking
```javascript
// Monitor system state
window.surgicallyUpdatedNodes = new Set(); // Track surgical updates
this.temporaryNodeUpdates = {}; // Track pending changes
this.isOpening = false; // Track modal state
```

## Common Issues & Solutions

### Issue 1: ACF Titles Overwritten
**Problem**: Regular title updates overwriting ACF field values
**Solution**: Check `data-acf-title="true"` attribute before updating DOM

### Issue 2: Double Updates
**Problem**: Both direct updates and AJAX updates firing
**Solution**: Use `surgicallyUpdatedNodes` Set to prevent backend overrides

### Issue 3: Modal State Conflicts
**Problem**: Multiple modals open or state not cleaned up  
**Solution**: `forceCloseModal()` with comprehensive state reset

### Issue 4: Lost Updates
**Problem**: Changes not persisting when modal closes quickly
**Solution**: Store in `temporaryNodeUpdates` immediately, apply on close

### Issue 5: Canvas Sync Failures
**Problem**: Canvas nodes not updating after modal changes
**Solution**: Ensure `window.refreshNodeWithD3Rebind` is available and target node exists

## Testing Checklist

### Frontend Communication Tests
- [ ] Canvas node click opens Direct Editor
- [ ] Modal title shows correct value (ACF vs regular)
- [ ] Field changes update `temporaryNodeUpdates`  
- [ ] Modal close applies changes to canvas immediately
- [ ] Background saves complete without errors

### Canvas Update Tests  
- [ ] Title changes appear immediately on canvas
- [ ] Status changes update badge color/text
- [ ] Difficulty changes update circle color/symbol
- [ ] Importance changes update circle color/number
- [ ] Keyword changes update text display

### ACF Integration Tests
- [ ] ACF titles display in modal (not WordPress title)
- [ ] ACF title edits preserve ACF field value
- [ ] Regular titles don't interfere with ACF titles
- [ ] Canvas ACF titles maintain `data-acf-title="true"`

### Error Recovery Tests
- [ ] Network failures don't break UI
- [ ] Invalid post IDs handled gracefully
- [ ] Missing functions degrade gracefully
- [ ] Surgical update failures fall back to basic sync

## File Locations Reference

### Key Frontend Files
- `assets/js/slmm-direct-editor.js` - Main Direct Editor functionality
- `assets/js/slmm-acf-integration.js` - ACF-specific canvas integration
- `assets/css/slmm-direct-editor.css` - Modal and interaction styling

### Key Backend Files  
- `includes/features/direct-editing/class-slmm-editor-ajax-handler.php` - AJAX endpoints
- `includes/interlinking/interlinking-suite.php` - Canvas integration and D3.js functions
- `plugin.php` - Main plugin entry point and initialization

### Configuration Files
- `sessions/tasks/h-implement-regular-title-editing.md` - Implementation documentation
- `sessions/tasks/h-fix-title-field-sync.md` - Bug fix documentation
- `CLAUDE.md` - Development guidelines and patterns

This architecture provides a robust, responsive, and maintainable system for bidirectional communication between the Direct Editor and canvas nodes, with proper error handling, performance optimizations, and security measures.