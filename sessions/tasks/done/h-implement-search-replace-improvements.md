---
task: h-implement-search-replace-improvements
branch: feature/implement-search-replace-improvements
status: in-progress
created: 2025-01-19
modules: [search-replace, ui, ajax]
---

# Implement Search and Replace UI/UX Improvements

## Problem/Goal
The current search and replace function needs enhanced preview capabilities and granular control over which pages/posts get modified. Users need to see context around changes and have page-by-page approval control with bulk selection options.

## Success Criteria
- [ ] Add preview dropdown showing 200 characters before and after each replacement
- [ ] Implement page-by-page approval with checkmarks for each post/page
- [ ] Add "Select All" and "Deselect All" buttons for bulk operations
- [ ] Add "Page content only" option that includes Classic Editor, G<PERSON>nberg, and all page builders
- [ ] Add "Classic Editor only" option in the "Select Tables to Search" section
- [ ] All changes work seamlessly with existing search and replace functionality
- [ ] Maintain proper WordPress security (nonces, capability checks, sanitization)

## Context Manifest

### How Search and Replace Currently Works

The existing search and replace feature is a comprehensive database search/replace system integrated into the SLMM SEO Bundle settings page as a tab. When a user navigates to the "Search & Replace" tab in the settings, the system loads a sophisticated form that handles both frontend UI and backend database operations.

The **frontend flow** begins when users click the "Search & Replace" tab button, triggering `initializeSearchReplace()` in `/assets/js/slmm-settings.js`. This function dynamically moves a hidden form container (`slmm-search-replace-form-container`) into the active tab area and loads the database tables via AJAX. The form uses the Better Search Replace plugin pattern with **BSR-style form interception** - instead of traditional form submission, JavaScript prevents the default form action and handles everything via AJAX calls to maintain better user experience and error handling.

The **table loading system** calls `slmm_get_tables` AJAX action which queries `SHOW TABLES` and returns a formatted list with checkboxes. Tables are categorized as "core" WordPress tables (wp_posts, wp_options, etc.) vs custom tables, with visual indicators and smart defaults that select core tables by default. Users can search through tables using a real-time filter input, and bulk select/deselect with dedicated buttons.

The **search execution flow** happens when users click "Start Search & Replace" button. The JavaScript serializes form data including search text, replace text, options (case insensitive, dry run, whole words), and selected tables array. This triggers the `slmm_search_replace` AJAX action handled by `handle_search_replace()` in `/includes/settings/general-settings.php`.

The **backend security system** implements multiple authorization layers beyond standard WordPress capabilities. First, it checks `check_ajax_referer('slmm_search_replace', 'nonce')` for CSRF protection. Then `current_user_can('manage_options')` for basic WordPress permissions. Most importantly, it verifies the user is in the **authorized_admins whitelist** stored in chatgpt_generator_options, plus `slmm_seo_check_visibility_authorization()` for the plugin's authorization system. For non-dry-run operations, it requires explicit confirmation via POST parameter and verifies recent backups exist through `verify_backup_status()`. Rate limiting restricts users to 3 operations per hour via WordPress transients.

The **database processing engine** uses `process_table_search_replace()` method for each selected table. This implements **serialized data handling** through `recursive_search_replace()` which safely unserializes WordPress data, processes nested arrays/objects, performs the search/replace, and re-serializes while maintaining data integrity. The system handles special cases like WordPress URLs, protects core options from modification, and tracks detailed change statistics including affected posts, modified columns, and replacement counts.

The **results display system** renders comprehensive feedback showing total replacements made, rows affected, tables processed, and a detailed breakdown per table. For affected posts, it displays post titles with direct edit/view links in new tabs. The UI distinguishes between dry run previews (with warning notices) and actual modifications with appropriate success messaging.

### For New Feature Implementation: Preview, Approval, and Filtering Enhancements

Since we're implementing preview dropdowns, page-by-page approval, and content filtering, these features need to integrate seamlessly with the existing search/replace infrastructure while extending its capabilities.

The **preview dropdown system** will need to modify the `process_table_search_replace()` method to capture 200 characters before and after each match location. Instead of just counting replacements, we'll need to store the actual text context for each potential change. This data structure will be passed back to the frontend and rendered as expandable preview sections for each affected post/page.

The **page-by-page approval system** requires extending the results display to include individual checkboxes for each affected item. The current results system already displays affected posts with titles and IDs - we'll add selection controls here. The JavaScript will need to manage checkbox states and provide "Select All"/"Deselect All" functionality similar to the existing table selection pattern. When users approve changes, a second AJAX call will execute only the selected modifications.

The **content filtering options** need to integrate with the existing table selection system. The "Page content only" option should filter the wp_posts table to only search posts/pages created with any editor type, while "Classic Editor only" option needs to leverage the existing editor type detection logic from the direct editing feature. The system already has `validate_post_for_editing()` methods that determine if content is classic, gutenberg, or system-generated - we can reuse this validation to filter posts before processing.

### Technical Reference Details

#### Core File Locations
- **Main AJAX Handler**: `/includes/settings/general-settings.php` - `handle_search_replace()` method (lines ~3100-3400)
- **Frontend JavaScript**: `/assets/js/slmm-settings.js` - `initializeSearchReplace()` function
- **Form Template**: `/includes/settings/general-settings.php` - HTML form around line 1600
- **CSS Styling**: `/assets/css/slmm-settings.css` - `.slmm-search-replace-*` classes

#### Security Patterns
- **AJAX Nonce**: `wp_nonce_field('slmm_search_replace', 'slmm_search_replace_nonce')`
- **Authorization Check**: `slmm_seo_check_visibility_authorization()` function
- **Capability Verification**: `current_user_can('manage_options')`
- **Whitelist Validation**: Checks `authorized_admins` array in options
- **Rate Limiting**: WordPress transient `slmm_search_replace_{user_id}`

#### Database Query Patterns
- **Table Discovery**: `SHOW TABLES` with WordPress prefix filtering
- **Content Processing**: Uses `recursive_search_replace()` for serialized data safety
- **Post Filtering**: Can leverage `validate_post_for_editing()` from direct editing feature
- **Result Tracking**: Detailed arrays of affected posts, columns, and change counts

#### UI Component Patterns
- **Checkbox Styling**: `.slmm-checkbox-field` class with custom purple checkboxes
- **Form Structure**: Separate form outside main settings to prevent conflicts
- **AJAX Loading**: Loading states with spinner and disabled button patterns
- **Results Display**: Table format with expandable sections and action links

#### Integration Points
- **Editor Type Detection**: Reuse validation logic from `/includes/features/direct-editing/`
- **Table Selection Pattern**: Existing checkbox group management in `slmm-settings.js`
- **Success Messaging**: `showNotification()` function for user feedback
- **Form Validation**: Built-in validation before AJAX submission

#### Configuration Requirements
- **WordPress Capabilities**: `manage_options` required
- **Plugin Authorization**: Must be in authorized_admins whitelist
- **Database Access**: Direct table access for search/replace operations
- **Backup Verification**: Checks for recent backup before destructive operations

#### File Size Considerations
- **Current general-settings.php**: ~4000 lines (exceeds 800 line limit)
- **Recommended Split**: Extract search/replace into separate class file
- **New File Location**: `/includes/features/search-replace/class-slmm-search-replace-engine.php`
- **Integration Required**: Update plugin.php to load new class

## User Notes
### Feature Requirements from Deme:

1. **Preview Text Before/After**:
   - Show dropdown button with 200 characters before and after the change
   - Should give context for each replacement location

2. **Page-by-Page Approval**:
   - Add checkmarks for each post/page in results
   - Only posts with checkmarks will be modified

3. **Bulk Selection Controls**:
   - "Select All" button to approve all changes at once
   - "Deselect All" button to clear all selections

4. **Page Content Only Option**:
   - New option that searches page content regardless of editor type
   - Should include Classic Editor, Gutenberg, and any page builder content

5. **Classic Editor Only Option**:
   - Add specific option in "Select Tables to Search" section
   - Should only search content created with Classic Editor

## Work Log
- [2025-01-19] Task created for search and replace UI/UX improvements