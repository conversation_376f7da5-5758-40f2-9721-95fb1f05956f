<?php
if (!defined('ABSPATH')) {
    die();
}

add_action('plugins_loaded', function() {
    $wpContext = new \WFPCore\WordPressContext();

    if (!(!$wpContext->is_frontend())) {
        return false;
    }

    // Ensure that the actions are added only once to avoid duplication
    if (!has_action('admin_enqueue_scripts', 'slmm_seo_text_helper_enqueue_scripts')) {
        add_action('admin_enqueue_scripts', 'slmm_seo_text_helper_enqueue_scripts');
    }
    if (!has_action('admin_footer', 'slmm_seo_text_helper_admin_footer')) {
        add_action('admin_footer', 'slmm_seo_text_helper_admin_footer');
    }
    if (!has_action('add_meta_boxes', 'slmm_seo_text_helper_add_keyword_checker_metabox')) {
        add_action('add_meta_boxes', 'slmm_seo_text_helper_add_keyword_checker_metabox');
    }

    function slmm_seo_text_helper_enqueue_scripts($hook) {
        if ('post.php' != $hook && 'post-new.php' != $hook) {
            return;
        }

        wp_enqueue_script('jquery');
        wp_enqueue_style('dashicons');
        wp_enqueue_style('slmm-admin-styles', plugins_url('/assets/css/slmm-admin.css', dirname(__FILE__)));
        
        // Add the new search-replace script
        wp_enqueue_script('slmm-search-replace', plugins_url('/src/searchReplace.js', dirname(__FILE__)), array('jquery', 'tinymce'), '1.0.0', true);
        
        // Add the new content duplication detector script
        wp_enqueue_script('slmm-content-duplication', plugins_url('/src/contentDuplication.js', dirname(__FILE__)), array('jquery', 'tinymce'), '1.0.0', true);
        
        // Load debug logger first
        wp_enqueue_script('slmm-debug-logger', plugins_url('/assets/js/slmm-debug-logger.js', dirname(__FILE__)), array('jquery'), '1.0.0', true);
        
        // Add the broken links detector script (depends on debug logger)
        wp_enqueue_script('slmm-broken-links', plugins_url('/src/brokenLinkDetector.js', dirname(__FILE__)), array('jquery', 'tinymce', 'slmm-debug-logger'), '1.1.0', true);
        
        // Add nonce for broken links AJAX requests and debug configuration
        $options = get_option('chatgpt_generator_options', array());
        $debug_logging_enabled = isset($options['enable_debug_logging']) ? $options['enable_debug_logging'] : false;
        $debug_categories = isset($options['debug_logging_categories']) ? $options['debug_logging_categories'] : array();
        
        // Localize data for both debug logger and broken links detector
        wp_localize_script('slmm-broken-links', 'slmmLinkCheck', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('slmm_link_check_nonce'),
            'debug_logging_enabled' => $debug_logging_enabled,
            'debug_categories' => $debug_categories,
            'debug_mode' => defined('WP_DEBUG') && WP_DEBUG
        ));
    }

    function slmm_seo_text_helper_add_keyword_checker_metabox() {
        add_meta_box(
            'keyword_checker_metabox',
            'Keyword Checker',
            'slmm_seo_text_helper_render_keyword_checker_metabox',
            null,
            'side',
            'default'
        );
    }

    function slmm_seo_text_helper_render_keyword_checker_metabox($post) {
        ?>
        <div id="keyword-checker-accordion" class="postbox">
            <button type="button" class="handlediv" aria-expanded="true">
                <span class="screen-reader-text">Toggle panel: Keyword Checker</span>
                <span class="toggle-indicator" aria-hidden="true"></span>
            </button>
            <h2 class="hndle"><span>Keyword Checker</span></h2>
            <div class="inside">
                <p><strong>Enter your keyword list (one per line):</strong></p>
                <textarea id="keyword-input" style="width:100%; min-height: 100px;"></textarea>
                <p><label><input type="checkbox" id="match-case-exactly" /> Match Case Exactly</label></p>
                <p><strong>Enter your comma-separated keyword list:</strong></p>
                <textarea id="keyword-input-2" style="width:100%; min-height: 60px;"></textarea>
                <p><button id="check-keywords" class="button">Check If Keywords Are In Text</button></p>
                <div id="keyword-results">
                    <p><strong>Keywords found:</strong></p>
                    <ul id="keyword-list"></ul>
                    <p style="color:red;"><strong>Keywords not used:</strong></p>
                    <ul id="unused-keyword-list"></ul>
                </div>
            </div>
        </div>
        <?php
    }

    function slmm_seo_text_helper_admin_footer() {
        $current_screen = get_current_screen();
        if ($current_screen->base !== 'post' && $current_screen->base !== 'page') {
            return;
        }
        ?>
        <script type="text/javascript">
        // SLMM debug system integration
        function debugLog(message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.log('SEO Text Helper', message, data);
            }
        }
        
        function debugError(message, data) {
            if (window.SLMM && window.SLMM.debug) {
                window.SLMM.debug.error('SEO Text Helper', message, data);
            }
        }

jQuery(document).ready(function($) {
            if ($('#custom-editor-buttons').length === 0) {
                var buttonHTML = '\
                <div id="custom-editor-buttons" style="margin-bottom: 10px; display: inline-block;">\
                    <button id="clean-and-title" class="button custom-button">Clean&Title</button>\
                    <input type="text" id="exclude-classes-input" placeholder="Exclude classes must be space separated" style="margin-left: 10px; padding: 5px; width: 300px;">\
                    <button id="sentence-case" class="button custom-button">Sentence Case</button>\
                    <button id="sentence" class="button custom-button">Sentence</button>\
                    <button id="hidden-div" class="button custom-button">Hidden Div<span class="hidden-div-indicator"></span><span class="hidden-div-count"></span></button>\
                    <button id="anchor-text" class="button custom-button">Anchors</button>\
                    <button id="insert-gmb-image" class="button custom-button">GMB Img</button>\
                    <button id="insert-youtube-video" class="button custom-button">Add YT</button>\
                    <button id="submit-gsc" class="button custom-button" data-gsc-url="">Submit GSC</button>\
                    <button id="copy-permalink" class="button custom-button" style="margin-left: 5px;">Copy URL</button>\
                </div>\
                ';
                $('#wp-content-editor-tools').append(buttonHTML);
            }

            // Add click handler for the Copy URL button
            $('#copy-permalink').on('click', function(e) {
                e.preventDefault();
                var permalinkElement = $('#sample-permalink');
                var fullPermalink = permalinkElement.find('a').attr('href') || permalinkElement.text();
                
                navigator.clipboard.writeText(fullPermalink).then(function() {
                    // Show success feedback
                    var $button = $('#copy-permalink');
                    var originalText = $button.text();
                    $button.text('Copied!');
                    setTimeout(function() {
                        $button.text(originalText);
                    }, 2000);
                }).catch(function(err) {
                    debugError('Failed to copy permalink', err);
                    alert('Failed to copy permalink to clipboard');
                });
            });

            function isVisualEditorActive() {
                return $('#wp-content-wrap').hasClass('tmce-active');
            }

            function getEditorContent() {
                if (isVisualEditorActive()) {
                    return tinymce.get('content').getContent({format: 'html'});
                } else {
                    return $('#content').val();
                }
            }

            function setEditorContent(content) {
                if (isVisualEditorActive()) {
                    tinymce.get('content').setContent(content);
                } else {
                    $('#content').val(content);
                }
            }

            function replaceSelectedText(textarea, text) {
                var start = textarea.selectionStart;
                var end = textarea.selectionEnd;
                var oldText = textarea.value;
                textarea.value = oldText.substring(0, start) + text + oldText.substring(end);
                textarea.selectionStart = textarea.selectionEnd = start + text.length;
            }

            // Helper function to escape special regex characters
            function escapeRegExp(string) {
                return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            }

            // Adjusted function to replace phrases in text nodes (case-insensitive)
            function replacePhrasesInTextNodes(node, searchReplaceArray) {
                if (node.nodeType === Node.TEXT_NODE) {
                    var textContent = node.textContent;
                    searchReplaceArray.forEach(function(pair) {
                        var regex = new RegExp('\\b' + escapeRegExp(pair.lower) + '\\b', 'gi');
                        textContent = textContent.replace(regex, function(match) {
                            return pair.original;
                        });
                    });
                    node.textContent = textContent;
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    // Process 'title' attribute if present
                    if (node.hasAttribute('title')) {
                        var titleAttr = node.getAttribute('title');
                        searchReplaceArray.forEach(function(pair) {
                            var regex = new RegExp('\\b' + escapeRegExp(pair.lower) + '\\b', 'gi');
                            titleAttr = titleAttr.replace(regex, function(match) {
                                return pair.original;
                            });
                        });
                        node.setAttribute('title', titleAttr);
                    }
                    // Recursively process child nodes
                    node.childNodes.forEach(function(child) {
                        replacePhrasesInTextNodes(child, searchReplaceArray);
                    });
                }
            }

        // Function 1: Clean and Title (Corrected)
$('#clean-and-title').on('click', function(e) {
    e.preventDefault();
    var html = getEditorContent();

    // Temporarily replace hidden divs
    var divCounter = 0;
    var divPlaceholders = [];
    html = html.replace(/<div\s+([^>]*data-nosnippet[^>]*)>/gi, function(match) {
        var placeholder = `__DIV_PLACEHOLDER_${divCounter}__`;
        divPlaceholders.push({placeholder: placeholder, original: match});
        divCounter++;
        return placeholder;
    });

    // Remove inline styles
    html = html.replace(/ style="[^"]*"/g, '');

    // Get excluded classes from input
    var excludedClasses = $('#exclude-classes-input').val().trim();
    var excludedClassList = excludedClasses ? excludedClasses.split(/\s+/) : [];
    
    // Function to check if a class combination should be preserved
    function shouldPreserveClasses(classString) {
        // If the exact space-separated combination is in the exclusion list, preserve it
        if (excludedClassList.includes(classString)) {
            return true;
        }
        
        // Check individual classes
        var classes = classString.split(/\s+/);
        var preservedClasses = classes.filter(function(className) {
            return excludedClassList.includes(className) || className === 'accordion' || className === 'panel';
        });
        
        return preservedClasses;
    }

    // Class preservation
    html = html.replace(/ class="([^"]*)"/g, function(match, group) {
        var preservedClasses = shouldPreserveClasses(group);
        if (Array.isArray(preservedClasses)) {
            return preservedClasses.length ? ' class="' + preservedClasses.join(' ') + '"' : '';
        }
        // If true was returned, preserve the entire class string
        return preservedClasses ? match : '';
    });

    // Empty tag handling - preserve elements with preserved classes
    html = html.replace(/<([a-z][a-z0-9]*)([^>]*)>(\s*)<\/\1>/gi, function(match, tag, attrs, content) {
        tag = tag.toLowerCase();
        
        // Check for preserved classes
        var classMatch = attrs.match(/class="([^"]*)"/i);
        var hasPreservedClass = false;
        
        if (classMatch) {
            var classString = classMatch[1];
            hasPreservedClass = shouldPreserveClasses(classString);
        }
        
        if ((tag !== 'p' && tag !== 'span' && !hasPreservedClass) || content.trim() === '') {
            return '';
        }
        return match;
    });

    // Attribute removal - preserve specified classes
    html = html.replace(/<([a-z][a-z0-9]*)([^>]*)>/gi, function(m, tag, attrs) {
        tag = tag.toLowerCase();
        if (tag === 'a' || tag === 'img') return m;

        // Extract and preserve valid classes
        const classMatch = attrs.match(/\bclass="([^"]*)"/i);
        if (classMatch) {
            var classString = classMatch[1];
            var preservedClasses = shouldPreserveClasses(classString);
            
            // If true was returned, preserve the entire class string
            if (preservedClasses === true) {
                return `<${tag} class="${classString}">`;
            }
            
            // If array was returned, join preserved classes
            if (Array.isArray(preservedClasses) && preservedClasses.length) {
                return `<${tag} class="${preservedClasses.join(' ')}">`;
            }
        }
        
        return `<${tag}>`;
    });

    // Process consecutive spaces
    html = html.replace(/\s{2,}/g, ' ');
    html = html.replace(/<!--[\s\S]*?-->/g, '');
    
    // Restore hidden divs and process images/H1s
    divPlaceholders.forEach(function(item) {
        html = html.replace(item.placeholder, item.original);
    });

    // Process H1 tags
    html = html.replace(/<h(\d)([^>]*)>(.*?)<\/h\1>/gi, function(m, level, attrs, content) {
        return '<h' + level + attrs + ' title="' + content + '">' + content + '</h' + level + '>';
    });

    // Process <img> tags
    html = html.replace(/<img([^>]*)>/gi, function(m, attrs) {
        var srcMatch = attrs.match(/src="([^"]*)"/i);
        var altMatch = attrs.match(/alt="([^"]*)"/i);
        var src = srcMatch ? srcMatch[1] : '';
        var alt = altMatch ? altMatch[1] : '';
        var width = '800';
        var height = 'auto';

        return '<img class="aligncenter" src="' + src + '" alt="' + alt + '" width="' + width + '" height="' + height + '" />';
    });

    // Set the cleaned HTML content
    setEditorContent(html.trim());
});




// Function 2: Sentence Case - Handled by protected-words.js
$('#sentence-case').on('click', function(e) {
    e.preventDefault();
    // The sentence case functionality is now handled entirely by protected-words.js
});

            // Function 3: Sentence
            $('#sentence').on('click', function(e) {
                e.preventDefault();
                if (isVisualEditorActive()) {
                    var editor = tinymce.get('content');
                    var selectedText = editor.selection.getContent({format: 'text'});
                    if (!selectedText) {
                        return;
                    }

                    var words = selectedText.split(',').map(function(word) {
                        return word.trim();
                    }).filter(Boolean);

                    if (words.length === 0) {
                        return;
                    }

                    var sentence = words.join(' ');
                    sentence = sentence.charAt(0).toUpperCase() + sentence.slice(1) + '.';

                    editor.selection.setContent(sentence);
                } else {
                    var textarea = $('#content')[0];
                    var selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd);
                    if (!selectedText) {
                        return;
                    }

                    var words = selectedText.split(',').map(function(word) {
                        return word.trim();
                    }).filter(Boolean);

                    if (words.length === 0) {
                        return;
                    }

                    var sentence = words.join(' ');
                    sentence = sentence.charAt(0).toUpperCase() + sentence.slice(1) + '.';

                    replaceSelectedText(textarea, sentence);
                }
            });

            // Function 4: Hidden Div
            $('#hidden-div').on('click', function(e) {
                e.preventDefault();
                var selectedText;
                if (isVisualEditorActive()) {
                    var editor = tinymce.get('content');
                    selectedText = editor.selection.getContent({format: 'html'});
                    if (!selectedText) {
                        return;
                    }

                    var sentence = selectedText.replace(/\s+/g, ' ').trim();
                    sentence = sentence.charAt(0).toUpperCase() + sentence.slice(1) + '.';
                    var htmlContent = '<div style="display:none;" aria-hidden="true" data-nosnippet>' + sentence + '</div>';
                    htmlContent = htmlContent.replace(/>\s+</g, '><').trim();

                    editor.selection.setContent(htmlContent);
                } else {
                    var textarea = $('#content')[0];
                    selectedText = textarea.value.substring(textarea.selectionStart, textarea.selectionEnd);
                    if (!selectedText) {
                        return;
                    }

                    var sentence = selectedText.replace(/\s+/g, ' ').trim();
                    sentence = sentence.charAt(0).toUpperCase() + sentence.slice(1) + '.';
                    var htmlContent = '<div style="display:none;" aria-hidden="true" data-nosnippet>' + sentence + '</div>';
                    htmlContent = htmlContent.replace(/>\s+</g, '><').trim();

                    replaceSelectedText(textarea, htmlContent);
                }
                updateButtonStatus('hidden-div');
                updateHiddenDivIndicator();
            });

            // Function 5: Anchor Text
            $('#anchor-text').on('click', function(e) {
                e.preventDefault();
                const excludeList = [
                    'facebook', 'youtube', 'whatsapp', 'instagram', 'tiktok', 'wechat',
                    'linkedin', 'snapchat', 'pinterest', 'twitter', 'reddit', 'telegram',
                    'quora', 'discord', 'tumblr', 'medium', 'line', 'viber', 'signal',
                    'clubhouse', 'follow', 'followers', 'share', 'likes', 'connections',
                    'connect', 'subscribe', 'edit template', 'edit with oxygen',
                    'titles settings', 'social', 'advanced',
                    'redirection global settings of the plugin.', 'save permalink',
                    'update image attributes', 'classic editor plugin', '/wp-admin/'
                ];

                const links = [];

                function scanContainer(container) {
                    const elements = container.querySelectorAll('a');
                    elements.forEach(element => {
                        const anchorText = element.textContent.trim();
                        const url = element.href.trim();
                        const lowerCaseAnchorText = anchorText.toLowerCase();
                        if (
                            anchorText &&
                            url &&
                            !excludeList.some(exclude => lowerCaseAnchorText.includes(exclude) || url.includes(exclude))
                        ) {
                            links.push({ element, anchorText, url });
                        }
                    });
                }

                const iframe = document.getElementById('content_ifr');
                if (iframe) {
                    const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                    scanContainer(iframeDocument);
                } else {
                    alert('TinyMCE iframe not found.');
                    return;
                }

                if (links.length === 0) {
                    alert('No links found.');
                    return;
                }

                const popup = document.createElement('div');
                popup.style.cssText = 'position: fixed; top: 100px; left: 20px; background-color: white; padding: 20px; z-index: 9999; border: 1px solid black; max-height: 80vh; overflow-y: auto; min-width: 600px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); border-radius: 4px;';

                const closeButton = document.createElement('button');
                closeButton.textContent = 'Close';
                closeButton.style.marginTop = '10px';
                closeButton.addEventListener('click', () => {
                    document.body.removeChild(popup);
                });

                const copyButton = document.createElement('button');
                copyButton.textContent = 'Copy Links';
                copyButton.style.marginTop = '10px';
                copyButton.addEventListener('click', () => {
                    const textToCopy = links.map(link => `${link.anchorText}: ${link.url}`).join('\n');
                    navigator.clipboard.writeText(textToCopy).then(() => {
                        alert('Links copied to clipboard!');
                        document.body.removeChild(popup);
                    }).catch(err => {
                        debugError('Unable to copy links', err);
                    });
                });

                const column1 = document.createElement('div');
                column1.style.cssText = 'float: left; width: 50%;';
                const column2 = document.createElement('div');
                column2.style.cssText = 'float: left; width: 50%;';

                links.forEach(link => {
                    const anchorText = document.createElement('a');
                    anchorText.textContent = link.anchorText;
                    anchorText.href = "#";
                    anchorText.style.display = 'block';
                    anchorText.addEventListener('click', () => {
                        link.element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    });
                    column1.appendChild(anchorText);

                    const url = document.createElement('div');
                    url.textContent = link.url;
                    column2.appendChild(url);
                });

                popup.appendChild(column1);
                popup.appendChild(column2);
                popup.appendChild(copyButton);
                popup.appendChild(closeButton);
                document.body.appendChild(popup);
                updateButtonStatus('anchor-text');
            });

            // Function 6: Insert GMB Image
            $('#insert-gmb-image').on('click', function(e) {
                e.preventDefault();
                navigator.clipboard.readText().then(function(text) {
                    if (text) {
                        var newUrl = text.replace(/=h\d+-no$/, '=h605-no');
                        var altText = '';

                        if (isVisualEditorActive()) {
                            var editor = tinymce.get('content');
                            var selection = editor.selection;
                            var selectedNode = selection.getNode();
                            var body = editor.getBody();
                            var currentNode = selectedNode;
                            var headingNode = null;

                            while (currentNode && currentNode !== body) {
                                if (/h[1-6]/i.test(currentNode.nodeName)) {
                                    headingNode = currentNode;
                                    break;
                                }
                                currentNode = currentNode.previousSibling || currentNode.parentNode;
                            }

                            var contentBetween = '';
                            if (headingNode) {
                                contentBetween += headingNode.innerText + ' ';
                                var node = headingNode.nextSibling;
                            } else {
                                var node = body.firstChild;
                            }

                            while (node && node !== selectedNode) {
                                if (node.nodeType === Node.TEXT_NODE) {
                                    contentBetween += node.textContent + ' ';
                                } else if (node.nodeType === Node.ELEMENT_NODE) {
                                    contentBetween += node.innerText + ' ';
                                }
                                node = node.nextSibling;
                            }

                            altText = contentBetween.trim();
                            var imgHtml = '<figure><img class="aligncenter size-medium" src="' + newUrl + '" alt="' + altText + '" width="1076" height="605" /></figure>';
                            editor.execCommand('mceInsertContent', false, imgHtml);
                        } else {
                            var textarea = $('#content')[0];
                            var cursorPosition = textarea.selectionStart;
                            var contentBeforeCursor = textarea.value.substring(0, cursorPosition);
                            var headingRegex = /<h[1-6][^>]*>(.*?)<\/h[1-6]>/gi;
                            var match;
                            var lastHeading = null;
                            while ((match = headingRegex.exec(contentBeforeCursor)) !== null) {
                                lastHeading = {
                                    text: match[1],
                                    index: match.index + match[0].length
                                };
                            }

                            var startIdx = lastHeading ? lastHeading.index : 0;
                            var contentBetween = textarea.value.substring(startIdx, cursorPosition);
                            altText = (lastHeading ? lastHeading.text + ' ' : '') + contentBetween.replace(/<[^>]*>/g, '').trim();

                            var imgHtml = '<figure><img class="aligncenter size-medium" src="' + newUrl + '" alt="' + altText + '" width="1076" height="605" /></figure>';
                            var oldText = textarea.value;
                            textarea.value = oldText.substring(0, cursorPosition) + imgHtml + oldText.substring(cursorPosition);
                            textarea.selectionStart = textarea.selectionEnd = cursorPosition + imgHtml.length;
                        }
                    } else {
                        alert('Clipboard is empty or does not contain text.');
                    }
                }).catch(function(err) {
                    debugError('Failed to read clipboard contents', err);
                });
                updateButtonStatus('insert-gmb-image');
            });

            // Function 7: Keyword Checker
            $('#check-keywords').on('click', function(e) {
                e.preventDefault();
                checkKeywords();
            });

            function checkKeywords() {
                // Clear previous results
                $('#keyword-results').empty();

                var matchCaseExact = $('#match-case-exactly').is(':checked');
                var editorContent = getEditorContent();
                var $tempDiv = $('<div>').html(editorContent);

                var keywords = $('#keyword-input').val().split("\n").filter(Boolean);
                var keywords2 = $('#keyword-input-2').val().split(',').filter(Boolean);
                var allKeywords = keywords.concat(keywords2).map(function(x) {
                    return matchCaseExact ? x.trim() : x.trim().toLowerCase();
                });

                var keywordsInHeadings = [];
                var keywordsInText = [];
                var keywordCounts = {headings: {}, text: {}};

                // Function to count keywords in a given text
                function countKeywords(text, keywordArray, countObject) {
                    var lowerText = matchCaseExact ? text : text.toLowerCase();
                    keywordArray.forEach(function(keyword) {
                        var regex = new RegExp("\\b" + escapeRegExp(keyword) + "\\b", matchCaseExact ? "g" : "gi");
                        var matches = lowerText.match(regex);
                        if (matches) {
                            countObject[keyword] = (countObject[keyword] || 0) + matches.length;
                        }
                    });
                }

                // Count keywords in headings
                var headingsText = '';
                $tempDiv.find('h1, h2, h3, h4, h5, h6').each(function() {
                    var headingText = $(this).text();
                    headingsText += ' ' + headingText;
                    countKeywords(headingText, allKeywords, keywordCounts.headings);
                    allKeywords.forEach(function(keyword) {
                        if (headingText.toLowerCase().includes(keyword.toLowerCase())) {
                            keywordsInHeadings.push(keyword);
                        }
                    });
                });

                // Count keywords in text (including hidden divs and alt tags)
                var bodyText = '';
                $tempDiv.find('p, span, div, img[alt]').each(function() {
                    if ($(this).is('img')) {
                        bodyText += ' ' + $(this).attr('alt');
                    } else {
                        bodyText += ' ' + $(this).text();
                    }
                });
                countKeywords(bodyText, allKeywords, keywordCounts.text);
                allKeywords.forEach(function(keyword) {
                    if (bodyText.toLowerCase().includes(keyword.toLowerCase())) {
                        keywordsInText.push(keyword);
                    }
                });

                // Calculate total words for each section
                var totalWordsInHeadings = headingsText.split(/\s+/).filter(Boolean).length;
                var totalWordsInText = bodyText.split(/\s+/).filter(Boolean).length;

                // Function to calculate percentage
                function calculatePercentage(count, total) {
                    return ((count / total) * 100).toFixed(2);
                }

                // Generate results HTML
                var resultsHTML = '<p><strong>Keywords in Headings:</strong></p><ul>';
                [...new Set(keywordsInHeadings)].forEach(function(keyword) {
                    var count = keywordCounts.headings[keyword] || 0;
                    resultsHTML += '<li>' + keyword + ' (' + count + ' - ' + calculatePercentage(count, totalWordsInHeadings) + '%)</li>';
                });
                resultsHTML += '</ul><p><strong>Keywords in Text:</strong></p><ul>';
                [...new Set(keywordsInText)].forEach(function(keyword) {
                    var count = keywordCounts.text[keyword] || 0;
                    resultsHTML += '<li>' + keyword + ' (' + count + ' - ' + calculatePercentage(count, totalWordsInText) + '%)</li>';
                });
                resultsHTML += '</ul>';

                // Display results
                $('#keyword-results').html(resultsHTML);

                // Identify and display unused keywords
                var unusedKeywords = allKeywords.filter(function(keyword) {
                    return !keywordsInHeadings.includes(keyword) && !keywordsInText.includes(keyword);
                });

                if (unusedKeywords.length > 0) {
                    var unusedKeywordsHTML = '<p style="color:red;"><strong>Keywords not used:</strong></p><ul>';
                    unusedKeywords.forEach(function(keyword) {
                        unusedKeywordsHTML += '<li>' + keyword + '</li>';
                    });
                    unusedKeywordsHTML += '</ul>';
                    $('#keyword-results').append(unusedKeywordsHTML);

                    // Copy unused keywords to clipboard
                    navigator.clipboard.writeText(unusedKeywords.join('\n')).then(function() {
                        alert('Unused keywords copied to clipboard.');
                    }).catch(function(err) {
                        debugError('Failed to copy unused keywords', err);
                    });
                }
            }

           // Function 8: Insert YouTube Video
$('#insert-youtube-video').on('click', function(e) {
    e.preventDefault();
    navigator.clipboard.readText().then(function(clipText) {
        var youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=)?(.+)/;
        var match = clipText.match(youtubeRegex);
        if (match) {
            var embedCode = '<div class="responsive-embed-container">[embed]' + clipText + '[/embed]</div>';
            if (isVisualEditorActive()) {
                tinymce.activeEditor.execCommand('mceInsertContent', false, embedCode);
            } else {
                var textarea = document.getElementById('content');
                var cursorPos = textarea.selectionStart;
                textarea.value = textarea.value.substring(0, cursorPos) + embedCode + textarea.value.substring(cursorPos);
            }
        } else {
            alert('Invalid YouTube URL in clipboard');
        }
    }).catch(function(err) {
        debugError('Failed to read clipboard contents', err);
    });
});
// Function to update the GSC URL
function updateGSCUrl() {
    var permalinkElement = $('#sample-permalink');
    var fullPermalink = permalinkElement.find('a').attr('href') || permalinkElement.text();
    debugLog('Full permalink: ' + fullPermalink);
    
    var urlParts = fullPermalink.split('/');
    var rootDomain = urlParts[2]; // This will be the domain part of the URL
    var protocol = urlParts[0].slice(0, -1); // Remove the colon from the protocol
    
    debugLog('Root domain: ' + rootDomain);
    debugLog('Protocol: ' + protocol);
    
    // Manually encode the protocol and slashes
    var encodedProtocol = encodeURIComponent(protocol + '://');
    // Replace the encoded slashes with the desired encoding
    encodedProtocol = encodedProtocol.replace(/%3A%2F%2F/g, '%3A%2F%2F');
    
    var gscUrl = 'https://search.google.com/search-console?resource_id=' + encodedProtocol + encodeURIComponent(rootDomain + '/');
    $('#submit-gsc').attr('data-gsc-url', gscUrl);
    debugLog('Generated GSC URL: ' + gscUrl);
}

// Call updateGSCUrl when the page loads
$(document).ready(function() {
    debugLog('Document ready, updating GSC URL');
    updateGSCUrl();
});

// Update GSC URL when the permalink changes
$('#edit-slug-box').on('DOMSubtreeModified', function() {
    debugLog('Permalink changed, updating GSC URL');
    updateGSCUrl();
});

// Function 9: Submit GSC
$('#submit-gsc').on('click', function(e) {
    e.preventDefault();
    debugLog('Submit GSC button clicked');
    
    // Step 1: Get the current full permalink and store it in the clipboard
    var permalinkElement = $('#sample-permalink');
    var fullPermalink = permalinkElement.find('a').attr('href') || permalinkElement.text();
    
    navigator.clipboard.writeText(fullPermalink).then(function() {
        debugLog('Full permalink copied to clipboard: ' + fullPermalink);
        
        // Step 2: Open Google Search Console with the pre-generated URL
        var gscUrl = $('#submit-gsc').attr('data-gsc-url');
        debugLog('Retrieved GSC URL: ' + gscUrl);
        
        if (gscUrl) {
            debugLog('Opening GSC URL in new tab');
            window.open(gscUrl, '_blank');
        } else {
            debugError('GSC URL not generated');
        }
    }).catch(function(err) {
        debugError('Failed to copy permalink', err);
    });
    
    updateButtonStatus('submit-gsc');
});

debugLog('Submit GSC script loaded');
            function updateButtonStatus(buttonId) {
                var button = document.getElementById(buttonId);
                if (button) {
                    button.classList.add('used');
                }
            }

            function updateHiddenDivIndicator() {
                var content = getEditorContent();
                var hiddenDivs = (content.match(/<div[^>]*(?:style="[^"]*display:\s*none[^"]*"|data-nosnippet)[^>]*>/gi) || []).length;
                var indicator = document.querySelector('#hidden-div .hidden-div-indicator');
                var count = document.querySelector('#hidden-div .hidden-div-count');
                
                if (hiddenDivs > 0) {
                    indicator.style.backgroundColor = 'green';
                    count.textContent = hiddenDivs > 1 ? ` x ${hiddenDivs}` : '';
                } else {
                    indicator.style.backgroundColor = '';
                    count.textContent = '';
                }
            }

            // Function to force update hidden div indicator
            function forceUpdateHiddenDivIndicator() {
                setTimeout(updateHiddenDivIndicator, 100);
            }

            // Update on tab switch
            $(document).on('click', '.wp-switch-editor', forceUpdateHiddenDivIndicator);

            // Update on hidden div button click
            $('#hidden-div').on('click', forceUpdateHiddenDivIndicator);

            // Update on editor init and content change
            if (typeof tinymce !== 'undefined') {
                tinymce.on('AddEditor', function(e) {
                    var editor = e.editor;
                    if (editor.id === 'content') {
                        editor.on('init', forceUpdateHiddenDivIndicator);
                        editor.on('change keyup input NodeChange', forceUpdateHiddenDivIndicator);
                    }
                });
            }

            // Update on text editor changes
            $('#content').on('input change keyup', forceUpdateHiddenDivIndicator);

            // Initial update
            forceUpdateHiddenDivIndicator();

            // Update when switching between visual/text editors
            $(document).on('tinymce-editor-setup', forceUpdateHiddenDivIndicator);
            $(document).on('tinymce-editor-init', forceUpdateHiddenDivIndicator);
        });

        jQuery(document).ready(function($) {
    var status = $('#post-status-display').text().trim();
    if (status === 'Privately Published') {
        $('<div>', {
            text: 'Page Not Visible: Privately Published',
            css: {
                'background-color': 'red',
                'color': 'white',
                'padding': '10px',
                'margin-bottom': '10px',
                'font-weight': 'bold'
            }
        }).insertBefore('#titlediv');
    }
});
        </script>
        <style>
        
            .hidden-div-indicator {
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                margin-left: 5px;
                vertical-align: middle;
            }
            .hidden-div-count {
                margin-left: 3px;
                font-size: 0.8em;
                vertical-align: middle;
            }
            .responsive-embed-container {
                position: relative;
                padding-bottom: 56.25%;
                height: 0;
                overflow: hidden;
                max-width: 100%;
            }
            .responsive-embed-container iframe,
            .responsive-embed-container object,
            .responsive-embed-container embed {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
            }
            .custom-button {
    border: 2px solid red;
    transition: border-color 0.3s;
}
.custom-button.used {
                border-color: green;
            }


        </style>
        <?php
    }
}, 10);

// Add TinyMCE plugin
add_filter('mce_external_plugins', 'slmm_add_tinymce_searchreplace_plugin');
function slmm_add_tinymce_searchreplace_plugin($plugins) {
    $plugins['custom_searchreplace'] = plugins_url('/src/searchReplace.js', dirname(__FILE__));
    $plugins['content_duplication'] = plugins_url('/src/contentDuplication.js', dirname(__FILE__));
    $plugins['broken_links'] = plugins_url('/src/brokenLinkDetector.js', dirname(__FILE__));
    return $plugins;
}

// Add TinyMCE button
add_filter('mce_buttons', 'slmm_register_searchreplace_button');
function slmm_register_searchreplace_button($buttons) {
    array_push($buttons, 'searchreplace');
    array_push($buttons, 'duplication');
    array_push($buttons, 'brokenlinks');
    return $buttons;
}
?>
