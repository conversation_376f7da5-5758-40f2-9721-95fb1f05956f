<?php
/**
 * Page Summarization AJAX Handler
 * 
 * Handles AJAX requests for AI-powered page summarization
 * using OpenAI, OpenRouter, or Anthropic APIs.
 * 
 * @package SLMM_SEO_Bundle
 * @version 4.10.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class SLMM_Page_Summarization_Handler {

    private static $instance = null;
    private static $hooks_registered = false;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Initialize the handler
     */
    private function __construct() {
        // CRITICAL FIX: Only register hooks once, not every time class is instantiated
        if (!self::$hooks_registered) {
            add_action('wp_ajax_slmm_summarize_page', array($this, 'handle_summarize_page'));
            add_action('wp_ajax_slmm_batch_summarize_pages', array($this, 'handle_batch_summarize'));
            add_action('wp_ajax_slmm_get_summary_status', array($this, 'handle_get_summary_status'));
            add_action('wp_ajax_slmm_check_summary', array($this, 'handle_check_summary'));
            add_action('wp_ajax_slmm_get_summary', array($this, 'handle_get_summary'));
            add_action('wp_ajax_slmm_delete_summary', array($this, 'handle_delete_summary'));
            add_action('wp_ajax_slmm_create_summary_table', array($this, 'handle_create_table'));

            // CRITICAL FIX: Only register init hook once to prevent 41k+ table creation attempts
            add_action('init', array($this, 'ensure_table_exists'));

            self::$hooks_registered = true;
        }
    }

    /**
     * Handle single page summarization request
     */
    public function handle_summarize_page() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            // Check capabilities
            if (!current_user_can('edit_posts')) {
                wp_send_json_error('Insufficient permissions');
                return;
            }

            // Sanitize and validate input data
            $url = esc_url_raw($_POST['url'] ?? '');
            $post_id = absint($_POST['post_id'] ?? 0);
            $force_regenerate = isset($_POST['force_regenerate']) && $_POST['force_regenerate'] === 'true';

            if (empty($url)) {
                wp_send_json_error(array('message' => 'URL is required'));
                return;
            }

            // Get page summarization settings
            $settings = $this->get_summarization_settings();
            if (!$settings['has_api_key']) {
                wp_send_json_error(array('message' => 'AI provider API key not configured'));
                return;
            }

            // Get content for summarization
            $content_result = $this->get_page_content($url, $post_id);
            if (!$content_result['success']) {
                wp_send_json_error(array('message' => $content_result['error']));
                return;
            }

            $content = $content_result['content'];
            $content_hash = SLMM_Page_Summary_Manager::get_instance()->generate_content_hash($content);

            // Check if summary already exists and is current
            if (!$force_regenerate) {
                $existing_summary = SLMM_Page_Summary_Manager::get_instance()->get_summary($url, $content_hash);
                if ($existing_summary) {
                    wp_send_json_success(array(
                        'summary' => $existing_summary->summary,
                        'generated_at' => $existing_summary->generated_at,
                        'provider' => $existing_summary->provider_used,
                        'model' => $existing_summary->model_used,
                        'from_cache' => true
                    ));
                    return;
                }
            }

            // Generate AI summary using the robust method
            $ai_response = $this->generate_ai_summary($content, $settings);
            if (!$ai_response['success']) {
                $error_message = 'API Error: ' . $ai_response['error'];
                error_log('SLMM Page Summarization API Error: ' . $error_message);
                error_log('SLMM Settings Debug: ' . print_r($settings, true));
                wp_send_json_error($error_message);
                return;
            }

            $generated_summary = $ai_response['summary'];

            // Save summary to database
            SLMM_Page_Summary_Manager::get_instance()->save_summary(
                $url,
                $content_hash,
                $generated_summary,
                $settings['prompt'],
                $settings['model'],
                $settings['provider']
            );

            // Return success with summary data and node_data for surgical update
            $response_data = array(
                'summary' => $generated_summary,
                'generated_at' => current_time('mysql'),
                'provider' => $settings['provider'],
                'model' => $settings['model'],
                'from_cache' => false
            );
            
            // Add COMPLETE node_data for surgical update if post_id is available (same as difficulty/importance handlers)
            if ($post_id > 0) {
                $fresh_post = get_post($post_id);
                if ($fresh_post) {
                    $response_data['node_data'] = array(
                        'id' => $post_id,
                        'name' => $fresh_post->post_title,
                        'post_status' => $fresh_post->post_status,
                        'post_type' => $fresh_post->post_type,
                        'permalink' => get_permalink($post_id),
                        'edit_url' => get_edit_post_link($post_id),
                        'post_date' => $fresh_post->post_date,
                        'post_modified' => $fresh_post->post_modified,
                        'difficulty_level' => get_post_meta($post_id, '_slmm_difficulty_level', true) ?: 'easy',
                        'importance_rating' => get_post_meta($post_id, '_slmm_importance_rating', true) ?: '1',
                        'target_keyword' => get_post_meta($post_id, '_slmm_target_keyword', true) ?: '',
                        'is_completed' => (get_post_meta($post_id, 'slmm_page_completion_status', true) === 'completed'),
                        'has_summary' => true  // Now has summary
                    );
                }
            }
            
            wp_send_json_success($response_data);

        } catch (Exception $e) {
            error_log('SLMM Page Summarization Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Internal error occurred'));
        }
    }

    /**
     * Handle batch summarization request
     */
    public function handle_batch_summarize() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            // Check capabilities
            if (!current_user_can('edit_posts')) {
                wp_send_json_error(array('message' => 'Insufficient permissions'));
                return;
            }

            // Get batch data
            $pages = $_POST['pages'] ?? array();
            $batch_size = absint($_POST['batch_size'] ?? 3);
            $force_regenerate = isset($_POST['force_regenerate']) && $_POST['force_regenerate'] === 'true';

            if (empty($pages) || !is_array($pages)) {
                wp_send_json_error(array('message' => 'No pages provided'));
                return;
            }

            // Limit batch size to prevent overload
            $batch_size = min($batch_size, 5);
            $pages = array_slice($pages, 0, $batch_size);

            $results = array();
            $settings = $this->get_summarization_settings();

            foreach ($pages as $page_data) {
                $url = esc_url_raw($page_data['url'] ?? '');
                $post_id = absint($page_data['post_id'] ?? 0);

                if (empty($url)) {
                    $results[] = array(
                        'url' => $url,
                        'success' => false,
                        'error' => 'Invalid URL'
                    );
                    continue;
                }

                // Process individual page
                $result = $this->process_single_page_for_batch($url, $post_id, $settings, $force_regenerate);
                $results[] = $result;

                // Add small delay between requests to respect API limits
                usleep(500000); // 0.5 seconds
            }

            wp_send_json_success(array(
                'results' => $results,
                'processed_count' => count($results)
            ));

        } catch (Exception $e) {
            error_log('SLMM Batch Summarization Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Batch processing failed'));
        }
    }

    /**
     * Handle get summary status request
     */
    public function handle_get_summary_status() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            $url = esc_url_raw($_POST['url'] ?? '');
            $content_hash = sanitize_text_field($_POST['content_hash'] ?? '');

            if (empty($url)) {
                wp_send_json_error(array('message' => 'URL is required'));
                return;
            }

            $status = SLMM_Page_Summary_Manager::get_instance()->get_summary_status($url, $content_hash);

            wp_send_json_success($status);

        } catch (Exception $e) {
            error_log('SLMM Summary Status Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Failed to get status'));
        }
    }

    /**
     * Handle check summary request
     */
    public function handle_check_summary() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            $url = esc_url_raw($_POST['url'] ?? '');
            $post_id = absint($_POST['post_id'] ?? 0);

            if (empty($url) && $post_id <= 0) {
                wp_send_json_error(array('message' => 'URL or post ID is required'));
                return;
            }

            // Get content to generate hash
            $content_result = $this->get_page_content($url, $post_id);
            if (!$content_result['success']) {
                wp_send_json_error(array('message' => 'Could not retrieve content'));
                return;
            }

            $content_hash = SLMM_Page_Summary_Manager::get_instance()->generate_content_hash($content_result['content']);
            $existing_summary = SLMM_Page_Summary_Manager::get_instance()->get_summary($url, $content_hash);

            wp_send_json_success(array(
                'exists' => !empty($existing_summary),
                'summary' => $existing_summary ? $existing_summary->summary : null
            ));

        } catch (Exception $e) {
            error_log('SLMM Check Summary Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Failed to check summary'));
        }
    }

    /**
     * Handle get summary request
     */
    public function handle_get_summary() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            $url = esc_url_raw($_POST['url'] ?? '');
            $post_id = absint($_POST['post_id'] ?? 0);

            if (empty($url) && $post_id <= 0) {
                wp_send_json_error(array('message' => 'URL or post ID is required'));
                return;
            }

            // Get content to generate hash
            $content_result = $this->get_page_content($url, $post_id);
            if (!$content_result['success']) {
                wp_send_json_error(array('message' => 'Could not retrieve content'));
                return;
            }

            $content_hash = SLMM_Page_Summary_Manager::get_instance()->generate_content_hash($content_result['content']);
            $existing_summary = SLMM_Page_Summary_Manager::get_instance()->get_summary($url, $content_hash);

            if ($existing_summary) {
                wp_send_json_success(array(
                    'summary' => $existing_summary->summary,
                    'generated_at' => $existing_summary->generated_at,
                    'provider' => $existing_summary->provider_used,
                    'model' => $existing_summary->model_used
                ));
            } else {
                wp_send_json_error(array('message' => 'Summary not found'));
            }

        } catch (Exception $e) {
            error_log('SLMM Get Summary Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Failed to get summary'));
        }
    }

    /**
     * Handle delete summary request
     */
    public function handle_delete_summary() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            // Check capabilities
            if (!current_user_can('edit_posts')) {
                wp_send_json_error(array('message' => 'Insufficient permissions'));
                return;
            }

            $url = esc_url_raw($_POST['url'] ?? '');
            $post_id = absint($_POST['post_id'] ?? 0);

            if (empty($url) && $post_id <= 0) {
                wp_send_json_error(array('message' => 'URL or post ID is required'));
                return;
            }

            // Delete summary from database
            $deleted = SLMM_Page_Summary_Manager::get_instance()->delete_summary($url);

            if ($deleted) {
                $response_data = array('message' => 'Summary deleted successfully');
                
                // Add COMPLETE node_data for surgical update if post_id is available (same as difficulty/importance handlers)
                if ($post_id > 0) {
                    $fresh_post = get_post($post_id);
                    if ($fresh_post) {
                        $response_data['node_data'] = array(
                            'id' => $post_id,
                            'name' => $fresh_post->post_title,
                            'post_status' => $fresh_post->post_status,
                            'post_type' => $fresh_post->post_type,
                            'permalink' => get_permalink($post_id),
                            'edit_url' => get_edit_post_link($post_id),
                            'post_date' => $fresh_post->post_date,
                            'post_modified' => $fresh_post->post_modified,
                            'difficulty_level' => get_post_meta($post_id, '_slmm_difficulty_level', true) ?: 'easy',
                            'importance_rating' => get_post_meta($post_id, '_slmm_importance_rating', true) ?: '1',
                            'target_keyword' => get_post_meta($post_id, '_slmm_target_keyword', true) ?: '',
                            'is_completed' => (get_post_meta($post_id, 'slmm_page_completion_status', true) === 'completed'),
                            'has_summary' => false  // No summary after deletion
                        );
                    }
                }
                
                wp_send_json_success($response_data);
            } else {
                wp_send_json_error(array('message' => 'Failed to delete summary or summary not found'));
            }

        } catch (Exception $e) {
            error_log('SLMM Delete Summary Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Failed to delete summary'));
        }
    }

    /**
     * Process single page for batch operation
     */
    private function process_single_page_for_batch($url, $post_id, $settings, $force_regenerate) {
        try {
            // Get content
            $content_result = $this->get_page_content($url, $post_id);
            if (!$content_result['success']) {
                return array(
                    'url' => $url,
                    'success' => false,
                    'error' => $content_result['error']
                );
            }

            $content = $content_result['content'];
            $content_hash = SLMM_Page_Summary_Manager::get_instance()->generate_content_hash($content);

            // Check existing summary
            if (!$force_regenerate) {
                $existing_summary = SLMM_Page_Summary_Manager::get_instance()->get_summary($url, $content_hash);
                if ($existing_summary) {
                    return array(
                        'url' => $url,
                        'success' => true,
                        'summary' => $existing_summary->summary,
                        'from_cache' => true
                    );
                }
            }

            // Generate AI summary
            $ai_response = $this->generate_ai_summary($content, $settings);
            if (!$ai_response['success']) {
                return array(
                    'url' => $url,
                    'success' => false,
                    'error' => $ai_response['error']
                );
            }

            // Save summary
            SLMM_Page_Summary_Manager::get_instance()->save_summary(
                $url,
                $content_hash,
                $ai_response['summary'],
                $settings['prompt'],
                $settings['model'],
                $settings['provider']
            );

            return array(
                'url' => $url,
                'success' => true,
                'summary' => $ai_response['summary'],
                'from_cache' => false
            );

        } catch (Exception $e) {
            return array(
                'url' => $url,
                'success' => false,
                'error' => 'Processing failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Get page content for summarization
     */
    private function get_page_content($url, $post_id = 0) {
        error_log('[SLMM Page Summarization] Getting content for URL: ' . $url . ' with post_id: ' . $post_id);
        
        // Try to get content from post ID first
        if ($post_id > 0) {
            $post = get_post($post_id);
            if ($post && $post->post_status === 'publish') {
                $content = $post->post_content;
                if (!empty($content)) {
                    error_log('[SLMM Page Summarization] Found content via post_id: ' . strlen($content) . ' characters');
                    return array(
                        'success' => true,
                        'content' => wp_strip_all_tags($content),
                        'source' => 'post_content'
                    );
                }
            }
        }
        
        // Try to find post by URL path if no post_id provided
        if ($post_id <= 0) {
            $post_id = $this->get_post_id_from_url($url);
            if ($post_id > 0) {
                error_log('[SLMM Page Summarization] Found post_id from URL: ' . $post_id);
                $post = get_post($post_id);
                if ($post && $post->post_status === 'publish') {
                    $content = $post->post_content;
                    if (!empty($content)) {
                        error_log('[SLMM Page Summarization] Found content via URL lookup: ' . strlen($content) . ' characters');
                        return array(
                            'success' => true,
                            'content' => wp_strip_all_tags($content),
                            'source' => 'url_to_post_content'
                        );
                    }
                }
            }
        }

        // Use URL renderer handler for content fetching
        error_log('[SLMM Page Summarization] Attempting URL fetch for: ' . $url);
        if (class_exists('SLMM_URL_Renderer_Handler')) {
            $renderer = new SLMM_URL_Renderer_Handler();
            // Use reflection to access private method if needed, or implement content fetching
            $content = $this->fetch_url_content($url);
            
            if (!empty($content)) {
                error_log('[SLMM Page Summarization] Found content via URL fetch: ' . strlen($content) . ' characters');
                return array(
                    'success' => true,
                    'content' => wp_strip_all_tags($content),
                    'source' => 'url_fetch'
                );
            } else {
                error_log('[SLMM Page Summarization] URL fetch returned empty content');
            }
        } else {
            error_log('[SLMM Page Summarization] SLMM_URL_Renderer_Handler class not found');
        }

        error_log('[SLMM Page Summarization] All content retrieval methods failed for: ' . $url);
        return array(
            'success' => false,
            'error' => 'Failed to retrieve page content'
        );
    }

    /**
     * Fetch content from URL with robust validation (mirrors URL renderer)
     */
    private function fetch_url_content($url) {
        // Validate URL security
        $validation_result = $this->validate_url_security($url);
        if (!$validation_result['valid']) {
            return '';
        }

        $args = array(
            'timeout' => 30,
            'user-agent' => 'SLMM Page Summarizer/1.0 (WordPress)',
            'headers' => array(
                'Accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language' => 'en-US,en;q=0.5',
            ),
            'sslverify' => true,
            'redirection' => 5
        );

        $response = wp_remote_get($url, $args);

        if (is_wp_error($response)) {
            return '';
        }

        // Check response code
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code >= 400) {
            return '';
        }

        $content_type = wp_remote_retrieve_header($response, 'content-type');
        $body = wp_remote_retrieve_body($response);
        
        // Check content size (1MB limit)
        if (strlen($body) > 1048576) {
            return '';
        }

        // Check if content is HTML
        if (strpos(strtolower($content_type), 'text/html') === false) {
            return '';
        }
        
        // Extract text content from HTML
        $dom = new DOMDocument();
        @$dom->loadHTML($body);
        
        // Remove script and style elements
        $xpath = new DOMXPath($dom);
        $nodes = $xpath->query('//script | //style');
        foreach ($nodes as $node) {
            $node->parentNode->removeChild($node);
        }
        
        return trim($dom->textContent);
    }

    /**
     * Validate URL security (mirrors URL renderer security)
     */
    private function validate_url_security($url) {
        // Parse URL
        $parsed = parse_url($url);
        if ($parsed === false) {
            return array('valid' => false);
        }
        
        // Check required components
        if (!isset($parsed['scheme']) || !isset($parsed['host'])) {
            return array('valid' => false);
        }
        
        // Check allowed protocols
        if (!in_array(strtolower($parsed['scheme']), ['http', 'https'])) {
            return array('valid' => false);
        }
        
        // Check for localhost/private IPs (basic security)
        $host = strtolower($parsed['host']);
        $private_hosts = ['localhost', '127.0.0.1', '0.0.0.0'];
        if (in_array($host, $private_hosts)) {
            // Allow localhost URLs in development mode
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('[SLMM Page Summarization] Allowing localhost URL in development mode: ' . $url);
                return array('valid' => true);
            }
            return array('valid' => false);
        }
        
        // Check for private IP ranges
        $ip = gethostbyname($host);
        if ($ip !== $host && filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
            return array('valid' => false);
        }
        
        return array('valid' => true);
    }
    
    /**
     * Get post ID from URL path
     */
    private function get_post_id_from_url($url) {
        // Parse URL to get path
        $parsed = parse_url($url);
        if (!isset($parsed['path'])) {
            return 0;
        }
        
        $path = trim($parsed['path'], '/');
        if (empty($path)) {
            return 0;
        }
        
        // Try to get post by slug/path
        $post = get_page_by_path($path, OBJECT, array('post', 'page'));
        if ($post && $post->post_status === 'publish') {
            return $post->ID;
        }
        
        // Try to get post by slug only (remove trailing path parts)
        $path_parts = explode('/', $path);
        $slug = end($path_parts);
        
        if ($slug !== $path) {
            $post = get_page_by_path($slug, OBJECT, array('post', 'page'));
            if ($post && $post->post_status === 'publish') {
                return $post->ID;
            }
        }
        
        return 0;
    }

    /**
     * Make direct API call like working system
     */
    private function make_direct_api_call($prompt, $settings) {
        error_log('SLMM API Call Debug - Provider: ' . $settings['provider']);
        error_log('SLMM API Call Debug - Model: ' . $settings['model']);
        error_log('SLMM API Call Debug - Prompt length: ' . strlen($prompt));
        
        if ($settings['provider'] === 'openrouter') {
            // Use OpenRouter
            require_once plugin_dir_path(__FILE__) . '../ai-integration/openrouter-integration.php';
            $openrouter = new SLMM_OpenRouter_Integration();
            
            if (!$openrouter->is_configured()) {
                return new WP_Error('api_error', 'OpenRouter API key is not configured');
            }
            
            $result = $openrouter->generate_content($prompt, $settings['model']);
            
            if (is_wp_error($result)) {
                return new WP_Error('api_error', 'Error calling OpenRouter API: ' . $result->get_error_message());
            }
            
            return $result;
        } else {
            // Use OpenAI - direct API call like working system
            $options = get_option('chatgpt_generator_options', array());
            $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
            
            if (empty($api_key)) {
                return new WP_Error('api_error', 'OpenAI API key is not configured');
            }

            // Make API call to OpenAI
            $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json',
                ),
                'body' => json_encode(array(
                    'model' => $settings['model'],
                    'messages' => array(
                        array('role' => 'user', 'content' => $prompt)
                    ),
                    'max_tokens' => $settings['max_tokens'],
                    'temperature' => $settings['temperature'],
                )),
                'timeout' => 60,
            ));

            if (is_wp_error($response)) {
                return new WP_Error('api_error', 'Error calling OpenAI API: ' . $response->get_error_message());
            }

            $body = json_decode(wp_remote_retrieve_body($response), true);

            // Debug the actual response
            error_log('SLMM OpenAI Response Debug: ' . print_r($body, true));
            error_log('SLMM OpenAI Response Code: ' . wp_remote_retrieve_response_code($response));

            if (isset($body['choices'][0]['message']['content'])) {
                return $body['choices'][0]['message']['content'];
            } else {
                // More detailed error message
                $error_details = '';
                if (isset($body['error'])) {
                    $error_details = ' - ' . $body['error']['message'] ?? 'Unknown error';
                }
                return new WP_Error('api_error', 'Unexpected response from OpenAI API' . $error_details);
            }
        }
    }

    /**
     * Get page summarization settings
     */
    private function get_summarization_settings() {
        $options = get_option('chatgpt_generator_options', array());
        $openrouter_key = $options['openrouter_api_key'] ?? '';
        $anthropic_key = get_option('slmm_anthropic_api_key', '');
        
        $openai_key = $options['openai_api_key'] ?? '';
        
        // Get summarization-specific settings or use defaults
        $summarization_settings = get_option('slmm_page_summarization_settings', array());
        
        $provider = $summarization_settings['provider'] ?? $options['ai_provider'] ?? 'openai';
        $model = $summarization_settings['model'] ?? $options['model'] ?? 'gpt-4';
        $prompt = $summarization_settings['prompt'] ?? 'What is this page about? Write a 100 word TLDR summary: {content}';
        $temperature = $summarization_settings['temperature'] ?? 0.7;
        $max_tokens = $summarization_settings['max_tokens'] ?? 200;

        // Determine API key based on provider
        $api_key = '';
        switch ($provider) {
            case 'openrouter':
                $api_key = $openrouter_key;
                $model = $summarization_settings['openrouter_model'] ?? 'openai/gpt-4';
                break;
            case 'anthropic':
                $api_key = $anthropic_key;
                $model = $summarization_settings['anthropic_model'] ?? 'claude-3-haiku-20240307';
                break;
            default:
                $api_key = $openai_key;
                break;
        }

        return array(
            'provider' => $provider,
            'api_key' => $api_key,
            'model' => $model,
            'prompt' => $prompt,
            'temperature' => $temperature,
            'max_tokens' => $max_tokens,
            'has_api_key' => !empty($api_key)
        );
    }

    /**
     * Generate AI summary using configured provider
     */
    private function generate_ai_summary($content, $settings) {
        // Limit content length to prevent token overflow
        $max_content_length = 8000; // Conservative limit
        if (strlen($content) > $max_content_length) {
            $content = substr($content, 0, $max_content_length) . '...';
        }

        // Replace {content} token in prompt
        $prompt = str_replace('{content}', $content, $settings['prompt']);

        switch ($settings['provider']) {
            case 'openai':
                return $this->make_openai_request($prompt, $settings);
            case 'openrouter':
                return $this->make_openrouter_request($prompt, $settings);
            case 'anthropic':
                return $this->make_anthropic_request($prompt, $settings);
            default:
                return array('success' => false, 'error' => 'Unknown AI provider');
        }
    }

    /**
     * Make OpenAI API request
     */
    private function make_openai_request($prompt, $settings) {
        $url = 'https://api.openai.com/v1/chat/completions';
        
        $data = array(
            'model' => $settings['model'],
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => absint($settings['max_tokens']),
            'temperature' => floatval($settings['temperature'])
        );

        $response = wp_remote_post($url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $settings['api_key'],
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data),
            'timeout' => 60
        ));

        return $this->process_openai_response($response);
    }

    /**
     * Make OpenRouter API request
     */
    private function make_openrouter_request($prompt, $settings) {
        $url = 'https://openrouter.ai/api/v1/chat/completions';
        
        $data = array(
            'model' => $settings['model'],
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            ),
            'max_tokens' => absint($settings['max_tokens']),
            'temperature' => floatval($settings['temperature'])
        );

        $response = wp_remote_post($url, array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $settings['api_key'],
                'Content-Type' => 'application/json',
                'HTTP-Referer' => home_url()
            ),
            'body' => json_encode($data),
            'timeout' => 60
        ));

        return $this->process_openai_response($response); // Same format as OpenAI
    }

    /**
     * Make Anthropic API request
     */
    private function make_anthropic_request($prompt, $settings) {
        $url = 'https://api.anthropic.com/v1/messages';
        
        $data = array(
            'model' => $settings['model'],
            'max_tokens' => absint($settings['max_tokens']),
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $prompt
                )
            )
        );

        $response = wp_remote_post($url, array(
            'headers' => array(
                'x-api-key' => $settings['api_key'],
                'Content-Type' => 'application/json',
                'anthropic-version' => '2023-06-01'
            ),
            'body' => json_encode($data),
            'timeout' => 60
        ));

        return $this->process_anthropic_response($response);
    }

    /**
     * Process OpenAI/OpenRouter API response
     */
    private function process_openai_response($response) {
        if (is_wp_error($response)) {
            return array('success' => false, 'error' => $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $response_code = wp_remote_retrieve_response_code($response);
        $data = json_decode($body, true);
        
        // Debug the actual response
        error_log('SLMM OpenAI Response Code: ' . $response_code);
        error_log('SLMM OpenAI Response Body: ' . $body);
        error_log('SLMM OpenAI Parsed Data: ' . print_r($data, true));

        if (!$data) {
            return array('success' => false, 'error' => 'Failed to parse JSON response: ' . $body);
        }

        if (!isset($data['choices'][0]['message']['content'])) {
            $error_msg = 'Invalid API response structure';
            if (isset($data['error'])) {
                $error_msg .= ': ' . $data['error']['message'];
            }
            return array('success' => false, 'error' => $error_msg);
        }

        return array('success' => true, 'summary' => trim($data['choices'][0]['message']['content']));
    }

    /**
     * Process Anthropic API response
     */
    private function process_anthropic_response($response) {
        if (is_wp_error($response)) {
            return array('success' => false, 'error' => $response->get_error_message());
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['content'][0]['text'])) {
            return array('success' => false, 'error' => 'Invalid API response');
        }

        return array('success' => true, 'summary' => trim($data['content'][0]['text']));
    }

    /**
     * Ensure the summary table exists
     */
    public function ensure_table_exists() {
        SLMM_Page_Summary_Manager::get_instance()->create_table();
    }

    /**
     * Handle table creation request
     */
    public function handle_create_table() {
        try {
            // Verify nonce (check both 'nonce' and 'security' parameters for compatibility)
            $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
            if (!wp_verify_nonce($nonce, 'slmm_page_summarization_nonce')) {
                wp_send_json_error(array('message' => 'Invalid nonce'));
                return;
            }

            // Check capabilities
            if (!current_user_can('manage_options')) {
                wp_send_json_error(array('message' => 'Insufficient permissions'));
                return;
            }

            // Create table
            SLMM_Page_Summary_Manager::get_instance()->create_table();

            wp_send_json_success(array('message' => 'Table created successfully'));

        } catch (Exception $e) {
            error_log('SLMM Table Creation Error: ' . $e->getMessage());
            wp_send_json_error(array('message' => 'Failed to create table'));
        }
    }
}

// Initialize the handler
SLMM_Page_Summarization_Handler::get_instance();