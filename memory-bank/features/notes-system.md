# Feature: Notes System

## Overview
Project notes functionality with admin bar integration and multi-context support.

## Key Components
- **Admin Bar Integration**: Notes icon in WordPress admin bar
- **Multi-layer Storage**: Primary `wp_usermeta` with backup systems
- **Bricks Builder Support**: Works in visual builder context
- **Visual Indicators**: Purple background when notes exist

## Architecture
- Class: `SLMM_Notes` (singleton pattern)
- Storage: Triple-redundant system for data persistence
- UI: Resizable popup with formatting toolbar
- Security: Nonce verification and user capability checks

## Storage Layers
1. Primary: `wp_usermeta` table (user-specific)
2. Backup: `wp_options` structured storage
3. Backup: Version-independent storage keys
4. Backup: Global backup system

## Settings Integration
- Enable/disable via `chatgpt_generator_options['enable_notes']`
- Default: Enabled for new installations
- Configurable in SLMM Settings → Features → User Experience

## Context Detection
- Admin area: Standard WordPress admin
- Bricks Builder: `?bricks=run` parameter detection
- Frontend: Disabled (admin-only feature)

## Visual States
- Default: Standard admin bar styling
- Has Content: Purple background (#7C3AED) with white text
- Hover: Lighter purple (#9F7AEA)
- Tooltip: Context-aware text based on content state