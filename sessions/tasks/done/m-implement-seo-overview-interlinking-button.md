---
task: m-implement-seo-overview-interlinking-button
branch: feature/seo-overview-interlinking-button
status: pending
created: 2025-09-01
modules: [seo-overview, interlinking-suite, acf-integration]
---

# SEO Overview Interlinking Suite Button

## Problem/Goal
Add a new button in the SEO Overview panel (next to "SILO STRUCTURE") that opens the Interlinking Suite in a new tab and automatically searches for the current page. The button should use the ACF field title if configured, otherwise fall back to the regular page title.

## Success Criteria
- [ ] But<PERSON> placed next to "SILO STRUCTURE" in SEO Overview panel
- [ ] Uses provided SVG icon (interlinking/link network icon)
- [ ] Opens `wp-admin/admin.php?page=slmm-interlinking-suite` in new tab
- [ ] Automatically fills the search bar (`slmm-search-pages`) with current page title
- [ ] Detects and uses ACF title field if configured in interlinking suite settings
- [ ] Falls back to regular page title if no ACF field configured
- [ ] Works within Classic Editor context only
- [ ] Maintains consistent styling with existing SEO Overview buttons

## Context Files
<!-- Added by context-gathering agent or manually -->
- @includes/interlinking/interlinking-suite.php:449   # Search bar element
- @includes/interlinking/interlinking-suite.php:83-91  # ACF settings endpoints
- Button placement location from provided screenshot
- SVG icon provided in task description

## Technical Requirements
**Button Specifications:**
- **SVG Icon:** 
```svg
<svg width="20px" height="20px" viewBox="0 0 0.4 0.4" xmlns="http://www.w3.org/2000/svg" fill="none">
  <path fill="#000000" fill-rule="evenodd" d="M0.156 0C0.139 0 0.125 0.014 0.125 0.031v0.088C0.125 0.136 0.139 0.15 0.156 0.15H0.175v0.025H0.025a0.019 0.019 0 0 0 0 0.038h0.063V0.25H0.056C0.039 0.25 0.025 0.264 0.025 0.281v0.088c0 0.017 0.014 0.031 0.031 0.031h0.088C0.161 0.4 0.175 0.386 0.175 0.369v-0.088C0.175 0.264 0.161 0.25 0.144 0.25H0.125V0.213h0.15V0.25h-0.019C0.239 0.25 0.225 0.264 0.225 0.281v0.088c0 0.017 0.014 0.031 0.031 0.031h0.088c0.017 0 0.031 -0.014 0.031 -0.031v-0.088c0 -0.017 -0.014 -0.031 -0.031 -0.031H0.313V0.213H0.375A0.019 0.019 0 0 0 0.375 0.175H0.213V0.15h0.031C0.261 0.15 0.275 0.136 0.275 0.119v-0.088C0.275 0.014 0.261 0 0.244 0zm0.106 0.288v0.075h0.075v-0.075zm-0.2 0v0.075h0.075v-0.075zm0.175 -0.175v-0.075h-0.075v0.075z" clip-rule="evenodd"/>
</svg>
```
- **Target URL:** `wp-admin/admin.php?page=slmm-interlinking-suite`
- **Search Target:** Element with ID `slmm-search-pages`
- **Placement:** Next to "SILO STRUCTURE" in SEO Overview panel

**Title Detection Logic:**
1. Check if ACF title field is configured globally in interlinking suite settings
2. If configured, attempt to get ACF field value for current page
3. If ACF field exists and has value, use that title
4. Otherwise, use standard WordPress page title
5. Insert title into search bar and trigger any necessary highlighting/filtering

**Classic Editor Only:**
- This feature will ONLY be deployed within the Classic Editor context
- No Gutenberg, Bricks Builder, or other editor compatibility required
- Simplifies implementation to focus on Classic Editor SEO Overview integration
- ACF field availability checking still required
- JavaScript localization for AJAX endpoints still needed

## User Notes
- Button should maintain visual consistency with existing SEO Overview styling
- Must work reliably within Classic Editor context only
- ACF field detection should be robust and not cause errors if ACF is unavailable
- Search functionality should match existing interlinking suite search behavior

## Context Manifest

### How SEO Overview Panel Currently Works

The SEO Overview metabox is a sophisticated WordPress meta box that provides real-time SEO metrics and page structure information. It's implemented as a class `SLMM_SEO_Overview_Meta_Box` in `src/seo_overview_meta_box.php` that registers itself conditionally based on settings and provides a dynamic interface for monitoring SEO elements.

When a user opens the Classic Editor, the SEO Overview metabox loads in the sidebar if enabled in settings (`chatgpt_generator_options['enable_seo_overview']`). The metabox displays four main sections: **SILO STRUCTURE**, **NAVIGATION**, **PRIORITY PAGES**, and **CONTENT/SEO ELEMENTS**. The SILO STRUCTURE section is particularly important for our button placement - it contains keyword, importance (1-5 circles), and difficulty (E/M/H/VH circles) controls that sync bidirectionally with the interlinking suite.

The JavaScript implementation in `src/seo_overview_meta_box.js` uses a module pattern `SEOOverviewMetaBox` that initializes on document ready and sets up real-time content monitoring. The system uses multiple data sources for efficiency: WordPress block editor data via `wp.data.select('core/editor')`, hidden meta fields for Classic Editor compatibility, and aggressive caching with 30-second TTL for performance. The metabox rebuilds its HTML content dynamically via `updateMetaBoxContent()` which constructs section HTML using string concatenation and jQuery DOM manipulation.

For AJAX communication, the metabox uses `slmmSeoOverview` localized data containing `ajaxurl` and `interlinkingNonce` (shared with interlinking suite). The nonce `slmm_interlinking_nonce` is critical as it's used across multiple systems for security. The metabox supports real-time updates triggered by TinyMCE changes, WordPress heartbeat, publish events, and periodic 5-second refreshes.

### How Interlinking Suite Search System Works

The Interlinking Suite (`includes/interlinking/interlinking-suite.php`) contains a comprehensive search system centered around the `slmm-search-pages` input element. When users type in this search field, it triggers real-time filtering of the D3.js canvas visualization using case-insensitive partial matching against node names. The search functionality is implemented with debounced event handlers that prevent performance issues during rapid typing.

The search input is located in the right controls section at line 449 of the interlinking suite file: `<input type="text" id="slmm-search-pages" class="slmm-search-input" placeholder="Search pages..." />`. The search system includes a clear button, keyboard shortcuts for expanding search results (spacebar/enter), and visual highlighting using the `.slmm-search-highlight` class that applies red dashed borders with animation.

For cross-tab integration, the key insight is that we need to pass search parameters via URL and then programmatically populate the search field and trigger the search functionality. The existing pattern shows that the search input responds to `.val()` and `.trigger('input')` jQuery methods, which can be used for automatic population from URL parameters.

### ACF Field Configuration and Detection System

The ACF integration system is centrally managed through settings stored in the WordPress options table under `slmm_acf_integration_settings`. This configuration includes whether ACF is enabled (`enabled` boolean), the field name to use (`field_name` string), validation status (`validated` boolean), and metadata like timestamps and version tracking.

The ACF field detection follows a robust multi-step process implemented in `ajax_validate_acf_field()` (interlinking-suite.php:11743). First, it checks if ACF plugin is active using `function_exists('get_field')`. Then it validates the field exists by querying all ACF field groups via `acf_get_field_groups()` and searching through their fields. Finally, it verifies the field type is suitable for titles (text, textarea, wysiwyg, number, email, url, select) and checks data availability across posts.

For title retrieval, the system uses `get_field($field_name, $post_id)` when ACF is configured and validated. The fallback chain is: ACF field value → WordPress post title → empty string. This pattern is implemented consistently across the interlinking suite, Direct Editor, and related systems. The key AJAX endpoints are `slmm_load_acf_settings` for configuration retrieval and `slmm_get_acf_titles_batch` for bulk title fetching.

The ACF state is managed globally via JavaScript with the `acfState` object that tracks enabled status, field configuration, validation state, and current values. Cross-tab synchronization uses localStorage triggers (`slmm_acf_sync_request`) to notify other tabs when ACF settings change, ensuring consistency across multiple browser tabs.

### Classic Editor Integration and JavaScript Patterns

The Classic Editor integration follows established patterns seen throughout the codebase. Scripts are enqueued conditionally using the `admin_enqueue_scripts` hook with proper hook detection (`post.php`, `post-new.php`). The SEO Overview system demonstrates this pattern in `enqueue_scripts()` method with version-controlled asset loading using `filemtime()` for cache busting.

JavaScript data localization follows the critical pattern documented in `memory-bank/patterns/data-localization.md`: **always localize data structures even if empty**. The SEO Overview uses `wp_localize_script('slmm-seo-overview', 'slmmSeoOverview', array(...))` to provide AJAX URL, checklist status, and the shared interlinking nonce. This ensures JavaScript has access to necessary data structures without conditional failures.

The codebase extensively uses jQuery for DOM manipulation and AJAX requests. The pattern for AJAX requests includes nonce verification, error handling with `wp_send_json_error()`, and success responses with `wp_send_json_success()`. Security follows WordPress standards with `wp_verify_nonce()` and capability checks using `current_user_can('manage_options')` or `current_user_can('edit_posts')`.

For cross-tab communication and URL parameter handling, the codebase shows existing patterns using `window.location.href`, `window.location.origin`, and URL construction. The `window.open(url, '_blank')` pattern is used consistently throughout for opening new tabs, as seen in the lorem detector, structure analyzer, and schema validator implementations.

### Button Implementation and Styling Patterns  

The existing button implementation in the SEO Overview follows specific styling patterns for consistency. The sibling copy button serves as the closest reference for our interlinking button. This button is implemented inline in the JavaScript at line 913 of `seo_overview_meta_box.js` with detailed styling:

```javascript
'<button type="button" class="button button-secondary seo-copy-siblings-btn" ' +
'title="Copy all sibling links for pasting into content" ' +
'style="margin-left: 8px; padding: 2px 4px; min-height: 20px; vertical-align: middle; border-radius: 3px; border: 1px solid #ddd; background: linear-gradient(to bottom, #f9f9f9, #ececec); box-shadow: 0 1px 2px rgba(0,0,0,0.1); display: inline-flex; align-items: center; justify-content: center;">' +
'<svg fill="currentColor" height="14px" width="14px" viewBox="0 0 0.4 0.4" style="display: block;">' +
'<path d="M0.225 0.125V0.075H0.075v0.15h0.05v0.025H0.05V0.05h0.2v0.075ZM0.15 0.15v0.2h0.2V0.15Z"/>' +
'</svg>' +
'</button>'
```

The CSS framework includes WordPress Core UI button standards from `assets/css/slmm-admin.css` that enforces 40px minimum height for all buttons, proper line-height (38px), and display properties for vertical alignment. The SEO Overview specific styling uses section titles with `.section-title` class that includes background color `#f8f9fa`, reduced font size (18px), and proper margins for compact display.

The button placement strategy involves modifying the `getSiloStructureStatus()` function to include a button in the section title area, similar to how the siblings copy button is embedded within the navigation section. The styling should maintain consistency with existing button patterns while using the provided SVG icon with proper sizing (20px width/height as specified in task requirements).

### Technical Reference Details

#### Component Interfaces & Signatures

```php
// SEO Overview Meta Box - Core Methods
class SLMM_SEO_Overview_Meta_Box {
    public function enqueue_scripts($hook)              // Asset loading with wp_localize_script
    public function render_meta_box($post)              // Initial HTML rendering with nonce
    public function get_silo_navigation_ajax()          // AJAX: get_silo_navigation action
}

// ACF Integration - Key Methods  
class SLMM_Interlinking_Suite {
    public function ajax_load_acf_settings()            // AJAX: slmm_load_acf_settings
    public function ajax_get_acf_titles_batch()         // AJAX: slmm_get_acf_titles_batch  
    public function ajax_validate_acf_field()           // AJAX: slmm_validate_acf_field
}
```

#### Data Structures

```javascript
// ACF Settings Structure (from ajax_load_acf_settings)
{
    enabled: boolean,           // ACF integration enabled
    field_name: string,        // ACF field name (e.g., 'seo_h1_heading')
    validated: boolean,        // Field validation status
    field_name_hash: string,   // MD5 hash for change detection
    last_validated: timestamp, // Last validation time
    version: '1.0.0'          // Settings version
}

// Localized Data Structure (slmmSeoOverview)
{
    ajaxurl: string,          // admin_url('admin-ajax.php')
    checklistEnabled: boolean, // From chatgpt_generator_options
    interlinkingNonce: string  // wp_create_nonce('slmm_interlinking_nonce')
}

// Post Meta Structure  
{
    _slmm_importance_rating: string,   // '1'-'5'
    _slmm_difficulty_level: string,    // 'easy'|'medium'|'hard'|'very-hard' 
    _slmm_target_keyword: string       // Target keyword text
}
```

#### Configuration Requirements

```php
// Required WordPress Options
'chatgpt_generator_options' => [
    'enable_seo_overview' => boolean  // Enable/disable SEO Overview metabox
]

'slmm_acf_integration_settings' => [
    'enabled' => boolean,            // ACF integration enabled
    'field_name' => string,         // ACF field name for titles
    'validated' => boolean          // Field validation status
]

// Required Nonces
'slmm_seo_overview_nonce'     // For SEO Overview AJAX requests
'slmm_interlinking_nonce'     // For interlinking suite AJAX (shared)
```

#### File Locations

- **Implementation goes here:** `/src/seo_overview_meta_box.js` (line ~374 SILO STRUCTURE section)
- **Button styling:** Inline styles following existing `.seo-copy-siblings-btn` pattern
- **ACF detection:** Use existing `slmm_load_acf_settings` AJAX endpoint  
- **Title retrieval:** Use existing `slmm_get_acf_titles_batch` AJAX endpoint for current post
- **URL construction:** `admin_url('admin.php?page=slmm-interlinking-suite&search=' + encodeURIComponent(title))`
- **Search population:** Target `#slmm-search-pages` input with `.val(title).trigger('input')`

## Work Log
- [2025-09-01] Created task, need to research SEO Overview integration points and ACF field detection patterns
- [2025-09-01] Completed comprehensive context gathering - ready for implementation