# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with this WordPress SEO Plugin (SLMM SEO Bundle) codebase.

# 🚨 CRITICAL: UNTOUCHABLE FEATURES (ABSOLUTELY PROTECTED)

## ⛔ THESE FEATURES MUST NEVER BE MODIFIED OR "IMPROVED" ⛔

**UNDER NO CIRCUMSTANCES should these features be touched, modified, enhanced, or "improved" in any way. They work PERFECTLY and ANY changes will break crucial functionality that users depend on.**

### 1. HOVER + NUMBER SHORTCUT SYSTEM (MISSION CRITICAL)
**Location**: `includes/interlinking/interlinking-suite.php` around lines 10658-10700
**Functionality**:
- **Importance Shortcuts**: Hover on `slmm-node-rect` + press `1`, `2`, `3`, `4`, `5` to change importance level
- **Difficulty Shortcuts**: Hover on `slmm-node-rect` + press `7`, `8`, `9`, `0` to change difficulty level (easy/medium/hard/very-hard)
- **Global Functions**: `window.slmmChangeImportance` and `window.slmmChangeDifficulty`

**🚨 WARNING**: This system is extremely fragile. Any modifications to keyboard event handling, function names, or event propagation WILL break this functionality.

### 2. EDIT/VIEW KEYBOARD SHORTCUTS (ESSENTIAL)
**Functionality**:
- **Edit Shortcut**: Hover + `E` to edit post/page
- **View Shortcut**: Hover + `V` to view post/page
- **Permalink Generation**: Complex permalink generation system for view functionality

**🚨 WARNING**: These shortcuts depend on precise event handling and DOM structure. DO NOT modify the keyboard event system.

### 3. BULK CHANGE SHORTCUTS (CRITICAL)
**Functionality**:
- **Bulk Change**: `c` and `C` keys for bulk operations
- Must not interfere with hover-based shortcuts

### 4. FIVE-FILTER SYSTEM (USER INTERFACE CORE)
**Visual Interface Elements**:
- **Status Filter**: Color-coded status filtering
- **Importance Filter**: Color-coded importance levels (1-5)
- **Difficulty Filter**: Color-coded difficulty levels with buttons
- **Filter Buttons**: All color-coded filter interface elements
- **Interface Layout**: Current 5-filter layout and visual design

**🚨 WARNING**: This interface has been perfected over time. Any changes to the filter system, colors, or layout will break user workflows.

### 5. ACTIVE TAB DETECTION SYSTEM (CRITICAL FOR POST TYPE CREATION)
**Location**: Multiple locations in `interlinking-suite.php` - lines ~7133, ~10342, ~11084
**Functionality**:
- **Tab Detection**: `const activeTab = $('.slmm-tab-button.active').data('tab');`
- **Post Type Override**: `const targetPostType = activeTab || 'page';`
- **Cross-System Integration**: Ensures bulk creation respects current tab context

**🚨 WARNING**: This system is ESSENTIAL for proper post type creation. Removing it breaks bulk creation functionality across all contexts. DO NOT REMOVE.

## 🔒 PROTECTION PROTOCOL

### BEFORE ANY CHANGES TO INTERLINKING FUNCTIONALITY:
1. **STOP** - Read this section again
2. **VERIFY** - Are you about to modify keyboard shortcuts, event handling, or filtering?
3. **ABORT** - If yes, DO NOT proceed. These features are PERFECT as they are.
4. **DOCUMENT** - If you must make changes elsewhere, document how you will preserve these features

### KEYBOARD EVENT HANDLING PROTECTION:
- **quickbulk-canvas-integration.js**: Must exclude crucial shortcut keys (`1`,`2`,`3`,`4`,`5`,`7`,`8`,`9`,`0`,`c`,`C`,`E`,`V`)
- **interlinking-suite.php**: Keyboard event handlers are UNTOUCHABLE
- **Event Propagation**: DO NOT modify how keyboard events flow through the system

## ☠️ DESTRUCTIVE CLEANUP WARNING (CRITICAL LESSON LEARNED)

**⚠️ NEVER EVER USE NUCLEAR EVENT CLEANUP OPTIONS ⚠️**

### THE FORBIDDEN LINE THAT BROKE EVERYTHING:
```javascript
$(document).off('keydown'); // ☠️ NUCLEAR OPTION - BREAKS ALL KEYBOARD SHORTCUTS
```

**ROOT CAUSE IDENTIFIED**: During memory leak cleanup efforts, the destructive line `$(document).off('keydown');` was used to "clear all keydown handlers", which completely removed ALL keyboard event listeners, breaking:
- Number shortcuts (1,2,3,4,5,7,8,9,0) for importance/difficulty
- Silo shortcut (s)
- Search shortcut (Option+/)
- ALL hover+click functionality

### SURGICAL CLEANUP PROTOCOL (MANDATORY):
```javascript
// ✅ CORRECT - Surgical cleanup preserving critical handlers
$(document).off('keydown.bulk-change-modal');        // Safe - specific namespace
$(document).off('keydown.slmm-move-modal');         // Safe - specific namespace
// PRESERVE keydown.slmm-search-focus - needed for Option+/ shortcut
// PRESERVE click.slmm-shortcuts - needed for hover+click functionality
// PRESERVE keyup.slmm-shortcuts - needed for hover+click functionality

// ❌ WRONG - Nuclear cleanup breaks everything
$(document).off('keydown');     // ☠️ DESTROYS ALL KEYBOARD FUNCTIONALITY
$(document).off('click');       // ☠️ DESTROYS ALL CLICK FUNCTIONALITY
$(document).off();              // ☠️ DESTROYS ALL EVENT HANDLING
```

### MEMORY CLEANUP MUST BE SURGICAL, NOT NUCLEAR:
- **Target specific namespaces** - Use `.off('event.namespace')`
- **Preserve critical handlers** - Comment which ones MUST be preserved
- **Test keyboard shortcuts** - Verify all shortcuts still work after cleanup
- **Never use blanket removal** - `$(document).off('keydown')` is FORBIDDEN

**🚨 THIS LESSON COST HOURS OF DEBUGGING - NEVER REPEAT THIS MISTAKE! 🚨**

### FUNCTION NAME PROTECTION:
- `window.slmmChangeImportance` - NEVER rename or modify
- `window.slmmChangeDifficulty` - NEVER rename or modify
- Any functions related to permalink generation - UNTOUCHABLE

## 🎯 WORKING COMMIT REFERENCES
- **Last Perfect Working State**: Commit `b2efefb77dec9e8e30bcd030db109024df0dc89a`
- **Working Branch**: `working-commit` contains the perfect state
- **Memory Fixes**: Can be cherry-picked from other commits but MUST NOT break these features

---

**🚨 FINAL WARNING: These features work PERFECTLY. Every attempt to "improve" them has broken crucial functionality. They are marked as UNTOUCHABLE for a reason. Respect this boundary.**

# 🎯 CORE PHILOSOPHY

## PRIMARY DIRECTIVE: EFFICIENT, PATTERN-DRIVEN DEVELOPMENT
- **🎯 98% SOLUTION CERTAINTY REQUIRED** - NEVER propose solutions without absolute research confidence
- **🧠 ALWAYS use serena MCP tools for codebase analysis** - This overrides reading entire files
- **📚 REUSE existing architectural patterns** - Study memory-bank/ documentation first
- **🔧 PRESERVE dual-system architecture** - NEVER modify working keyboard shortcuts
- **📝 CLASSIC EDITOR ONLY** - All features must work perfectly in WordPress Classic Editor
- **⚡ MINIMAL viable changes** - Build on existing code rather than rewriting
- **🔍 SYMBOLIC OVER FULL FILE READS** - Use get_symbols_overview and find_symbol first
- **FALLBACK MECHANISMS** - NEVER! Make fallback mechanisms. Just make it work correctly the first time. It is a waste of code.
- **NEVER BREAK EXISTING FUNCTIONALITY** - If it works, don't touch it!
- **NO PLACEHOLDERS** - When creating a fucntion, don't just add the HTML it MUST have the correct code connetced and tested as well. Don't just make placeholders with no functionality.

## 🎯 SOLUTION CERTAINTY PROTOCOL (ABSOLUTE REQUIREMENT)
**BEFORE proposing ANY solution, you MUST achieve 98% real-world confidence:**

### Research Validation Requirements (MANDATORY)
1. **Comprehensive serena analysis** - Use find_symbol, get_symbols_overview, search_for_pattern extensively
2. **Existing pattern verification** - Confirm solution follows established codebase patterns  
3. **Integration point analysis** - Verify compatibility with dual-system architecture
4. **Dependency validation** - Check all required functions/classes exist and work as expected
5. **Real-world testing scenarios** - Mental simulation of solution in production environment
6. **Edge case consideration** - Account for WordPress variations, plugin conflicts, version differences

### Confidence Level Assessment (ENFORCE STRICTLY)
**Rate your confidence before proposing solutions:**
- **98-100%**: Solution thoroughly researched, patterns verified, integration confirmed → PROCEED
- **90-97%**: Additional research required, gaps in understanding → CONTINUE RESEARCH  
- **<90%**: Insufficient knowledge, major unknowns present → REQUEST CLARIFICATION

### Research Documentation Protocol
**Every solution proposal MUST include:**
```markdown
## Solution Confidence: [98-100%]
## Research Conducted:
- [Specific serena tools used and findings]
- [Existing patterns analyzed]
- [Integration points verified]
- [Dependencies confirmed]
## Risks Identified: [None/Low/Medium - if any Medium risks, continue research]
## Testing Approach: [How solution will be validated]
```

## CODE ANALYSIS PRINCIPLES
1. **Start with memory-bank/ documentation** to understand established patterns
2. **Use get_symbols_overview** before reading any source code files
3. **Find existing implementations** with find_symbol and search_for_pattern
4. **Analyze symbol relationships** with find_referencing_symbols
5. **Read full files only as last resort** when symbolic tools insufficient

# 🗂️ CODEBASE ORGANIZATION & FILE MANAGEMENT (TOP PRIORITY)

## 800-LINE MAXIMUM RULE (ABSOLUTE REQUIREMENT)
**EVERY PHP file MUST be under 800 lines - NO EXCEPTIONS**
** THE ONLY file that is except is the interlinking-suite.php file. This file is allowed to exceed 800 lines because it is a core controller file that cannot be split.NEVER SPLIT THIS FILE under ANY CIRCUMSTANCES.**

### Pre-Implementation Size Estimation
**MANDATORY**: Before writing ANY code, you MUST:
1. **Estimate final file size** using serena symbolic analysis of similar files
2. **Plan file architecture** if implementation will exceed 700 lines (100-line buffer)
3. **Create file split strategy** with clear separation of concerns
4. **Document size projections** in serena memory for tracking

### File Size Monitoring During Development
```php
// Check current file line count before adding code
wc -l target-file.php

// If approaching 700 lines, IMMEDIATELY split the file
if (lines > 700) {
    // Create new file with specific functionality
    // Update plugin.php integration
    // Document the architectural decision
}
```

### File Splitting Strategies
**When file approaches 700 lines:**
1. **Extract classes** into separate files in appropriate subdirectories
2. **Separate concerns** - utilities, settings, UI components
3. **Create factory patterns** for complex object creation
4. **Use WordPress autoloading** or explicit require_once patterns

### New File Creation Workflow (MANDATORY PROCESS)
**Every new file MUST follow this exact process:**

1. **Plan file structure** using serena get_symbols_overview of similar files
2. **Estimate implementation size** based on existing patterns
3. **Create file in proper directory** following existing organization
4. **Add to plugin.php** with proper initialization order
5. **Document in serena memory** with architectural decisions
6. **Test integration** with dual-system architecture

### Plugin.php Integration Requirements
**ALL new files MUST be properly integrated:**
```php
// In plugin.php - Add new file includes in logical order
require_once __DIR__ . '/includes/new-feature/class-new-feature.php';

// In slmm-seo-plugin.php - Initialize new classes properly
function slmm_seo_plugin_init() {
    // Existing initialization code...
    
    // Add new feature initialization
    if (class_exists('SLMM_New_Feature')) {
        new SLMM_New_Feature();
    }
}
```

## MANDATORY SERENA MEMORY CREATION (CRITICAL)
**EVERY complex task (3+ implementation steps) MUST create serena memory**

### Context Window Protection Protocol
**BEFORE starting any complex feature implementation:**
1. **Create comprehensive PRD** using serena write_memory
2. **Document file size estimates** and architectural decisions  
3. **Map integration points** with existing codebase
4. **Create implementation roadmap** with step-by-step breakdown
5. **Update memory** after each major implementation milestone

### PRD Documentation Template (MANDATORY)
```markdown
# Feature: [Feature Name] - Implementation PRD
## Date: [YYYY-MM-DD]
## Context: [Why this feature is needed]
## Architecture: [High-level design decisions]
## File Structure: [New files and estimated sizes]
## Integration Points: [How it connects to existing code]
## Implementation Steps: [Detailed breakdown]
## Size Estimates: [File-by-file line count projections]
## Testing Plan: [Validation requirements]
## Plugin.php Changes: [Required modifications]
```

### Serena Memory Naming Convention
**Use structured naming for easy retrieval:**
- `feature-[name]-prd` - Product requirements document
- `feature-[name]-architecture` - Architectural decisions
- `feature-[name]-implementation` - Step-by-step implementation notes
- `feature-[name]-testing` - Testing protocols and results

## FOLDER ORGANIZATION STANDARDS
**Maintain clean, logical directory structure:**
```
includes/
├── settings/           # Settings-related classes (max 800 lines each)
├── ai-integration/     # AI provider integrations (max 800 lines each)  
├── utils/             # Utility functions and helpers
├── features/          # Feature-specific implementations
│   ├── search-replace/    # Complex features in subdirectories
│   ├── lorem-detector/    # Each with multiple organized files
│   └── [new-feature]/     # Follow established patterns
└── interfaces/        # Abstract classes and interfaces
```

## FILE SIZE MONITORING COMMANDS
```bash
# Check all PHP files over 700 lines (warning threshold)
find . -name "*.php" -exec wc -l {} \; | awk '$1 > 700 {print $0}' | sort -nr

# Check files approaching limit (750+ lines)  
find . -name "*.php" -exec wc -l {} \; | awk '$1 > 750 {print "WARNING: " $0}' | sort -nr

# Check files at or over limit (800+ lines)
find . -name "*.php" -exec wc -l {} \; | awk '$1 >= 800 {print "ERROR: " $0}' | sort -nr
```

# 🔬 MANDATORY RESEARCH PROCESS

## PRE-IMPLEMENTATION RESEARCH (CRITICAL - 98% CERTAINTY REQUIRED)
**Before starting ANY new feature, you MUST complete this research process and achieve 98% solution confidence:**

### 1. Architecture Analysis (Use serena MCP - MANDATORY)
- **Check memory-bank/ documentation** for existing patterns and decisions
- **Search for similar implementations** using search_for_pattern extensively
- **Identify reusable code patterns** with find_symbol across the codebase  
- **Understand dual-system architecture** (buttons vs shortcuts) constraints
- **Review authorization system** integration requirements
- **🎯 CONFIDENCE CHECK**: Can you explain exactly how your solution integrates? YES/NO

### 2. WordPress Integration Points (VERIFY ALL)
- **Plugin initialization flow** in slmm-seo-plugin.php - confirm hook order
- **Hook system usage** (admin_enqueue_scripts, plugins_loaded, etc.) - verify timing
- **Settings storage patterns** (chatgpt_generator_options, slmm_gpt_prompts) - check format
- **AJAX endpoint patterns** and nonce verification - confirm security implementation  
- **Capability checks** and authorization system integration - validate permissions
- **🎯 CONFIDENCE CHECK**: Have you verified each integration point exists and works? YES/NO

### 3. AI Integration Analysis (VALIDATE COMPATIBILITY)
- **Multi-provider support** (OpenAI, OpenRouter, Anthropic) - check API compatibility
- **Prompt execution systems** (dual architecture constraints) - verify both systems work
- **Data localization requirements** (slmmGptPromptData structure) - confirm data format
- **API key management** and secure storage patterns - validate security approach
- **🎯 CONFIDENCE CHECK**: Will your solution work with ALL existing AI providers? YES/NO

### 4. Implementation Planning (CERTAINTY VALIDATION)
- **Break down using existing patterns** from memory-bank/patterns/ - confirm pattern match
- **Plan dual-system integration** (if GPT prompts involved) - test both execution paths
- **Consider Bricks Builder compatibility** requirements - verify visual builder works
- **Define memory-bank documentation** updates needed - plan knowledge preservation  
- **🎯 CONFIDENCE CHECK**: Have you mentally tested this solution in production? YES/NO

### 5. Solution Confidence Assessment (MANDATORY BEFORE PROCEEDING)
**STOP HERE and evaluate your research confidence:**
- [ ] All serena tools used extensively (find_symbol, search_for_pattern, get_symbols_overview)
- [ ] Existing patterns thoroughly analyzed and confirmed compatible
- [ ] Integration points verified to exist and function as expected
- [ ] Dependencies validated (all required functions/classes confirmed)
- [ ] Edge cases considered (WordPress versions, plugin conflicts, browser differences)
- [ ] Mental production testing completed with realistic scenarios
- [ ] **OVERALL CONFIDENCE: [__]%** (Must be 98%+ to proceed)

## PARALLEL RESEARCH METHODOLOGY
- **Always use serena tools in parallel** for efficiency
- **Multiple concurrent symbol searches** across different aspects
- **Parallel pattern analysis** using search_for_pattern
- **Concurrent architecture review** across multiple files

# 🛠️ serena MCP TOOL USAGE

## MANDATORY SYMBOLIC APPROACH
**NEVER read entire source files without using symbolic tools first!**

### Primary Workflow
```
1. get_symbols_overview - Understand file structure and top-level symbols
2. find_symbol - Locate specific functions/classes with include_body=false first
3. find_symbol (with include_body=true) - Read only necessary symbol bodies
4. find_referencing_symbols - Understand usage patterns and dependencies
5. search_for_pattern - Find similar implementations or specific patterns
6. Read (full file) - ONLY as absolute last resort
```

### Efficient Pattern Discovery
```javascript
// CORRECT - Symbolic approach
1. get_symbols_overview("includes/ai-integration/openai-integration.php")
2. find_symbol("execute_prompt", relative_path="includes/", substring_matching=true)
3. find_referencing_symbols("executePromptDirectly", relative_path="snippets/chat_gpt_title_and_description_generator_v2_0.php")

// WRONG - Reading entire files first
1. Read("includes/ai-integration/openai-integration.php") // 🚫 INEFFICIENT
```

### Memory Bank Integration
- **Always check memory-bank/** before analyzing code
- **Use read_memory** for established patterns and decisions
- **Write new findings** to memory bank for future sessions
- **Reference existing documentation** in memory-bank/patterns/
- **Follow memory management patterns** from memory-bank/memory-management-patterns.md

## search_for_pattern Usage Patterns
```javascript
// Find GPT prompt implementations
search_for_pattern("executePromptDirectly", restrict_search_to_code_files=true)

// Find authorization patterns
search_for_pattern("slmm_seo_check_visibility", paths_include_glob="*.php")

// Find data localization patterns
search_for_pattern("slmmGptPromptData", paths_include_glob="**/*.php")
```

# 🚨 CRITICAL PROTECTION RULES

## ABSOLUTE KEYBOARD SHORTCUT PROTECTION
**NEVER EVER modify these systems unless explicitly broken:**
- `snippets/chat_gpt_title_and_description_generator_v2_0.php` - Keyboard shortcut system
- `assets/js/slmm-keyboard-shortcuts.js` - Shortcut key bindings
- `executePromptDirectly()` function - Direct prompt execution

**These are mission-critical systems. ANY changes can break keyboard shortcuts.**

## DUAL-SYSTEM ARCHITECTURE (NON-NEGOTIABLE)
- **Button System**: `assets/js/slmm-prompt-execution.js` (DOM-driven)
- **Keyboard Shortcut System**: `snippets/chat_gpt_title_and_description_generator_v2_0.php` (data-driven)
- **NEVER assume these work the same way**
- **ALWAYS test both systems independently**
- **Data localization MUST support both systems**

## DATA LOCALIZATION PROTECTION
**CRITICAL RULE**: Always localize `slmmGptPromptData` regardless of prompt availability:
```php
// CORRECT - Always localize (from memory-bank/patterns/data-localization.md)
wp_localize_script('script', 'slmmGptPromptData', array(
    'prompts' => $prompts ?: array(),
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_execute_gpt_prompt')
));

// WRONG - Conditional localization breaks shortcuts
if (!empty($prompts)) {
    wp_localize_script('script', 'slmmGptPromptData', array('prompts' => $prompts));
}
```

## AUTHORIZATION SYSTEM PROTECTION
- **Legacy super admin**: username `deme` - Maintained for backward compatibility
- **Sophisticated emergency access**: 64-character random tokens like `&slmm_emergency=accessFJNHftqE2u0tTnw9qNWmGRKyamIIs4BTSNvahYglf6lfC3EZvdgcP8SqTO78DUc1`
- **URL format**: `http://localhost:8884/wp-admin/admin.php?page=chatgpt-generator-settings&slmm_emergency=[64-char-token]`
- **Enhanced security**: Requires pre-existing admin authentication + auto-whitelisting + comprehensive logging
- **Authorization check function**: `slmm_seo_check_visibility_authorization()` - Core security

# 🔒 MANDATORY SECURITY PATTERNS (CRITICAL REQUIREMENT)

**EVERY new feature MUST implement ALL security patterns - NO EXCEPTIONS**

**Admin-only environment with explicit user whitelisting:**
- All features restricted to `manage_options` capability
- Additional whitelist layer via `slmm_seo_check_visibility_authorization()`
- API keys acceptable in this admin-only context
- Defense-in-depth remains MANDATORY for plugin integrity

## 📋 Security Implementation Checklist
- [ ] **Nonce verification** for all AJAX/forms (`wp_verify_nonce`)
- [ ] **Capability checks** (`current_user_can('manage_options')`)
- [ ] **Plugin authorization** (`slmm_seo_check_visibility_authorization()`)
- [ ] **Input sanitization** (`sanitize_textarea_field`, etc.)
- [ ] **Output escaping** (`esc_html`, `esc_attr`, `esc_url`)
- [ ] **Prepared statements** for all database queries (`$wpdb->prepare`)

## 📖 Complete Security Documentation
**See [docs/security-patterns.md](docs/security-patterns.md) for:**
- Detailed implementation patterns and code examples
- Security testing protocols and validation functions
- Attack vector protection and documentation requirements
- Phase-by-phase implementation priorities

# 📋 ESSENTIAL DEVELOPMENT PATTERNS

**Plugin Architecture (v4.10.0):**
- Entry point: `plugin.php` → loads `slmm-seo-plugin.php`
- Initialization with visibility check first
- Modular settings in `includes/settings/`
- Provider-specific AI integrations
- Version-controlled asset loading

**Critical Requirements:**
- ALL new files registered in plugin.php
- File size under 800 lines (except interlinking-suite.php)
- Follow established directory patterns
- Use serena memory for complex tasks (3+ steps)

## 📖 Complete Development Documentation
**See [docs/development-patterns.md](docs/development-patterns.md) for:**
- Plugin architecture and initialization patterns
- File registration and dependency management
- Multi-instance support and AJAX integration
- Asset loading and plugin.php integration protocols

### ESSENTIAL SECURITY PATTERNS (ALL MANDATORY)
**Triple Security Layer Required for ALL Features:**
1. Nonce verification (`wp_verify_nonce`)
2. Capability checks (`current_user_can('manage_options')`)
3. Plugin authorization (`slmm_seo_check_visibility_authorization()`)

**Implementation Priority:** Authorization → Nonce → Input sanitization → SQL prepared statements → Output escaping → Testing

# 📋 ESSENTIAL DEVELOPMENT PATTERNS

**Plugin Architecture (v4.10.0):**
- Entry point: `plugin.php` → loads `slmm-seo-plugin.php`
- Initialization with visibility check first
- Modular settings in `includes/settings/`
- Provider-specific AI integrations
- Version-controlled asset loading

**Critical Requirements:**
- ALL new files registered in plugin.php
- File size under 800 lines (except interlinking-suite.php)
- Follow established directory patterns
- Use serena memory for complex tasks (3+ steps)

## 📖 Complete Development Documentation
**See [docs/development-patterns.md](docs/development-patterns.md) for:**
- Plugin architecture and initialization patterns
- File registration and dependency management
- Multi-instance support and AJAX integration
- Asset loading and plugin.php integration protocols

# 🎨 CURRENT STYLING & UI PATTERNS (v4.10.0)

**WordPress Admin Standards:**
- 40px minimum button height with proper vertical alignment
- Dark theme warning boxes with orange dashed borders
- Professional checkbox design with hover states
- Consistent icon system across all tabs

## 📖 Complete UI Documentation
**See [docs/ui-styling-patterns.md](docs/ui-styling-patterns.md) for:**
- Button standardization and alignment patterns
- Dark theme styling and warning box designs
- Professional checkbox and form element styling
- Icon system consistency and tab navigation

# 💾 MEMORY BANK SYSTEM INTEGRATION

**Directory Structure:** `memory-bank/` with decisions, patterns, issues, features, and todos

**Workflow:** Always check existing memories first, read relevant documentation, write new findings

**Serena Requirements:** Create PRD memory for complex tasks (3+ steps), document architectural decisions, estimate file sizes, create implementation roadmaps

## 📖 Complete Memory Bank Documentation
**See [docs/memory-bank-integration.md](docs/memory-bank-integration.md) for:**
- Directory structure and workflow patterns
- Context window protection protocols
- PRD memory templates and naming standards
- Progress tracking and emergency protocols

# 🔍 ENHANCED DEBUGGING & TESTING (v4.10.0)

**Console Debug Commands:** Check dual-system data availability, test shortcut execution, verify authorization

**Development Mode:** Admin bar colorization, environment detection, visual indicators

**Authorization Testing:** Multiple debug access methods, comprehensive system validation

**Dual System Validation:** Independent testing of button and keyboard shortcut systems

## 📖 Complete Debug Documentation
**See [docs/debugging-testing.md](docs/debugging-testing.md) for:**
- Console debug commands and system validation
- Development mode configuration and indicators
- Authorization testing methods and workflows
- Dual-system validation and troubleshooting

# 🔧 CURRENT FEATURES & INTEGRATION POINTS (v4.10.0)

**Advanced Search & Replace:** Database operations with professional UI and AJAX progress indicators

**Lorem Ipsum Detector:** Content scanning with configurable sensitivity and direct remediation links

**Development Mode:** Admin bar colorization and visual indicators for environment identification

**Notes System:** wp_usermeta storage with admin bar integration and Bricks compatibility

**AI Integration:** Multi-provider support (OpenAI, OpenRouter, Anthropic) with unified execution

**Bricks Builder:** Automatic detection and conditional asset loading for visual builder context

## 📖 Complete Features Documentation
**See [docs/current-features.md](docs/current-features.md) for:**
- Detailed feature specifications and capabilities
- Integration architecture and security implementations
- UI components and user interaction patterns
- Cross-system compatibility and builder integrations

# ⚡ SUCCESS CRITERIA

**A successfully implemented feature MUST:**
1. **🎯 ACHIEVE 98% SOLUTION CERTAINTY** - Comprehensive research and validation before proposal (ABSOLUTE TOP PRIORITY)
2. **⚡ MAINTAIN 800-LINE FILE LIMIT** - ALL files under 800 lines, NO EXCEPTIONS  
3. **📋 CREATE SERENA PRD MEMORY** - Comprehensive task documentation before implementation
4. **🔗 INTEGRATE WITH PLUGIN.PHP** - Proper file registration and initialization order  
5. **🧠 USE SERENA MCP TOOLS EFFICIENTLY** - Start with symbolic analysis, avoid full file reads
6. **📚 FOLLOW ESTABLISHED PATTERNS** - Reuse existing architectural decisions from memory-bank/
7. **🔧 PRESERVE DUAL-SYSTEM ARCHITECTURE** - Never break keyboard shortcuts or button systems
8. **🔐 INCLUDE PROPER AUTHORIZATION** - Integration with visibility and security systems
9. **🎨 FOLLOW v4.10.0 STYLING STANDARDS** - 40px buttons, dark theme, professional checkboxes
10. **💾 UPDATE MEMORY BANK** - Document new patterns and architectural decisions
11. **🧪 MAINTAIN WORDPRESS CODING STANDARDS** - Nonces, capability checks, input sanitization

**🏆 QUALITY INDICATORS:** 98% solution certainty + thorough serena research + file size compliance + PRD documentation + proper plugin integration + dual-system compatibility + professional UI consistency + authorization integration + minimal codebase growth

**🚨 FAILURE CONDITIONS:** <98% confidence + inadequate research + oversized files + missing PRD + broken shortcuts + compromised architecture + missing authorization + security violations

**Remember: 98% solution certainty + 800-line file limit + serena PRD creation + plugin.php integration + efficient serena usage + existing pattern reuse + dual-system preservation = successful implementation. NEVER propose solutions without absolute research confidence.**

# 🏗️ PROJECT INFORMATION

**Version:** 4.10.0 | **License:** Proprietary (Massive Organic) | **Min WP:** 5.0 | **Min PHP:** 7.2

**Key Files:** Entry: `/plugin.php` → Main: `/slmm-seo-plugin.php` | Settings: `/includes/settings/` | AI: `/includes/ai-integration/` | Assets: `/assets/` | Docs: `/memory-bank/` & `/assets/docs/`

**Debug Workflow:** Check console → Verify authorization → Test dual systems → Review memory bank → Check Bricks compatibility

---

**FINAL REMINDER:**
🎯 98% solution certainty + serena research + 800-line limit + PRD memories + plugin.php integration + preserve shortcuts + symbolic analysis + dual-system architecture + memory bank documentation + security patterns = ZERO failed implementations. NEVER break existing functionality!

## DO NOR BE A SYCOPHANT! ## - write like a normal professional person.
## Sessions System Behaviors
@CLAUDE.sessions.md
