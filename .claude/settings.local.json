{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(php:*)", "Bash(node:*)", "Bash(grep:*)", "Bash(rg:*)", "mcp__serena__list_dir", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__list_memories", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "mcp__serena__get_symbols_overview", "mcp__serena__think_about_collected_information", "mcp__serena__write_memory", "mcp__serena__read_memory", "Bash(find:*)", "WebFetch(domain:developer.wordpress.org)", "WebFetch(domain:wordpress.org)", "WebFetch(domain:github.com)", "mcp__context7__resolve-library-id", "Bash(git reset:*)", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__think_about_task_adherence", "mcp__serena__find_file", "Bash(awk:*)", "Bash(rm:*)", "mcp__context7__get-library-docs", "WebFetch(domain:core.trac.wordpress.org)", "Bash(git ls-tree:*)", "Bash(ls:*)", "WebFetch(domain:d3js.org)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(mysql:*)", "Bash(wp option get:*)", "mcp__ide__executeCode", "Bash(cp:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git merge:*)", "Bash(git push:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git pull:*)", "Bash(git merge:*)", "Bash(git push:*)", "mcp__serena__replace_symbol_body", "Bash(git branch:*)", "mcp__serena__onboarding", "<PERSON><PERSON>(python3:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(sed:*)", "Bash(wp db query:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./.claude/agents/migrate-debug test-console-example.js:*)", "Bash(git stash:*)", "Bash(./.claude/agents/migrate-debug:*)", "WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:developers.google.com)", "WebFetch(domain:developer.chrome.com)", "Bash(export NODE_OPTIONS=\"--max-old-space-size=8192\")", "<PERSON><PERSON>(claude --resume)", "Bash(NODE_OPTIONS=\"--max-old-space-size=8192\" claude sessions)", "Bash(claude --version)", "Bash(claude migrate-installer:*)", "Bash(/Users/<USER>/.nvm/versions/node/v22.12.0/bin/claude --version)", "Bash(sudo rm:*)", "Bash(echo $PATH)", "Bash(npm:*)", "Bash(sudo chown:*)", "<PERSON><PERSON>(daic)", "Bash(git rm:*)", "Bash(wp option update:*)", "Read(//Users/<USER>/Studio/v4/**)", "Bash(git log:*)", "WebFetch(domain:platform.openai.com)", "Bash(git fetch:*)", "Bash(git config:*)", "WebFetch(domain:openrouter.ai)", "Bash(__NEW_LINE__ echo \"Fixed test API URL\")", "Bash(git show-branch:*)", "WebSearch", "<PERSON><PERSON>(mv:*)", "Bash(/Users/<USER>/Studio/v4/wp-content/plugins/slmm_seo_bundle/.claude/tools/agent.py context-gathering \"Create comprehensive context manifest for automatic interlinking task\" --task-file \"sessions/tasks/h-implement-automatic-interlinking.md\")", "Bash(git cherry-pick:*)", "Bash(ssh:*)", "Bash(nc:*)", "Bash(for port in 2222 2022 22222)", "Bash(do echo \"Testing port $port:\")", "Bash(done)", "Bash(brew install:*)", "Read(//Users/<USER>/**)", "Bash(nvm:*)", "Bash(xargs:*)", "Bash(do echo \"Checking: $dir\")", "Bash(xxd:*)", "Bash(/compile)", "Bash(wp post meta get:*)", "<PERSON><PERSON>(echo:*)", "Bash(/dev/null)", "Bash(./tests/load-testing-tools.sh:*)", "Bash(git rev-parse:*)", "Bash(wp option:*)", "WebFetch(domain:localhost)", "Bash(perl:*)", "Ba<PERSON>(wp:*)"], "deny": []}}