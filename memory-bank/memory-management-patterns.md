# Memory Management Patterns

## Overview
This document outlines the memory management patterns and progressive loading strategies implemented across the SLMM SEO Bundle to prevent memory leaks and ensure stable long-term operation.

## Core Memory Management Principles

### 1. Timeout Tracking Pattern
**Problem**: JavaScript timeouts can accumulate and cause memory leaks in long-running admin sessions.

**Solution**: Track all timeouts in arrays for comprehensive cleanup.

```javascript
// Standard implementation pattern
var ComponentName = {
    timeouts: [], // Track all timeouts for cleanup
    isDestroyed: false, // Prevent operations after cleanup
    
    addTimeout: function(callback, delay) {
        var timeoutId = setTimeout(callback, delay);
        this.timeouts.push(timeoutId);
        return timeoutId;
    },
    
    clearTimeouts: function() {
        this.timeouts.forEach(function(timeoutId) {
            clearTimeout(timeoutId);
        });
        this.timeouts = [];
    }
};
```

**Implementation**: Used in `content-segmentation-simple.js:22-24` and `content-segmentation-simple.js:835-862`

### 2. Component Destruction Pattern
**Problem**: JavaScript components need proper cleanup when no longer needed.

**Solution**: Implement comprehensive destroy methods with multiple cleanup phases.

```javascript
destroy: function() {
    // Phase 1: Mark as destroyed
    this.isDestroyed = true;
    
    // Phase 2: Clear timeouts
    this.clearTimeouts();
    
    // Phase 3: Remove event listeners
    $(document).off('.component-namespace');
    
    // Phase 4: Clear specific timers
    if (this.overlayUpdateTimer) {
        clearTimeout(this.overlayUpdateTimer);
        this.overlayUpdateTimer = null;
    }
    
    // Phase 5: Reset state
    this.currentStage = 0;
    this.overlaysVisible = false;
}
```

**Implementation**: Used in `content-segmentation-simple.js:845-862`

### 3. Recursive Call Prevention
**Problem**: Recursive function calls can cause stack overflow and memory issues.

**Solution**: Use flags to prevent recursive calls and add proper state checks.

```javascript
waitingForTinyMCE: false, // Prevent recursive calls

waitForTinyMCE: function(callback) {
    // PREVENT RECURSIVE CALLS - major memory leak fix
    if (this.waitingForTinyMCE) {
        console.log('SLMM: Already waiting for TinyMCE, skipping recursive call');
        return;
    }
    
    this.waitingForTinyMCE = true;
    
    var timeoutId = setTimeout(() => {
        this.waitingForTinyMCE = false;
        callback();
    }, 200);
    
    this.timeouts.push(timeoutId);
}
```

**Implementation**: Used in `content-segmentation-simple.js:214` and `content-segmentation-simple.js:24`

### 4. Progressive Loading Strategy
**Problem**: Loading large datasets can cause memory spikes and poor performance.

**Solution**: Implement depth-limited loading with lazy expansion.

```javascript
// Depth limitation for progressive loading
max_depth: 5, // Lazy loading depth limit

// Implementation in AJAX calls
data: {
    action: 'slmm_get_tree_data',
    max_depth: 5,  // Prevent excessive initial loading
    include_posts: true,
    cache_bust: Date.now() // Force fresh data when needed
}
```

**Implementation**: Used throughout `interlinking-suite.php` in multiple AJAX endpoints

### 5. Batch Processing Pattern
**Problem**: Processing large numbers of items can cause memory spikes and timeouts.

**Solution**: Implement configurable batch processing with delays between batches.

```javascript
function processBatch(items, batchSize, processFunction) {
    let batchIndex = 0;
    
    const processBatchRecursive = () => {
        const startIndex = batchIndex * batchSize;
        const batch = items.slice(startIndex, startIndex + batchSize);
        
        if (batch.length === 0) return;
        
        batch.forEach(item => processFunction(item));
        
        batchIndex++;
        if (startIndex + batchSize < items.length) {
            setTimeout(processBatchRecursive, 2000); // Delay between batches
        }
    };
    
    processBatchRecursive();
}
```

**Implementation**: Used in `interlinking-suite.php:8305-8322` for bulk operations

### 6. Event Listener Cleanup Pattern
**Problem**: Event listeners can accumulate and cause memory leaks.

**Solution**: Use namespaced events and proper cleanup procedures.

```javascript
// Namespaced event binding
$(document).on('click.component-name', '.selector', handler);

// Proper cleanup
$(document).off('.component-name'); // Remove all namespaced events

// Page unload cleanup
$(window).on('beforeunload', function() {
    ComponentName.destroy();
});
```

**Implementation**: Used in `content-segmentation-simple.js:910`

## Memory Optimization Techniques

### 1. Debouncing Strategy
**Purpose**: Prevent excessive function calls during rapid user interactions.

```javascript
var searchDebounceTimer = null;

function debouncedSearch(query) {
    clearTimeout(searchDebounceTimer);
    searchDebounceTimer = setTimeout(() => {
        performSearch(query);
    }, 300);
}
```

**Implementation**: Used in `interlinking-suite.php:4761-4762` and `content-segmentation-simple.js:57-61`

### 2. Conditional Resource Loading
**Purpose**: Only load resources when actually needed.

```javascript
// Only re-apply overlays if user enabled them
if (self.overlaysVisible) {
    setTimeout(function() {
        console.log('SLMM: Re-applying user-enabled overlays');
        self.updateOverlays(editor);
    }, 200);
}
```

**Implementation**: Used in `content-segmentation-simple.js:47-52`

### 3. Spatial Indexing for Performance
**Purpose**: Use efficient data structures for spatial queries.

```javascript
// D3.js quadtree implementation for O(log n) performance
// Eliminates O(n²) complexity from original implementations
```

**Implementation**: Referenced in `interlinking-suite.php:134-135`

## Debug and Monitoring Patterns

### Memory Usage Monitoring
```javascript
// Debug mode memory tracking
if (window.slmmDebugMode) {
    console.log('Active timeouts:', ComponentName.timeouts.length);
    console.log('Is destroyed:', ComponentName.isDestroyed);
}
```

**Implementation**: Used in `content-segmentation-simple.js:922` and `content-segmentation-simple.js:940-941`

### Automatic Cleanup Validation
```javascript
// Page unload handler for automatic cleanup
$(window).on('beforeunload', function() {
    if (SLMMContentSegmentation && !SLMMContentSegmentation.isDestroyed) {
        SLMMContentSegmentation.destroy();
        console.log('SLMM: Emergency cleanup on page unload');
    }
});
```

**Implementation**: Used in `content-segmentation-simple.js:909-911`

## Implementation Checklist

When implementing new JavaScript components:

- [ ] **Timeout Tracking**: All `setTimeout` calls tracked in arrays
- [ ] **Destroy Method**: Comprehensive cleanup method implemented
- [ ] **Recursive Prevention**: Flags prevent infinite loops
- [ ] **Event Cleanup**: Namespaced events with proper removal
- [ ] **Page Unload**: Automatic cleanup on page exit
- [ ] **Debug Monitoring**: Memory usage tracking in debug mode
- [ ] **Progressive Loading**: Large datasets loaded progressively
- [ ] **Batch Processing**: Bulk operations use configurable batching
- [ ] **Debouncing**: Rapid operations debounced appropriately
- [ ] **State Validation**: Proper state checks before operations

## Performance Metrics

### Before Memory Optimization
- Console spam from excessive logging
- Memory leaks in long-running sessions
- Recursive function calls causing stack overflow
- Unlimited loading causing browser freezes

### After Memory Optimization
- Clean console output with minimal logging
- Stable memory usage over extended periods
- Prevented recursive calls with proper flags
- Progressive loading with depth limits
- Comprehensive cleanup on component destruction

## Related Files
- `assets/js/content-segmentation-simple.js` - Primary implementation
- `includes/interlinking/interlinking-suite.php` - Server-side patterns
- `sessions/tasks/h-fix-memory-usage.md` - Original memory issue task
- Git commits: `7ee3e34`, `625c2e1` - Console logging cleanup

## Future Enhancements
- **Memory Pool Management**: Implement object pooling for frequently created/destroyed objects
- **Lazy Loading**: Further optimize with intersection observer patterns
- **Web Workers**: Move heavy computations to background threads
- **Performance Monitoring**: Add automatic performance metrics collection