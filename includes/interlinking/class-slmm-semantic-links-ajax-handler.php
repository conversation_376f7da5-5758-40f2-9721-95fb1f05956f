<?php
/**
 * SLMM Semantic Links AJAX Handler
 * Handles AJAX requests for semantic link operations
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Interlinking
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_Semantic_Links_AJAX_Handler
 * 
 * Manages AJAX endpoints for semantic link CRUD operations
 * with proper WordPress security and validation
 */
class SLMM_Semantic_Links_AJAX_Handler {
    
    /**
     * Singleton instance
     * @var SLMM_Semantic_Links_AJAX_Handler|null
     */
    private static $instance = null;
    
    /**
     * Database handler instance
     * @var SLMM_Semantic_Links_DB
     */
    private $db;
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_Semantic_Links_AJAX_Handler
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct() {
        $this->db = SLMM_Semantic_Links_DB::get_instance();
        $this->init();
    }
    
    /**
     * Initialize AJAX handler
     */
    private function init() {
        // Register AJAX endpoints
        add_action('wp_ajax_slmm_create_semantic_link', array($this, 'ajax_create_semantic_link'));
        add_action('wp_ajax_slmm_delete_semantic_link', array($this, 'ajax_delete_semantic_link'));
        add_action('wp_ajax_slmm_get_semantic_links', array($this, 'ajax_get_semantic_links'));
        add_action('wp_ajax_slmm_check_semantic_link_exists', array($this, 'ajax_check_semantic_link_exists'));
        add_action('wp_ajax_slmm_get_semantic_link_suggestions', array($this, 'ajax_get_semantic_link_suggestions'));
        add_action('wp_ajax_slmm_batch_create_semantic_links', array($this, 'ajax_batch_create_semantic_links'));
        add_action('wp_ajax_slmm_get_semantic_links_count', array($this, 'ajax_get_semantic_links_count'));
        add_action('wp_ajax_slmm_delete_all_semantic_links', array($this, 'ajax_delete_all_semantic_links'));

        error_log('[SLMM Semantic Links AJAX] Handler initialized with 8 endpoints');
    }
    
    /**
     * Create a new semantic link relationship
     * AJAX endpoint: wp_ajax_slmm_create_semantic_link
     */
    public function ajax_create_semantic_link() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_create_semantic_link')) {
            return;
        }
        
        // Get and validate parameters
        $sender_id = absint($_POST['sender_id'] ?? 0);
        $receiver_id = absint($_POST['receiver_id'] ?? 0);
        
        if (!$sender_id || !$receiver_id) {
            wp_send_json_error('Invalid post IDs provided');
            return;
        }
        
        // Create semantic link
        $result = $this->db->create_semantic_link($sender_id, $receiver_id);

        if ($result === false) {
            wp_send_json_error('Failed to create semantic link relationship');
            return;
        }

        if ($result === 'duplicate') {
            wp_send_json_error('Semantic link already exists between these posts');
            return;
        }

        wp_send_json_success(array(
            'link_id' => $result,
            'sender_id' => $sender_id,
            'receiver_id' => $receiver_id,
            'message' => 'Semantic link created successfully'
        ));
    }
    
    /**
     * Delete a semantic link relationship
     * AJAX endpoint: wp_ajax_slmm_delete_semantic_link
     */
    public function ajax_delete_semantic_link() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_delete_semantic_link')) {
            return;
        }
        
        // Get and validate parameters
        $sender_id = absint($_POST['sender_id'] ?? 0);
        $receiver_id = absint($_POST['receiver_id'] ?? 0);
        
        if (!$sender_id || !$receiver_id) {
            wp_send_json_error('Invalid post IDs provided');
            return;
        }
        
        // Delete semantic link
        $result = $this->db->delete_semantic_link($sender_id, $receiver_id);
        
        if (!$result) {
            wp_send_json_error('Failed to delete semantic link relationship');
            return;
        }
        
        wp_send_json_success(array(
            'sender_id' => $sender_id,
            'receiver_id' => $receiver_id,
            'message' => 'Semantic link deleted successfully'
        ));
    }
    
    /**
     * Get semantic links for a specific sender post
     * AJAX endpoint: wp_ajax_slmm_get_semantic_links
     */
    public function ajax_get_semantic_links() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_get_semantic_links')) {
            return;
        }
        
        // Get and validate parameters
        $sender_id = absint($_POST['sender_id'] ?? 0);
        
        if (!$sender_id) {
            wp_send_json_error('Invalid post ID provided');
            return;
        }
        
        // Get semantic links
        $links = $this->db->get_semantic_links_for_sender($sender_id);
        
        wp_send_json_success(array(
            'sender_id' => $sender_id,
            'semantic_links' => $links,
            'count' => count($links)
        ));
    }
    
    /**
     * Check if a semantic link relationship exists
     * AJAX endpoint: wp_ajax_slmm_check_semantic_link_exists
     */
    public function ajax_check_semantic_link_exists() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_check_semantic_link_exists')) {
            return;
        }
        
        // Get and validate parameters
        $sender_id = absint($_POST['sender_id'] ?? 0);
        $receiver_id = absint($_POST['receiver_id'] ?? 0);
        
        if (!$sender_id || !$receiver_id) {
            wp_send_json_error('Invalid post IDs provided');
            return;
        }
        
        // Check if semantic link exists
        $exists = $this->db->semantic_link_exists($sender_id, $receiver_id);
        
        wp_send_json_success(array(
            'sender_id' => $sender_id,
            'receiver_id' => $receiver_id,
            'exists' => $exists
        ));
    }
    
    /**
     * Get suggested posts for semantic linking
     * AJAX endpoint: wp_ajax_slmm_get_semantic_link_suggestions
     */
    public function ajax_get_semantic_link_suggestions() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_get_semantic_link_suggestions')) {
            return;
        }
        
        // Get and validate parameters
        $sender_id = absint($_POST['sender_id'] ?? 0);
        $search_term = sanitize_text_field($_POST['search_term'] ?? '');
        $limit = absint($_POST['limit'] ?? 20);
        
        if (!$sender_id) {
            wp_send_json_error('Invalid post ID provided');
            return;
        }
        
        // Get suggestions
        $suggestions = $this->db->get_semantic_link_suggestions($sender_id, $search_term, $limit);
        
        wp_send_json_success(array(
            'sender_id' => $sender_id,
            'suggestions' => $suggestions,
            'search_term' => $search_term,
            'count' => count($suggestions)
        ));
    }
    
    /**
     * Create multiple semantic links in a batch
     * AJAX endpoint: wp_ajax_slmm_batch_create_semantic_links
     */
    public function ajax_batch_create_semantic_links() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_batch_create_semantic_links')) {
            return;
        }
        
        // Get and validate parameters
        $sender_id = absint($_POST['sender_id'] ?? 0);
        $receiver_ids = $_POST['receiver_ids'] ?? array();
        
        if (!$sender_id || !is_array($receiver_ids) || empty($receiver_ids)) {
            wp_send_json_error('Invalid parameters provided');
            return;
        }
        
        // Sanitize receiver IDs
        $receiver_ids = array_map('absint', $receiver_ids);
        $receiver_ids = array_filter($receiver_ids); // Remove zero values
        
        if (empty($receiver_ids)) {
            wp_send_json_error('No valid receiver IDs provided');
            return;
        }
        
        $created_links = array();
        $failed_links = array();
        
        // Create each semantic link
        foreach ($receiver_ids as $receiver_id) {
            $result = $this->db->create_semantic_link($sender_id, $receiver_id);
            
            if ($result !== false) {
                $created_links[] = array(
                    'link_id' => $result,
                    'sender_id' => $sender_id,
                    'receiver_id' => $receiver_id
                );
            } else {
                $failed_links[] = array(
                    'sender_id' => $sender_id,
                    'receiver_id' => $receiver_id,
                    'error' => 'Failed to create link'
                );
            }
        }
        
        wp_send_json_success(array(
            'sender_id' => $sender_id,
            'created_links' => $created_links,
            'failed_links' => $failed_links,
            'total_requested' => count($receiver_ids),
            'total_created' => count($created_links),
            'total_failed' => count($failed_links)
        ));
    }
    
    /**
     * Get semantic links count for a post
     * AJAX endpoint: wp_ajax_slmm_get_semantic_links_count
     */
    public function ajax_get_semantic_links_count() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_get_semantic_links_count')) {
            return;
        }
        
        // Get and validate parameters
        $post_id = absint($_POST['post_id'] ?? 0);
        
        if (!$post_id) {
            wp_send_json_error('Invalid post ID provided');
            return;
        }
        
        // Get count
        $count = $this->db->get_semantic_links_count($post_id);
        
        wp_send_json_success(array(
            'post_id' => $post_id,
            'semantic_links_count' => $count
        ));
    }

    /**
     * Delete ALL semantic links for a post (both sent and received)
     * AJAX endpoint: wp_ajax_slmm_delete_all_semantic_links
     */
    public function ajax_delete_all_semantic_links() {
        // Security validation
        if (!$this->validate_ajax_request('slmm_delete_all_semantic_links')) {
            return;
        }

        // Get and validate post ID
        $post_id = absint($_POST['post_id'] ?? 0);

        if (!$post_id) {
            wp_send_json_error('Invalid post ID provided');
            return;
        }

        // Verify the post exists
        $post = get_post($post_id);
        if (!$post) {
            wp_send_json_error('Post not found');
            return;
        }

        // Delete all semantic links for this post
        $result = $this->db->delete_all_semantic_links_for_post($post_id);

        if (!$result) {
            wp_send_json_error('Failed to delete semantic links');
            return;
        }

        // Get updated count (should be 0)
        $updated_count = $this->db->get_semantic_links_count($post_id);

        wp_send_json_success(array(
            'message' => 'All semantic links deleted successfully',
            'post_id' => $post_id,
            'semantic_links_count' => $updated_count
        ));
    }

    /**
     * Validate AJAX request with nonce and capability checks
     *
     * @param string $action AJAX action name
     * @return bool          True if valid, false otherwise
     */
    private function validate_ajax_request($action) {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'slmm_semantic_links_nonce')) {
            wp_send_json_error('Security check failed');
            return false;
        }
        
        // Check user capabilities
        if (!current_user_can('edit_posts')) {
            wp_send_json_error('Insufficient permissions');
            return false;
        }
        
        // Check if user is authorized (using existing SLMM authorization system)
        if (function_exists('slmm_seo_check_visibility_authorization')) {
            if (!slmm_seo_check_visibility_authorization()) {
                wp_send_json_error('Access denied - SLMM authorization required');
                return false;
            }
        }
        
        return true;
    }
}