(function($) {
    'use strict';

    let protectedPhrases = [];

    // Initialize protected words from WordPress
    function initializeProtectedWords() {
        try {
            if (typeof slmmProtectedWords !== 'undefined' && slmmProtectedWords.words) {
                protectedPhrases = slmmProtectedWords.words
                    .filter(word => word && word.trim())
                    .sort((a, b) => b.length - a.length); // Sort by length descending
            }
        } catch (e) {
            console.error('Error initializing protected words:', e);
        }
    }

    // Initialize on load
    initializeProtectedWords();

    // Function to escape special characters in string for regex
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Function to capitalize first letter of a string
    function capitalizeFirst(str) {
        return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
    }

    // Function to capitalize all words in a string (for headings)
    function capitalizeAllWords(str) {
        // Split by spaces while preserving HTML tags
        const parts = str.split(/(<[^>]*>|\s+)/);
        return parts.map(part => {
            // Skip HTML tags and empty strings
            if (part.startsWith('<') || !part.trim()) {
                return part;
            }
            // Capitalize first letter of each word
            return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
        }).join('');
    }

    // Function to apply sentence case - ONLY for titles and headings
    function applySentenceCase(text, isTitle = false) {
        if (!text) return text;

        // Special handling for titles with pipe symbols
        if (isTitle) {
            return text.split('|').map(segment => {
                segment = segment.trim();
                if (!segment) return segment;
                return capitalizeFirst(segment);
            }).join(' | ');
        }

        // For content, we only process headings
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;

        // Only process headings
        tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
            heading.textContent = capitalizeFirst(heading.textContent);
        });

        return tempDiv.innerHTML;
    }

    // Function to clean up extra whitespace and &nbsp;
    function cleanupText(text) {
        return text
            // Remove multiple &nbsp; entities
            .replace(/(&nbsp;[\s]*){2,}/gi, '')
            // Remove single &nbsp; at the end
            .replace(/&nbsp;[\s]*$/gi, '')
            // Remove excessive newlines at the end
            .replace(/[\n\s]+$/g, '\n')
            // Ensure single newline at end if needed
            .trim() + '\n';
    }

    // Function to apply protected words
    function applyProtectedWords(text) {
        // Create a temporary div to work with the HTML content
        // This DOM-based approach ensures we only modify text content and not attributes like href or src
        // This prevents links and image URLs from being affected by protected words replacements
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = text;
        
        // Process only text nodes, not attributes
        function processTextNodes(node) {
            // If it's a text node, apply protected words
            if (node.nodeType === Node.TEXT_NODE) {
                let content = node.textContent;
                protectedPhrases.forEach(phrase => {
                    // Use word boundaries to ensure we only replace whole words/phrases
                    const regex = new RegExp('\\b' + escapeRegExp(phrase) + '\\b', 'gi');
                    content = content.replace(regex, phrase);
                });
                node.textContent = content;
            } 
            // If it's an element, process its child nodes
            else if (node.nodeType === Node.ELEMENT_NODE) {
                // Process all child nodes
                Array.from(node.childNodes).forEach(childNode => {
                    processTextNodes(childNode);
                });
            }
        }
        
        // Start processing from the root
        processTextNodes(tempDiv);
        
        // Return the processed HTML
        return tempDiv.innerHTML;
    }

    // Handle adding new protected words
    function addProtectedWords(words) {
        const spinner = $('#slmm-add-protected-words .spinner');
        const button = $('#slmm-protect-words-btn');
        
        spinner.addClass('is-active');
        button.prop('disabled', true);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'add_protected_words',
                words: words
            },
            success: function(response) {
                if (response.success) {
                    // Update the protected words array
                    protectedPhrases = response.data.words;
                    // Clear the textarea
                    $('#slmm-new-protected-words').val('');
                    // Optional: Show success message
                    alert('Protected words added successfully!');
                } else {
                    alert('Error: ' + (response.data || 'Failed to add protected words'));
                }
            },
            error: function() {
                alert('Error: Failed to add protected words');
            },
            complete: function() {
                spinner.removeClass('is-active');
                button.prop('disabled', false);
            }
        });
    }

    // Handle sentence case button click
    $(document).ready(function() {
        // Add protected words button handler
        $('#slmm-protect-words-btn').on('click', function() {
            const words = $('#slmm-new-protected-words').val().trim();
            if (words) {
                addProtectedWords(words);
            } else {
                alert('Please enter some words to protect');
            }
        });


        $('#sentence-case').on('click', function(e) {
            e.preventDefault();
            
            // Handle title field
            const titleField = $('#titlediv #title');
            if (titleField.length) {
                let titleContent = titleField.val();
                titleContent = applySentenceCase(titleContent, true);
                
                // For plain text title, apply protected words directly
                // Title is plain text so we don't need to worry about HTML attributes here
                let processedTitle = titleContent;
                protectedPhrases.forEach(phrase => {
                    // Use word boundaries to ensure we only replace whole words/phrases
                    const regex = new RegExp('\\b' + escapeRegExp(phrase) + '\\b', 'gi');
                    processedTitle = processedTitle.replace(regex, phrase);
                });
                
                titleContent = processedTitle;
                titleContent = cleanupText(titleContent);
                titleField.val(titleContent);
            }

            const editor = tinymce.get('content');
            if (!editor) return;

            // Switch to text mode
            if (!editor.isHidden()) {
                editor.hide();
            }

            // Get the raw content from textarea
            const textarea = $('#content');
            let content = textarea.val();

            // First apply sentence case
            content = applySentenceCase(content, false);

            // Then apply protected words
            content = applyProtectedWords(content);

            // Clean up any extra whitespace and &nbsp;
            content = cleanupText(content);

            // Update textarea content
            textarea.val(content);

            // Switch back to visual mode
            editor.show();
        });
    });

})(jQuery);
