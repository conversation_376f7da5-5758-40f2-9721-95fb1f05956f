<?php 

if(!defined('ABSPATH')) { die(); }  

add_action('plugins_loaded', function() {

// Display the options page HTML
function chatgpt_generator_options_page_html() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Redirect to the main settings page instead of showing duplicate settings
    $settings_url = admin_url('admin.php?page=chatgpt-generator-settings');
    ?>
    <div class="wrap">
        <h1>ChatGPT Generator Settings</h1>
        <div class="notice notice-info">
            <p><strong>Settings have been moved!</strong> Please use the <a href="<?php echo esc_url($settings_url); ?>">main SLMM SEO settings page</a> to configure your ChatGPT options including model selection.</p>
        </div>
        <p><a href="<?php echo esc_url($settings_url); ?>" class="button button-primary">Go to SLMM SEO Settings</a></p>
    </div>
    <?php
}

// Register and define the settings (keeping for backward compatibility)
function chatgpt_generator_settings_init() {
    // These are now handled in the main settings, but keeping for backward compatibility
    register_setting('chatgpt_generator_options', 'chatgpt_generator_options');
}
add_action('admin_init', 'chatgpt_generator_settings_init');

// Enqueue the scripts
function chatgpt_generator_enqueue_scripts($hook) {
    // Check if we're in a post editor OR Bricks Builder context
    $is_bricks_context = isset($_GET['bricks']) && $_GET['bricks'] === 'run';
    
    if (($hook === 'post.php' || $hook === 'post-new.php') || $is_bricks_context) {
        wp_enqueue_script('jquery');
        
        // Ensure CSS is loaded in all contexts including Bricks
        wp_enqueue_style('slmm-admin-styles', plugins_url('/assets/css/slmm-admin.css', dirname(__FILE__)));

        $options = get_option('chatgpt_generator_options', array());
        $localized_data = array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('chatgpt_generate_content'),
            'api_key' => $options['openai_api_key'] ?? '',
            'openrouter_api_key' => $options['openrouter_api_key'] ?? '',
            'title_prompt' => $options['title_prompt'] ?? '',
            'description_prompt' => $options['description_prompt'] ?? '',
            'business_name' => $options['business_name'] ?? '',
            'phone_number' => $options['phone_number'] ?? '',
            'title_model' => $options['model_for_title'] ?? 'gpt-4o',
            'description_model' => $options['model_for_description'] ?? 'gpt-4o',
            'title_provider' => $options['model_for_title_provider'] ?? 'openai',
            'description_provider' => $options['model_for_description_provider'] ?? 'openai',
            'is_bricks_context' => $is_bricks_context
        );

        wp_add_inline_script('jquery', "
            jQuery(document).ready(function($) {
                // Debug logging utility for ChatGPT Generator
                const debugLog = function(message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.log('ChatGPT Generator', message, data);
                    }
                };
                const debugError = function(message, data) {
                    if (window.SLMM && window.SLMM.debug) {
                        window.SLMM.debug.error('ChatGPT Generator', message, data);
                    }
                };
                
                // Early exit if chatgptGeneratorData is not available
                if (typeof chatgptGeneratorData === 'undefined') {
                    debugLog('chatgptGeneratorData not available, skipping ChatGPT Generator initialization');
                    return;
                }
                
                // Use global data if local data is not available (for Bricks compatibility)
                if (typeof slmmGptPromptData === 'undefined' && typeof slmmGptPromptDataGlobal !== 'undefined') {
                    debugLog('Using slmmGptPromptDataGlobal as fallback');
                    window.slmmGptPromptData = slmmGptPromptDataGlobal;
                }
                
                if (typeof slmmGptPromptData !== 'undefined') {
                    debugLog('slmmGptPromptData loaded', slmmGptPromptData);
                } else {
                    debugError('No prompt data available at all!');
                }
                debugLog('TinyMCE available: ' + (typeof tinyMCE !== 'undefined'));
                if (typeof tinyMCE !== 'undefined') {
                    debugLog('TinyMCE editors available', Object.keys(tinyMCE.editors));
                }
                
                // Force inject CSS for Bricks Builder context to ensure tooltips work
                if (chatgptGeneratorData.is_bricks_context) {
                    var bricksCss = `
                        <style id=\"slmm-bricks-override\">
                        /* SLMM Tooltip styles for Bricks Builder */
                        .slmm-tooltip {
                            position: relative !important;
                            display: inline-block !important;
                        }
                        
                        .slmm-tooltip .slmm-tooltip-text {
                            visibility: hidden !important;
                            width: 200px !important;
                            background-color: #333 !important;
                            color: #fff !important;
                            text-align: center !important;
                            border-radius: 6px !important;
                            padding: 8px !important;
                            position: absolute !important;
                            z-index: 999999 !important;
                            bottom: 125% !important;
                            left: 50% !important;
                            margin-left: -100px !important;
                            opacity: 0 !important;
                            transition: opacity 0.3s !important;
                            font-size: 12px !important;
                            line-height: 1.3 !important;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.2) !important;
                        }
                        
                        .slmm-tooltip .slmm-tooltip-text::after {
                            content: \"\" !important;
                            position: absolute !important;
                            top: 100% !important;
                            left: 50% !important;
                            margin-left: -5px !important;
                            border-width: 5px !important;
                            border-style: solid !important;
                            border-color: #333 transparent transparent transparent !important;
                        }
                        
                        .slmm-tooltip:hover .slmm-tooltip-text {
                            visibility: visible !important;
                            opacity: 1 !important;
                        }
                        
                        /* GPT Notification styles for Bricks */
                        #slmm-gpt-notification-container {
                            position: fixed !important;
                            top: 50% !important;
                            left: 50% !important;
                            transform: translate(-50%, -50%) !important;
                            z-index: 9999999 !important;
                            max-width: 400px !important;
                            width: 90% !important;
                            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif !important;
                            pointer-events: none !important;
                            visibility: hidden !important;
                            opacity: 0 !important;
                            transition: visibility 0.2s, opacity 0.2s !important;
                        }
                        
                        #slmm-gpt-notification-container.active {
                            visibility: visible !important;
                            opacity: 1 !important;
                        }
                        
                        .slmm-gpt-notification {
                            background: #7C3AED !important;
                            color: white !important;
                            font-size: 16px !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            text-align: center !important;
                            min-height: 40px !important;
                            padding: 12px 16px !important;
                            border-radius: 8px !important;
                            box-shadow: 0 8px 16px rgba(0,0,0,0.2) !important;
                            border: 2px solid #7C3AED !important;
                            margin: 0 !important;
                        }
                        </style>
                    `;
                    $('head').append(bricksCss);
                    debugLog('Injected Bricks-specific CSS');
                }
                
                // Comprehensive debug summary after 3 seconds
                setTimeout(function() {
                }, 3000);

                function generateContent(prompt, model, provider, successCallback) {
                    $.ajax({
                        url: chatgptGeneratorData.ajax_url,
                        method: 'POST',
                        data: {
                            action: 'slmm_generate_content',
                            security: chatgptGeneratorData.nonce,
                            prompt: prompt,
                            model: model,
                            provider: provider
                        },
                        success: function(response) {
                            debugLog('API Response received', response);
                            if (response.success) {
                                successCallback(response.data);
                            } else {
                                debugError('API Error', response.data);
                                alert('Error generating content: ' + response.data);
                            }
                        },
                        error: function(xhr, status, error) {
                            debugError('AJAX Error: ' + error);
                            debugLog('AJAX Response', xhr.responseText);
                            alert('Error generating content. Please check your API configuration and try again.');
                        }
                    });
                }

                $('#chatgpt-title-generator-button button.generate').on('click', function() {
                    var originalTitle = $('#title').val();
                    var prompt = chatgptGeneratorData.title_prompt.replace('[TOPIC]', originalTitle);
                    debugLog('Title Prompt prepared', prompt);

                    generateContent(prompt, chatgptGeneratorData.title_model, chatgptGeneratorData.title_provider, function(generatedTitle) {
                        var businessName = chatgptGeneratorData.business_name;
                        var phoneNumber = chatgptGeneratorData.phone_number;
                        var modifiedTitle = originalTitle + ' | ' + businessName + ' | ' + phoneNumber + ' | ' + generatedTitle.replace(originalTitle + ' | ', '');
                        $('#title').val(modifiedTitle);
                    });
                });

                $('#chatgpt-description-generator-button button.generate').on('click', function() {
                    var seoDescription = $('#seopress_titles_desc_meta').val();
                    var prompt = chatgptGeneratorData.description_prompt.replace('{INSERT}', seoDescription);
                    debugLog('Description Prompt prepared', prompt);

                    generateContent(prompt, chatgptGeneratorData.description_model, chatgptGeneratorData.description_provider, function(generatedDescription) {
                        $('#seopress_titles_desc_meta').val(generatedDescription);
                    });
                });

                $('#chatgpt-title-generator-button button.auto-populate-titles').on('click', function() {
                    var titleContent = $('#title').val();
                    $('#seopress_titles_title_meta').val(titleContent);
                    $('#seopress_social_fb_title_meta').val(titleContent);
                    $('#seopress_social_twitter_title_meta').val(titleContent);
                });

                $('#chatgpt-description-generator-button button.auto-populate-descriptions').on('click', function() {
                    var descriptionContent = $('#seopress_titles_desc_meta').val();
                    $('#seopress_social_fb_desc_meta').val(descriptionContent);
                    $('#seopress_social_twitter_desc_meta').val(descriptionContent);
                });

                $('#chatgpt-description-generator-button button.prefill-from-clipboard').on('click', function() {
                    navigator.clipboard.readText()
                        .then(function(clipboardText) {
                            // Fill all three description fields with clipboard content
                            $('#seopress_titles_desc_meta').val(clipboardText);
                            $('#seopress_social_fb_desc_meta').val(clipboardText);
                            $('#seopress_social_twitter_desc_meta').val(clipboardText);
                        })
                        .catch(function(err) {
                            debugError('Could not read clipboard contents', err);
                            alert('Failed to read from clipboard. Please check browser permissions.');
                        });
                });
            });
        ");

        wp_localize_script('jquery', 'chatgptGeneratorData', $localized_data);
    }
}
add_action('admin_enqueue_scripts', 'chatgpt_generator_enqueue_scripts');

// Handle AJAX request for generating Vs Snippet
function chatgpt_generate_vs_snippet() {
    // Verify nonce for security
    check_ajax_referer('chatgpt_generate_vs_snippet', 'security');

    // Check user capabilities
    if (!current_user_can('edit_posts')) {
        wp_send_json_error('Unauthorized', 403);
    }

    // Get the prompt from the AJAX request
    $prompt = isset($_POST['prompt']) ? sanitize_text_field($_POST['prompt']) : '';

    if (empty($prompt)) {
        wp_send_json_error('Prompt is empty', 400);
    }

    // Get the API key and model from the options
    $options = get_option('chatgpt_generator_options', array());
    $api_key = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
    $vs_snippet_model = get_option('chatgpt_vs_snippet_model', 'gpt-3.5-turbo');

    if (empty($api_key)) {
        wp_send_json_error('API key is missing', 500);
    }

    // Prepare the API request
    $data = array(
        'model' => $vs_snippet_model,
        'messages' => array(
            array(
                'role' => 'user',
                'content' => $prompt
            )
        ),
        'max_tokens' => 1000,
        'n' => 1,
        'stop' => null,
        'temperature' => 0.7
    );

    $args = array(
        'headers' => array(
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $api_key
        ),
        'body' => wp_json_encode($data),
        'timeout' => 60
    );

    $response = wp_remote_post('https://api.openai.com/v1/chat/completions', $args);

    if (is_wp_error($response)) {
        wp_send_json_error($response->get_error_message(), 500);
    } else {
        $body = wp_remote_retrieve_body($response);
        $result = json_decode($body, true);

        if (isset($result['choices'][0]['message']['content'])) {
            $generatedVsSnippet = trim($result['choices'][0]['message']['content']);
            wp_send_json_success($generatedVsSnippet);
        } else {
            wp_send_json_error('Unexpected API response structure', 500);
        }
    }
}
add_action('wp_ajax_chatgpt_generate_vs_snippet', 'chatgpt_generate_vs_snippet');

// Keep the keyboard shortcuts and other functionality
add_action('admin_footer', function() {
    ?>
    <script type="text/javascript">
    jQuery(document).ready(function($) {
        // Wait for TinyMCE to be fully initialized
        if (typeof tinyMCE !== 'undefined') {
            $(document).on('tinymce-editor-setup', function(event, editor) {
                // Add keyboard shortcuts to TinyMCE
                editor.on('init', function() {
                    setupTinyMCEKeyboardShortcuts(editor);
                    updateExistingMenus();
                });
            });
            
            // Also handle already initialized editors
            if (tinyMCE.activeEditor && tinyMCE.activeEditor.initialized) {
                setupTinyMCEKeyboardShortcuts(tinyMCE.activeEditor);
                updateExistingMenus();
            }
            
            // Special handling for Bricks Builder
            if (chatgptGeneratorData.is_bricks_context) {
                debugLog('Bricks context detected - setting up special handling');
                
                // Poll for Bricks TinyMCE editors
                var pollForBricksEditor = function() {
                    debugLog('Polling for Bricks editors...');
                    var foundEditor = false;
                    
                    if (typeof tinyMCE !== 'undefined') {
                        debugLog('Available editors', Object.keys(tinyMCE.editors));
                        
                        // Look for Bricks editors by name pattern
                        for (var editorId in tinyMCE.editors) {
                            debugLog('Checking editor: ' + editorId);
                            if (editorId.indexOf('brickswpeditor') !== -1) {
                                var editor = tinyMCE.editors[editorId];
                                debugLog('Found potential Bricks editor: ' + editorId + ', initialized: ' + (editor ? editor.initialized : 'null'));
                                if (editor && editor.initialized) {
                                    debugLog('Setting up shortcuts for Bricks editor: ' + editorId);
                                    setupTinyMCEKeyboardShortcuts(editor);
                                    foundEditor = true;
                                }
                            }
                        }
                    } else {
                        debugLog('TinyMCE not available yet');
                    }
                    
                    // Continue polling if no editor found yet
                    if (!foundEditor) {
                        debugLog('No Bricks editor found, continuing to poll...');
                        setTimeout(pollForBricksEditor, 500);
                    } else {
                        debugLog('Bricks editor setup complete');
                    }
                };
                
                // Start polling after a brief delay
                setTimeout(pollForBricksEditor, 1000);
            }
        }
        
        function updateExistingMenus() {
            // Check for any dropdown with the class (supports multiple instances)
            var $dropdowns = $('.slmm-gpt-prompt-dropdown, #slmm-gpt-prompt-dropdown');
            if ($dropdowns.length) {
                $dropdowns.each(function() {
                    var $dropdown = $(this);
                    // Add numbers to the options directly
                    $dropdown.find('option:not(:first)').each(function(index) {
                        var $option = $(this);
                        if (index < 9 && !$option.text().match(/^\d+\.\s/)) {
                            $option.text((index + 1) + '. ' + $option.text());
                        }
                    });
                });
            }
            
            // Monitor for menu appearance
            setTimeout(function() {
                $('.mce-menu-item').each(function() {
                    var $item = $(this);
                    var $textSpan = $item.find('.mce-text');
                    var text = $textSpan.text();
                    
                    // Skip if it's a separator or already numbered
                    if (text === '-' || text.match(/^\d+\.\s/) || text.includes('Use ⌘⌃')) {
                        return;
                    }
                    
                    // Try to find which prompt this is by matching with any dropdown
                    var $anyDropdown = $('.slmm-gpt-prompt-dropdown').first();
                    if (!$anyDropdown.length) {
                        $anyDropdown = $('#slmm-gpt-prompt-dropdown');
                    }
                    
                    if ($anyDropdown.length) {
                        $anyDropdown.find('option:not(:first)').each(function(index) {
                            var optionText = $(this).text().replace(/^\d+\.\s/, ''); // Remove any numbering
                            if (text === optionText && index < 9) {
                                $textSpan.text((index + 1) + '. ' + text);
                                return false; // Break the loop
                            }
                        });
                    }
                });
            }, 100);
        }

        // Setup keyboard shortcuts for the editor
        function setupTinyMCEKeyboardShortcuts(editor) {
            // Debug system: Use SLMM debug logger
            var shortcutDebugLog = function(message, data) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.log('Keyboard Shortcuts', message, data);
                }
            };
            var shortcutDebugError = function(message, data) {
                if (window.SLMM && window.SLMM.debug) {
                    window.SLMM.debug.error('Keyboard Shortcuts', message, data);
                }
            };
            
            shortcutDebugLog('setupTinyMCEKeyboardShortcuts called for editor: ' + (editor ? editor.id : 'null'));
            
            // Debug check for data availability
            if (typeof slmmGptPromptData === 'undefined') {
                shortcutDebugError('slmmGptPromptData is undefined - shortcuts will not work');
                shortcutDebugError('Available global objects: ' + Object.keys(window).join(', '));
                return;
            }
            
            shortcutDebugLog('slmmGptPromptData available - prompts loaded: ' + (slmmGptPromptData.prompts ? slmmGptPromptData.prompts.length : 0));
            
            if (!slmmGptPromptData.prompts) {
                shortcutDebugLog('No prompts available - shortcuts will not be created');
                shortcutDebugLog('slmmGptPromptData.prompts', slmmGptPromptData.prompts);
                return;
            }
            
            // Get available prompts directly from slmmGptPromptData (data-driven approach)
            var availablePrompts = [];
            
            // Use the prompt data directly instead of parsing dropdown (GOLDEN RULE #1)
            for (var index in slmmGptPromptData.prompts) {
                if (slmmGptPromptData.prompts.hasOwnProperty(index)) {
                    availablePrompts.push({
                        index: index,
                        title: slmmGptPromptData.prompts[index].title || 'Prompt ' + (parseInt(index) + 1)
                    });
                }
            }
            
            // Set up keyboard shortcuts for available prompts (max 9)
            var maxShortcuts = Math.min(availablePrompts.length, 9);
            
            for (var i = 0; i < maxShortcuts; i++) {
                (function(promptData, num) {
                    var shortcut = 'meta+ctrl+' + (num + 1);
                    
                    try {
                        editor.addShortcut(shortcut, 'GPT Prompt: ' + promptData.title, function() {
                            executePromptDirectly(promptData.index, editor);
                        });
                    } catch (error) {
                        shortcutDebugError('Error adding shortcut ' + shortcut, error);
                    }
                })(availablePrompts[i], i);
            }
        }
        
        // Function to execute prompt directly by its dropdown index value (UPDATED FOR BRICKS SUPPORT)
        function executePromptDirectly(promptIndex, editor) {
            // Check if data is available first
            if (typeof slmmGptPromptData === 'undefined') {
                alert('GPT Prompt data not available. Please refresh the page and try again.');
                return;
            }
            
            // Get the correct editor - prioritize the passed editor, then find active editor
            var activeEditor = editor;
            if (!activeEditor) {
                // In Bricks context, find the active Bricks editor
                if (chatgptGeneratorData.is_bricks_context && typeof tinyMCE !== 'undefined') {
                    for (var editorId in tinyMCE.editors) {
                        if (editorId.indexOf('brickswpeditor') !== -1) {
                            var bricksEditor = tinyMCE.editors[editorId];
                            if (bricksEditor && bricksEditor.initialized && bricksEditor.hasFocus()) {
                                activeEditor = bricksEditor;
                                break;
                            }
                        }
                    }
                    // If no focused editor, use the first available Bricks editor
                    if (!activeEditor) {
                        for (var editorId in tinyMCE.editors) {
                            if (editorId.indexOf('brickswpeditor') !== -1) {
                                var bricksEditor = tinyMCE.editors[editorId];
                                if (bricksEditor && bricksEditor.initialized) {
                                    activeEditor = bricksEditor;
                                    break;
                                }
                            }
                        }
                    }
                } else if (typeof tinyMCE !== 'undefined' && tinyMCE.activeEditor) {
                    activeEditor = tinyMCE.activeEditor;
                }
            }
            
            debugLog('Active editor for prompt execution: ' + (activeEditor ? activeEditor.id : 'none'));
            
            // Get the selected text
            var selectedText = '';
            if (activeEditor) {
                selectedText = activeEditor.selection.getContent({format: 'text'});
                if (!selectedText) {
                    alert('Please select some text before executing a prompt.');
                    return;
                }
            }
            
            // Get prompt data directly from slmmGptPromptData (data-driven approach)
            var promptData = slmmGptPromptData.prompts[promptIndex];
            if (!promptData) {
                alert('Prompt not found at index: ' + promptIndex);
                debugError('Prompt not found at index: ' + promptIndex + '. Available prompts', slmmGptPromptData.prompts);
                return;
            }
            
            // Show temporary notification
            showTempNotification('Generating content...');
            
            // Make AJAX request using the exact same approach as working commit
            $.ajax({
                url: slmmGptPromptData.ajax_url,
                method: 'POST',
                data: {
                    action: 'slmm_execute_gpt_prompt',
                    nonce: slmmGptPromptData.nonce,
                    prompt_index: promptIndex.toString(),
                    selected_text: selectedText
                },
                success: function(response) {
                    if (response.success) {
                        // Replace selected text with generated content
                        if (activeEditor) {
                            activeEditor.selection.setContent(response.data);
                            activeEditor.focus();
                        }
                        showTempNotification('Content generated successfully!');
                    } else {
                        alert('Error: ' + response.data);
                    }
                },
                error: function() {
                    alert('Network error. Please try again.');
                }
            });
        }
        
        // Notification system for keyboard shortcuts
        function showNotification(message, type = 'success') {
            const notificationId = 'slmm-prompt-notification';
            
            // Remove any existing notification
            $('#' + notificationId).remove();
            
            const colors = {
                'success': '#7C3AED',
                'error': '#7C3AED',
                'warning': '#7C3AED',
                'info': '#7C3AED'
            };
            
            const notification = $(`
                <div id="${notificationId}" style="
                    position: fixed !important;
                    top: 50px !important;
                    right: 20px !important;
                    z-index: 999999 !important;
                    background: ${colors[type]} !important;
                    color: white !important;
                    padding: 12px 20px !important;
                    border-radius: 6px !important;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.2) !important;
                    font-size: 14px !important;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif !important;
                    max-width: 350px !important;
                    opacity: 0 !important;
                    transform: translateX(100%) !important;
                    transition: all 0.3s ease !important;
                    pointer-events: none !important;
                ">${message}</div>
            `);
            
            $('body').append(notification);
            
            // Animate in
            setTimeout(() => {
                notification.css({
                    'opacity': '1',
                    'transform': 'translateX(0)'
                });
            }, 10);
            
            // Auto fade out after 3 seconds
            setTimeout(() => {
                notification.css({
                    'opacity': '0',
                    'transform': 'translateX(100%)'
                });
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
        
        // Temporary notification function that doesn't depend on the TinyMCE plugin
        function showTempNotification(message) {
            var containerId = 'slmm-gpt-notification-container';
            var container = document.getElementById(containerId);
            
            if (!container) {
                container = document.createElement('div');
                container.id = containerId;
                document.body.appendChild(container);
            }

            // Create notification
            var notification = document.createElement('div');
            notification.className = 'slmm-gpt-notification';
            notification.innerHTML = '<span style="margin-right:8px;">✓</span> ' + message;
            container.appendChild(notification);
            
            // Show the container
            container.classList.add('active');
            
            // Hide after 2 seconds
            setTimeout(function() {
                container.classList.remove('active');
                // Remove the notification element after hiding
                setTimeout(function() {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 200);
            }, 2000);
        }
    });
    </script>
    <?php
});

}, 10);