<?php
// File: includes/interlinking/authority-distributor.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Authority Distributor - Advanced PageRank simulation and link authority flow
 * 
 * Handles sophisticated authority distribution calculations using:
 * - Modified PageRank algorithm
 * - Link value weighting
 * - Authority flow analysis
 * - Silo containment optimization
 */
class SLMM_Authority_Distributor {
    
    private $damping_factor = 0.85;
    private $convergence_threshold = 0.001;
    private $max_iterations = 50;
    
    /**
     * Calculate comprehensive authority distribution across silo pages
     * 
     * @param array $silo_data Complete silo structure
     * @param array $options Calculation options
     * @return array Authority distribution results
     */
    public function distribute_authority($silo_data, $options = array()) {
        // Merge with default options
        $options = wp_parse_args($options, array(
            'damping_factor' => $this->damping_factor,
            'max_iterations' => $this->max_iterations,
            'convergence_threshold' => $this->convergence_threshold,
            'weighted_links' => true,
            'external_authority_leak' => 0.05
        ));
        
        if (!isset($silo_data['pages']) || empty($silo_data['pages'])) {
            return array('error' => 'No pages found in silo data');
        }
        
        $pages = $silo_data['pages'];
        $links = isset($silo_data['links']) ? $silo_data['links'] : array();
        
        // Build enhanced link graph with weights
        $link_graph = $this->build_weighted_link_graph($pages, $links, $options);
        
        // Calculate initial authority scores
        $authority_scores = $this->calculate_pagerank($link_graph, $options);
        
        // Apply silo containment adjustments
        $contained_scores = $this->apply_silo_containment($authority_scores, $link_graph, $options);
        
        // Calculate authority flow patterns
        $flow_analysis = $this->analyze_authority_flow($contained_scores, $link_graph);
        
        // Generate distribution recommendations
        $recommendations = $this->generate_authority_recommendations($contained_scores, $flow_analysis);
        
        $results = array(
            'authority_scores' => $contained_scores,
            'flow_analysis' => $flow_analysis,
            'recommendations' => $recommendations,
            'metadata' => array(
                'total_pages' => count($pages),
                'total_links' => count($links),
                'iterations_used' => $flow_analysis['iterations'],
                'convergence_achieved' => $flow_analysis['converged']
            )
        );
        
        error_log('[SLMM Authority Distributor] Authority distribution completed for ' . count($pages) . ' pages');
        
        return $results;
    }
    
    /**
     * Build weighted link graph with advanced link value calculations
     * 
     * @param array $pages
     * @param array $links
     * @param array $options
     * @return array Weighted link graph
     */
    private function build_weighted_link_graph($pages, $links, $options) {
        $graph = array();
        
        // Initialize graph structure
        foreach ($pages as $page_id => $page_data) {
            $graph[$page_id] = array(
                'outbound' => array(),
                'inbound' => array(),
                'page_data' => $page_data,
                'content_quality' => $this->calculate_content_quality($page_data),
                'page_authority_modifier' => 1.0
            );
        }
        
        // Process each link with weight calculation
        foreach ($links as $link) {
            if (!isset($link['from']) || !isset($link['to']) || 
                !isset($graph[$link['from']]) || !isset($graph[$link['to']])) {
                continue;
            }
            
            $from = $link['from'];
            $to = $link['to'];
            
            // Calculate link weight based on multiple factors
            $link_weight = $this->calculate_link_weight($link, $graph[$from], $graph[$to], $options);
            
            // Add weighted link to graph
            $graph[$from]['outbound'][$to] = array(
                'weight' => $link_weight,
                'link_data' => $link
            );
            
            $graph[$to]['inbound'][$from] = array(
                'weight' => $link_weight,
                'link_data' => $link
            );
        }
        
        // Calculate outbound normalization factors
        foreach ($graph as $page_id => &$page_info) {
            $total_outbound_weight = 0;
            foreach ($page_info['outbound'] as $target_id => $link_info) {
                $total_outbound_weight += $link_info['weight'];
            }
            $page_info['total_outbound_weight'] = max(1.0, $total_outbound_weight);
        }
        
        return $graph;
    }
    
    /**
     * Calculate link weight based on contextual factors
     * 
     * @param array $link Link data
     * @param array $source_page Source page data
     * @param array $target_page Target page data
     * @param array $options Calculation options
     * @return float Link weight (0.1 - 2.0)
     */
    private function calculate_link_weight($link, $source_page, $target_page, $options) {
        $base_weight = 1.0;
        
        // Anchor text relevance weight
        if (isset($link['anchor_text']) && !empty($link['anchor_text'])) {
            $anchor_relevance = $this->calculate_anchor_relevance(
                $link['anchor_text'], 
                $target_page['page_data']
            );
            $base_weight *= (0.8 + ($anchor_relevance * 0.4)); // 0.8 - 1.2 multiplier
        }
        
        // Link position weight (higher in content = more value)
        if (isset($link['position'])) {
            $position_weight = $this->calculate_position_weight($link['position']);
            $base_weight *= $position_weight;
        }
        
        // Content context weight
        if (isset($link['context']) && !empty($link['context'])) {
            $context_relevance = $this->calculate_context_relevance(
                $link['context'], 
                $source_page['page_data'], 
                $target_page['page_data']
            );
            $base_weight *= (0.9 + ($context_relevance * 0.2)); // 0.9 - 1.1 multiplier
        }
        
        // Link type weight
        if (isset($link['type'])) {
            $type_weight = $this->get_link_type_weight($link['type']);
            $base_weight *= $type_weight;
        }
        
        // Ensure weight stays within reasonable bounds
        return max(0.1, min(2.0, $base_weight));
    }
    
    /**
     * Calculate content quality score for authority modification
     * 
     * @param array $page_data
     * @return float Quality score (0.5 - 1.5)
     */
    private function calculate_content_quality($page_data) {
        $quality_score = 1.0;
        
        if (!isset($page_data['content'])) {
            return 0.5;
        }
        
        $content = strip_tags($page_data['content']);
        $word_count = str_word_count($content);
        
        // Word count factor
        if ($word_count > 1500) {
            $quality_score += 0.3;
        } elseif ($word_count > 1000) {
            $quality_score += 0.2;
        } elseif ($word_count > 500) {
            $quality_score += 0.1;
        } elseif ($word_count < 300) {
            $quality_score -= 0.2;
        }
        
        // Structure quality (headings, paragraphs)
        if (isset($page_data['content'])) {
            $heading_count = substr_count($page_data['content'], '<h2>') + 
                           substr_count($page_data['content'], '<h3>');
            
            if ($heading_count >= 3) {
                $quality_score += 0.1;
            }
            
            // Paragraph count
            $paragraph_count = substr_count($page_data['content'], '<p>');
            if ($paragraph_count >= 5) {
                $quality_score += 0.1;
            }
        }
        
        return max(0.5, min(1.5, $quality_score));
    }
    
    /**
     * Calculate anchor text relevance to target page
     * 
     * @param string $anchor_text
     * @param array $target_page_data
     * @return float Relevance score (0-1)
     */
    private function calculate_anchor_relevance($anchor_text, $target_page_data) {
        if (empty($anchor_text)) {
            return 0.5; // Neutral for empty anchor
        }
        
        $anchor_lower = strtolower($anchor_text);
        $relevance_score = 0.0;
        
        // Check against page title
        if (isset($target_page_data['title'])) {
            $title_lower = strtolower($target_page_data['title']);
            if (strpos($title_lower, $anchor_lower) !== false || 
                strpos($anchor_lower, $title_lower) !== false) {
                $relevance_score += 0.4;
            }
        }
        
        // Check against target keywords
        if (isset($target_page_data['target_keywords'])) {
            foreach ($target_page_data['target_keywords'] as $keyword) {
                $keyword_lower = strtolower($keyword);
                if (strpos($anchor_lower, $keyword_lower) !== false || 
                    strpos($keyword_lower, $anchor_lower) !== false) {
                    $relevance_score += 0.3;
                    break; // Don't double-count
                }
            }
        }
        
        // Check for exact keyword match
        if (isset($target_page_data['content'])) {
            $content_lower = strtolower(strip_tags($target_page_data['content']));
            if (strpos($content_lower, $anchor_lower) !== false) {
                $relevance_score += 0.3;
            }
        }
        
        return min(1.0, $relevance_score);
    }
    
    /**
     * Calculate link position weight (higher in content = more valuable)
     * 
     * @param string $position Link position in content
     * @return float Position weight (0.7 - 1.3)
     */
    private function calculate_position_weight($position) {
        switch (strtolower($position)) {
            case 'header':
                return 0.8;
            case 'navigation':
                return 0.7;
            case 'content_top':
                return 1.2;
            case 'content_middle':
                return 1.3;
            case 'content_bottom':
                return 1.1;
            case 'sidebar':
                return 0.8;
            case 'footer':
                return 0.7;
            default:
                return 1.0;
        }
    }
    
    /**
     * Get weight multiplier for different link types
     * 
     * @param string $link_type
     * @return float Type weight (0.5 - 1.2)
     */
    private function get_link_type_weight($link_type) {
        switch (strtolower($link_type)) {
            case 'contextual':
                return 1.2;
            case 'navigational':
                return 0.8;
            case 'breadcrumb':
                return 0.7;
            case 'related':
                return 1.0;
            case 'tag':
                return 0.6;
            case 'category':
                return 0.7;
            case 'author':
                return 0.5;
            default:
                return 1.0;
        }
    }
    
    /**
     * Enhanced PageRank calculation with convergence detection
     * 
     * @param array $graph Weighted link graph
     * @param array $options Calculation options
     * @return array Authority scores
     */
    private function calculate_pagerank($graph, $options) {
        $damping = $options['damping_factor'];
        $max_iterations = $options['max_iterations'];
        $threshold = $options['convergence_threshold'];
        
        $page_count = count($graph);
        if ($page_count == 0) {
            return array();
        }
        
        // Initialize scores
        $scores = array();
        $initial_score = 1.0 / $page_count;
        
        foreach ($graph as $page_id => $page_info) {
            $scores[$page_id] = $initial_score * $page_info['page_authority_modifier'];
        }
        
        $iterations = 0;
        $converged = false;
        
        for ($i = 0; $i < $max_iterations; $i++) {
            $new_scores = array();
            $max_change = 0.0;
            
            foreach ($graph as $page_id => $page_info) {
                $rank = (1 - $damping) / $page_count;
                
                // Calculate weighted authority from inbound links
                foreach ($page_info['inbound'] as $source_id => $link_info) {
                    if (isset($graph[$source_id]) && isset($scores[$source_id])) {
                        $source_total_weight = $graph[$source_id]['total_outbound_weight'];
                        $normalized_weight = $link_info['weight'] / $source_total_weight;
                        $rank += $damping * $scores[$source_id] * $normalized_weight;
                    }
                }
                
                // Apply page quality modifier
                $rank *= $page_info['page_authority_modifier'];
                
                $new_scores[$page_id] = $rank;
                
                // Track convergence
                if (isset($scores[$page_id])) {
                    $change = abs($rank - $scores[$page_id]);
                    $max_change = max($max_change, $change);
                }
            }
            
            $scores = $new_scores;
            $iterations = $i + 1;
            
            // Check for convergence
            if ($max_change < $threshold) {
                $converged = true;
                break;
            }
        }
        
        // Store convergence info for analysis
        foreach ($scores as $page_id => &$score) {
            // Normalize scores
            $score = round($score * 100, 4); // Convert to percentage-like scores
        }
        
        error_log('[SLMM Authority Distributor] PageRank converged in ' . $iterations . ' iterations. Max change: ' . $max_change);
        
        return $scores;
    }
    
    /**
     * Apply silo containment to minimize authority leak
     * 
     * @param array $authority_scores
     * @param array $graph
     * @param array $options
     * @return array Adjusted authority scores
     */
    private function apply_silo_containment($authority_scores, $graph, $options) {
        $leak_factor = isset($options['external_authority_leak']) ? 
                      $options['external_authority_leak'] : 0.05;
        
        $contained_scores = $authority_scores;
        
        // Calculate total authority in silo
        $total_authority = array_sum($authority_scores);
        
        // Apply containment adjustments
        foreach ($contained_scores as $page_id => &$score) {
            // Check for external links that might leak authority
            if (isset($graph[$page_id]['outbound'])) {
                $external_outbound = 0;
                $total_outbound = count($graph[$page_id]['outbound']);
                
                // Count external links (simplified - assume any link not in graph is external)
                foreach ($graph[$page_id]['outbound'] as $target_id => $link_info) {
                    if (!isset($graph[$target_id])) {
                        $external_outbound++;
                    }
                }
                
                if ($total_outbound > 0) {
                    $leak_ratio = $external_outbound / $total_outbound;
                    $authority_retained = 1 - ($leak_ratio * $leak_factor);
                    $score *= $authority_retained;
                }
            }
        }
        
        return $contained_scores;
    }
    
    /**
     * Analyze authority flow patterns and identify optimization opportunities
     * 
     * @param array $authority_scores
     * @param array $graph
     * @return array Flow analysis results
     */
    private function analyze_authority_flow($authority_scores, $graph) {
        $analysis = array(
            'top_pages' => array(),
            'authority_gaps' => array(),
            'flow_bottlenecks' => array(),
            'orphaned_pages' => array(),
            'hub_pages' => array(),
            'statistics' => array()
        );
        
        // Sort pages by authority score
        arsort($authority_scores);
        
        // Identify top authority pages
        $analysis['top_pages'] = array_slice($authority_scores, 0, 5, true);
        
        // Calculate statistics
        $scores = array_values($authority_scores);
        $analysis['statistics'] = array(
            'mean_authority' => array_sum($scores) / count($scores),
            'max_authority' => max($scores),
            'min_authority' => min($scores),
            'authority_range' => max($scores) - min($scores),
            'total_pages' => count($authority_scores),
            'iterations' => 50, // Placeholder - should come from calculation
            'converged' => true // Placeholder - should come from calculation
        );
        
        // Find authority gaps (pages with very low scores)
        $mean_authority = $analysis['statistics']['mean_authority'];
        foreach ($authority_scores as $page_id => $score) {
            if ($score < $mean_authority * 0.3) {
                $analysis['authority_gaps'][$page_id] = $score;
            }
        }
        
        // Find orphaned pages (no inbound links)
        foreach ($graph as $page_id => $page_info) {
            if (empty($page_info['inbound'])) {
                $analysis['orphaned_pages'][] = $page_id;
            }
        }
        
        // Find hub pages (many outbound links)
        foreach ($graph as $page_id => $page_info) {
            $outbound_count = count($page_info['outbound']);
            if ($outbound_count >= 5) {
                $analysis['hub_pages'][$page_id] = $outbound_count;
            }
        }
        
        // Find flow bottlenecks (high authority but few outbound links)
        foreach ($authority_scores as $page_id => $score) {
            if ($score > $mean_authority * 1.5) { // High authority page
                $outbound_count = isset($graph[$page_id]) ? count($graph[$page_id]['outbound']) : 0;
                if ($outbound_count < 2) { // But few outbound links
                    $analysis['flow_bottlenecks'][$page_id] = array(
                        'authority' => $score,
                        'outbound_links' => $outbound_count
                    );
                }
            }
        }
        
        return $analysis;
    }
    
    /**
     * Generate authority optimization recommendations
     * 
     * @param array $authority_scores
     * @param array $flow_analysis
     * @return array Recommendations
     */
    private function generate_authority_recommendations($authority_scores, $flow_analysis) {
        $recommendations = array();
        
        // Recommendations for orphaned pages
        if (!empty($flow_analysis['orphaned_pages'])) {
            $recommendations[] = array(
                'type' => 'linking',
                'priority' => 'high',
                'title' => 'Fix Orphaned Pages',
                'description' => count($flow_analysis['orphaned_pages']) . ' pages have no inbound links. Add internal links to these pages.',
                'affected_pages' => $flow_analysis['orphaned_pages'],
                'impact' => 'High'
            );
        }
        
        // Recommendations for authority gaps
        if (!empty($flow_analysis['authority_gaps'])) {
            $recommendations[] = array(
                'type' => 'authority',
                'priority' => 'medium',
                'title' => 'Boost Low-Authority Pages',
                'description' => count($flow_analysis['authority_gaps']) . ' pages have very low authority. Consider linking to them from high-authority pages.',
                'affected_pages' => array_keys($flow_analysis['authority_gaps']),
                'impact' => 'Medium'
            );
        }
        
        // Recommendations for flow bottlenecks
        if (!empty($flow_analysis['flow_bottlenecks'])) {
            $recommendations[] = array(
                'type' => 'distribution',
                'priority' => 'medium',
                'title' => 'Improve Authority Distribution',
                'description' => count($flow_analysis['flow_bottlenecks']) . ' high-authority pages are not distributing their authority effectively.',
                'affected_pages' => array_keys($flow_analysis['flow_bottlenecks']),
                'impact' => 'Medium'
            );
        }
        
        // Hub page optimization
        if (!empty($flow_analysis['hub_pages'])) {
            $recommendations[] = array(
                'type' => 'optimization',
                'priority' => 'low',
                'title' => 'Optimize Hub Pages',
                'description' => count($flow_analysis['hub_pages']) . ' pages act as hubs. Ensure their outbound links are strategic and relevant.',
                'affected_pages' => array_keys($flow_analysis['hub_pages']),
                'impact' => 'Low'
            );
        }
        
        return $recommendations;
    }
    
    /**
     * Calculate context relevance between linking pages
     * 
     * @param string $context Link context/surrounding text
     * @param array $source_page Source page data
     * @param array $target_page Target page data
     * @return float Context relevance (0-1)
     */
    private function calculate_context_relevance($context, $source_page, $target_page) {
        if (empty($context)) {
            return 0.5; // Neutral for no context
        }
        
        $context_lower = strtolower($context);
        $relevance = 0.0;
        
        // Check if context contains target page keywords
        if (isset($target_page['target_keywords'])) {
            foreach ($target_page['target_keywords'] as $keyword) {
                if (strpos($context_lower, strtolower($keyword)) !== false) {
                    $relevance += 0.3;
                }
            }
        }
        
        // Check if context relates to source page topic
        if (isset($source_page['target_keywords'])) {
            foreach ($source_page['target_keywords'] as $keyword) {
                if (strpos($context_lower, strtolower($keyword)) !== false) {
                    $relevance += 0.2;
                }
            }
        }
        
        return min(1.0, $relevance);
    }
}