---
task: h-implement-auto-content-segmentation
branch: feature/auto-segmentation
status: in-progress
created: 2025-01-09
started: 2025-01-09
modules: [content-segmentation, ai-integration, slmm-direct-editor]
---

# Automatic Content Segmentation System

## Problem/Goal
Implement intelligent automatic content segmentation that eliminates the need for manual 3-click marker placement. The system should analyze content structure and automatically divide articles into optimal TOP, MIDDLE, and BOTTOM sections based on content length, semantic structure, and user preferences.

## Success Criteria
- [ ] One-click automatic segmentation with "Auto-Segment" button
- [ ] Configurable percentage splits (default: 33%/67%, customizable to any ratio)
- [ ] AI-driven semantic boundary detection for natural content breaks
- [ ] Content-aware positioning that respects paragraph and heading boundaries
- [ ] Smart fallback system when automatic detection fails
- [ ] User override capability to manually adjust auto-generated segments
- [ ] Performance optimization for large content (>5000 words)
- [ ] Visual feedback during auto-segmentation process
- [ ] Integration with existing manual segmentation system
- [ ] Preserve all current functionality while adding automation

## Current System Analysis

### Existing Infrastructure (Ready to Leverage)
The manual segmentation system already has **all core capabilities** needed for automation:

#### **1. Percentage Calculation Engine** ✅
```javascript
// From assets/js/content-segmentation-simple.js:519+
calculateSectionPositions: function(content) {
    var cleanContent = content.replace(/<[^>]*>/g, '').replace(/&[^;]+;/g, ' ');
    var totalLength = cleanContent.length;
    
    sections.topSection = {
        startPercent: (topStartClean / totalLength) * 100,
        endPercent: (topEndClean / totalLength) * 100
    };
}
```

#### **2. Smart Positioning Logic** ✅
```javascript
// From includes/content-segmentation/class-content-segmentation.php:508+
private function determine_smart_position($content, $markers, $insertion_plan) {
    switch ($insertion_plan['stage']) {
        case 2: // First divider at ~33%
            return intval($content_length * 0.33);
        case 3: // Second divider at ~67%  
            return intval($content_length * 0.67);
    }
}
```

#### **3. Boundary Detection** ✅
```javascript
// From includes/content-segmentation/class-content-segmentation.php:541+
private function find_content_boundary($content, $start, $end, $direction) {
    $boundaries = array('</h1>', '</h2>', '</h3>', '</p>', '</ul>', '</div>');
    // Finds natural content breaks near target positions
}
```

#### **4. Marker Insertion System** ✅
```javascript
// All marker insertion logic already implemented
$this->segmentation->insert_sequential_markers($content, $position);
```

## Technical Requirements

### Auto-Segmentation Approaches

#### **Approach 1: Percentage-Based (Immediate Implementation)**
**Complexity: LOW** - Uses existing `determine_smart_position()` logic

```javascript
autoSegmentByPercentage: function(topPercent, bottomPercent) {
    var editor = this.getActiveEditor();
    var content = editor.getContent();
    var contentLength = content.length;
    
    // Calculate positions
    var topPosition = Math.floor(contentLength * (topPercent / 100));
    var bottomPosition = Math.floor(contentLength * (bottomPercent / 100));
    
    // Find smart boundaries near target positions
    var smartTopPos = this.findSmartBoundary(content, topPosition);
    var smartBottomPos = this.findSmartBoundary(content, bottomPosition);
    
    // Insert all markers automatically
    this.insertAutoMarkers(content, smartTopPos, smartBottomPos);
}
```

#### **Approach 2: Content-Aware Segmentation (Advanced)**
**Complexity: MEDIUM** - Analyzes content structure intelligently

```javascript
autoSegmentByContent: function() {
    var content = this.getActiveEditor().getContent();
    
    // Analyze content structure
    var structure = this.analyzeContentStructure(content);
    
    // Find optimal break points based on:
    // - Heading hierarchy (H1, H2, H3)
    // - Paragraph groupings
    // - Content density
    // - Semantic meaning shifts
    
    var breakpoints = this.findOptimalBreakpoints(structure);
    this.insertAutoMarkers(content, breakpoints.first, breakpoints.second);
}
```

#### **Approach 3: AI-Driven Segmentation (Future Enhancement)**
**Complexity: HIGH** - Uses AI to understand content semantics

```javascript
autoSegmentByAI: function() {
    var content = this.getActiveEditor().getContent();
    
    // Send content to AI for analysis
    $.ajax({
        url: ajaxurl,
        method: 'POST',
        data: {
            action: 'slmm_ai_segment_content',
            content: content,
            nonce: slmmSegmentationData.nonce
        },
        success: function(response) {
            // AI returns optimal segmentation points
            self.insertAutoMarkers(content, response.data.segments);
        }
    });
}
```

### User Interface Design

#### **Auto-Segment Button Integration**
Add to existing Content Segmentation section in Direct Editor sidebar:

```html
<div class="slmm-segmentation-controls">
    <div class="slmm-segmentation-buttons">
        <!-- Existing manual buttons -->
        <button id="slmm-segment-insert-btn" class="slmm-segment-btn">Insert Segments</button>
        <button id="slmm-segment-clear-btn" class="slmm-segment-btn-secondary">Clear</button>
        
        <!-- NEW: Auto-segmentation button -->
        <button id="slmm-auto-segment-btn" class="slmm-segment-btn slmm-auto-btn">⚡ Auto-Segment</button>
    </div>
    
    <!-- NEW: Configuration dropdown -->
    <div class="slmm-auto-config" style="display: none;">
        <label>Split Ratio:</label>
        <select id="slmm-split-ratio">
            <option value="33,67">33% / 67% (Balanced)</option>
            <option value="25,75">25% / 75% (Short Top)</option>
            <option value="40,80">40% / 80% (Long Top)</option>
            <option value="custom">Custom...</option>
        </select>
        
        <label>Method:</label>
        <select id="slmm-segment-method">
            <option value="percentage">Percentage-Based</option>
            <option value="content-aware">Content-Aware</option>
            <option value="ai-driven">AI-Driven</option>
        </select>
    </div>
</div>
```

#### **Enhanced Button Styling**
```css
.slmm-auto-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    position: relative;
    overflow: hidden;
}

.slmm-auto-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.slmm-auto-btn:hover::before {
    left: 100%;
}

.slmm-auto-config {
    margin-top: 8px;
    padding: 8px;
    background: #1f2937;
    border-radius: 4px;
    font-size: 11px;
}
```

## Implementation Strategy

### Phase 1: Core Auto-Segmentation (Week 1)
**Files to Create/Modify:**
```
✨ NEW: assets/js/auto-segmentation.js                     (~300 lines)
📝 EDIT: assets/js/content-segmentation-simple.js         (add 50 lines)
📝 EDIT: assets/css/content-segmentation.css              (add 30 lines)
✨ NEW: includes/ajax/auto-segmentation-handler.php       (~150 lines)
```

#### **1. Basic Percentage-Based Auto-Segmentation**
- Add `autoSegmentByPercentage()` method
- Implement configurable split ratios (25/75, 33/67, 40/80)
- Create "Auto-Segment" button with loading states
- Add AJAX handler for percentage-based segmentation

#### **2. Smart Boundary Detection Enhancement**
```javascript
findSmartBoundary: function(content, targetPosition) {
    // Enhanced version of existing find_content_boundary()
    var searchRadius = Math.floor(content.length * 0.05); // 5% search radius
    var boundaries = ['</h1>', '</h2>', '</h3>', '</h4>', '</p>', '</ul>', '</ol>', '</blockquote>'];
    
    // Find closest natural break point within search radius
    for (var i = 0; i < boundaries.length; i++) {
        var boundary = boundaries[i];
        var searchStart = Math.max(0, targetPosition - searchRadius);
        var searchEnd = Math.min(content.length, targetPosition + searchRadius);
        
        var pos = content.indexOf(boundary, searchStart);
        if (pos !== -1 && pos <= searchEnd) {
            return pos + boundary.length;
        }
    }
    
    return targetPosition; // Fallback to exact percentage
}
```

### Phase 2: Content-Aware Segmentation (Week 2)
**Advanced content structure analysis:**

#### **1. Content Structure Analyzer**
```javascript
analyzeContentStructure: function(content) {
    var structure = {
        headings: [],
        paragraphs: [],
        lists: [],
        sections: [],
        wordCount: 0,
        readingTime: 0
    };
    
    // Extract headings with positions
    var headingMatches = content.matchAll(/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi);
    for (let match of headingMatches) {
        structure.headings.push({
            level: parseInt(match[1]),
            text: match[2],
            position: match.index,
            length: match[0].length
        });
    }
    
    // Analyze paragraph distribution
    var paragraphMatches = content.matchAll(/<p[^>]*>(.*?)<\/p>/gi);
    for (let match of paragraphMatches) {
        structure.paragraphs.push({
            text: match[1],
            position: match.index,
            length: match[0].length,
            wordCount: match[1].split(' ').length
        });
    }
    
    return structure;
}
```

#### **2. Optimal Breakpoint Algorithm**
```javascript
findOptimalBreakpoints: function(structure) {
    var breakpoints = { first: null, second: null };
    var contentLength = structure.totalLength;
    
    // Strategy 1: Use heading hierarchy
    if (structure.headings.length >= 2) {
        breakpoints = this.segmentByHeadings(structure.headings, contentLength);
    }
    // Strategy 2: Use paragraph clustering
    else if (structure.paragraphs.length >= 6) {
        breakpoints = this.segmentByParagraphs(structure.paragraphs, contentLength);
    }
    // Strategy 3: Fallback to smart percentage
    else {
        breakpoints = {
            first: Math.floor(contentLength * 0.35),
            second: Math.floor(contentLength * 0.70)
        };
    }
    
    return breakpoints;
}
```

### Phase 3: AI-Driven Segmentation (Future)
**Integration with existing AI providers:**

#### **1. AI Content Analysis Endpoint**
```php
// includes/ajax/auto-segmentation-handler.php
public function handle_ai_segment_content() {
    // Security checks...
    
    $content = sanitize_textarea_field($_POST['content']);
    $clean_text = strip_tags($content);
    
    $prompt = "Analyze this article and suggest optimal segmentation points for TOP (introduction/overview), MIDDLE (main content), and BOTTOM (conclusion/action) sections. Return character positions:\n\n" . $clean_text;
    
    // Use existing AI integration
    $ai_integration = new SLMM_OpenAI_Integration();
    $response = $ai_integration->generate_content($prompt, 'gpt-4o', 200);
    
    // Parse AI response for segmentation points
    $segments = $this->parse_ai_segments($response);
    
    wp_send_json_success(array(
        'segments' => $segments,
        'method' => 'ai-driven',
        'confidence' => $this->calculate_confidence($segments)
    ));
}
```

## Integration Points

### Existing System Compatibility
**ZERO BREAKING CHANGES** - Auto-segmentation runs alongside manual system:

#### **1. Preserve Manual Workflow**
```javascript
// User can still use 3-click manual process
// Auto-segment is purely additive functionality
if (this.hasExistingMarkers(content)) {
    this.showOverrideDialog(); // Ask user to clear or keep existing
}
```

#### **2. Unified Marker System**
- Auto-segmentation uses same HTML comment markers
- Same visual overlay system
- Same clearing functionality
- Same AI integration points

#### **3. Enhanced User Control**
```javascript
// After auto-segmentation, user can:
// 1. Accept as-is (markers remain)
// 2. Fine-tune positions (drag markers)
// 3. Clear and start over (existing clear button)
// 4. Switch to manual mode (existing workflow)
```

### WordPress Integration Requirements

#### **1. Plugin.php Integration (Additive Only)**
```php
// ADD after existing segmentation includes
require_once __DIR__ . '/includes/ajax/auto-segmentation-handler.php';

// ADD to existing initialization
if (class_exists('SLMM_Auto_Segmentation_Handler')) {
    new SLMM_Auto_Segmentation_Handler();
}
```

#### **2. Asset Integration**
```php
// Add to existing slmm_enqueue_direct_editor_assets()
wp_enqueue_script(
    'slmm-auto-segmentation',
    SLMM_SEO_PLUGIN_URL . 'assets/js/auto-segmentation.js',
    array('slmm-content-segmentation'), // Depends on existing segmentation
    SLMM_SEO_VERSION,
    true
);

wp_localize_script('slmm-auto-segmentation', 'slmmAutoSegmentData', array(
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_auto_segmentation'),
    'defaults' => array(
        'split_ratio' => '33,67',
        'method' => 'percentage',
        'smart_boundaries' => true
    )
));
```

## File Structure (Minimal Additions)

### New Files Required
```
includes/
├── ajax/
│   └── auto-segmentation-handler.php          (~150 lines) - AJAX endpoints
assets/
├── js/
│   └── auto-segmentation.js                   (~300 lines) - Core auto-logic
└── css/
    └── [additions to existing content-segmentation.css] (~30 lines)
```

### File Size Compliance
- **auto-segmentation-handler.php**: ~150 lines (well under 800)
- **auto-segmentation.js**: ~300 lines (well under 800)
- **CSS additions**: ~30 lines (added to existing file)

**Total New Code**: ~480 lines across 2 new files

## Risk Assessment

### Technical Risks
1. **Performance with Large Content (>10,000 words)**
   - **Risk**: Slow processing during content analysis
   - **Mitigation**: Implement chunked processing, loading indicators
   - **Fallback**: Limit auto-segmentation to <5,000 words

2. **Boundary Detection Accuracy**
   - **Risk**: Poor break point selection in complex HTML
   - **Mitigation**: Multiple fallback strategies (headings → paragraphs → percentage)
   - **Solution**: User override capability always available

3. **AI Integration Latency**
   - **Risk**: Slow AI response times for real-time segmentation
   - **Mitigation**: Show progress indicators, allow cancellation
   - **Alternative**: Cache common segmentation patterns

### User Experience Risks
1. **Configuration Complexity**
   - **Risk**: Too many options confusing users
   - **Mitigation**: Smart defaults (33%/67%), collapsible advanced options
   - **Solution**: One-click "Auto-Segment" with sensible defaults

2. **Unexpected Results**
   - **Risk**: Auto-segmentation doesn't match user intent
   - **Mitigation**: Clear visual feedback, easy undo/redo
   - **Fallback**: Manual adjustment always available

## Development Priority: HIGH

### Business Impact
- **Eliminates manual 3-click workflow** - Major UX improvement
- **Scales to large content libraries** - Bulk segmentation potential
- **Leverages existing infrastructure** - Minimal development overhead
- **Maintains backward compatibility** - No disruption to current users

### Technical Feasibility
- **90% of infrastructure exists** - Uses current percentage/boundary systems
- **Low complexity implementation** - Percentage-based approach is straightforward
- **Incremental enhancement path** - Can add AI features later
- **Zero breaking changes** - Purely additive functionality

### Success Metrics
1. **Adoption Rate**: % of users who try auto-segmentation vs manual
2. **Accuracy Score**: User satisfaction with auto-generated segments
3. **Time Savings**: Average time reduction vs manual workflow
4. **Override Rate**: How often users manually adjust auto-segments

## Future Enhancements

### Advanced Features (Phase 4+)
1. **Bulk Auto-Segmentation**: Process multiple posts simultaneously
2. **Learning System**: Remember user preferences and adjustments
3. **Template System**: Save/reuse segmentation patterns
4. **A/B Testing**: Compare manual vs auto-segmented content performance
5. **Content Type Detection**: Different algorithms for news vs tutorials vs reviews

### AI Integration Expansion
1. **Semantic Analysis**: Understand content topics for better boundaries
2. **Link Suggestion Integration**: Auto-segment + auto-suggest links in one workflow
3. **SEO Optimization**: Segment based on keyword density and SEO best practices
4. **Multilingual Support**: Language-aware segmentation algorithms

**Status: READY FOR IMPLEMENTATION** 🚀

---

**Implementation Approach**: Start with Phase 1 (percentage-based) for immediate value, then incrementally add content-aware and AI-driven capabilities. The existing segmentation infrastructure provides a solid foundation for rapid development with minimal risk.