<?php
/**
 * SLMM Bulk Page Creator
 * Core class for creating multiple WordPress pages in batch operations
 * 
 * @package SLMM_SEO_Bundle
 * @subpackage Bulk_Creation
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Class SLMM_Bulk_Page_Creator
 * 
 * Handles bulk page creation with optimized performance, security, and WordPress integration.
 * Includes batch processing, error handling, and integration with the interlinking suite.
 */
class SLMM_Bulk_Page_Creator {
    
    /**
     * Singleton instance
     * @var SLMM_Bulk_Page_Creator|null
     */
    private static $instance = null;
    
    /**
     * Maximum pages per batch to prevent timeout/memory issues
     * @var int
     */
    private $max_batch_size = 25;
    
    /**
     * Maximum execution time per batch in seconds
     * @var int
     */
    private $max_execution_time = 120;
    
    /**
     * Default content template for new pages
     * @var string
     */
    private $default_content_template = '';
    
    /**
     * Get singleton instance
     * 
     * @return SLMM_Bulk_Page_Creator
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Private constructor to enforce singleton pattern
     */
    private function __construct() {
        $this->default_content_template = $this->get_default_content_template();
    }
    
    /**
     * Create pages in batch with comprehensive error handling and optimization
     * 
     * @param array $page_data Array of page data to create
     * @param array $options Creation options
     * @return array Creation results with success/error details
     */
    public function create_pages_batch($page_data, $options = array()) {
        // Validate and sanitize options
        $options = $this->validate_creation_options($options);
        
        // Initialize results structure
        $results = array(
            'created' => array(),
            'failed' => array(),
            'total_processed' => 0,
            'success_count' => 0,
            'error_count' => 0,
            'processing_time' => 0,
            'memory_used' => 0,
            'warnings' => array()
        );
        
        // Validate input data
        if (!is_array($page_data) || empty($page_data)) {
            $results['failed'][] = array(
                'error' => 'No valid page data provided',
                'code' => 'INVALID_INPUT'
            );
            return $results;
        }
        
        $start_time = microtime(true);
        $start_memory = memory_get_usage(true);
        
        try {
            // Check for hierarchical mode
            $is_hierarchical = $this->detect_hierarchical_mode($page_data);
            if ($is_hierarchical) {
                error_log("SLMM Hierarchy: Hierarchical creation mode detected");
                return $this->create_pages_hierarchical($page_data, $options);
            }

            // Optimize WordPress for bulk operations
            $this->optimize_for_bulk_operations();
            
            // Process pages in batches
            $batches = array_chunk($page_data, $this->max_batch_size);
            
            foreach ($batches as $batch_index => $batch) {
                $batch_results = $this->process_batch($batch, $options, $batch_index);
                
                // Merge batch results
                $results['created'] = array_merge($results['created'], $batch_results['created']);
                $results['failed'] = array_merge($results['failed'], $batch_results['failed']);
                $results['total_processed'] += $batch_results['total_processed'];
                $results['success_count'] += $batch_results['success_count'];
                $results['error_count'] += $batch_results['error_count'];
                $results['warnings'] = array_merge($results['warnings'], $batch_results['warnings']);
                
                // Memory management between batches
                if ($batch_index < count($batches) - 1) {
                    $this->cleanup_between_batches();
                    
                    // Check execution time to prevent timeout
                    if ((microtime(true) - $start_time) > $this->max_execution_time) {
                        $results['warnings'][] = 'Execution time limit reached. Some pages may not have been processed.';
                        break;
                    }
                }
            }
            
        } catch (Exception $e) {
            $results['failed'][] = array(
                'error' => 'Batch processing failed: ' . $e->getMessage(),
                'code' => 'BATCH_PROCESSING_ERROR'
            );
            
            error_log('[SLMM Bulk Creator] Batch processing exception: ' . $e->getMessage());
        } finally {
            // Restore WordPress optimizations
            $this->restore_wordpress_optimizations();
        }
        
        // Calculate final metrics
        $results['processing_time'] = round(microtime(true) - $start_time, 2);
        $results['memory_used'] = round((memory_get_usage(true) - $start_memory) / 1024 / 1024, 2); // MB
        
        // Log operation summary
        $this->log_bulk_operation($results);
        
        return $results;
    }

    /**
     * Detect if page data contains hierarchical slug patterns
     *
     * @param array $page_data Array of page data
     * @return bool True if hierarchical mode should be used
     */
    private function detect_hierarchical_mode($page_data) {
        foreach ($page_data as $page) {
            if (isset($page['hierarchy']) && $page['hierarchy']['is_hierarchical']) {
                return true;
            }
        }
        return false;
    }

    /**
     * Create pages in hierarchical mode with dependency-based ordering
     *
     * @param array $page_data Array of page data with hierarchy information
     * @param array $options Creation options
     * @return array Results of hierarchical creation
     */
    private function create_pages_hierarchical($page_data, $options) {
        // Initialize results structure
        $results = array(
            'created' => array(),
            'failed' => array(),
            'total_processed' => 0,
            'success_count' => 0,
            'error_count' => 0,
            'processing_time' => 0,
            'memory_used' => 0,
            'warnings' => array()
        );

        $start_time = microtime(true);
        $start_memory = memory_get_usage(true);

        try {
            // Optimize WordPress for bulk operations
            $this->optimize_for_bulk_operations();

            // Track created pages to avoid duplicates
            $created_pages = array(); // Maps full_path => page_id

            // Process each page with hierarchy
            foreach ($page_data as $page) {
                if (!isset($page['hierarchy']) || !$page['hierarchy']['is_hierarchical']) {
                    // Non-hierarchical page - create normally
                    $result = $this->create_single_page($page, $options);
                    if ($result && !is_wp_error($result)) {
                        $created = $this->format_created_page_data($result, $page, $options);
                        $results['created'][] = $created;
                        $results['success_count']++;
                    } else {
                        $results['failed'][] = array(
                            'title' => $page['title'],
                            'error' => is_wp_error($result) ? $result->get_error_message() : 'Unknown error'
                        );
                        $results['error_count']++;
                    }
                    $results['total_processed']++;
                    continue;
                }

                // Get the full hierarchical path
                $full_path = $page['hierarchy']['sanitized_slug'];
                $path_segments = explode('/', $full_path);

                error_log("SLMM: Processing hierarchical page: {$full_path} with " . count($path_segments) . " levels");

                // Start from the base parent
                $current_parent_id = intval($options['parent_id']);
                $current_path = '';
                $page_created = false;

                // Create each level of the hierarchy
                foreach ($path_segments as $index => $segment) {
                    // Build the current path
                    $current_path = empty($current_path) ? $segment : $current_path . '/' . $segment;

                    // Check if we've already created this page
                    if (isset($created_pages[$current_path])) {
                        $current_parent_id = $created_pages[$current_path];
                        error_log("SLMM: Using existing page for path '{$current_path}' (ID: {$current_parent_id})");
                        continue;
                    }

                    // Check if page already exists in WordPress
                    global $wpdb;
                    $existing_id = $wpdb->get_var($wpdb->prepare(
                        "SELECT ID FROM {$wpdb->posts}
                         WHERE post_name = %s
                         AND post_parent = %d
                         AND post_type = %s
                         AND post_status NOT IN ('trash', 'auto-draft')
                         LIMIT 1",
                        $segment,
                        $current_parent_id,
                        $options['post_type'] ?? 'page'
                    ));

                    if ($existing_id) {
                        // Page exists, use it as parent for next level
                        $current_parent_id = $existing_id;
                        $created_pages[$current_path] = $existing_id;
                        error_log("SLMM: Found existing page '{$segment}' (ID: {$existing_id}) at path '{$current_path}'");
                    } else {
                        // Create the page
                        $is_final = ($index === count($path_segments) - 1);

                        // Use original title for final page, generate title for intermediate pages
                        $page_title = $is_final ? $page['title'] : ucwords(str_replace('-', ' ', $segment));

                        // Prepare page data
                        $page_to_create = array(
                            'title' => $page_title,
                            'slug' => $segment  // Use just the segment, not full path
                        );

                        // Merge with options and set parent
                        $page_options = $options;
                        $page_options['parent_id'] = $current_parent_id;

                        $post_type = $options['post_type'] ?? 'page';
                        error_log("SLMM: Creating {$post_type} '{$page_title}' with slug '{$segment}' and parent {$current_parent_id}");

                        // Create the page
                        $page_id = $this->create_single_page($page_to_create, $page_options);

                        if ($page_id && !is_wp_error($page_id)) {
                            // Success - track the created page
                            $current_parent_id = $page_id;
                            $created_pages[$current_path] = $page_id;

                            // Add meta to track the full hierarchical path
                            update_post_meta($page_id, '_slmm_hierarchical_path', $current_path);

                            // Only add to results if this is the final target page
                            if ($is_final) {
                                $created = $this->format_created_page_data($page_id, $page, $page_options);
                                $results['created'][] = $created;
                                $results['success_count']++;
                                $page_created = true;
                            }

                            error_log("SLMM: Successfully created page ID {$page_id} at path '{$current_path}'");
                        } else {
                            // Failed to create page
                            $error_msg = is_wp_error($page_id) ? $page_id->get_error_message() : 'Unknown error';
                            error_log("SLMM: Failed to create page '{$segment}': {$error_msg}");

                            $results['failed'][] = array(
                                'title' => $page_title,
                                'path' => $current_path,
                                'error' => $error_msg
                            );
                            $results['error_count']++;
                            break; // Stop processing this chain
                        }
                    }
                }

                $results['total_processed']++;
            }

        } catch (Exception $e) {
            error_log("SLMM: Exception during hierarchical creation: " . $e->getMessage());
            $results['failed'][] = array(
                'error' => 'Exception: ' . $e->getMessage(),
                'code' => 'HIERARCHY_EXCEPTION'
            );
        } finally {
            // Restore WordPress optimizations
            $this->restore_wordpress_optimizations();
        }

        // Calculate metrics
        $results['processing_time'] = round(microtime(true) - $start_time, 2);
        $results['memory_used'] = round((memory_get_usage(true) - $start_memory) / 1024 / 1024, 2);

        error_log("SLMM: Hierarchical creation completed - {$results['success_count']} created, {$results['error_count']} failed in {$results['processing_time']}s");

        return $results;
    }

    // Removed group_pages_by_chains method - no longer needed with simplified approach

    // Removed find_page_by_slug method - no longer needed with simplified approach

    /**
     * Extract parent slug from hierarchical slug
     *
     * @param string $slug Hierarchical slug (e.g., 'diamond/care/repair')
     * @return string Parent slug (e.g., 'diamond/care') or empty string if no parent
     */
    private function extract_parent_slug($slug) {
        $segments = explode('/', $slug);

        if (count($segments) <= 1) {
            return ''; // No parent (root level)
        }

        // Remove last segment to get parent path
        array_pop($segments);
        return implode('/', $segments);
    }


    /**
     * Extract the final segment from a hierarchical slug for WordPress slug
     *
     * @param string $hierarchical_slug Full slug like 'diamond/care/repair'
     * @return string Final segment like 'repair'
     */
    private function extract_final_slug_segment($hierarchical_slug) {
        $segments = explode('/', $hierarchical_slug);
        return end($segments);
    }

    /**
     * Process a single batch of pages
     * 
     * @param array $batch Page data for this batch
     * @param array $options Creation options
     * @param int $batch_index Current batch index
     * @return array Batch processing results
     */
    private function process_batch($batch, $options, $batch_index) {
        $batch_results = array(
            'created' => array(),
            'failed' => array(),
            'total_processed' => 0,
            'success_count' => 0,
            'error_count' => 0,
            'warnings' => array()
        );
        
        foreach ($batch as $page_index => $page_item) {
            $batch_results['total_processed']++;
            
            try {
                // Create individual page
                $page_id = $this->create_single_page($page_item, $options);
                
                if ($page_id && !is_wp_error($page_id)) {
                    $created_page = $this->format_created_page_data($page_id, $page_item, $options);
                    $batch_results['created'][] = $created_page;
                    $batch_results['success_count']++;

                    // Register with interlinking suite if applicable
                    if ($options['register_with_interlinking']) {
                        // Use visual parent from formatted data for posts with categories
                        $options_for_interlinking = $options;
                        $options_for_interlinking['parent_id'] = $created_page['parent_id'];
                        $this->register_with_interlinking_suite($page_id, $page_item, $options_for_interlinking);
                    }
                    
                } else {
                    $error_message = is_wp_error($page_id) ? $page_id->get_error_message() : 'Unknown error creating page';
                    
                    $batch_results['failed'][] = array(
                        'title' => $page_item['title'] ?? 'Unknown',
                        'error' => $error_message,
                        'code' => is_wp_error($page_id) ? $page_id->get_error_code() : 'UNKNOWN_ERROR',
                        'batch_index' => $batch_index,
                        'page_index' => $page_index
                    );
                    $batch_results['error_count']++;
                }
                
            } catch (Exception $e) {
                $batch_results['failed'][] = array(
                    'title' => $page_item['title'] ?? 'Unknown',
                    'error' => $e->getMessage(),
                    'code' => 'EXCEPTION',
                    'batch_index' => $batch_index,
                    'page_index' => $page_index
                );
                $batch_results['error_count']++;
                
                error_log('[SLMM Bulk Creator] Page creation exception: ' . $e->getMessage());
            }
            
            // Yield control occasionally to prevent timeout
            if (($batch_results['total_processed'] % 5) === 0) {
                $this->yield_control();
            }
        }
        
        return $batch_results;
    }
    
    /**
     * Create a single WordPress page
     * 
     * @param array $page_item Page data
     * @param array $options Creation options
     * @return int|WP_Error Page ID or error
     */
    private function create_single_page($page_item, $options) {
        // Validate required fields
        if (empty($page_item['title'])) {
            $post_type = $options['post_type'] ?? 'page';
            $post_type_obj = get_post_type_object($post_type);
            $singular_name = $post_type_obj ? $post_type_obj->labels->singular_name : ucfirst($post_type);
            return new WP_Error('missing_title', $singular_name . ' title is required');
        }
        
        // Generate slug if not provided
        $slug = $page_item['slug'] ?? sanitize_title($page_item['title']);
        
        // Ensure slug is unique for this post type
        $post_type = $options['post_type'] ?? 'page';
        $unique_slug = $this->ensure_unique_slug($slug, $post_type);
        
        // CRITICAL DEBUG: Log post type before creation
        $final_post_type = $options['post_type'] ?? 'page';
        error_log("SLMM Bulk Creator: Creating post with type: " . $final_post_type);
        error_log("SLMM Bulk Creator: Options received: " . print_r($options, true));

        // CRITICAL: Check if post type is registered and what capabilities it requires
        $post_type_obj = get_post_type_object($final_post_type);
        error_log("SLMM Bulk Creator: Post type object: " . print_r($post_type_obj, true));

        if ($post_type_obj) {
            $required_cap = $post_type_obj->cap->create_posts ?? $post_type_obj->cap->edit_posts ?? 'edit_posts';
            $user_can_create = current_user_can($required_cap);
            error_log("SLMM Bulk Creator: Required capability: {$required_cap}, User can create: " . ($user_can_create ? 'YES' : 'NO'));
        } else {
            error_log("SLMM Bulk Creator: ❌ POST TYPE '{$final_post_type}' NOT REGISTERED!");
        }

        // Prepare post data
        $post_data = array(
            'post_title' => sanitize_text_field($page_item['title']),
            'post_name' => $unique_slug,
            'post_content' => $this->generate_page_content($page_item, $options),
            'post_status' => sanitize_text_field($options['status']),
            'post_type' => $final_post_type, // Use resolved post type
            'post_author' => intval($options['author_id']),
            'post_parent' => intval($options['parent_id']),
            'menu_order' => intval($page_item['menu_order'] ?? 0),
            'comment_status' => 'closed',
            'ping_status' => 'closed'
        );

        error_log("SLMM Bulk Creator: wp_insert_post data: " . print_r($post_data, true));

        // Add excerpt if provided
        if (!empty($page_item['excerpt'])) {
            $post_data['post_excerpt'] = sanitize_textarea_field($page_item['excerpt']);
        }

        // CRITICAL DEBUG: Add hooks to track any modifications during wp_insert_post
        $debug_post_data_before = $post_data;

        // CRITICAL: Check what filters are active on wp_insert_post_data
        global $wp_filter;
        if (isset($wp_filter['wp_insert_post_data'])) {
            error_log("SLMM Bulk Creator: Active filters on wp_insert_post_data: " . print_r($wp_filter['wp_insert_post_data'], true));
        }

        // Hook to track if wp_insert_post modifies our data
        add_action('wp_insert_post_data', function($data, $postarr) use ($debug_post_data_before) {
            error_log("SLMM Bulk Creator: HOOK wp_insert_post_data called");
            error_log("SLMM Bulk Creator: Original post_type: " . ($debug_post_data_before['post_type'] ?? 'not set'));
            error_log("SLMM Bulk Creator: wp_insert_post_data post_type: " . ($data['post_type'] ?? 'not set'));
            error_log("SLMM Bulk Creator: Full wp_insert_post_data: " . print_r($data, true));
            if (($debug_post_data_before['post_type'] ?? '') !== ($data['post_type'] ?? '')) {
                error_log("SLMM Bulk Creator: ❌ POST TYPE CHANGED IN wp_insert_post_data hook!");
            }
            return $data;
        }, 10, 2);

        // Hook to track post creation process
        add_action('wp_insert_post', function($post_id, $post, $update) {
            error_log("SLMM Bulk Creator: HOOK wp_insert_post called - ID: {$post_id}, Type: {$post->post_type}, Update: " . ($update ? 'yes' : 'no'));
        }, 10, 3);

        // INTENSIVE DEBUG: Final check before wp_insert_post
        error_log("SLMM Bulk Creator: FINAL CHECK - About to call wp_insert_post with post_type: " . $post_data['post_type']);
        error_log("SLMM Bulk Creator: FINAL CHECK - post_parent will be: " . $post_data['post_parent']);
        error_log("SLMM Bulk Creator: FINAL CHECK - Full post_data: " . print_r($post_data, true));

        // Create the page
        $page_id = wp_insert_post($post_data, true);

        // Remove our debug hooks immediately after
        remove_all_actions('wp_insert_post_data');

        error_log("SLMM Bulk Creator: wp_insert_post result: " . print_r($page_id, true));

        if ($page_id && !is_wp_error($page_id)) {
            $created_post = get_post($page_id);
            error_log("SLMM Bulk Creator: Created post details: ID={$page_id}, type={$created_post->post_type}, permalink=" . get_permalink($page_id));

            // CRITICAL: Verify post type was set correctly
            if ($created_post->post_type !== $final_post_type) {
                error_log("SLMM Bulk Creator: ERROR - Post type mismatch! Expected: {$final_post_type}, Got: {$created_post->post_type}");
            } else {
                error_log("SLMM Bulk Creator: ✅ Post type correct: {$created_post->post_type}");
            }
            // Add custom meta fields
            $this->add_page_meta_fields($page_id, $page_item, $options);
            
            // Set featured image if provided
            if (!empty($page_item['featured_image_id'])) {
                set_post_thumbnail($page_id, intval($page_item['featured_image_id']));
            }
            
            // Add to specific categories/tags if specified
            $categories_to_assign = array();

            error_log("SLMM Bulk Creator: Category assignment debug:");
            error_log("SLMM Bulk Creator: page_item[categories]: " . print_r($page_item['categories'] ?? 'not set', true));
            error_log("SLMM Bulk Creator: options[categories]: " . print_r($options['categories'] ?? 'not set', true));

            // Prioritize page-specific categories, then fall back to bulk options
            if (!empty($page_item['categories'])) {
                $categories_to_assign = array_map('intval', $page_item['categories']);
                error_log("SLMM Bulk Creator: Using page-specific categories: " . print_r($categories_to_assign, true));
            } elseif (!empty($options['categories'])) {
                $categories_to_assign = array_map('intval', $options['categories']);
                error_log("SLMM Bulk Creator: Using bulk options categories: " . print_r($categories_to_assign, true));
            }

            if (!empty($categories_to_assign)) {
                $result = wp_set_post_categories($page_id, $categories_to_assign);
                error_log("SLMM Bulk Creator: wp_set_post_categories result: " . print_r($result, true));
                error_log("SLMM Bulk Creator: Post categories after assignment: " . print_r(wp_get_post_categories($page_id, array('fields' => 'ids')), true));
            } else {
                error_log("SLMM Bulk Creator: No categories to assign");
            }
            
            if (!empty($page_item['tags'])) {
                wp_set_post_tags($page_id, $page_item['tags']);
            }
        }
        
        return $page_id;
    }
    
    /**
     * Generate content for the new page
     * 
     * @param array $page_item Page data
     * @param array $options Creation options
     * @return string Generated content
     */
    private function generate_page_content($page_item, $options) {
        // Use custom content if provided
        if (!empty($page_item['content'])) {
            return wp_kses_post($page_item['content']);
        }
        
        // Use custom template if provided in options
        if (!empty($options['content_template'])) {
            $template = $options['content_template'];
        } else {
            $template = $this->default_content_template;
        }
        
        // Replace placeholders in template
        $post_type = $options['post_type'] ?? 'page';
        $post_type_obj = get_post_type_object($post_type);
        $singular_name = $post_type_obj ? strtolower($post_type_obj->labels->singular_name) : $post_type;

        $placeholders = array(
            '{TITLE}' => $page_item['title'],
            '{POST_TYPE}' => $singular_name,
            '{DATE}' => current_time('F j, Y'),
            '{PARENT_TITLE}' => $options['parent_title'] ?? '',
            '{SLUG}' => $page_item['slug'] ?? sanitize_title($page_item['title']),
            '{KEYWORDS}' => $this->extract_keywords_from_title($page_item['title'])
        );
        
        $content = str_replace(array_keys($placeholders), array_values($placeholders), $template);
        
        return wp_kses_post($content);
    }
    
    /**
     * Add meta fields to the created page
     * 
     * @param int $page_id Created page ID
     * @param array $page_item Page data
     * @param array $options Creation options
     */
    private function add_page_meta_fields($page_id, $page_item, $options) {
        // SLMM-specific meta fields
        update_post_meta($page_id, '_slmm_created_via_bulk', current_time('mysql'));
        update_post_meta($page_id, '_slmm_needs_content_optimization', 1);
        update_post_meta($page_id, '_slmm_bulk_creation_batch', $options['batch_id'] ?? '');
        
        // Set default importance level to 3 for bulk created items
        update_post_meta($page_id, '_slmm_importance_rating', 3);
        
        // SEO meta fields
        if (!empty($page_item['meta_description'])) {
            update_post_meta($page_id, '_slmm_meta_description', sanitize_textarea_field($page_item['meta_description']));
        }
        
        if (!empty($page_item['target_keywords'])) {
            $keywords = is_array($page_item['target_keywords']) ? 
                       $page_item['target_keywords'] : 
                       array_map('trim', explode(',', $page_item['target_keywords']));
            update_post_meta($page_id, '_slmm_target_keywords', array_map('sanitize_text_field', $keywords));
        } else {
            // Auto-generate basic keywords from title
            $auto_keywords = $this->extract_keywords_from_title($page_item['title']);
            update_post_meta($page_id, '_slmm_target_keywords', $auto_keywords);
        }
        
        // Content optimization flags
        update_post_meta($page_id, '_slmm_content_depth', 'minimal');
        update_post_meta($page_id, '_slmm_seo_score', 45); // Starting score for new pages
        update_post_meta($page_id, '_slmm_last_seo_analysis', current_time('mysql'));
        
        // Parent relationship data for interlinking
        if (!empty($options['parent_id'])) {
            update_post_meta($page_id, '_slmm_parent_page_id', intval($options['parent_id']));
            update_post_meta($page_id, '_slmm_auto_link_to_parent', $options['auto_link'] ? 1 : 0);
        }
        
        // Additional custom meta fields
        if (!empty($page_item['custom_meta']) && is_array($page_item['custom_meta'])) {
            foreach ($page_item['custom_meta'] as $meta_key => $meta_value) {
                $sanitized_key = sanitize_key($meta_key);
                if (strpos($sanitized_key, '_slmm_') === 0) { // Only allow SLMM prefixed meta
                    update_post_meta($page_id, $sanitized_key, sanitize_text_field($meta_value));
                }
            }
        }
    }
    
    /**
     * Format created page data for response
     *
     * @param int $page_id Created page ID
     * @param array $page_item Original page data
     * @param array $options Creation options (includes categories)
     * @return array Formatted page data
     */
    private function format_created_page_data($page_id, $page_item, $options = array()) {
        $page = get_post($page_id);

        // CRITICAL FIX: For tree visualization, posts with categories need visual parent relationship
        $visual_parent_id = $page->post_parent; // Default to actual WordPress parent

        if ($page->post_type === 'post' && !empty($options['categories'])) {
            // For posts with categories, use the first category as visual parent for tree placement
            $primary_category_id = reset($options['categories']);
            $visual_parent_id = 'category_' . $primary_category_id;
            error_log("SLMM Bulk Creator: Setting visual parent for post {$page_id} to category_{$primary_category_id} for tree visualization");
        }

        return array(
            'id' => $page_id,
            'title' => $page->post_title,
            'slug' => $page->post_name,
            'url' => get_permalink($page_id),
            'edit_url' => admin_url('post.php?post=' . $page_id . '&action=edit'),
            'status' => $page->post_status,
            'parent_id' => $visual_parent_id, // Use visual parent for tree visualization
            'actual_parent_id' => $page->post_parent, // Keep actual WordPress parent for reference
            'post_type' => $page->post_type, // Include post type for frontend identification
            'categories' => $page->post_type === 'post' ? wp_get_post_categories($page_id, array('fields' => 'ids')) : array(),
            'created_time' => $page->post_date,
            'author_id' => $page->post_author,
            'menu_order' => $page->menu_order
        );
    }
    
    /**
     * Register created page with interlinking suite
     * 
     * @param int $page_id Created page ID
     * @param array $page_item Original page data
     * @param array $options Creation options
     */
    private function register_with_interlinking_suite($page_id, $page_item, $options) {
        // Add to interlinking suite data structure
        if (class_exists('SLMM_Interlinking_Suite')) {
            $interlinking = SLMM_Interlinking_Suite::get_instance();
            
            // Register the new page in the tree structure
            if (method_exists($interlinking, 'add_page_to_tree')) {
                $interlinking->add_page_to_tree($page_id, $options['parent_id'] ?? 0);
            }
            
            // Create automatic internal link to parent if requested
            // Skip for category-based parents (format: category_XX)
            if ($options['auto_link'] && !empty($options['parent_id']) && strpos($options['parent_id'], 'category_') !== 0) {
                $this->create_auto_link_to_parent($page_id, $options['parent_id']);
            }
        }
        
        // Trigger action for external integrations
        do_action('slmm_bulk_post_created', $page_id, $page_item, $options);
    }
    
    /**
     * Create automatic internal link from new page to parent
     * 
     * @param int $page_id New page ID
     * @param int $parent_id Parent page ID
     */
    private function create_auto_link_to_parent($page_id, $parent_id) {
        $page = get_post($page_id);
        $parent = get_post($parent_id);
        
        if (!$page || !$parent) {
            return;
        }
        
        $parent_url = get_permalink($parent_id);
        $parent_title = $parent->post_title;
        
        // Add contextual link to the new page content
        $link_html = sprintf(
            '<p><em>This page is part of our <a href="%s">%s</a> resource collection.</em></p>',
            esc_url($parent_url),
            esc_html($parent_title)
        );
        
        // Append to existing content
        $current_content = $page->post_content;
        $updated_content = $current_content . "\n\n" . $link_html;
        
        wp_update_post(array(
            'ID' => $page_id,
            'post_content' => $updated_content
        ));
    }
    
    /**
     * Ensure slug is unique by appending numbers if necessary
     *
     * @param string $slug Base slug
     * @param string $post_type Post type to check against
     * @return string Unique slug
     */
    private function ensure_unique_slug($slug, $post_type = 'page') {
        $original_slug = $slug;
        $counter = 1;

        while ($this->slug_exists($slug, $post_type)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
            
            // Prevent infinite loops
            if ($counter > 1000) {
                $slug = $original_slug . '-' . time();
                break;
            }
        }
        
        return $slug;
    }
    
    /**
     * Check if slug already exists for the specified post type
     *
     * @param string $slug Slug to check
     * @param string $post_type Post type to check against
     * @return bool True if slug exists
     */
    private function slug_exists($slug, $post_type = 'page') {
        global $wpdb;

        $exists = $wpdb->get_var($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} WHERE post_name = %s AND post_type = %s AND post_status != 'trash'",
            $slug,
            $post_type
        ));

        return !empty($exists);
    }
    
    /**
     * Extract keywords from page title
     * 
     * @param string $title Page title
     * @return array Extracted keywords
     */
    private function extract_keywords_from_title($title) {
        // Remove common stop words
        $stop_words = array('the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'could', 'can', 'may', 'might', 'must', 'shall');
        
        // Clean and split title
        $words = preg_split('/[^\w]+/', strtolower($title));
        $keywords = array();
        
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) > 2 && !in_array($word, $stop_words)) {
                $keywords[] = $word;
            }
        }
        
        // Return up to 5 keywords
        return array_slice(array_unique($keywords), 0, 5);
    }
    
    /**
     * Validate and sanitize creation options
     * 
     * @param array $options Raw options
     * @return array Validated options
     */
    private function validate_creation_options($options) {
        $defaults = array(
            'status' => 'draft',
            'author_id' => get_current_user_id(),
            'parent_id' => 0,
            'parent_title' => '',
            'auto_link' => true,
            'content_template' => '',
            'batch_id' => uniqid('batch_'),
            'register_with_interlinking' => true
        );
        
        $validated = wp_parse_args($options, $defaults);
        
        // Sanitize values
        $validated['status'] = in_array($validated['status'], array('draft', 'publish', 'private')) ? 
                              $validated['status'] : 'draft';
        $validated['author_id'] = absint($validated['author_id']) ?: get_current_user_id();
        $validated['parent_id'] = absint($validated['parent_id']);
        $validated['parent_title'] = sanitize_text_field($validated['parent_title']);
        $validated['auto_link'] = (bool) $validated['auto_link'];
        $validated['content_template'] = wp_kses_post($validated['content_template']);
        $validated['batch_id'] = sanitize_key($validated['batch_id']);
        $validated['register_with_interlinking'] = (bool) $validated['register_with_interlinking'];
        
        return $validated;
    }
    
    /**
     * Optimize WordPress settings for bulk operations
     */
    private function optimize_for_bulk_operations() {
        // Suspend cache operations during bulk creation
        wp_suspend_cache_addition(true);
        
        // Increase memory limit if possible
        if (function_exists('ini_set')) {
            $current_limit = ini_get('memory_limit');
            if ($current_limit && $current_limit !== '-1') {
                $limit_bytes = $this->convert_memory_limit_to_bytes($current_limit);
                if ($limit_bytes < 256 * 1024 * 1024) { // Less than 256MB
                    @ini_set('memory_limit', '256M');
                }
            }
        }
        
        // Disable automatic spam/moderation checks
        remove_action('transition_post_status', '_transition_post_status', 10);
        
        // Remove unnecessary actions during bulk operation
        $this->removed_actions = array();
        
        // Store and remove post updated messages
        if (has_action('post_updated_messages')) {
            $this->removed_actions['post_updated_messages'] = true;
            remove_all_actions('post_updated_messages');
        }
    }
    
    /**
     * Restore WordPress optimizations after bulk operation
     */
    private function restore_wordpress_optimizations() {
        // Re-enable cache operations
        wp_suspend_cache_addition(false);
        
        // Restore removed actions
        if (isset($this->removed_actions['post_updated_messages'])) {
            add_action('transition_post_status', '_transition_post_status', 10, 3);
        }
        
        // Clear any accumulated cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
    }
    
    /**
     * Cleanup operations between batches
     */
    private function cleanup_between_batches() {
        // Clear object cache
        if (function_exists('wp_cache_flush')) {
            wp_cache_flush();
        }
        
        // Force garbage collection
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
        
        // Yield control to prevent timeout
        $this->yield_control();
    }
    
    /**
     * Yield control back to server to prevent timeout
     */
    private function yield_control() {
        if (function_exists('fastcgi_finish_request')) {
            fastcgi_finish_request();
        }
        
        // Brief pause to prevent overwhelming server
        usleep(10000); // 10ms
    }
    
    /**
     * Convert memory limit string to bytes
     * 
     * @param string $limit Memory limit string (e.g., '128M')
     * @return int Bytes
     */
    private function convert_memory_limit_to_bytes($limit) {
        $value = intval($limit);
        $unit = strtoupper(substr($limit, -1));
        
        switch ($unit) {
            case 'G':
                $value *= 1024 * 1024 * 1024;
                break;
            case 'M':
                $value *= 1024 * 1024;
                break;
            case 'K':
                $value *= 1024;
                break;
        }
        
        return $value;
    }
    
    /**
     * Log bulk operation results
     * 
     * @param array $results Operation results
     */
    private function log_bulk_operation($results) {
        $log_data = array(
            'timestamp' => current_time('mysql'),
            'user_id' => get_current_user_id(),
            'success_count' => $results['success_count'],
            'error_count' => $results['error_count'],
            'processing_time' => $results['processing_time'],
            'memory_used' => $results['memory_used']
        );
        
        error_log('[SLMM Bulk Creator] Operation completed: ' . wp_json_encode($log_data));
        
        // Store operation log in database for admin review if needed
        $this->store_operation_log($log_data);
    }
    
    /**
     * Store operation log in database
     * 
     * @param array $log_data Log data to store
     */
    private function store_operation_log($log_data) {
        $logs = get_option('slmm_bulk_creation_logs', array());
        
        // Keep only last 50 operations
        if (count($logs) >= 50) {
            $logs = array_slice($logs, -49);
        }
        
        $logs[] = $log_data;
        update_option('slmm_bulk_creation_logs', $logs, false);
    }
    
    /**
     * Get default content template for new pages
     * 
     * @return string Default template
     */
    private function get_default_content_template() {
        return '<h1>{TITLE}</h1>

<p>This {POST_TYPE} was created on {DATE} and is ready for content development as part of your SEO content strategy.</p>

<h2>Content Development Guidelines</h2>
<p>To optimize this {POST_TYPE} for search engines and user experience, consider including:</p>

<ul>
<li><strong>Target Keywords:</strong> {KEYWORDS}</li>
<li><strong>Comprehensive Content:</strong> Aim for 1500+ words of valuable, in-depth information</li>
<li><strong>Internal Linking:</strong> Link to related pages in your content silo structure</li>
<li><strong>Visual Elements:</strong> Include relevant images, videos, or infographics</li>
<li><strong>Call-to-Action:</strong> Guide users to the next step in their journey</li>
</ul>

<h2>SEO Optimization Checklist</h2>
<ul>
<li>✅ Page title optimized for target keywords</li>
<li>⏳ Meta description (add in SEO settings)</li>
<li>⏳ Header structure (H1, H2, H3) for content organization</li>
<li>⏳ Internal links to related pages</li>
<li>⏳ External links to authoritative sources</li>
<li>⏳ Image alt text for accessibility and SEO</li>
<li>⏳ Page loading speed optimization</li>
</ul>

<div style="background: #f8f9fa; padding: 20px; border-left: 4px solid #007cba; margin: 20px 0;">
<p><strong>💡 Content Development Tips:</strong></p>
<ul>
<li>Research what your audience is searching for related to "{TITLE}"</li>
<li>Analyze competitor content to identify content gaps</li>
<li>Create content that provides unique value and insights</li>
<li>Update and refresh content regularly to maintain relevance</li>
</ul>
</div>

<p><em>This {POST_TYPE} is part of your website\'s content architecture. <a href="#edit-content">Start editing</a> to transform this template into valuable content for your audience.</em></p>';
    }
    
    /**
     * Get bulk creation statistics for admin dashboard
     * 
     * @return array Statistics data
     */
    public function get_bulk_creation_stats() {
        $logs = get_option('slmm_bulk_creation_logs', array());
        
        if (empty($logs)) {
            return array(
                'total_operations' => 0,
                'total_pages_created' => 0,
                'average_processing_time' => 0,
                'success_rate' => 0
            );
        }
        
        $total_operations = count($logs);
        $total_pages = array_sum(array_column($logs, 'success_count'));
        $total_errors = array_sum(array_column($logs, 'error_count'));
        $average_time = array_sum(array_column($logs, 'processing_time')) / $total_operations;
        $success_rate = $total_pages > 0 ? ($total_pages / ($total_pages + $total_errors)) * 100 : 0;
        
        return array(
            'total_operations' => $total_operations,
            'total_pages_created' => $total_pages,
            'total_errors' => $total_errors,
            'average_processing_time' => round($average_time, 2),
            'success_rate' => round($success_rate, 1)
        );
    }
    
    /**
     * Clean up old bulk creation logs
     * 
     * @param int $days_to_keep Number of days to keep logs
     */
    public function cleanup_old_logs($days_to_keep = 30) {
        $logs = get_option('slmm_bulk_creation_logs', array());
        $cutoff_date = date('Y-m-d H:i:s', strtotime("-{$days_to_keep} days"));
        
        $filtered_logs = array_filter($logs, function($log) use ($cutoff_date) {
            return isset($log['timestamp']) && $log['timestamp'] >= $cutoff_date;
        });
        
        if (count($filtered_logs) !== count($logs)) {
            update_option('slmm_bulk_creation_logs', array_values($filtered_logs));
            return count($logs) - count($filtered_logs);
        }
        
        return 0;
    }
}