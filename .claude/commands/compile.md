---
description: Compile WordPress plugin into production-ready ZIP file
allowed-tools: Bash(*)
---

# IMPORTANT 
Run this command EVERY time I run it, even if I have already run it before.

# WordPress Plugin Compiler

Compile the SLMM SEO Bundle plugin into a production-ready ZIP file by copying everything and then removing development files.

## Compilation Process

```bash
#!/bin/bash

# 1. Setup variables
VERSION=$(grep 'Version:' plugin.php | sed 's/.*Version: *\([0-9.]*\).*/\1/')
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="$HOME/Desktop/SLMM-SEO-Bundle-Builds"
OUTPUT_FILE="$OUTPUT_DIR/slmm-seo-bundle-v${VERSION}-${TIMESTAMP}.zip"

echo "📦 Compiling SLMM SEO Bundle v${VERSION}"

# 2. Create output directory
mkdir -p "$OUTPUT_DIR"

# 3. Create temp directory and copy everything
TEMP_DIR=$(mktemp -d)
cp -R . "$TEMP_DIR/slmm_seo_bundle"
cd "$TEMP_DIR/slmm_seo_bundle"

# 4. Delete excluded directories (following .gitignore)
rm -rf .git .claude .serena .cursor memory-bank docs doc sessions node_modules

# 5. Delete excluded files (following .gitignore)
rm -f .DS_Store .gitignore @golden-rules-for-shortcuts.md
rm -f CLAUDE.md CLAUDE.sessions.md CLAUDE.local.md CHANGELOG.md
rm -f temp-*.php test-*.php debug-*.php

# 6. Create ZIP with plugin directory structure
cd "$TEMP_DIR"
zip -r "$OUTPUT_FILE" slmm_seo_bundle/ >/dev/null 2>&1

# 7. Get file size and display summary
FILESIZE=$(stat -f%z "$OUTPUT_FILE" 2>/dev/null || stat -c%s "$OUTPUT_FILE" 2>/dev/null)
if [ "$FILESIZE" -gt 1048576 ]; then
    SIZE_MB=$(echo "scale=1; $FILESIZE/1048576" | bc)
    READABLE_SIZE="${SIZE_MB} MB"
else
    SIZE_KB=$(echo "scale=1; $FILESIZE/1024" | bc)
    READABLE_SIZE="${SIZE_KB} KB"
fi

# 8. Cleanup and notify
cd "$HOME"
rm -rf "$TEMP_DIR"

echo "✅ COMPILATION COMPLETE!"
echo "📦 File: slmm-seo-bundle-v${VERSION}-${TIMESTAMP}.zip"
echo "📏 Size: $READABLE_SIZE"
echo "📂 Location: $OUTPUT_DIR"
echo "🔧 Ready for WordPress installation!"

# Play completion sound
afplay /System/Library/Sounds/Submarine.aiff 2>/dev/null &
```

## Excluded Items

### Directories:
- `.git/`
- `.claude/`
- `.serena/`
- `.cursor/`
- `memory-bank/`
- `docs/`
- `doc/`
- `sessions/`
- `node_modules/`

### Files:
- `.DS_Store`
- `.gitignore`
- `@golden-rules-for-shortcuts.md`
- `CLAUDE.md`
- `CLAUDE.sessions.md`
- `CLAUDE.local.md`
- `temp-*.php`
- `test-*.php`
- `debug-*.php`
- `CHANGELOG.md`
- `readme.txt`
- `CHANGELOG.md`