# Interlinking Suite CLAUDE.md

## Purpose
Advanced SEO silo builder with mathematical authority distribution, providing visual D3.js-based site architecture management and real-time SEO optimization for WordPress sites.

## Narrative Summary
The Interlinking Suite is a sophisticated standalone admin page that combines mathematical SEO algorithms with interactive D3.js visualizations. It enables users to build and optimize site architecture through drag-and-drop interfaces, implements PageRank-style authority distribution calculations, and provides comprehensive site analysis tools. The system is designed for high-performance operation on large sites (10,000+ pages) with progressive loading patterns and memory leak protection.

Key implementation features include hierarchical grid layouts for large site visualization, batch processing systems for bulk operations, and comprehensive memory management patterns to prevent resource leaks during extended usage sessions.

## Key Files
- `interlinking-suite.php` - Main controller class and admin page renderer
- `seo-calculator.php` - Mathematical SEO algorithms and authority distribution
- `authority-distributor.php` - PageRank-style link authority calculations
- `grid-generator.php` - Hierarchical grid layout system
- `performance-foundation.php` - Performance optimization utilities
- `class-interlinking-multiselect.php` - Multi-selection interface components

## JavaScript Components
- D3.js tree visualization with quadtree spatial indexing
- Progressive loading with lazy depth limiting (max_depth: 5)
- Memory leak protection with timeout tracking and cleanup
- Batch processing for bulk operations (configurable batch sizes)

## API Endpoints
- `POST /wp-ajax/slmm_generate_silo_grid` - Generate site architecture visualization
- `POST /wp-ajax/slmm_analyze_silo_performance` - SEO performance analysis
- `POST /wp-ajax/slmm_process_large_silo` - Large site processing with batching
- `POST /wp-ajax/slmm_get_acf_titles_batch` - ACF field batch processing
- `POST /wp-ajax/slmm_batch_move_pages` - Bulk page hierarchy operations
- `POST /wp-ajax/slmm_batch_delete_pages` - Bulk page deletion with progress tracking
- `POST /wp-ajax/slmm_import_silo_data` - CSV import with batched processing

## Integration Points
### Consumes
- WordPress post hierarchy system
- ACF (Advanced Custom Fields) for title management
- Page Summarization Manager for content analysis
- D3.js library for visualizations
- Direct Editor system for inline editing

### Provides
- SEO authority distribution calculations
- Visual site architecture interface
- Bulk page management operations
- CSV export/import functionality
- Real-time SEO optimization feedback

## Configuration
Required settings:
- WordPress admin access with `manage_options` capability
- D3.js library loaded for visualizations
- ACF plugin for advanced title field management (optional)
- Sufficient PHP memory limits for large site processing

## Memory Management Patterns

### Progressive Loading Implementation
- **Lazy Loading Depth Limit**: `max_depth: 5` prevents excessive initial data loading
- **Batch Processing**: Configurable batch sizes (3-10 items) for bulk operations
- **Debounced Search**: Search calls debounced for performance optimization
- **Cache Busting**: Controlled cache invalidation with `cache_bust: Date.now()`

### Memory Leak Protection
- **Timeout Tracking**: All JavaScript timeouts tracked in arrays for cleanup
- **Destruction Pattern**: Comprehensive cleanup methods with `destroy()` functions
- **Event Listener Cleanup**: Proper removal of event handlers during cleanup
- **Recursive Call Prevention**: Flags to prevent recursive function calls

### Resource Cleanup Patterns
```javascript
// From content-segmentation-simple.js:lines 22-24
timeouts: [], // Track all timeouts for cleanup
isDestroyed: false, // Prevent operations after cleanup
waitingForTinyMCE: false, // Prevent recursive waitForTinyMCE calls

// Cleanup implementation:lines 835-862
clearTimeouts: function() {
    this.timeouts.forEach(function(timeoutId) {
        clearTimeout(timeoutId);
    });
    this.timeouts = [];
},

destroy: function() {
    this.isDestroyed = true;
    this.clearTimeouts();
    // Additional cleanup...
}
```

## Performance Optimizations

### Hierarchical Grid Layout System
- **Purpose**: Constrains visualization width for large sites (200+ pages)
- **Implementation**: Tiered grid system grouping nodes by WordPress hierarchy depth
- **Location**: `interlinking-suite.php:5807-5868`
- **User Control**: Column width input field with auto/manual modes

### Batch Processing Architecture
- **Bulk Summarization**: `executeBulkSummarization()` with configurable batch sizes
- **Bulk Interlinking**: `executeBulkInterlinking()` with 2-second delays between batches
- **CSV Import**: `startBatchedImport()` with session-based processing
- **Progress Tracking**: Real-time progress bars and status updates

### Search and Filtering Optimization
- **Debounced Search**: Search calls optimized with timeout-based debouncing
- **Spatial Indexing**: D3.js quadtree implementation for O(log n) performance
- **Progressive Expansion**: `expandNextLevel()` for controlled tree expansion
- **Search State Management**: Cleanup tracking for expanded nodes during search

## Security Implementation
- **Nonce Verification**: All AJAX endpoints protected with WordPress nonces
- **Capability Checks**: `manage_options` requirement enforced
- **Input Sanitization**: Proper sanitization of all user inputs
- **SQL Injection Prevention**: Prepared statements for all database queries

## Key Patterns
- **Singleton Pattern**: Main class uses singleton pattern for instance management
- **Progressive Loading**: Lazy loading with depth limits for performance
- **Batch Processing**: Configurable batch sizes for bulk operations
- **Memory Management**: Comprehensive timeout tracking and cleanup patterns
- **Event-Driven Architecture**: Uses WordPress hooks and custom events
- **Spatial Optimization**: D3.js quadtree for efficient spatial queries

## Recent Improvements (Memory Leak Fixes)
- **Console Logging Cleanup**: Removed excessive debugging output (commits 7ee3e34, 625c2e1)
- **Memory Leak Protection**: Added comprehensive timeout tracking and cleanup
- **Progressive Loading**: Implemented depth-limited loading for large sites
- **Resource Management**: Added destruction patterns for proper cleanup
- **Performance Optimization**: Optimized search and filtering operations

## Testing Requirements
- **Large Site Testing**: Verify performance with 1000+ pages
- **Memory Usage Monitoring**: Track JavaScript heap usage during extended sessions
- **Batch Operation Testing**: Validate bulk operations with various batch sizes
- **Browser Compatibility**: Test D3.js visualizations across browsers
- **Mobile Responsiveness**: Ensure touch-friendly interface on mobile devices

## Related Documentation
- `assets/docs/interlinking-suite-prd.md` - Product requirements document
- `docs/interlinking-hierarchical-grid-layout.md` - Grid layout implementation
- `sessions/tasks/h-fix-memory-usage.md` - Memory optimization task documentation
- `memory-bank/interlinking-search-functionality.md` - Search system patterns