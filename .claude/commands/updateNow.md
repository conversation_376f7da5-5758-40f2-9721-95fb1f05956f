# Golden Rules for SLMM SEO Bundle WordPress Plugin Version Updates

Use Subtasks and agents for this

## Pre-Release Analysis

Analyze recent code changes and update CHANGELOG.md with a new version entry. Look for changes in:

1. **WordPress Plugin Core**:
   - `plugin.php` for version updates (ONLY change version number - keep exact format)
   - `slmm-seo-plugin.php` for main functionality changes
   - `includes/` directory for feature and improvement changes
   - `assets/` directory for CSS/JS updates
   - `snippets/` directory for template and snippet changes

2. **AI Integration & Features**:
   - `includes/ai-integration/` for AI provider updates
   - `includes/settings/` for settings panel changes
   - `includes/features/` for new feature implementations
   - Search & Replace, Lorem Detector, Direct Editing improvements

3. **WordPress Integration**:
   - Hook implementations and WordPress standards compliance
   - Security updates (nonces, capability checks, sanitization)
   - Authorization system changes (`slmm_seo_check_visibility_authorization`)
   - AJAX endpoint security and functionality

4. **Documentation & Memory Bank**:
   - `memory-bank/` directory for updated patterns
   - `docs/` directory for documentation changes
   - `CLAUDE.md` for development guidance updates
   - Session management and protocol updates

## Version Update Checklist

### 1. Core File Updates
- **ALWAYS** update `plugin.php` version field ONLY (line 5: Version: X.X.X)
- **KEEP** all other plugin.php content exactly the same format
- **ALWAYS** update `CHANGELOG.md` with new version entry at TOP
- **ALWAYS** update relevant documentation if function changes occurred
- Follow semantic versioning (major.minor.patch)

### 2. WordPress Plugin Verification
**CRITICAL**: Check if any new WordPress integrations have been added:
- Search for new `add_action()` and `add_filter()` calls in codebase
- Look for new AJAX handlers (`wp_ajax_*` actions)
- Ensure ALL new features follow WordPress security standards
- Verify nonce verification in all new AJAX endpoints
- Check capability requirements (`manage_options`) are enforced
- Validate authorization system integration for new features

### 3. SLMM SEO Bundle Specific Requirements
- Verify dual-system architecture (buttons vs shortcuts) maintained
- Check keyboard shortcut system functionality not broken
- Ensure authorization system (`slmm_seo_check_visibility_authorization`) integrated
- Validate Bricks Builder compatibility maintained
- Test Classic Editor integration remains functional
- Verify all new features respect 800-line file limit

### 4. Feature Integration Verification
- **AI Providers**: New providers integrated with existing prompt system
- **Settings Integration**: All features have proper settings panel integration
- **Security**: All features implement required security patterns (nonces, capabilities)
- **Memory Bank**: New patterns documented for future development

## Version History Analysis

When looking for updates:
- Go back to last committed version on main/origin branch
- Move forward to identify all changes since last release
- Focus on WordPress-specific improvements and new features
- Include any new AI integration or settings functionality
- Document security improvements and WordPress standards compliance

## Changelog Format

Follow existing WordPress plugin changelog structure:
- Use semantic versioning (major.minor.patch)
- Use today's date for release
- Include technical details for WordPress plugin changes
- Mention security improvements and new features
- Create summary statement for Github commit description

## Commit Message Standards

### Git Commit Format
Start Github messages with version number followed by message:
```
v6.1.1 improves search and replace UI with preview functionality
```

### `comGen` Command Workflow
When running "comGen" command:
1. Generate comprehensive commit message based on recent changes
2. Follow established commit message format with version prefix
3. Include technical details for WordPress plugin specific changes
4. Prepare for Github push to main/origin branch

## Release Workflow

1. **Analysis Phase**: Review all changes since last version
2. **Documentation Phase**: Update changelog and version in plugin.php
3. **Verification Phase**: Test WordPress integration, security, features
4. **Commit Phase**: Use proper commit message format with version prefix
5. **Release Phase**: Push to main/origin branch

## WordPress Plugin Specific Considerations

### WordPress Standards Compliance
- Ensure all new code follows WordPress Coding Standards
- Verify hook usage follows WordPress best practices
- Check database queries use prepared statements
- Validate all output is properly escaped

### Security Requirements
- All AJAX endpoints have proper nonce verification
- Capability checks (`manage_options`) enforced on all admin features
- Authorization system integration for all new functionality
- Input sanitization and output escaping implemented

### Plugin Architecture
- Maintain 800-line file size limit (split files if needed)
- Follow established directory structure in includes/
- Preserve dual-system architecture (button + keyboard shortcuts)
- Keep plugin.php integration pattern for new files

### Brand Consistency
- Maintain SLMM SEO Bundle styling standards
- Follow established UI patterns in settings panels
- Preserve authorization system visual indicators
- Maintain Classic Editor focus and compatibility

## Testing Requirements

Before version release:
- Test in WordPress admin dashboard with Classic Editor
- Verify all AI providers (OpenAI, OpenRouter, Anthropic) functional
- Test authorization system with different user roles
- Validate keyboard shortcuts work independently of button system
- Test Bricks Builder compatibility if applicable
- Verify no PHP errors or warnings in debug mode
- Test settings import/export functionality if changed

## Automatic Commands

After completing all manual steps above, automatically execute:
```
/compile
```