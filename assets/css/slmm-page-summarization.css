/**
 * SLMM Page Summarization - CSS Styles
 * 
 * Styles for summary buttons, popups, and animations
 * 
 * @version 4.10.0
 */

/* Summary <PERSON><PERSON> Styles */
.slmm-node-summary-button {
    fill: #6b7280;
    stroke: #4b5563;
    stroke-width: 1;
    cursor: pointer;
    transition: all 0.3s ease;
}

.slmm-node-summary-button:hover {
    fill: #9ca3af;
    stroke: #6b7280;
}

.slmm-node-summary-button.has-summary {
    fill: #3b82f6;
    stroke: #2563eb;
    stroke-width: 2;
}

.slmm-node-summary-button.has-summary:hover {
    fill: #2563eb;
    stroke: #1d4ed8;
    filter: brightness(1.1);
}

.slmm-node-summary-button.generating {
    fill: #f59e0b;
    stroke: #d97706;
    stroke-width: 2;
}

.slmm-node-summary-button.error {
    fill: #ef4444;
    stroke: #dc2626;
    stroke-width: 2;
}

.slmm-node-summary-icon {
    fill: white;
    font-size: 10px;
    font-weight: bold;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    pointer-events: none;
    user-select: none;
    text-anchor: middle;
    dominant-baseline: central;
}

/* Summary Popup Styles */
.slmm-summary-popup {
    position: fixed;
    background: #1e1e1e !important;
    border: 1px solid #404040 !important;
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6) !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 13px;
    line-height: 1.4;
    z-index: 999999;
    max-width: 400px;
    min-width: 300px;
    animation: slideInFade 0.2s ease-out;
    color: #e0e0e0 !important;
}

.slmm-summary-popup::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid #374151;
}

.slmm-summary-popup.popup-left::before {
    left: auto;
    right: -8px;
    border-right: none;
    border-left: 8px solid #374151;
}

/* Popup Header */
.slmm-summary-header {
    padding: 16px 20px 12px;
    border-bottom: 1px solid #404040;
    background: #2a2a2a;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.slmm-summary-header h4 {
    margin: 0;
    font-weight: 600;
    color: #ffffff;
    font-size: 16px;
}

.slmm-summary-close {
    background: transparent;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 20px;
    line-height: 1;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.slmm-summary-close:hover {
    background: #404040;
    color: #fff;
}

.slmm-summary-meta {
    padding: 16px 20px;
    background: #252525;
    border-bottom: 1px solid #404040;
}

.slmm-summary-title,
.slmm-summary-url {
    margin-bottom: 8px;
    font-size: 13px;
    line-height: 1.4;
    color: #d0d0d0;
}

.slmm-summary-title:last-child,
.slmm-summary-url:last-child {
    margin-bottom: 0;
}

.slmm-summary-title strong,
.slmm-summary-url strong {
    color: #4a9eff;
    font-weight: 500;
}

/* Popup Content */
.slmm-summary-text {
    padding: 20px;
    color: #e0e0e0;
    word-wrap: break-word;
    line-height: 1.6;
    font-size: 14px;
    max-height: 300px;
    overflow-y: auto;
    background: #1e293b;
}

.slmm-summary-content::-webkit-scrollbar {
    width: 4px;
}

.slmm-summary-content::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 2px;
}

.slmm-summary-content::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 2px;
}

.slmm-summary-content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Popup Footer */
.slmm-summary-footer {
    padding: 8px 16px;
    border-top: 1px solid #374151;
    font-size: 11px;
    color: #94a3b8;
    text-align: center;
    background: rgba(55, 65, 81, 0.3);
}

/* Popup Actions */
.slmm-summary-actions {
    padding: 16px 20px;
    border-top: 1px solid #404040;
    background: #252525;
    border-radius: 0 0 8px 8px;
    display: flex;
    gap: 12px;
    justify-content: flex-end;
}

.slmm-summary-rescan,
.slmm-summary-delete {
    background: #404040;
    border: 1px solid #606060;
    border-radius: 6px;
    color: #e0e0e0;
    cursor: pointer;
    font-size: 12px;
    padding: 8px 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.slmm-summary-rescan:hover {
    background: #4a9eff;
    border-color: #4a9eff;
    color: #ffffff;
}

.slmm-summary-delete {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}

.slmm-summary-delete:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
    color: #ffffff !important;
}

.slmm-summary-close {
    color: #dc3545 !important;
}

.slmm-summary-close:hover {
    background: #dc3545 !important;
    color: #fff !important;
}

.slmm-summary-rescan:active,
.slmm-summary-delete:active {
    transform: translateY(1px);
}

/* Notification Styles */
.slmm-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 999999;
    font-size: 14px;
    font-weight: 500;
    max-width: 300px;
    word-wrap: break-word;
    animation: slideInRight 0.3s ease-out;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.slmm-notification-success {
    background: #10b981;
    color: white;
}

.slmm-notification-error {
    background: #ef4444;
    color: white;
}

.slmm-notification-info {
    background: #3b82f6;
    color: white;
}

/* Settings Page Styles - Now handled by slmm-interlinking-prompts.css for consistency */

.slmm-page-summarization-prompt {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Model Selector Styles (matching existing interlinking prompts) */
.slmm-page-summarization-prompt .slmm-model-selector {
    display: flex;
    align-items: center;
    gap: 8px;
}

.slmm-page-summarization-prompt .slmm-model-selector select {
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
}

.slmm-page-summarization-prompt .slmm-model-selector button {
    padding: 6px 10px;
    border: 1px solid #ddd;
    background: #f7f7f7;
    border-radius: 4px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.slmm-page-summarization-prompt .slmm-model-selector button:hover {
    background: #e7e7e7;
}

/* Control Styles */
.slmm-gpt-prompt-controls {
    margin: 15px 0;
}

.slmm-control-row {
    display: flex;
    gap: 20px;
    align-items: center;
}

.slmm-control-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.slmm-control-label {
    font-size: 12px;
    font-weight: 600;
    color: #555;
    min-width: 80px;
}

.slmm-temperature-slider {
    width: 100px;
}

.slmm-temperature-value {
    font-size: 12px;
    color: #666;
    min-width: 30px;
    text-align: center;
}

.slmm-max-tokens-input {
    width: 80px;
    padding: 4px 6px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 12px;
}

/* Prompt Content Styles */
.slmm-gpt-prompt-content {
    margin-top: 15px;
}

.slmm-prompt-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
    margin-bottom: 5px;
    display: block;
}

.slmm-prompt-description {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
    font-style: italic;
}

.slmm-gpt-prompt-textarea {
    width: 100%;
    min-height: 100px;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.slmm-gpt-prompt-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* Prompt Actions */
.slmm-prompt-actions {
    margin-top: 10px;
    display: flex;
    gap: 8px;
}

.slmm-prompt-actions button {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background: #f7f7f7;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.slmm-prompt-actions button:hover {
    background: #e7e7e7;
    border-color: #ccc;
}

.slmm-prompt-actions .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Animations */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes slideInFade {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Button State Transitions */
.slmm-node-summary-button {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slmm-node-summary-button:hover {
    filter: brightness(1.1) saturate(1.2);
}

/* Loading State */
.slmm-node-summary-button.generating .slmm-node-summary-icon {
    animation: spin 1s linear infinite;
    transform-origin: center;
}

/* Accessibility */
.slmm-node-summary-button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

.slmm-summary-actions button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .slmm-summary-popup {
        max-width: calc(100vw - 40px);
        min-width: 280px;
    }
    
    .slmm-notification {
        max-width: calc(100vw - 40px);
        right: 20px;
        left: 20px;
    }
}

/* Dark Theme Compatibility */
.slmm-interlinking-theme .slmm-page-summarization-section {
    background: var(--slmm-dark-surface);
    border-color: var(--slmm-dark-border);
}

.slmm-interlinking-theme .slmm-page-summarization-section h3 {
    color: var(--slmm-text-primary);
}

.slmm-interlinking-theme .slmm-page-summarization-section p {
    color: var(--slmm-text-secondary);
}

.slmm-interlinking-theme .slmm-page-summarization-prompt {
    background: var(--slmm-dark-bg);
    border-color: var(--slmm-dark-border);
}

.slmm-interlinking-theme .slmm-gpt-prompt-textarea {
    background: var(--slmm-dark-surface);
    border-color: var(--slmm-dark-border);
    color: var(--slmm-text-primary);
}

.slmm-interlinking-theme .slmm-gpt-prompt-textarea:focus {
    border-color: var(--slmm-primary);
    box-shadow: 0 0 0 2px var(--slmm-primary-light);
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .slmm-node-summary-button {
        stroke-width: 2;
    }
    
    .slmm-summary-popup {
        border-width: 2px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .slmm-node-summary-button,
    .slmm-summary-popup,
    .slmm-notification {
        animation: none;
        transition: none;
    }
    
    .slmm-node-summary-button.generating .slmm-node-summary-icon {
        animation: none;
    }
}