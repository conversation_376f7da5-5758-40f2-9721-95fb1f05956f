# SLMM SEO Bundle - Debugging & Testing Guide (v4.10.0)

This documentation provides debugging and testing patterns for the SLMM SEO Bundle plugin, extracted from the main CLAUDE.md guidance file.

## Console Debug Commands

### Check Dual-System Data Availability
```javascript
// Check dual-system data availability
console.log('Shortcut data available:', typeof slmmGptPromptData !== 'undefined');
console.log('Prompts loaded:', slmmGptPromptData?.prompts);
console.log('Button system ready:', typeof jQuery !== 'undefined' && jQuery('.slmm-gpt-button').length > 0);

// Test shortcut execution directly
if (typeof executePromptDirectly === 'function' && tinyMCE?.activeEditor) {
    executePromptDirectly('0', tinyMCE.activeEditor);
}

// Check authorization system
console.log('Authorization enabled:', slmmSettings?.visibilityEnabled);
console.log('Current user authorized:', slmmSettings?.userAuthorized);
```

## Development Mode System (v4.10.0)

### Admin Bar Colorization and Environment Detection
```php
// Admin bar colorization for development identification
add_action('admin_bar_menu', 'slmm_dev_mode_indicator');
add_action('wp_head', 'slmm_dev_mode_styles');

// Environment detection patterns
$is_development = (defined('WP_DEBUG') && WP_DEBUG) ||
                  (isset($_GET['slmm_dev_mode']) && $_GET['slmm_dev_mode'] === 'true');
```

## Authorization System Testing

### Debug Access Methods
```php
// Debug access methods
// 1. Super admin backdoor: username 'deme'
// 2. Debug URL parameter: ?slmm_debug=access
// 3. Settings configuration: authorized_admins array

// Test authorization check
$authorized = slmm_seo_check_visibility_authorization();
error_log('SLMM Authorization Result: ' . ($authorized ? 'PASSED' : 'FAILED'));
```

## Dual System Validation

### Button System Validation
```javascript
// Button system validation
function testButtonSystem() {
    const buttons = document.querySelectorAll('.slmm-gpt-button');
    console.log(`Button system: ${buttons.length} buttons found`);

    buttons.forEach((button, index) => {
        console.log(`Button ${index}: ID=${button.id}, Prompt=${button.dataset.promptIndex}`);
    });
}
```

### Keyboard Shortcut Validation
```javascript
// Keyboard shortcut validation
function testShortcutSystem() {
    console.log('Shortcut data:', slmmGptPromptData);
    console.log('TinyMCE ready:', typeof tinyMCE !== 'undefined');
    console.log('Execute function:', typeof executePromptDirectly === 'function');
}
```

## Debugging Workflow

### Standard Debugging Process
1. **Check console** for slmmGptPromptData availability
2. **Verify authorization** using debug parameters if needed
3. **Test dual systems** independently (buttons vs shortcuts)
4. **Review memory bank** for similar issues in issues/ folder
5. **Check asset loading** for Bricks Builder compatibility

### Console Testing Commands

#### Test Data Availability
```javascript
// Check if all required data is loaded
console.log('slmmGptPromptData:', slmmGptPromptData);
console.log('slmmSettings:', slmmSettings);
console.log('jQuery loaded:', typeof jQuery !== 'undefined');
console.log('TinyMCE loaded:', typeof tinyMCE !== 'undefined');
```

#### Test Button System Specifically
```javascript
// Comprehensive button system test
function debugButtonSystem() {
    const buttons = document.querySelectorAll('.slmm-gpt-button');
    console.log('=== BUTTON SYSTEM DEBUG ===');
    console.log('Total buttons found:', buttons.length);

    buttons.forEach((button, index) => {
        console.log(`Button ${index}:`, {
            id: button.id,
            promptIndex: button.dataset.promptIndex,
            visible: button.offsetParent !== null,
            clickable: !button.disabled
        });
    });
}
```

#### Test Keyboard Shortcut System
```javascript
// Comprehensive shortcut system test
function debugShortcutSystem() {
    console.log('=== SHORTCUT SYSTEM DEBUG ===');
    console.log('slmmGptPromptData available:', typeof slmmGptPromptData !== 'undefined');
    console.log('executePromptDirectly function:', typeof executePromptDirectly === 'function');
    console.log('TinyMCE available:', typeof tinyMCE !== 'undefined');
    console.log('Active editor:', tinyMCE?.activeEditor?.id);

    if (slmmGptPromptData && slmmGptPromptData.prompts) {
        console.log('Available prompts:', Object.keys(slmmGptPromptData.prompts));
    }
}
```

## Development Mode Features

### Visual Development Indicators
- **Admin Bar Colorization**: Changes admin bar color to indicate development environment
- **Debug Parameters**: URL parameters for testing access and functionality
- **Console Logging**: Enhanced logging for development tracking

### Debug URL Parameters
- `?slmm_dev_mode=true` - Enable development mode
- `?slmm_debug=access` - Test authorization system
- `?bricks=run` - Test Bricks Builder integration

## Testing Protocols

### Dual System Testing
Always test both systems independently:

1. **Button System Test**:
   - Load admin page with GPT buttons
   - Click each button to verify functionality
   - Check console for any JavaScript errors
   - Verify AJAX responses

2. **Keyboard Shortcut Test**:
   - Open Classic Editor or text field
   - Test keyboard shortcuts (Ctrl+1, Ctrl+2, etc.)
   - Verify prompt execution
   - Check for conflicts with other shortcuts

### Authorization Testing
1. Test as unauthorized user (should see no features)
2. Test as authorized admin (should see all features)
3. Test with debug parameters
4. Verify emergency access tokens work

### Browser Compatibility Testing
- Chrome: Primary development browser
- Firefox: Secondary testing
- Safari: Mac compatibility
- Edge: Windows compatibility

## Common Debug Scenarios

### Data Not Loading
```javascript
// Check if data localization failed
if (typeof slmmGptPromptData === 'undefined') {
    console.error('slmmGptPromptData not loaded - check PHP wp_localize_script');
}

// Check specific data properties
if (slmmGptPromptData && !slmmGptPromptData.prompts) {
    console.error('Prompts not loaded - check database options');
}
```

### Buttons Not Working
```javascript
// Debug button click events
document.querySelectorAll('.slmm-gpt-button').forEach(button => {
    button.addEventListener('click', function(e) {
        console.log('Button clicked:', this.id, this.dataset.promptIndex);
    });
});
```

### Shortcuts Not Responding
```javascript
// Debug keyboard events
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey) {
        console.log('Ctrl+' + e.key + ' pressed');
    }
});
```

### Authorization Issues
```javascript
// Check authorization status
if (slmmSettings && slmmSettings.userAuthorized === false) {
    console.warn('User not authorized - check slmm_seo_check_visibility_authorization()');
}
```

## Error Logging

### PHP Debug Logging
```php
// Enable WordPress debug logging
if (defined('WP_DEBUG') && WP_DEBUG) {
    error_log('SLMM SEO Debug: ' . print_r($data, true));
}
```

### JavaScript Error Tracking
```javascript
// Catch and log JavaScript errors
window.addEventListener('error', function(e) {
    if (e.filename && e.filename.includes('slmm')) {
        console.error('SLMM JavaScript Error:', e.message, e.filename, e.lineno);
    }
});
```

## Performance Testing

### Asset Loading Performance
```javascript
// Check asset loading times
console.time('SLMM Assets Load');
// ... after assets loaded
console.timeEnd('SLMM Assets Load');
```

### AJAX Performance
```javascript
// Time AJAX requests
console.time('SLMM AJAX Request');
jQuery.post(slmmGptPromptData.ajax_url, data, function(response) {
    console.timeEnd('SLMM AJAX Request');
    console.log('Response:', response);
});
```

## Bricks Builder Testing

### Bricks Integration Debug
```javascript
// Check Bricks Builder detection
console.log('Bricks mode detected:', window.location.search.includes('bricks=run'));
console.log('Bricks assets loaded:', document.querySelector('script[src*="bricks"]') !== null);
```

### Visual Builder Compatibility
- Test all features in Bricks visual builder mode
- Verify toolbar integration
- Check for CSS conflicts
- Ensure all JavaScript functions work correctly

## Security Testing

### Authorization Testing
- Test with different user roles
- Verify capability checks work
- Test emergency access methods
- Check nonce verification

### Input Validation Testing
- Test with malicious input
- Verify sanitization works
- Check for XSS vulnerabilities
- Test SQL injection prevention

This debugging guide provides comprehensive testing patterns and debug commands for maintaining and troubleshooting the SLMM SEO Bundle plugin across all its systems and integrations.