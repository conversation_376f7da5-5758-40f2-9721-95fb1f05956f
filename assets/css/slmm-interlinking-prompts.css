/* AI Interlinking Prompts - Matching Features Tab Design System */

/* Auto-Segmentation and Page Summarization Grid Layout */
.slmm-interlinking-suite-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Auto-Segmentation Section Styling */
.slmm-auto-segmentation-section {
    background: transparent;
    border: 1px solid #3b82f6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 0;
    max-width: none;
}

/* Page Summarization Section Styling - Match Auto-Segmentation */
.slmm-page-summarization-section {
    background: #1e293b !important;
    border: 1px solid #334155 !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    margin-bottom: 0 !important;
    max-width: none !important;
}

/* CSV Import Configuration Section Styling - Match Auto-Segmentation */
.slmm-csv-import-config-section {
    background: transparent;
    border: 1px solid #3b82f6;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 0;
    max-width: none;
    min-height: 400px;
}

/* Auto-Segmentation Section - Add matching min-height */
.slmm-auto-segmentation-section {
    min-height: 400px;
}

/* CSV Import Debug Checkbox - Make visible */
.slmm-csv-import-config-section #csv_import_debug_mode + .slmm-checkmark {
    display: block !important;
    visibility: visible !important;
    width: 20px !important;
    height: 20px !important;
    border: 2px solid #334155 !important;
    border-radius: 4px !important;
    background: #0f172a !important;
    position: relative;
    flex-shrink: 0;
}

.slmm-csv-import-config-section #csv_import_debug_mode:checked + .slmm-checkmark {
    background: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

.slmm-csv-import-config-section #csv_import_debug_mode:checked + .slmm-checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.slmm-csv-import-config-section .slmm-checkbox-label {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    color: #f1f5f9;
    font-size: 0.875rem;
}

.slmm-auto-segmentation-section h3,
.slmm-page-summarization-section h3,
.slmm-csv-import-config-section h3 {
    color: #f1f5f9 !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    margin-bottom: 0.5rem !important;
    margin-top: 0 !important;
}

.slmm-auto-segmentation-section > p,
.slmm-page-summarization-section > p,
.slmm-csv-import-config-section > p {
    color: #94a3b8 !important;
    margin-bottom: 1.5rem !important;
    font-size: 0.875rem !important;
}

/* Ensure all form elements in page summarization match dark theme */
.slmm-page-summarization-section .slmm-interlinking-prompt {
    background: #1e293b !important;
    border: 1px solid #334155 !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    transition: all 0.2s ease !important;
}

.slmm-page-summarization-section .slmm-interlinking-prompt:hover {
    border-color: #475569 !important;
    background: #263241 !important;
}

.slmm-page-summarization-section .slmm-gpt-prompt-content {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 1rem !important;
    border-radius: 6px !important;
    min-height: 120px !important;
    resize: vertical !important;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
    font-size: 0.875rem !important;
    line-height: 1.5 !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.slmm-page-summarization-section .slmm-gpt-prompt-content:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.slmm-page-summarization-section .slmm-gpt-prompt-title {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.slmm-page-summarization-section .slmm-interlinking-provider,
.slmm-page-summarization-section .slmm-gpt-prompt-model {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    min-height: 38px !important;
    font-size: 0.875rem !important;
}

.slmm-page-summarization-section .slmm-interlinking-provider:focus,
.slmm-page-summarization-section .slmm-gpt-prompt-model:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.slmm-page-summarization-section .slmm-refresh-interlinking-models {
    background: #374151 !important;
    border: 1px solid #4b5563 !important;
    color: #f1f5f9 !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    min-height: 38px !important;
    font-size: 1rem !important;
}

.slmm-page-summarization-section .slmm-refresh-interlinking-models:hover {
    background: #4b5563 !important;
    border-color: #6b7280 !important;
}

.slmm-page-summarization-section .slmm-gpt-prompt-temperature,
.slmm-page-summarization-section .slmm-gpt-prompt-max-tokens {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.375rem 0.5rem !important;
    border-radius: 4px !important;
    width: 80px !important;
    font-size: 0.875rem !important;
}

.slmm-page-summarization-section .slmm-gpt-prompt-temperature:focus,
.slmm-page-summarization-section .slmm-gpt-prompt-max-tokens:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* Additional targeting for exact match */
.slmm-interlinking-prompt[data-type="summarization"] .slmm-gpt-prompt-temperature,
.slmm-interlinking-prompt[data-type="summarization"] .slmm-gpt-prompt-max-tokens {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.375rem 0.5rem !important;
    border-radius: 4px !important;
    width: 80px !important;
    font-size: 0.875rem !important;
}

.slmm-interlinking-prompt[data-type="summarization"] .slmm-gpt-prompt-temperature:focus,
.slmm-interlinking-prompt[data-type="summarization"] .slmm-gpt-prompt-max-tokens:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* Override any WordPress core input styling */
.slmm-interlinking-prompt[data-type="summarization"] input[type="number"] {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.375rem 0.5rem !important;
    border-radius: 4px !important;
    width: 80px !important;
    font-size: 0.875rem !important;
    height: auto !important;
    box-shadow: none !important;
}

.slmm-form-field {
    margin-bottom: 1.25rem !important;
}

.slmm-field-label {
    color: #f1f5f9 !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    display: block !important;
    margin-bottom: 0.5rem !important;
}

.slmm-percentage-input-wrapper {
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
    margin-bottom: 0.5rem !important;
    max-width: 300px !important;
}

.slmm-percentage-input {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.5rem 0.75rem !important;
    border-radius: 6px !important;
    font-size: 0.875rem !important;
    width: 70px !important;
    flex-shrink: 0 !important;
}

.slmm-percentage-input:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

.slmm-percentage-unit,
.slmm-percentage-label {
    color: #94a3b8 !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
}

.slmm-field-description {
    color: #64748b !important;
    font-size: 0.75rem !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0 !important;
}

/* Main AI Interlinking container styling */
.slmm-ai-interlinking-section {
    margin-top: 2rem;
}

.slmm-ai-interlinking-section h3 {
    color: #f1f5f9;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.slmm-ai-interlinking-section p {
    color: #94a3b8;
    margin-bottom: 2rem;
}

/* Container for all prompt boxes */
#slmm-interlinking-prompts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Individual prompt box - matching Features tab card design */
.slmm-interlinking-prompt {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.slmm-interlinking-prompt:hover {
    border-color: #475569;
    background: #263241;
}

/* Header section */
.slmm-gpt-prompt-header {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.slmm-gpt-prompt-title {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 600 !important;
    font-size: 1rem !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

.slmm-gpt-prompt-handle {
    display: none;
}

/* Model selector section */
.slmm-model-selector {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.slmm-model-selector > div:first-child {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.slmm-interlinking-provider,
.slmm-gpt-prompt-model {
    background: #0f172a;
    border: 1px solid #334155;
    color: #f1f5f9;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    min-height: 38px;
    font-size: 0.875rem;
}

.slmm-interlinking-provider:focus,
.slmm-gpt-prompt-model:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.slmm-refresh-interlinking-models {
    background: #374151;
    border: 1px solid #4b5563;
    color: #f1f5f9;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    cursor: pointer;
    min-height: 38px;
    font-size: 1rem;
}

.slmm-refresh-interlinking-models:hover {
    background: #4b5563;
    border-color: #6b7280;
}

/* API key note */
.slmm-model-selector em {
    color: #94a3b8;
    font-size: 0.75rem;
    font-style: italic;
}

/* Prompt textarea */
.slmm-gpt-prompt-content {
    background: #0f172a;
    border: 1px solid #334155;
    color: #f1f5f9;
    padding: 1rem;
    border-radius: 6px;
    min-height: 120px;
    resize: vertical;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
    font-size: 0.875rem;
    line-height: 1.5;
    width: 100%;
    box-sizing: border-box;
}

.slmm-gpt-prompt-content:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.slmm-gpt-prompt-content::placeholder {
    color: #64748b;
}

/* Footer controls */
.slmm-gpt-prompt-footer {
    display: flex;
    gap: 1.5rem;
    margin-top: 1rem;
    align-items: center;
}

.slmm-gpt-prompt-footer label {
    color: #94a3b8;
    font-size: 0.875rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.slmm-gpt-prompt-temperature,
.slmm-gpt-prompt-max-tokens {
    background: #0f172a;
    border: 1px solid #334155;
    color: #f1f5f9;
    padding: 0.375rem 0.5rem;
    border-radius: 4px;
    width: 80px;
    font-size: 0.875rem;
}

.slmm-gpt-prompt-temperature:focus,
.slmm-gpt-prompt-max-tokens:focus {
    border-color: #3b82f6;
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Linking Rules Section */
.slmm-linking-rules-section {
    background: #1e293b;
    border: 1px solid #334155;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.slmm-linking-rules-section h4 {
    color: #f1f5f9;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.slmm-linking-rules-section p {
    color: #94a3b8;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

#interlinking_rules {
    background: #0f172a !important;
    border: 1px solid #334155 !important;
    color: #f1f5f9 !important;
    padding: 1rem !important;
    border-radius: 6px !important;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
    font-size: 0.875rem !important;
    line-height: 1.6 !important;
    min-height: 200px !important;
    resize: vertical !important;
}

#interlinking_rules:focus {
    border-color: #3b82f6 !important;
    outline: none !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

#interlinking_rules::placeholder {
    color: #64748b !important;
}

/* Responsive design */
@media (max-width: 1200px) {
    #slmm-interlinking-prompts-container {
        grid-template-columns: 1fr;
        gap: 1.25rem;
    }
}

/* Hover states and animations */
.slmm-interlinking-prompt:hover .slmm-gpt-prompt-title {
    border-color: #475569;
}

.slmm-interlinking-prompt:hover .slmm-gpt-prompt-content {
    border-color: #475569;
}

/* Loading states for refresh buttons */
.slmm-refresh-interlinking-models.loading {
    opacity: 0.7;
    cursor: not-allowed;
}

.slmm-refresh-interlinking-models.loading::after {
    content: ' ↻';
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}