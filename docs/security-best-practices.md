# SLMM SEO Bundle - Security Best Practices Guide

**Document Version**: 1.0  
**Date**: September 16, 2025  
**Context**: Admin-only plugin with whitelisted user access and client-side AI integration  
**Status**: Active - Based on comprehensive security audit and successful remediation (26/34 tasks completed)

## Table of Contents

1. [Security Context & Philosophy](#security-context--philosophy)
2. [Authorization System Integration](#authorization-system-integration)
3. [AJAX Handler Security Standards](#ajax-handler-security-standards)
4. [Database Security Patterns](#database-security-patterns)
5. [XSS Prevention Guidelines](#xss-prevention-guidelines)
6. [Client-Side AI Security](#client-side-ai-security)
7. [Dual-System Architecture Security](#dual-system-architecture-security)
8. [WordPress Integration Security](#wordpress-integration-security)
9. [Input Validation & Sanitization](#input-validation--sanitization)
10. [Security Testing & Validation](#security-testing--validation)
11. [Emergency Access & Recovery](#emergency-access--recovery)
12. [Code Review Checklist](#code-review-checklist)

---

## Security Context & Philosophy

### Admin-Only, Whitelisted Environment

The SLMM SEO Bundle operates in a **significantly reduced attack surface** compared to public-facing plugins:

- **Admin-only access**: All functionality requires `manage_options` capability minimum
- **Whitelisted users**: Authorization system restricts access to approved admin users
- **Internal tool**: Used by content creators and SEO professionals, not end users
- **Controlled environment**: WordPress admin dashboard with existing security layers

### Defense-in-Depth Approach

While the admin-only context reduces risk, we implement **multiple security layers**:

1. **Authorization Layer**: Custom visibility system + WordPress capabilities
2. **Request Validation**: Nonce verification + input sanitization  
3. **Database Protection**: Prepared statements + query validation
4. **Output Escaping**: XSS prevention on all dynamic content
5. **Emergency Access**: Super admin backdoor for recovery scenarios

### Risk Assessment Reality

**REVISED RISK LEVELS** (Based on admin-only context):
- **Public-facing vulnerabilities**: High → Medium (admin access required)
- **SQL injection**: Critical → High (limited to admin users)
- **XSS attacks**: High → Medium (admin context, trusted users)
- **CSRF attacks**: Medium → Low (nonce protection + admin sessions)

**Note**: While risks are reduced, we maintain high security standards for **defense-in-depth** and **audit compliance**.

---

## Authorization System Integration

### Core Authorization Function

**CRITICAL**: All features must integrate with the plugin's authorization system:

```php
// MANDATORY: Check authorization before feature initialization
if (!slmm_seo_check_visibility_authorization()) {
    return; // Stop execution if user not authorized
}
```

### Authorization Patterns

#### ✅ **CORRECT**: Dual Authorization Check
```php
// 1. Plugin-level authorization
if (!slmm_seo_check_visibility_authorization()) {
    wp_send_json_error(array('message' => 'Access denied'));
    return;
}

// 2. WordPress capability check
if (!current_user_can('manage_options')) {
    wp_send_json_error(array('message' => 'Insufficient permissions'));
    return;
}
```

#### ❌ **INCORRECT**: WordPress-only Check
```php
// Missing plugin-level authorization - bypasses whitelist
if (!current_user_can('manage_options')) {
    wp_die('Insufficient permissions');
}
```

### Emergency Access Methods

**Super Admin Backdoor** (PRESERVE - DO NOT REMOVE):
- Username `deme` always has access
- URL parameter `?slmm_debug=access` for emergency access
- Settings-based authorized admin list

### Capability Mapping

**Standard capability requirements**:
- **Basic features**: `manage_options` (admin users)
- **Content editing**: `edit_posts` or `edit_pages`
- **Database operations**: `manage_options` (admin only)
- **Settings management**: `manage_options` (admin only)

---

## AJAX Handler Security Standards

### Mandatory Security Pattern

**EVERY AJAX handler MUST implement this exact pattern**:

```php
class SLMM_Feature_AJAX_Handler {
    
    public function handle_ajax_action() {
        // 1. AUTHORIZATION CHECK (plugin-level)
        if (!slmm_seo_check_visibility_authorization()) {
            wp_send_json_error(array('message' => 'Access denied'));
            return;
        }
        
        // 2. NONCE VERIFICATION (robust pattern)
        $nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
        if (!wp_verify_nonce($nonce, 'your_nonce_action')) {
            wp_send_json_error(array('message' => 'Security check failed'));
            return;
        }
        
        // 3. CAPABILITY CHECK (WordPress-level)
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => 'Insufficient permissions'));
            return;
        }
        
        // 4. INPUT SANITIZATION
        $input = sanitize_textarea_field($_POST['content'] ?? '');
        $post_id = absint($_POST['post_id'] ?? 0);
        
        // 5. VALIDATION
        if (empty($input) || $post_id <= 0) {
            wp_send_json_error(array('message' => 'Invalid input data'));
            return;
        }
        
        // 6. PROCESS REQUEST
        $result = $this->process_request($input, $post_id);
        
        // 7. RETURN RESPONSE
        wp_send_json_success($result);
    }
}
```

### Nonce Handling Standards

**Based on `/docs/nonce-handling-standards.md` - Use robust nonce validation**:

```php
// ✅ RECOMMENDED: Accept both parameter formats
$nonce = $_POST['nonce'] ?? $_POST['security'] ?? '';
if (!wp_verify_nonce($nonce, 'slmm_execute_gpt_prompt')) {
    wp_send_json_error(array('message' => 'Invalid nonce'));
    return;
}
```

**JavaScript side - ALWAYS use 'nonce' parameter**:
```javascript
$.ajax({
    url: ajaxUrl,
    type: 'POST',
    data: {
        action: 'slmm_action',
        nonce: slmmData.nonce,  // Consistent naming
        content: content
    }
});
```

### Multi-Nonce Support Pattern

**For components that integrate with multiple systems**:

```php
// Accept multiple valid nonces for system integration
$nonce_valid = wp_verify_nonce($nonce, 'slmm_direct_edit_nonce') || 
               wp_verify_nonce($nonce, 'slmm_execute_gpt_prompt') ||
               wp_verify_nonce($nonce, 'slmm_interlinking_nonce');
               
if (!$nonce_valid) {
    wp_send_json_error(array('message' => 'Security check failed'));
    return;
}
```

### AJAX Registration Pattern

```php
// Standard AJAX hook registration
add_action('wp_ajax_slmm_action_name', array($this, 'handle_action'));
// Note: No wp_ajax_nopriv_ hooks - admin-only plugin
```

---

## Database Security Patterns

### SQL Injection Prevention

**MANDATORY**: All database queries MUST use prepared statements.

#### ✅ **CORRECT**: Prepared Statements
```php
// Safe database query with prepared statements
global $wpdb;
$results = $wpdb->get_results(
    $wpdb->prepare(
        "SELECT * FROM {$wpdb->posts} WHERE post_type = %s AND post_status = %s AND ID > %d",
        $post_type,
        $status,
        $min_id
    )
);
```

#### ❌ **DANGEROUS**: Direct Variable Insertion
```php
// NEVER DO THIS - SQL injection vulnerability
$results = $wpdb->get_results(
    "SELECT * FROM {$wpdb->posts} WHERE post_type = '{$post_type}'"
);
```

### WordPress Database Patterns

**Use WordPress database methods when possible**:

```php
// ✅ WordPress-native methods (automatically safe)
$post = get_post($post_id);
$meta = get_post_meta($post_id, 'meta_key', true);
update_post_meta($post_id, 'meta_key', $meta_value);

// ✅ WordPress query classes
$query = new WP_Query(array(
    'post_type' => 'page',
    'meta_query' => array(
        array(
            'key' => 'custom_field',
            'value' => $search_value,
            'compare' => 'LIKE'
        )
    )
));
```

### Search & Replace Security

**For database search/replace operations** (high-risk functionality):

```php
// Enhanced security for search/replace operations
class SLMM_Database_SearchReplace {
    
    public function perform_search_replace($search, $replace, $tables) {
        // 1. Enhanced authorization (database operations are high-risk)
        if (!current_user_can('manage_options')) {
            return new WP_Error('insufficient_permissions', 'Database operations require admin access');
        }
        
        // 2. Input validation and sanitization
        $search = sanitize_text_field($search);
        $replace = sanitize_text_field($replace);
        
        // 3. Validate table names against whitelist
        $allowed_tables = $this->get_allowed_tables();
        $tables = array_intersect($tables, $allowed_tables);
        
        // 4. Use prepared statements for all queries
        global $wpdb;
        foreach ($tables as $table) {
            $wpdb->query(
                $wpdb->prepare(
                    "UPDATE `{$table}` SET column_name = REPLACE(column_name, %s, %s)",
                    $search,
                    $replace
                )
            );
        }
    }
    
    private function get_allowed_tables() {
        global $wpdb;
        return array(
            $wpdb->posts,
            $wpdb->postmeta,
            $wpdb->options,
            // Explicit whitelist only
        );
    }
}
```

### Data Validation Patterns

```php
// Input validation before database operations
function validate_database_input($data) {
    // Validate required fields
    if (empty($data['required_field'])) {
        return new WP_Error('missing_data', 'Required field missing');
    }
    
    // Validate data types
    if (!is_numeric($data['numeric_field'])) {
        return new WP_Error('invalid_type', 'Numeric field required');
    }
    
    // Validate against constraints
    if (strlen($data['text_field']) > 1000) {
        return new WP_Error('too_long', 'Text field too long');
    }
    
    return true;
}
```

---

## XSS Prevention Guidelines

### Output Escaping Standards

**MANDATORY**: All dynamic content MUST be escaped for output context.

#### ✅ **CORRECT**: Context-Appropriate Escaping
```php
// HTML content escaping
echo '<p>' . esc_html($user_content) . '</p>';

// Attribute escaping
echo '<input type="text" value="' . esc_attr($user_input) . '">';

// URL escaping
echo '<a href="' . esc_url($user_url) . '">Link</a>';

// JavaScript escaping
echo '<script>var userVar = ' . wp_json_encode($user_data) . ';</script>';

// Textarea escaping
echo '<textarea>' . esc_textarea($user_text) . '</textarea>';
```

#### ❌ **DANGEROUS**: No Escaping
```php
// NEVER output user data directly
echo '<p>' . $user_content . '</p>';  // XSS vulnerability
echo '<input type="text" value="' . $user_input . '">';  // XSS vulnerability
```

### WordPress Escaping Functions

**Use appropriate WordPress escaping functions**:

| Context | Function | Usage |
|---------|----------|-------|
| HTML Content | `esc_html()` | User-generated text in HTML |
| HTML Attributes | `esc_attr()` | Values in HTML attributes |
| URLs | `esc_url()` | User-provided URLs |
| JavaScript | `wp_json_encode()` | Data passed to JavaScript |
| Textarea | `esc_textarea()` | Content in textarea elements |
| SQL | `esc_sql()` | **AVOID** - Use prepared statements instead |

### Rich Content Handling

**For content that may contain allowed HTML**:

```php
// Use wp_kses for filtered HTML output
$allowed_html = array(
    'p' => array(),
    'br' => array(),
    'strong' => array(),
    'em' => array(),
    'a' => array(
        'href' => array(),
        'title' => array()
    )
);

echo wp_kses($user_content, $allowed_html);
```

### AJAX Response Security

```php
// Secure AJAX responses
function secure_ajax_response($data) {
    // Escape data for JSON context
    $safe_data = array(
        'title' => esc_html($data['title']),
        'content' => esc_html($data['content']),
        'url' => esc_url($data['url']),
        'html' => wp_kses_post($data['html'])  // For allowed HTML
    );
    
    wp_send_json_success($safe_data);
}
```

---

## Client-Side AI Security

### Context: Functional Requirement, Not Vulnerability

**IMPORTANT**: Client-side AI API keys are a **functional requirement** for this plugin's architecture, NOT a security vulnerability.

**Why client-side keys are necessary**:
- **Real-time content generation** during editing requires immediate API response
- **Keyboard shortcut system** executes prompts without server round-trip
- **Visual editor integration** (Bricks Builder) needs direct browser-to-API communication
- **User experience** demands instant feedback without AJAX delays

### Secure API Key Management

**Client-side keys are appropriately secured for admin-only context**:

```php
// Secure API key localization (admin users only)
function localize_ai_settings() {
    // Only expose keys to authorized admin users
    if (!slmm_seo_check_visibility_authorization() || !current_user_can('manage_options')) {
        return;
    }
    
    $api_settings = array(
        'openai_key' => get_option('chatgpt_generator_openai_api_key', ''),
        'anthropic_key' => get_option('slmm_anthropic_api_key', ''),
        'openrouter_key' => get_option('slmm_openrouter_api_key', '')
    );
    
    wp_localize_script('slmm-ai-integration', 'slmmAiSettings', $api_settings);
}
```

### API Request Security

**Client-side API calls include security measures**:

```javascript
// Secure API request pattern
function makeSecureAPIRequest(prompt, apiKey) {
    // Input validation
    if (!prompt || !apiKey) {
        console.error('Missing required parameters');
        return;
    }
    
    // Rate limiting (client-side)
    if (isRateLimited()) {
        console.warn('Rate limit exceeded');
        return;
    }
    
    // Secure headers
    const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
        'User-Agent': 'SLMM-SEO-Plugin/4.10.0'
    };
    
    // Request timeout and error handling
    fetch(apiEndpoint, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({
            model: selectedModel,
            messages: [{ role: 'user', content: prompt }],
            max_tokens: 1000,
            temperature: 0.7
        }),
        timeout: 30000
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`API request failed: ${response.status}`);
        }
        return response.json();
    })
    .catch(error => {
        console.error('AI API Error:', error);
        // Graceful degradation
    });
}
```

### API Key Storage Guidelines

**Secure storage in WordPress options**:

```php
// Encrypted storage for API keys
function store_api_key_securely($key_name, $api_key) {
    // Optional: Encrypt keys at rest
    if (function_exists('openssl_encrypt')) {
        $encryption_key = wp_salt('auth');
        $encrypted_key = openssl_encrypt($api_key, 'AES-256-CBC', $encryption_key, 0, substr($encryption_key, 0, 16));
        update_option($key_name, $encrypted_key);
    } else {
        // Fallback: Plain storage (admin-only access already restricts exposure)
        update_option($key_name, $api_key);
    }
}
```

### Content Security Policy

**For pages with AI integration**:

```php
// Add CSP headers for API domains
function add_ai_security_headers() {
    if (slmm_seo_check_visibility_authorization()) {
        header("Content-Security-Policy: connect-src 'self' https://api.openai.com https://api.anthropic.com https://openrouter.ai;");
    }
}
add_action('admin_init', 'add_ai_security_headers');
```

---

## Dual-System Architecture Security

### System Overview

The plugin implements **two parallel execution systems**:

1. **Button System**: DOM-driven via `slmm-prompt-execution.js`
2. **Keyboard Shortcut System**: Data-driven via `chat_gpt_title_and_description_generator_v2_0.php`

### Critical Security Rule

**NEVER modify keyboard shortcut system unless explicitly broken** - this is mission-critical functionality.

### Data Localization Security

**MANDATORY**: Always localize prompt data regardless of availability:

```php
// ✅ CORRECT: Always localize for dual-system support
wp_localize_script('slmm-script', 'slmmGptPromptData', array(
    'prompts' => get_option('slmm_gpt_prompts', array()),  // Empty array if no prompts
    'ajax_url' => admin_url('admin-ajax.php'),
    'nonce' => wp_create_nonce('slmm_execute_gpt_prompt'),
    'user_authorized' => slmm_seo_check_visibility_authorization()
));

// ❌ WRONG: Conditional localization breaks shortcuts
if (!empty($prompts)) {
    wp_localize_script('slmm-script', 'slmmGptPromptData', array('prompts' => $prompts));
}
```

### System-Specific Security Patterns

#### Button System Security
```javascript
// Button system security validation
function executeButtonPrompt(buttonElement) {
    // Validate authorization
    if (!slmmGptPromptData.user_authorized) {
        alert('Access denied');
        return;
    }
    
    // Validate prompt data
    const promptIndex = buttonElement.dataset.promptIndex;
    if (!slmmGptPromptData.prompts[promptIndex]) {
        console.error('Invalid prompt index');
        return;
    }
    
    // Execute with validation
    executePromptWithSecurity(promptIndex);
}
```

#### Keyboard Shortcut System Security
```php
// Keyboard shortcut authorization (in PHP handler)
function executePromptDirectly($prompt_index, $editor) {
    // Authorization check before execution
    if (!slmm_seo_check_visibility_authorization()) {
        return 'Access denied';
    }
    
    // Validate prompt index
    $prompts = get_option('slmm_gpt_prompts', array());
    if (!isset($prompts[$prompt_index])) {
        return 'Invalid prompt';
    }
    
    // Execute with security context
    return execute_secure_prompt($prompts[$prompt_index], $editor);
}
```

### Bricks Builder Integration Security

**Special considerations for visual builder context**:

```php
// Detect Bricks Builder context
$is_bricks = isset($_GET['bricks']) && $_GET['bricks'] === 'run';

if ($is_bricks) {
    // Enhanced security for visual builder
    if (!slmm_seo_check_visibility_authorization()) {
        // Fail gracefully in visual context
        wp_die('Access denied in visual builder context');
    }
    
    // Conditional asset loading
    wp_enqueue_script('slmm-bricks-integration', SLMM_SEO_PLUGIN_URL . 'assets/js/bricks-integration.js');
}
```

---

## WordPress Integration Security

### Hook Security

**Secure hook implementations**:

```php
// Secure admin menu hooks
add_action('admin_menu', function() {
    if (!slmm_seo_check_visibility_authorization()) {
        return;  // Don't add menus for unauthorized users
    }
    
    add_submenu_page(
        'options-general.php',
        'SLMM SEO Settings',
        'SLMM SEO',
        'manage_options',  // Proper capability requirement
        'slmm-seo-settings',
        'slmm_seo_settings_page'
    );
});

// Secure script enqueuing
add_action('admin_enqueue_scripts', function($hook) {
    // Only load on relevant admin pages
    if (!in_array($hook, array('post.php', 'post-new.php', 'settings_page_slmm-seo'))) {
        return;
    }
    
    // Authorization check
    if (!slmm_seo_check_visibility_authorization()) {
        return;
    }
    
    wp_enqueue_script('slmm-admin-script', SLMM_SEO_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), SLMM_SEO_VERSION);
});
```

### Settings Security

**Secure settings handling**:

```php
class SLMM_Settings_Manager {
    
    public function register_settings() {
        // Register settings with validation callbacks
        register_setting(
            'slmm_seo_settings',
            'slmm_api_keys',
            array(
                'sanitize_callback' => array($this, 'sanitize_api_keys'),
                'show_in_rest' => false,  // Never expose in REST API
                'default' => array()
            )
        );
    }
    
    public function sanitize_api_keys($input) {
        // Validate user permissions
        if (!current_user_can('manage_options')) {
            return get_option('slmm_api_keys', array());
        }
        
        $sanitized = array();
        
        // Sanitize each API key
        if (isset($input['openai_key'])) {
            $sanitized['openai_key'] = sanitize_text_field($input['openai_key']);
        }
        
        return $sanitized;
    }
}
```

### Custom Post Type Security

```php
// Secure custom post type registration
function register_secure_post_type() {
    register_post_type('slmm_content', array(
        'public' => false,              // Never public
        'publicly_queryable' => false, // Not queryable
        'show_ui' => true,             // Show in admin
        'show_in_menu' => true,
        'capability_type' => 'post',
        'capabilities' => array(
            'create_posts' => 'manage_options',  // Admin only
            'edit_posts' => 'manage_options',
            'edit_others_posts' => 'manage_options',
            'delete_posts' => 'manage_options'
        ),
        'supports' => array('title', 'editor')
    ));
}
```

### REST API Security

**If REST endpoints are needed**:

```php
// Secure REST API endpoint
register_rest_route('slmm/v1', '/secure-endpoint', array(
    'methods' => 'POST',
    'callback' => 'slmm_secure_rest_callback',
    'permission_callback' => function() {
        return current_user_can('manage_options') && slmm_seo_check_visibility_authorization();
    },
    'args' => array(
        'data' => array(
            'required' => true,
            'validate_callback' => function($param) {
                return is_string($param) && strlen($param) < 1000;
            },
            'sanitize_callback' => 'sanitize_textarea_field'
        )
    )
));
```

---

## Input Validation & Sanitization

### Comprehensive Input Handling

**ALL user input MUST be validated and sanitized**:

```php
class SLMM_Input_Validator {
    
    public static function validate_and_sanitize($input, $type, $constraints = array()) {
        switch ($type) {
            case 'text':
                $sanitized = sanitize_text_field($input);
                if (isset($constraints['max_length']) && strlen($sanitized) > $constraints['max_length']) {
                    return new WP_Error('too_long', 'Input exceeds maximum length');
                }
                return $sanitized;
                
            case 'textarea':
                $sanitized = sanitize_textarea_field($input);
                if (isset($constraints['max_length']) && strlen($sanitized) > $constraints['max_length']) {
                    return new WP_Error('too_long', 'Input exceeds maximum length');
                }
                return $sanitized;
                
            case 'url':
                $sanitized = esc_url_raw($input);
                if (empty($sanitized)) {
                    return new WP_Error('invalid_url', 'Invalid URL provided');
                }
                return $sanitized;
                
            case 'email':
                $sanitized = sanitize_email($input);
                if (!is_email($sanitized)) {
                    return new WP_Error('invalid_email', 'Invalid email address');
                }
                return $sanitized;
                
            case 'integer':
                $sanitized = absint($input);
                if (isset($constraints['min']) && $sanitized < $constraints['min']) {
                    return new WP_Error('too_small', 'Value below minimum');
                }
                if (isset($constraints['max']) && $sanitized > $constraints['max']) {
                    return new WP_Error('too_large', 'Value above maximum');
                }
                return $sanitized;
                
            case 'array':
                if (!is_array($input)) {
                    return new WP_Error('not_array', 'Expected array input');
                }
                return array_map('sanitize_text_field', $input);
                
            default:
                return new WP_Error('unknown_type', 'Unknown validation type');
        }
    }
}
```

### AJAX Input Validation Example

```php
public function handle_content_update() {
    // Authorization checks...
    
    // Validate and sanitize inputs
    $post_id = SLMM_Input_Validator::validate_and_sanitize($_POST['post_id'], 'integer', array('min' => 1));
    if (is_wp_error($post_id)) {
        wp_send_json_error(array('message' => $post_id->get_error_message()));
        return;
    }
    
    $content = SLMM_Input_Validator::validate_and_sanitize($_POST['content'], 'textarea', array('max_length' => 50000));
    if (is_wp_error($content)) {
        wp_send_json_error(array('message' => $content->get_error_message()));
        return;
    }
    
    // Additional business logic validation
    if (!get_post($post_id)) {
        wp_send_json_error(array('message' => 'Post does not exist'));
        return;
    }
    
    // Proceed with validated data...
}
```

### File Upload Security

**If file uploads are implemented**:

```php
function secure_file_upload($file) {
    // Check file type
    $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx');
    $file_ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_ext, $allowed_types)) {
        return new WP_Error('invalid_type', 'File type not allowed');
    }
    
    // Check file size (5MB limit)
    if ($file['size'] > 5 * 1024 * 1024) {
        return new WP_Error('too_large', 'File size exceeds limit');
    }
    
    // Use WordPress upload handling
    if (!function_exists('wp_handle_upload')) {
        require_once(ABSPATH . 'wp-admin/includes/file.php');
    }
    
    $upload_overrides = array('test_form' => false);
    $uploaded_file = wp_handle_upload($file, $upload_overrides);
    
    if (isset($uploaded_file['error'])) {
        return new WP_Error('upload_error', $uploaded_file['error']);
    }
    
    return $uploaded_file;
}
```

---

## Security Testing & Validation

### Automated Security Testing

**Testing patterns for security validation**:

```php
/**
 * Security Test Suite for SLMM SEO Bundle
 */
class SLMM_Security_Tests {
    
    public function test_ajax_authorization() {
        // Test unauthorized access
        wp_set_current_user(0);  // Logged out user
        $response = $this->make_ajax_request('slmm_secure_action', array('test' => 'data'));
        $this->assertContains('Access denied', $response);
        
        // Test authorized access
        $admin_user = $this->factory->user->create(array('role' => 'administrator'));
        wp_set_current_user($admin_user);
        $response = $this->make_ajax_request('slmm_secure_action', array('test' => 'data'));
        $this->assertNotContains('Access denied', $response);
    }
    
    public function test_nonce_verification() {
        $admin_user = $this->factory->user->create(array('role' => 'administrator'));
        wp_set_current_user($admin_user);
        
        // Test with invalid nonce
        $response = $this->make_ajax_request('slmm_secure_action', array(
            'nonce' => 'invalid_nonce',
            'data' => 'test'
        ));
        $this->assertContains('Security check failed', $response);
        
        // Test with valid nonce
        $valid_nonce = wp_create_nonce('slmm_secure_action');
        $response = $this->make_ajax_request('slmm_secure_action', array(
            'nonce' => $valid_nonce,
            'data' => 'test'
        ));
        $this->assertNotContains('Security check failed', $response);
    }
    
    public function test_sql_injection_prevention() {
        // Test malicious SQL input
        $malicious_input = "'; DROP TABLE wp_posts; --";
        
        $result = $this->test_search_functionality($malicious_input);
        
        // Verify database integrity
        global $wpdb;
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '{$wpdb->posts}'");
        $this->assertEquals($wpdb->posts, $table_exists, 'Posts table should still exist');
    }
    
    public function test_xss_prevention() {
        $malicious_script = '<script>alert("XSS")</script>';
        
        // Test output escaping
        $escaped_output = esc_html($malicious_script);
        $this->assertNotContains('<script>', $escaped_output);
        $this->assertContains('&lt;script&gt;', $escaped_output);
    }
}
```

### Manual Security Testing

**Security testing checklist**:

1. **Authorization Testing**:
   - Test with logged-out users
   - Test with non-admin users
   - Test with unauthorized admin users
   - Verify super admin backdoor works

2. **AJAX Security Testing**:
   - Test all AJAX endpoints without nonces
   - Test with invalid/expired nonces
   - Test with insufficient capabilities
   - Verify error messages don't leak information

3. **Input Validation Testing**:
   - Test with malicious SQL input
   - Test with XSS payloads
   - Test with extremely long inputs
   - Test with unexpected data types

4. **Database Security Testing**:
   - Verify all queries use prepared statements
   - Test search/replace with malicious input
   - Check for information disclosure in error messages

### Browser Console Testing

**Client-side security validation**:

```javascript
// Test dual-system data availability
console.log('Authorization check:', typeof slmmGptPromptData !== 'undefined');
console.log('User authorized:', slmmGptPromptData?.user_authorized);
console.log('Prompts available:', slmmGptPromptData?.prompts?.length || 0);

// Test nonce availability
console.log('Nonce available:', !!slmmGptPromptData?.nonce);

// Test API key exposure (should only show in admin context)
console.log('API settings exposed:', typeof slmmAiSettings !== 'undefined');
```

### Security Monitoring

**Log security events for monitoring**:

```php
function log_security_event($event_type, $details) {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log(sprintf(
            'SLMM Security Event [%s]: %s - User ID: %d, IP: %s',
            $event_type,
            $details,
            get_current_user_id(),
            $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ));
    }
}

// Usage in security checks
if (!wp_verify_nonce($nonce, 'slmm_action')) {
    log_security_event('NONCE_FAILURE', 'Invalid nonce in AJAX request');
    wp_send_json_error(array('message' => 'Security check failed'));
    return;
}
```

---

## Emergency Access & Recovery

### Super Admin Backdoor

**CRITICAL**: Maintain emergency access methods (DO NOT REMOVE):

```php
// Emergency access methods in slmm_seo_check_visibility_authorization()
function slmm_seo_check_visibility_authorization() {
    // 1. Super admin backdoor (username 'deme')
    $current_user = wp_get_current_user();
    if ($current_user->user_login === 'deme') {
        return true;
    }
    
    // 2. Debug URL parameter for emergency access
    if (isset($_GET['slmm_debug']) && $_GET['slmm_debug'] === 'access') {
        if (current_user_can('manage_options')) {
            return true;
        }
    }
    
    // 3. Settings-based authorized admins
    $authorized_admins = get_option('slmm_authorized_admins', array());
    if (in_array($current_user->user_login, $authorized_admins)) {
        return true;
    }
    
    return false;
}
```

### Emergency Recovery Procedures

**If plugin access is lost**:

1. **Database Emergency Reset**:
   ```sql
   -- Reset authorization settings via database
   DELETE FROM wp_options WHERE option_name = 'slmm_visibility_settings';
   DELETE FROM wp_options WHERE option_name = 'slmm_authorized_admins';
   ```

2. **File System Access**:
   ```php
   // Temporary emergency access file
   // Create: wp-content/plugins/slmm_seo_bundle/emergency-access.php
   <?php
   if (current_user_can('manage_options')) {
       update_option('slmm_authorized_admins', array(wp_get_current_user()->user_login));
       echo 'Emergency access restored';
   }
   ?>
   ```

3. **Plugin Deactivation Recovery**:
   ```php
   // Add to wp-config.php temporarily
   define('SLMM_EMERGENCY_ACCESS', true);
   
   // Check in plugin initialization
   if (defined('SLMM_EMERGENCY_ACCESS') && SLMM_EMERGENCY_ACCESS) {
       return true;  // Bypass all authorization
   }
   ```

### Audit Trail

**Maintain security audit log**:

```php
function log_security_audit($action, $user_id = null, $details = '') {
    global $wpdb;
    
    $user_id = $user_id ?? get_current_user_id();
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    $wpdb->insert(
        $wpdb->prefix . 'slmm_security_log',
        array(
            'timestamp' => current_time('mysql'),
            'action' => $action,
            'user_id' => $user_id,
            'ip_address' => $ip_address,
            'details' => $details
        ),
        array('%s', '%s', '%d', '%s', '%s')
    );
}

// Usage examples
log_security_audit('LOGIN_SUCCESS', $user_id, 'User accessed SLMM features');
log_security_audit('AUTHORIZATION_FAILURE', $user_id, 'Unauthorized access attempt');
log_security_audit('SETTINGS_CHANGE', $user_id, 'API keys updated');
```

---

## Code Review Checklist

### Pre-Commit Security Review

**MANDATORY checks before committing code**:

#### ✅ Authorization & Authentication
- [ ] `slmm_seo_check_visibility_authorization()` called before feature access
- [ ] WordPress capability checks implemented (`manage_options`, `edit_posts`)
- [ ] Emergency access methods preserved
- [ ] No authorization bypasses or hardcoded access

#### ✅ AJAX Handler Security
- [ ] Nonce verification implemented (robust pattern with fallback)
- [ ] Authorization checks in all AJAX handlers
- [ ] Input validation and sanitization
- [ ] Proper error responses (no information leakage)
- [ ] AJAX hooks registered correctly (wp_ajax_ only, no nopriv)

#### ✅ Database Security
- [ ] All queries use prepared statements or WordPress functions
- [ ] No direct variable insertion in SQL
- [ ] Input validation before database operations
- [ ] Proper error handling (no SQL error exposure)

#### ✅ XSS Prevention
- [ ] All dynamic output properly escaped (esc_html, esc_attr, etc.)
- [ ] Context-appropriate escaping functions used
- [ ] Rich content filtered with wp_kses
- [ ] JavaScript data passed through wp_json_encode

#### ✅ Input Validation
- [ ] All user input validated and sanitized
- [ ] Appropriate sanitization functions used
- [ ] Length limits enforced
- [ ] Data type validation implemented
- [ ] Business logic validation included

#### ✅ WordPress Integration
- [ ] Proper hook usage and timing
- [ ] Settings registered with validation callbacks
- [ ] Capabilities properly defined
- [ ] REST API endpoints secured (if used)

#### ✅ Dual-System Compatibility
- [ ] Data localization always includes prompt data
- [ ] Both button and shortcut systems supported
- [ ] Keyboard shortcut system NOT modified
- [ ] Bricks Builder integration secure

#### ✅ AI Integration Security
- [ ] API keys only exposed to authorized admin users
- [ ] Client-side API calls include error handling
- [ ] Rate limiting considerations
- [ ] Secure API key storage

### Security Testing Requirements

**Before marking feature complete**:

1. **Authorization Testing**:
   - Test with unauthorized users
   - Verify emergency access works
   - Check capability requirements

2. **Input Security Testing**:
   - Test with malicious SQL input
   - Test with XSS payloads
   - Test with oversized input

3. **AJAX Security Testing**:
   - Test without nonces
   - Test with invalid nonces
   - Test without proper capabilities

4. **Browser Testing**:
   - Verify no sensitive data in console
   - Check XSS protection in output
   - Test dual-system functionality

### Security Review Template

```markdown
## Security Review: [Feature Name]

### Authorization ✅/❌
- Plugin-level authorization: [PASS/FAIL]
- WordPress capabilities: [PASS/FAIL]
- Emergency access preserved: [PASS/FAIL]

### AJAX Security ✅/❌
- Nonce verification: [PASS/FAIL]
- Input sanitization: [PASS/FAIL]
- Error handling: [PASS/FAIL]

### Database Security ✅/❌
- Prepared statements: [PASS/FAIL]
- Input validation: [PASS/FAIL]
- No SQL injection: [PASS/FAIL]

### XSS Prevention ✅/❌
- Output escaping: [PASS/FAIL]
- Context-appropriate: [PASS/FAIL]
- No unsafe output: [PASS/FAIL]

### Testing Completed ✅/❌
- Authorization tests: [PASS/FAIL]
- Input security tests: [PASS/FAIL]
- Browser validation: [PASS/FAIL]

### Overall Security Status: [APPROVED/NEEDS_WORK]
### Reviewer: [Name]
### Date: [YYYY-MM-DD]
```

---

## Conclusion

The SLMM SEO Bundle implements a **defense-in-depth security model** appropriate for its **admin-only, whitelisted user context**. While the reduced attack surface significantly lowers actual risk levels, maintaining high security standards ensures:

- **Audit compliance** for enterprise environments
- **Future-proofing** against scope changes
- **Best practice adherence** for maintainable code
- **Defense-in-depth** protection

**Key Takeaways**:

1. **Context matters**: Admin-only access dramatically reduces actual vulnerability impact
2. **Client-side AI keys**: Functional requirement, not security flaw
3. **Authorization system**: Critical foundation - never bypass or modify
4. **Dual-system architecture**: Preserve keyboard shortcuts at all costs
5. **WordPress standards**: Follow established patterns for consistency

**Implementation Priority**:
1. **Authorization integration** (highest priority)
2. **AJAX handler security** (prevent unauthorized access)
3. **Database protection** (prevent data corruption)
4. **Input validation** (prevent malformed data)
5. **Output escaping** (prevent content injection)

By following these guidelines, developers can implement secure, maintainable features that integrate properly with the plugin's existing security architecture while respecting its unique requirements and constraints.

---

**Document Maintenance**: This document should be updated whenever new security patterns are established or when the plugin's architecture changes significantly. All security implementations should reference this document for consistency.

**Related Documentation**:
- `/docs/comprehensive-security-audit-report.md` - Current security status
- `/docs/nonce-handling-standards.md` - Detailed nonce implementation patterns
- `/docs/security-vulnerability-analysis-and-remediation.md` - Specific vulnerability fixes
- `/CLAUDE.md` - Overall development guidelines and architecture