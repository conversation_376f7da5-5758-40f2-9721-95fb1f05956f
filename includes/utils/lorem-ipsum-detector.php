<?php
// File: includes/utils/lorem-ipsum-detector.php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class SLMM_Lorem_Ipsum_Detector {
    
    public function __construct() {
        add_action('wp_ajax_start_lorem_ipsum_scan', array($this, 'handle_scan'));
    }

    public function handle_scan() {
        // Log scan attempt
        error_log('[SLMM Lorem Detector] Scan requested by user: ' . wp_get_current_user()->user_login);
        
        // Security checks
        if (!current_user_can('manage_options')) {
            error_log('[SLMM Lorem Detector] Unauthorized access attempt by user: ' . wp_get_current_user()->user_login);
            wp_send_json_error('Unauthorized access');
        }

        if (!check_ajax_referer('slmm_lorem_ipsum_scan', 'nonce', false)) {
            error_log('[SLMM Lorem Detector] Nonce verification failed');
            wp_send_json_error('Security check failed');
        }

        global $wpdb;

        // Define Lorem Ipsum keywords
        $keywords = array('lorem', 'ipsum', 'consectetur', 'adipiscing', 'eiusmod', 'incididunt');

        // Build LIKE clauses and query values
        $like_clauses = array();
        $query_values = array();

        // Get all searchable post types, excluding revisions
        $all_post_types = get_post_types(array('public' => true), 'names');
        unset($all_post_types['revision']);

        // Add post statuses (publish, draft, private)
        $post_statuses = array('publish', 'draft', 'private');
        
        // Add placeholders for post statuses
        $status_placeholders = implode(',', array_fill(0, count($post_statuses), '%s'));
        
        // Add placeholders for post types
        $post_types_placeholders = implode(',', array_fill(0, count($all_post_types), '%s'));

        // Merge post statuses and post types at the beginning of query values
        $query_values = array_merge($post_statuses, array_values($all_post_types));

        // Build LIKE clauses for keywords
        foreach ($keywords as $keyword) {
            $like_clauses[] = 'post_content LIKE %s';
            $query_values[] = '%' . $wpdb->esc_like($keyword) . '%';
        }

        // Create the WHERE clause for keywords
        $where_clause_placeholder = implode(' OR ', $like_clauses);

        // Build the complete SQL query
        $sql = "SELECT ID, post_title, post_type, post_status, post_date
                FROM {$wpdb->posts}
                WHERE post_status IN ($status_placeholders)
                AND post_type IN ($post_types_placeholders)
                AND ($where_clause_placeholder)
                ORDER BY post_status DESC, post_date DESC";

        // Execute the prepared query
        $query = $wpdb->prepare($sql, $query_values);
        error_log('[SLMM Lorem Detector] Executing database query');
        
        $results = $wpdb->get_results($query);
        
        // Check for database errors
        if ($wpdb->last_error) {
            error_log('[SLMM Lorem Detector] Database error: ' . $wpdb->last_error);
            wp_send_json_error('Database error occurred during scan');
        }
        
        error_log('[SLMM Lorem Detector] Database query completed. Found ' . count($results) . ' results.');

        // Process results
        $response_html = $this->prepare_results_table($results);
        
        // Log successful scan
        error_log('[SLMM Lorem Detector] Scan completed. Found ' . count($results) . ' results.');
        
        // Send WordPress standard AJAX success response
        wp_send_json_success(array('html' => $response_html));
    }

    private function prepare_results_table($results) {
        if (empty($results)) {
            return '<div class="slmm-lorem-no-results">
                        <div class="slmm-lorem-icon">✅</div>
                        <h3>No Lorem Ipsum Found</h3>
                        <p>Great! No Lorem Ipsum placeholder text was detected in your content.</p>
                    </div>';
        }

        // Count results by status
        $status_counts = array('publish' => 0, 'draft' => 0, 'private' => 0);
        foreach ($results as $result) {
            if (isset($status_counts[$result->post_status])) {
                $status_counts[$result->post_status]++;
            }
        }

        $html = '<div class="slmm-lorem-results">';
        
        // Summary section
        $html .= '<div class="slmm-lorem-summary">';
        $html .= '<h3>Lorem Ipsum Detection Results</h3>';
        $html .= '<div class="slmm-lorem-counts">';
        $html .= '<span class="slmm-status-count slmm-status-publish">Published: ' . $status_counts['publish'] . '</span>';
        $html .= '<span class="slmm-status-count slmm-status-draft">Drafts: ' . $status_counts['draft'] . '</span>';
        $html .= '<span class="slmm-status-count slmm-status-private">Private: ' . $status_counts['private'] . '</span>';
        $html .= '<span class="slmm-status-count slmm-status-total">Total: ' . count($results) . '</span>';
        $html .= '</div>';
        $html .= '</div>';

        // Results table
        $html .= '<div class="slmm-lorem-table-wrapper">';
        $html .= '<table class="slmm-lorem-table">';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Title</th>';
        $html .= '<th>Type</th>';
        $html .= '<th>Status</th>';
        $html .= '<th>Date</th>';
        $html .= '<th>Actions</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';

        foreach ($results as $result) {
            $edit_url = get_edit_post_link($result->ID);
            $view_url = get_permalink($result->ID);
            $post_type_obj = get_post_type_object($result->post_type);
            $post_type_label = $post_type_obj ? $post_type_obj->labels->singular_name : $result->post_type;
            
            // Status badge
            $status_class = 'slmm-status-badge slmm-status-' . $result->post_status;
            $status_label = ucfirst($result->post_status);
            
            $html .= '<tr>';
            $html .= '<td class="slmm-lorem-title">' . esc_html($result->post_title ?: '(No Title)') . '</td>';
            $html .= '<td class="slmm-lorem-type">' . esc_html($post_type_label) . '</td>';
            $html .= '<td class="slmm-lorem-status"><span class="' . $status_class . '">' . $status_label . '</span></td>';
            $html .= '<td class="slmm-lorem-date">' . mysql2date('M j, Y', $result->post_date) . '</td>';
            $html .= '<td class="slmm-lorem-actions">';
            
            if ($edit_url) {
                $html .= '<a href="' . esc_url($edit_url) . '" class="slmm-action-link slmm-edit-link" target="_blank">Edit</a>';
            }
            
            if ($result->post_status === 'publish' && $view_url) {
                $html .= '<a href="' . esc_url($view_url) . '" class="slmm-action-link slmm-view-link" target="_blank">View</a>';
            }
            
            $html .= '</td>';
            $html .= '</tr>';
        }

        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';
        $html .= '</div>';

        return $html;
    }

}