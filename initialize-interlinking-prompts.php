<?php
// Temporary script to initialize AI interlinking prompts
require_once('/Users/<USER>/Studio/v4/wp-config.php');

// Default interlinking prompts (copied from the method)
$default_prompts = array(
    'parent' => array(
        'title' => 'Parent Links',
        'provider' => 'openai',
        'model' => 'gpt-4o',
        'temperature' => '0.7',
        'max_tokens' => '1000',
        'prompt' => 'Analyze the following content and create 3 variations of anchor text for linking FROM child pages TO parent pages (upward hierarchy linking for SEO authority flow). Focus on semantic relevance and natural integration.

Content to analyze: {content}
Target parent page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Exact keyword/entity match
2. Descriptive Phrase: Natural explanatory context 
3. LSI-Rich: Semantic variants with related keywords

Format as JSON with surrounding context for each suggestion.'
    ),
    'child' => array(
        'title' => 'Child Links',
        'provider' => 'openai',
        'model' => 'gpt-4o',
        'temperature' => '0.7',
        'max_tokens' => '1000',
        'prompt' => 'Analyze the following content and create 3 variations of anchor text for linking FROM parent pages TO child pages (downward hierarchy linking for topic exploration). Focus on topic expansion and user journey.

Content to analyze: {content}
Target child page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Specific topic/keyword match
2. Descriptive Phrase: Exploratory context phrase
3. LSI-Rich: Topic-related semantic variants

Format as JSON with surrounding context for each suggestion.'
    ),
    'sibling' => array(
        'title' => 'Sibling Links',
        'provider' => 'openai',
        'model' => 'gpt-4o',
        'temperature' => '0.7',
        'max_tokens' => '1000',
        'prompt' => 'Analyze the following content and create 3 variations of anchor text for linking BETWEEN sibling pages (same hierarchy level for contextual relevance). Focus on related topics and cross-references.

Content to analyze: {content}
Target sibling page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Related topic match
2. Descriptive Phrase: Contextual bridge phrase
3. LSI-Rich: Cross-topic semantic variants

Format as JSON with surrounding context for each suggestion.'
    ),
    'semantic' => array(
        'title' => 'Semantic Links',
        'provider' => 'openai',
        'model' => 'gpt-4o',
        'temperature' => '0.7',
        'max_tokens' => '1000',
        'prompt' => 'Analyze the following content and create 3 variations of anchor text for semantic/contextual linking (topic-based relevance regardless of hierarchy). Focus on natural language and user intent.

Content to analyze: {content}
Target page: {target_page}
Page hierarchy: {page_hierarchy}
Linking rules: {linking_rules}

Create 3 anchor text variations:
1. Direct Entity: Core concept match
2. Descriptive Phrase: Natural context integration
3. LSI-Rich: Conceptual semantic variants

Format as JSON with surrounding context for each suggestion.'
    )
);

$result = update_option('slmm_interlinking_prompts', $default_prompts);

if ($result) {
    echo "✅ Successfully created slmm_interlinking_prompts option in database\n";
    echo "You can now delete this file: " . __FILE__ . "\n";
} else {
    echo "❌ Failed to create the option\n";
}
?>